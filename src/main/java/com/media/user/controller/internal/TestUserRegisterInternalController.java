package com.media.user.controller.internal;

import com.media.core.request.ClientInfoContext;
import com.media.user.dto.request.*;
import com.media.user.dto.response.*;
import com.media.user.enums.PlatformEnums;
import com.media.user.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/internal/user")
public class TestUserRegisterInternalController {

    @Autowired
    UserRegisterService userRegisterService;

    @Autowired
    PhoneService phoneService;

    @Autowired
    SmsService smsService;

    /**
     * 内部测试注册接口 TODO 上线需关闭
     * @param request
     */
    @PostMapping("/test/register")
    public AuthTokenResponse testRegister(@Validated @RequestBody ClientUserRequest request){
        log.info("=== TEST REGISTER START === Request received: {}", request);
        try {
        String platformType = ClientInfoContext.get().getPlatformType();
        request.setSourceType(PlatformEnums.get(platformType).getCode());
            log.info("=== TEST REGISTER DEBUG === Platform type: {}, source type: {}", platformType, request.getSourceType());

            AuthTokenResponse response = userRegisterService.register(request);
            log.info("=== TEST REGISTER SUCCESS === Registration completed, response: {}", response);
            return response;
        } catch (Exception e) {
            log.error("=== TEST REGISTER ERROR === Registration failed, request: {}, error: {}", request, e.getMessage(), e);
            throw e;
        }
    }


    /**
     * 内部查询发送验证码 TODO 上线需关闭
     * @param request
     */
    @PostMapping("/test/getSendCode")
    public String testGetSendCode(@Validated @RequestBody TestGetCodeRequest request){
        log.info("testGetSendCode: {}", request);
        //邮箱
        if(StringUtils.isNotEmpty(request.getEmail())){
            return smsService.getEmailCode(request.getUid(), request.getEmail(), request.getBusinessType());
        }else if(StringUtils.isNotEmpty(request.getPhone())){
            //手机号
            return phoneService.getPhoneCode(request.getUid(), request.getCountryCode(), request.getPhonePrefix(), request.getPhone(), request.getBusinessType());
        }else{
            return "";
        }
    }





}

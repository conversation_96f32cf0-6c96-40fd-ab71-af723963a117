package com.media.user.service;

import com.media.user.dto.request.UserDeviceRequest;
import com.media.user.feign.client.DeviceClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class DeviceService {
    @Autowired
    private DeviceClient deviceClient;

    @Async("threadPoolTaskExecutor")
    public void AsyncUserBindDevice(String deviceId, Long userId) {
        try {
            UserBindDevice(deviceId, userId);
        } catch (Exception e) {
            log.error("用户绑定异常 deviceId: {}, userId: {}", deviceId, userId, e);
        }
    }

    public void UserBindDevice(String deviceId, Long userId) {
        if (StringUtils.isBlank(deviceId) || userId == null || userId <= 0) {
            return;
        }
        UserDeviceRequest request = new UserDeviceRequest();
        request.setDeviceId(deviceId);
        request.setUserId(userId);
        log.info("UserBindDevice start deviceId {} userId {}", deviceId, userId);
        deviceClient.UserBindDevice(request);
    }

    @Async("threadPoolTaskExecutor")
    public void AsyncUpdateUserDevice(Map<String, String> headers) {
        try {
            UpdateUserDevice(headers);
        } catch (Exception e) {
            log.error("request:{} UpdateUserDevice error", headers, e);
        }
    }

    public void UpdateUserDevice(Map<String, String> headers) {
        log.info("UpdateUserDevice headers: {}", headers);
        deviceClient.UpdateUserDevice(headers);
    }
}

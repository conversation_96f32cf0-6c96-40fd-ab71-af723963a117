package com.media.core.exception;

import com.alibaba.fastjson.JSONObject;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;


@Slf4j
public class ExceptionResponse extends ResponseEntity<ExceptionCode> {

    public ExceptionResponse(ExceptionCode exceptionCode, LinkedMultiValueMap<String, String> linkedMultiValueMap) {
        super(exceptionCode, linkedMultiValueMap, HttpStatus.valueOf(exceptionCode.status()));
    }

    public static ExceptionResponse create(ExceptionCode exceptionCode, Throwable e) {
        LinkedMultiValueMap<String, String> m = new LinkedMultiValueMap<>();
        if (isInternal()) {
            m.add("feign-exception", e.getClass().getName());
            try {
                m.add("feign-message", URLEncoder.encode(exceptionCode.getMessage(), "utf8"));
                if (exceptionCode.getCode() != null) {
                    m.add("feign-code", String.valueOf(exceptionCode.getCode()));
                }
                if (exceptionCode.getArgs() != null) {
                    m.add("feign-args", URLEncoder.encode(JSONObject.toJSONString(exceptionCode.getArgs()), "utf8"));
                }
                if (exceptionCode.getParam() != null) {
                    m.add("feign-param", String.valueOf(exceptionCode.getParam()));
                }
            } catch (UnsupportedEncodingException unsupportedEncodingException) {
                log.error(unsupportedEncodingException.getMessage(), e);
            }
        }
        return new ExceptionResponse(exceptionCode, m);
    }

    public static boolean isInternal() {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (servletRequestAttributes != null) {
            HttpServletRequest r = servletRequestAttributes.getRequest();
            return r.getRequestURI().startsWith("/internal/");
        }
        return false;
    }
}

package com.media.user.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Data
public class PartnerAccessTokenRequest {
    
    @NotNull(message = "partner_id不能为空")
    @JsonProperty("partner_id")
    private Long partnerId;

    @NotBlank(message = "user_id不能为空")
    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("user_name")
    private String userName;

    @JsonProperty("avatar")
    private String avatar;

    @NotBlank(message = "sign不能为空")
    @JsonProperty("sign")
    private String sign;
} 
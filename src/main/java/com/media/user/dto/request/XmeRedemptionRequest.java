package com.media.user.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * XME兑换请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class XmeRedemptionRequest {
    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 数量
     */
    private Integer amount;

    /**
     * 币种
     */
    private String ccy;

    /**
     * 网络
     */
    private String network;

    /**
     * 事件ID
     */
    private Integer eventId;

    /**
     * 幂等ID
     */
    private String idempotent;
}

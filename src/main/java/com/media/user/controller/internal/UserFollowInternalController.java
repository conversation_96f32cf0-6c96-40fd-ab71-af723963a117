package com.media.user.controller.internal;

import com.alibaba.fastjson.JSONObject;
import com.media.core.constant.PageResponse;
import com.media.user.dto.query.UserFollowingMemberQuery;
import com.media.user.dto.request.UserFollowBatchRequest;
import com.media.user.dto.request.internal.TwitterUserFollowBatchRequest;
import com.media.user.dto.request.internal.UserRegisterRequest;
import com.media.user.dto.response.UserFollowingMemberResponse;
import com.media.user.dto.response.internal.PreRegisterUserResponse;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.service.UserFollowService;
import com.media.user.service.internal.UserFollowInternalService;
import com.media.user.service.internal.UserInternalService;
import com.media.core.utils.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/user/internal/user/follow")
public class UserFollowInternalController {


    @Autowired
    UserFollowService userFollowService;

    @Autowired
    UserInternalService userInternalService;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    UserFollowInternalService userFollowInternalService;

    /**
     * 内部批量关注用户
     * @return
     */
    @PostMapping("/batch")
    public void batchFollow(@Validated @RequestBody TwitterUserFollowBatchRequest request){
        if(StringUtils.isBlank(request.getTwitterId()) || request.getFollowers().size() == 0){
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }

        String param = MD5Util.getMD5(JSONObject.toJSONString(request.getFollowers()));
        RLock rLock = redissonClient.getLock("user:internal:follow:batch:" + param);
        try {
            rLock.lock(3000, TimeUnit.MILLISECONDS);
            this.batchFollowDeal(request);
        }finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    private void batchFollowDeal(TwitterUserFollowBatchRequest request){
        UserRegisterRequest userRegisterRequest = new UserRegisterRequest()
                .setThirdPlatType(1)
                .setTwitterId(request.getTwitterId());
        //获取关注者 uid
        PreRegisterUserResponse preRegisterUserResponse = userInternalService.preRegister(userRegisterRequest);

        List<Long> uids = new ArrayList<>();
        for(String twitterId : request.getFollowers()){
            UserRegisterRequest followerRequest = new UserRegisterRequest()
                    .setThirdPlatType(1)
                    .setTwitterId(twitterId);
            PreRegisterUserResponse response = userInternalService.preRegister(followerRequest);
            if(response != null) {
                uids.add(response.getUid());
            }
        }
        Set<Long> uniqueSet = new HashSet<>(uids);
        uids = new ArrayList<>(uniqueSet);
        if(uids.size() > 0){
            UserFollowBatchRequest userFollowBatchRequest = new UserFollowBatchRequest();
            userFollowBatchRequest.setUids(uids);
            userFollowService.batchFollow(preRegisterUserResponse.getUid(), userFollowBatchRequest);
        }
    }



    /**
     * 我的关注列表
     * @return
     */
    @PostMapping("/followers")
    public PageResponse<UserFollowingMemberResponse> followers(@RequestBody UserFollowingMemberQuery query){
        return userFollowInternalService.getUserFollowMemberPage(query);
    }

    /**
     * 查询用户粉丝列表
     * @return
     */
    @PostMapping("/fans")
    public PageResponse<UserFollowingMemberResponse> fans(@RequestBody UserFollowingMemberQuery query) {
        return userFollowInternalService.getFansPage(query);
    }



}

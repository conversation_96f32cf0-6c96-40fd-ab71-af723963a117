package com.media.user.service;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.core.request.ClientInfoContext;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.*;
import com.media.user.dto.request.*;
import com.media.user.dto.response.*;
import com.media.user.enums.*;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.core.id.InviteCodeGenerator;
import com.media.user.mapper.*;
import com.media.user.service.cache.ClientUserCacheService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;

@Slf4j
@Service
public class UserAddressService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    ClientUserService clientUserService;

    @Autowired
    UserRegisterService userRegisterService;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @Autowired
    ClientUserMapper clientUserMapper;

    @Autowired
    UserFamilyService userFamilyService;

    @Autowired
    UserAddressMapper userAddressMapper;

    @Autowired
    UserPropsMapper userPropsMapper;

    @Autowired
    UserLoginLogService userLoginLogService;

    @Autowired
    InviteCodeGenerator inviteCodeGenerator;

    private final Long maxInviteNumber = 10L;

    public AuthTokenResponse addressLogin(AddressLoginRequest request) {
        Object object = stringRedisTemplate.opsForValue().get("user:address:" + request.getAddress());
        UserAddressModel userAddressModel;
        if (object == null) {
            userAddressModel = getUserByAddress(request.getAddress());
            if (userAddressModel == null) {
                throw new ApiException(MediaUserExceptionCodeApi.ADDRESS_NOT_REGISTER);
            }
            stringRedisTemplate.opsForValue().set(MediaUserConstant.USER_ADDRESS + request.getAddress(), JSON.toJSONString(userAddressModel));
            stringRedisTemplate.expire(MediaUserConstant.USER_ADDRESS + request.getAddress(), Duration.ofDays(7));
        } else {
            userAddressModel = JSON.parseObject(object.toString(), UserAddressModel.class);
        }

        // 异步记录用户登录日志信息
        userLoginLogService.loginLog(userAddressModel.getUserId(), "wallet", PlatformEnums.get(ClientInfoContext.get().getPlatformType()).getCode());

        // 生成token
        return clientUserService.generateToken(userAddressModel.getUserId(), MediaUserConstant.timeout, MediaUserConstant.timeUnit);
    }

    private UserAddressModel getUserByAddress(String address) {
        QueryWrapper<UserAddressModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("address", address);
        return userAddressMapper.selectOne(queryWrapper);
    }

    private UserAddressModel getUserAddress(Long uid) {
        QueryWrapper<UserAddressModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", uid);
        return userAddressMapper.selectOne(queryWrapper);
    }

    @Transactional
    public AuthTokenResponse addressRegister(HttpServletRequest httpRequest, ClientUserRequest request) {
        UserAddressModel userAddressModel = getUserByAddress(request.getAddress());
        if (userAddressModel != null) {
            throw new ApiException(MediaUserExceptionCodeApi.ADDRESS_IS_REGISTER);
        }
        //注册
        AuthTokenResponse authTokenResponse = userRegisterService.register(request);
        //写入关联表
        userAddressModel = new UserAddressModel();
        userAddressModel.setUserId(authTokenResponse.getUserId());
        userAddressModel.setAddress(request.getAddress());
        userAddressMapper.insert(userAddressModel);
        //写入缓存
        stringRedisTemplate.opsForValue().set(MediaUserConstant.USER_ADDRESS + request.getAddress(), JSON.toJSONString(userAddressModel));
        stringRedisTemplate.expire(MediaUserConstant.USER_ADDRESS + request.getAddress(), Duration.ofDays(7));
        return authTokenResponse;
    }

    public Map<String, Object> addressInvite(Long uid) {
        Map<String, Object> result = new HashMap<>();
        Long inviteSize = getUserInviteSize(uid);
        //当前已经邀请的用户数量
        result.put("inviteSize", inviteSize);
        //0 未达标，1 已达标，2 已领取
        if (hasFamilyToken(uid)) {
            result.put("inviteStatus", 2);
        } else {
            if (inviteSize < maxInviteNumber) {
                result.put("inviteStatus", 0);
            } else {
                result.put("inviteStatus", 1);
            }
        }
        ClientUserResponse userResponse =  clientUserCacheService.me(uid);
        String inviteCode = userResponse.getInviteCode();
        if(userResponse.getFaceLivenessStatus() == 0){
            inviteCode = ""; //人脸识别未认证，邀请码为空
        }
        result.put("inviteCode", inviteCode);
        result.put("address", getUserAddress(uid).getAddress());
        return result;
    }

    public Map<String, Object> addressInfo() throws ParseException {
        Map<String, Object> result = new HashMap<>();
        result.put("pushTimestamp", stopReceiveTime("2025-02-25 23:59:59"));
        result.put("appTimestamp", stopReceiveTime("2024-10-15 23:59:59"));
        result.put("maxInviteNumber", maxInviteNumber);
        result.put("addressList", getAddressList());
        return result;
    }

    private List<String> getAddressList() {
        List<String> list = new ArrayList<>();
        //获取最新的 100 个邮箱地址
        List<String> addressList = stringRedisTemplate.opsForList().range(MediaUserConstant.USER_RECEIVE_LIST, 0, 100);
        if (addressList != null) {
            for (String address : addressList) {
                list.add(addressChange(address));
            }
        }
        //虚假用户
        for (int i = 0; i < 10; i++) {
            list.add(addressChange("0x" + inviteCodeGenerator.generateInviteCode(40)));
        }
        return list;
    }

    public String addressChange(String address) {
        int len = address.length();
        return address.substring(0, 6) + "****" + address.substring(len - 4, len);
    }

    private boolean hasFamilyToken(Long uid) {
        QueryWrapper<UserPropsModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", uid);
        queryWrapper.eq("props_id", PropsTypeEnums.ATOM_FAMILY_CARD.getCode());
        return userPropsMapper.exists(queryWrapper);
    }

    @Transactional
    public boolean receiveToken(Long uid) {
        if (hasFamilyToken(uid)) {
            throw new ApiException(MediaUserExceptionCodeApi.ADDRESS_IS_RECEIVED_TOKEN);
        }
        //检查用户是否满足条件
        if (getUserInviteSize(uid) < maxInviteNumber) {
            throw new ApiException(MediaUserExceptionCodeApi.ADDRESS_CAN_NOT_RECEIVED);
        }
        try {
            //判断是否已经超过截至时间
//            if ((System.currentTimeMillis() / 100) > stopReceiveTime()) {
//                throw new ApiException(MediaUserExceptionCodeApi.RECEIVE_IS_STOP);
//            }
            //创建关系 和 家族
            createUserProps(uid);
            //将已经领取的用户，放入redis
            stringRedisTemplate.opsForList().leftPush(MediaUserConstant.USER_RECEIVE_LIST, getUserAddress(uid).getAddress());
            return true;
        } catch (Exception e) {
            log.error("UserAddressService.receiveToken error, uid:{}, error:{}", uid, e.getMessage());
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }
    }

    private long stopReceiveTime(String dateString) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = format.parse(dateString);
        return date.getTime() / 1000;
    }

    private long getUserInviteSize(Long uid) {
        //获取用户当前已经邀请的人数
//        String inviteKey = MediaUserConstant.USER_INVITE_REDIS_KEY_PREFIX + uid;
//        Long inviteSize = stringRedisTemplate.opsForHash().size(inviteKey);
//        return inviteSize == null ? 0 : inviteSize;
        return 0;
    }

    private void createUserProps(Long uid) {
        //创建关系
        UserPropsModel userPropsModel = new UserPropsModel();
        userPropsModel.setUserId(uid);
        userPropsModel.setPropsId(PropsTypeEnums.ATOM_FAMILY_CARD.getCode());
        userPropsMapper.insert(userPropsModel);
        //创建家族
        userFamilyService.createUserFamily(uid, PropsTypeEnums.ATOM_FAMILY_CARD);
    }
}

package com.media.user.service;


import com.media.user.convert.mapper.UserFeedbackConvert;
import com.media.user.domain.UserFeedbackModel;
import com.media.user.dto.request.UserFeedbackRequest;
import com.media.user.enums.UserStatusEnum;
import com.media.core.id.RedisIdGenerator;
import com.media.user.mapper.UserFeedbackMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;


@Service
public class UserFeedbackService {

    @Autowired
    private RedisIdGenerator redisIdGenerator;

    @Autowired
    private UserFeedbackMapper userFeedbackMapper;

    @Transactional(rollbackFor = Exception.class)
    public void submitFeedback(UserFeedbackRequest request){
        UserFeedbackModel userFeedbackModel = UserFeedbackConvert.INSTANCE.req2Model(request);
        userFeedbackModel
                .setId(redisIdGenerator.generate())
                .setStatus(UserStatusEnum.DISABLED.getStatus())
                .setCreatedTime(new Date());
        userFeedbackMapper.insert(userFeedbackModel);
    }
}
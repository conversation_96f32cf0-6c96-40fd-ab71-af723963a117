package com.media.user.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.media.user.constant.MediaUserConstant;
import com.media.user.dto.response.PointDetailsResponse;
import com.media.user.service.UserFamilyService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class FamilyAssetsConsumer {

    @Autowired
    private UserFamilyService userFamilyService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

//    @KafkaListener(topics = KafkaTopicConstant.FAMILY_ASSETS, groupId = KafkaTopicConstant.USER_GROUP)
    public void updateFamilyAssets(String message, Acknowledgment ack) {

        PointDetailsResponse pointDetailsResponse = JSON.parseObject(message, PointDetailsResponse.class);
        log.info("FamilyAssetsConsumer.updateFamilyAssets id:{}, data:{}", pointDetailsResponse.getId(), message);
        RLock rLock = redissonClient.getLock(MediaUserConstant.FAMILY_ASSETS_PREFIX + pointDetailsResponse.getUid());
        try {
            rLock.lock(5000, TimeUnit.MILLISECONDS);
            if (stringRedisTemplate.opsForValue().setIfAbsent(MediaUserConstant.FAMILY_ASSETS_REPEAT_PREFIX + pointDetailsResponse.getId(), pointDetailsResponse.getId().toString(), 5, TimeUnit.HOURS)) {
                // 如果设置成功，表示这是第一次请求，执行业务逻辑
                userFamilyService.updateFamilyAssets(pointDetailsResponse);
            }
            ack.acknowledge();
        }finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }
}

package com.media.core.config.twitter;

import com.twitter.clientlib.auth.TwitterOAuth20Service;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "twitter")
public class TwitterConfiguration {

    private String clientId;
    private String clientSecret;
    private String redirectUri;
    private String accessToken;
    private String apiKeyId;
    private String apiKeySecret;


    @Value("${twitter.official.account.id:}")
    private String officialAccountId;

    @Bean
    public TwitterOAuth20Service twitterOAuth20Service() {
        return new TwitterOAuth20Service(
                clientId,
                clientSecret,
                redirectUri,
                "tweet.read users.read follows.read like.read list.read offline.access"
        );
    }

}

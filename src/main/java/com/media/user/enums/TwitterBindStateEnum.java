package com.media.user.enums;

import lombok.Getter;

import java.util.function.Supplier;

/**
 * 是否绑定(0:未绑定;1:已绑定;2:取消绑定)
 */
@Getter
public enum TwitterBindStateEnum implements Supplier<Byte> {

    NOBIND(Byte.valueOf("0")), BIND(Byte.valueOf("1")), UNBIND(Byte.valueOf("2"));

    private final Byte state;

    TwitterBindStateEnum(Byte state) {
        this.state = state;
    }

    @Override
    public Byte get() {
        return this.state;
    }
}

package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.core.request.ClientInfoContext;
import com.media.user.dto.response.InviteShareResponse;
import com.media.user.dto.response.ShareResponse;
import com.media.user.service.ShareContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/user/api/share")
public class ShareContentController {

    @Autowired
    private ShareContentService shareContentService;

    /**
     * 生成分享文案
     */
    @PostMapping("/content")
    @RequireAuth
    public Map<String, Object> shareContent(){
        return shareContentService.shareContent(CloudSession.getUid(), ClientInfoContext.getLanguage());
    }

    /**
     * 生成排行榜分享文案
     */
    @PostMapping("/task/leaderboard")
    @RequireAuth
    public ShareResponse taskContent(){
        return shareContentService.getTaskShareContent(CloudSession.getUid(), ClientInfoContext.getLanguage());
    }

    /**
     * 活动分享
     */
    @GetMapping("/activity")
    @RequireAuth
    public ShareResponse shareActivity(@RequestParam("activity_id") String activityID){
        return shareContentService.getActivityContent(CloudSession.getUid(), ClientInfoContext.getLanguage(),activityID);
    }

    @GetMapping("/invite")
    @RequireAuth
    public InviteShareResponse inviteShare(@RequestParam("activity_id") String activityID){
        return shareContentService.getInviteShareContent(CloudSession.getUid(), ClientInfoContext.getLanguage(),activityID);
    }
}

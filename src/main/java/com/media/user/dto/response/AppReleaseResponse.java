package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AppReleaseResponse {

    private Long id;

    private String name;

    private String title;

    private String version;

    private String minVersion;

    private String platformType;

    private String url;

    private String qrCode;

    private Long packageSize;

    private String updateContent;

    private boolean forceUpdate;
}
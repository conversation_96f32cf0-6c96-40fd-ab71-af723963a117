package com.media.user.controller.internal;

import com.google.common.eventbus.EventBus;
import com.media.core.constant.BeanConstant;
import com.media.user.dto.request.NewBieTaskRequest;
import com.media.user.service.AwsFaceService;
import com.media.user.service.UserInviteFaceRecordService;
import com.xme.xme_base_depends.enums.UserBehaviorEventEnum;
import com.xme.xme_base_depends.mq.message.UserFinishEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/internal/face")
public class FaceInternalController
{
    @Autowired
    AwsFaceService awsFaceService;

    @Autowired
    @Qualifier(value = BeanConstant.USER_EVENT_BUS_BEAN)
    private EventBus userEventBus;

    /**
     * TODO Test send xme
     */
    @PostMapping("/send")
    public void sendXme(@Validated @RequestBody NewBieTaskRequest request){
        // 更新用户人脸识别状态(数据库和缓存)
        awsFaceService.updateUserFaceStatus(request.getUid(), null);
        userEventBus.post(new UserFinishEvent( UserBehaviorEventEnum.FACE_FINISH,request.getUid()));
    }

    /**
     * TODO Test del face
     */
    @GetMapping("/delFace")
    public void delFace(Long uid, String faceId){
        awsFaceService.delFace(uid, faceId);
    }
    @GetMapping("/delCollection")
    public void delCollection(Long uid){
        awsFaceService.delCollection(uid);
    }
    @GetMapping("/collection")
    public String getCollection(){
        return awsFaceService.getCollection();
    }
}

package com.media.user.feign.client;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSONObject;
import com.media.user.feign.vo.ClientUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class RecommendClient {

    @Value(value = "${recommend.uri}")
    private String recommendUri;

    @Value(value = "${recommend.apiKey}")
    private String recommendApiKey;

    /**
     * 发送用户信息给推荐系统
     * @param uid
     */
    @Async("threadPoolTaskExecutor")
    public void recommendSendUserId(String uid) {
        this.recommendSend(uid);
    }


    public Integer recommendSend(String uid) {
        log.info("recommendSend pushing... uid = {}", uid);
        ClientUserVo clientUserVo = new ClientUserVo();
        clientUserVo.setUserId(uid);
        HttpRequest httpRequest = new HttpRequest(UrlBuilder.of(recommendUri + "/api/user"));
        httpRequest.header("X-API-Key",recommendApiKey);
        httpRequest.method(Method.POST);
        httpRequest.body(JSONObject.toJSONString(clientUserVo));
        try (HttpResponse httpResponse = httpRequest.execute()) {
            if (httpResponse.isOk()) {
                String responseBoy = httpResponse.body();
                log.info("recommendSend return data: {}", responseBoy);
                JSONObject jsonObject = JSONObject.parseObject(responseBoy);
                return jsonObject.getInteger("RowAffected");
            }else{
                return 0;
            }
        }
    }

    /**
     * 发送用户信息给推荐系统
     * @param uid
     */
    @Async("threadPoolTaskExecutor")
    public void recommendSendUserIdAndTwitterId(String uid,String twitterId) {
        log.info("recommendSend pushing... uid = {}", uid);
        ClientUserVo clientUserVo = new ClientUserVo();
        clientUserVo.setUserId(uid);
        clientUserVo.setTwitterId(twitterId);
        HttpRequest httpRequest = new HttpRequest(UrlBuilder.of(recommendUri + "/api/user"));
        httpRequest.header("X-API-Key",recommendApiKey);
        httpRequest.method(Method.POST);
        httpRequest.body(JSONObject.toJSONString(clientUserVo));
        try (HttpResponse httpResponse = httpRequest.execute()) {
            if (httpResponse.isOk()) {
                String responseBoy = httpResponse.body();
//                System.out.println(responseBoy);
                log.info("recommendSend pushing return data: {}", responseBoy);
            }
        }
    }

}

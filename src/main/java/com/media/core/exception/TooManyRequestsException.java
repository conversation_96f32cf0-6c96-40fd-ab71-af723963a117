package com.media.core.exception;


import org.springframework.http.HttpStatus;

public class TooManyRequestsException extends BaseException {

    public TooManyRequestsException() {
        this(HttpStatus.TOO_MANY_REQUESTS.getReasonPhrase());
    }

    public TooManyRequestsException(String message) {
        this(HttpStatus.TOO_MANY_REQUESTS.value(), message, null);
    }

    public TooManyRequestsException(String message, Integer code) {
        this(HttpStatus.TOO_MANY_REQUESTS.value(), message, code);
    }

    public TooManyRequestsException(Integer status, String message) {
        this(status, message, null);
    }

    public TooManyRequestsException(Integer status, String message, Integer code) {
        super(status, message, code);
    }

    public TooManyRequestsException(Throwable cause) {
        super(cause);
        setStatus(HttpStatus.TOO_MANY_REQUESTS.value());
    }

}

package com.media.user.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.media.user.domain.UserSignInModel;
import com.media.user.dto.request.internal.SignInEventRequest;
import com.media.user.dto.response.SignTaskRuleResponse;
import com.media.user.dto.response.internal.SignInJobResponse;
import com.media.user.enums.SignInJobEventEnums;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.UserPointClient;
import com.media.core.id.RedisIdGenerator;
import com.media.user.mapper.*;
import com.media.user.mq.provider.UserSendMessageProvider;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserSignTaskService {

    @Autowired
    RedisIdGenerator redisIdGenerator;

    @Autowired
    UserSignInMapper userSignInMapper;

    @Autowired
    UserPointClient userPointClient;

    @Autowired
    ClientUserService clientUserService;

    @Autowired
    UserSendMessageProvider userSendMessageProvider;

    /**
     * 查询签到规则
     * @param uid
     */
    public Map<String, Object> getSignTaskRule(Long uid){
        //签到配置
        List<SignInJobResponse> list = this.signInTaskRule();
        Map<Long, Integer> configMap = list.stream().collect(Collectors.toMap(SignInJobResponse::getId, SignInJobResponse::getPointChangeCount));

        String today = DateUtil.today();
        log.info("sign date: {} - {}", uid, today);

        Map<String, Object> rtnMap = new HashMap<>();
        Integer totalPoint = configMap.values().stream().mapToInt(Integer::intValue).sum();
        rtnMap.put("totalPoint", totalPoint); //总的积分数
        List<SignTaskRuleResponse> listRules = new ArrayList<>();

        if(uid != null && uid.compareTo(0L) > 0){
            // 检查当天是否已经签到
            UserSignInModel signToday = userSignInMapper.existsByUidAndSignDate(uid, today);
            if(signToday != null){
                SignTaskRuleResponse response = null;
                for(Long eventId : configMap.keySet()){
                    response = new SignTaskRuleResponse();
                    response.setDay(Objects.requireNonNull(SignInJobEventEnums.getEnumsByEventId(eventId)).getSortNumber());
                    response.setPoint(configMap.get(eventId));
                    response.setSign(0);
                    if(response.getDay() <= signToday.getDayIndex()){
                        response.setSign(1);
                    }
                    listRules.add(response);
                }

                //规则
                rtnMap.put("rules", listRules);

                rtnMap.put("today", signToday.getDayIndex()); //当前是第几天
                rtnMap.put("sign", 1); //当天是否签到
                rtnMap.put("continue", signToday.getDayIndex()); //连续的签到天数
                return rtnMap;
            }

            //当天无签到，检查昨天签到
            String yesterday = DateUtil.yesterday().toDateStr(); // 获取前一天的日期
            log.info("yesterday date: {} - {}", uid,  yesterday);
            UserSignInModel signYesterday = userSignInMapper.existsByUidAndSignDate(uid, yesterday);
            log.info("yesterday is sign: {} - {}", uid, signYesterday);

            if(signYesterday != null && signYesterday.getDayIndex() < list.size()){
                SignTaskRuleResponse response = null;
                for(Long eventId : configMap.keySet()){
                    response = new SignTaskRuleResponse();
                    response.setDay(Objects.requireNonNull(SignInJobEventEnums.getEnumsByEventId(eventId)).getSortNumber());
                    response.setPoint(configMap.get(eventId));
                    response.setSign(0);
                    if(response.getDay() <= signYesterday.getDayIndex()){
                        response.setSign(1);
                    }
                    listRules.add(response);
                }

                //规则
                rtnMap.put("rules", listRules);

                rtnMap.put("today", signYesterday.getDayIndex() + 1); //当前是第几天
                rtnMap.put("sign", 0); //当天是否签到
                rtnMap.put("continue", signYesterday.getDayIndex()); //连续的签到天数
                return rtnMap;
            }
        }

        //默认没有签到
        SignTaskRuleResponse response = null;
        for(Long eventId : configMap.keySet()){
            response = new SignTaskRuleResponse();
            response.setDay(Objects.requireNonNull(SignInJobEventEnums.getEnumsByEventId(eventId)).getSortNumber());
            response.setPoint(configMap.get(eventId));
            response.setSign(0);
            listRules.add(response);
        }

        //规则
        rtnMap.put("rules", listRules);
        rtnMap.put("today", 1); //当前是第几天
        rtnMap.put("sign", 0); //当天是否签到
        rtnMap.put("continue", 0); //连续的签到天数
        return rtnMap;

    }



    /**
     * 签到
     * @param uid
     */
    @Transactional
    public void signIn(Long uid){
        String today = DateUtil.today();
        log.info("sign date: {} - {}", uid, today);

        // 检查当天是否已经签到
        UserSignInModel signToday = userSignInMapper.existsByUidAndSignDate(uid, today);
        if (signToday != null && signToday.getSignId() > 0) {
            throw new ApiException(MediaUserExceptionCodeApi.SIGN_ALREADY_ERROR);
        }

        //签到配置
        List<SignInJobResponse> list = this.signInTaskRule();
        Map<Long, Integer> configMap = list.stream().collect(Collectors.toMap(SignInJobResponse::getId, SignInJobResponse::getPointChangeCount));

        //获取昨天时间签到记录
        String yesterday = DateUtil.yesterday().toDateStr(); // 获取前一天的日期
        log.info("yesterday date: {} - {}", uid, yesterday);
        UserSignInModel signYesterday = userSignInMapper.existsByUidAndSignDate(uid, yesterday);
        log.info("yesterday is sign :{} - {}", uid, signYesterday);

        if(signYesterday == null || signYesterday.getSignId() == 0){
            log.info("yesterday is not signed, start anew... uid:{}, {}", uid, yesterday);
            //重新记录签到信息
            UserSignInModel userSignInSave = new UserSignInModel();
            userSignInSave.setSignId(redisIdGenerator.generate());
            userSignInSave.setUid(uid);
            userSignInSave.setSignDate(today);
            userSignInSave.setDayIndex(1);
            userSignInSave.setDayPoint(configMap.get(SignInJobEventEnums.SIGN_IN_ONE.getEventId()));
            userSignInSave.setCreatedTime(new Date());
            userSignInMapper.insert(userSignInSave);
            //发送签到事件
            //如果不是早鸟活动 发送签到积分
//            if(!clientUserService.earlyBirdSwitch()){
//                this.sendEvent(uid, SignInJobEventEnums.SIGN_IN_ONE.getEventId());
//            }
        }else{

            Integer dayIndex = signYesterday.getDayIndex();
            //如果 dayIndex 和 config 的数量一样，则从头开始
            if(dayIndex == list.size()){
                dayIndex = 1;
            }else{
                dayIndex++;
            }
            log.info("yesterday is signed, go on ... uid:{}, {}, {}", uid, yesterday, dayIndex);
            UserSignInModel userSignInSave = new UserSignInModel();
            userSignInSave.setSignId(redisIdGenerator.generate());
            userSignInSave.setUid(uid);
            userSignInSave.setSignDate(today);
            userSignInSave.setDayIndex(dayIndex);
            userSignInSave.setDayPoint(configMap.get(SignInJobEventEnums.getEnumsBySortNum(dayIndex).getEventId()));
            userSignInSave.setCreatedTime(new Date());
            userSignInMapper.insert(userSignInSave);

            //发送签到事件
            //如果不是早鸟活动 发送签到积分
            if(!clientUserService.earlyBirdSwitch()) {
                log.info("uid={}, not open the earlyBird activity", uid);
//                this.sendEvent(uid, SignInJobEventEnums.getEnumsBySortNum(dayIndex).getEventId());
            }else{
                if(dayIndex == 7) {
                    log.info("uid={}, already sign task full 7 days, push a turntable chance", uid);
                    //早鸟活动发送抽奖卡 -- 2025-04-14  不再发卡
//                    String idempotent = MD5Util.getMD5(uid + today + dayIndex);
//                    userSendMessageProvider.sendTurntableChanceKafka(uid, idempotent);
                }
            }
        }
        //只标记 已签到
        this.sendSignJobEvent(uid);

    }



    private List<SignInJobResponse> signInTaskRule(){
        try {
            ApiResponse result = userPointClient.signInJob();
            if(result.getCode() != 200){
                return null;
            }
            return JSONObject.parseArray(JSONObject.toJSONString(result.getResult()), SignInJobResponse.class);
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return null;
    }

    private void sendEvent(Long uid, Long eventId){
        boolean flag = false;
        try {
            SignInEventRequest request = new SignInEventRequest();
            request.setEventId(eventId);
            request.setUid(uid);
            ApiResponse response = userPointClient.jobEvent(request);
            System.out.println(response);
            if (response.getCode() != 200) {
                throw new ApiException(MediaUserExceptionCodeApi.SIGN_ERROR);
            }
        }catch (Exception e){
            log.error(e.getMessage());
            flag = true;
        }
        log.info("send sign Event:{}, {}, {}",uid,  eventId, flag);
        if(flag){
            throw new ApiException(MediaUserExceptionCodeApi.SIGN_ERROR);
        }
    }

    private void sendSignJobEvent(Long uid){
        boolean flag = false;
        try {
            SignInEventRequest request = new SignInEventRequest();
            request.setEventId(1L);  //默认 event 传 1 只标记已签到过
            request.setUid(uid);
            ApiResponse response = userPointClient.signJobEvent(request);
            System.out.println(response);
            if (response.getCode() != 200) {
                throw new ApiException(MediaUserExceptionCodeApi.SIGN_ERROR);
            }
        }catch (Exception e){
            log.error(e.getMessage());
            flag = true;
        }
        log.info("send sign Event:{}, {}",uid, flag);
        if(flag){
            throw new ApiException(MediaUserExceptionCodeApi.SIGN_ERROR);
        }
    }


}

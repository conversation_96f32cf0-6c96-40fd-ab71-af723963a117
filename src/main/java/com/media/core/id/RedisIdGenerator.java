package com.media.core.id;

import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalTime;
import java.util.Collections;

/**
 * ID生成器.
 *
 * <AUTHOR>
 */
@Component
public class RedisIdGenerator {

    @Autowired
    private RedisIdGeneratorProperties redisIdGeneratorProperties;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private static final String BUCKET_KEY = "ID:GENERATOR:";
    private static final String SEQ_BUCKET_KEY = "ID:GENERATOR:SEQ:";
    private static final long START_TIME = 1640966400000L; // 2022-01-01 00:00:00
    private static final long DAY_LIMITER = 24 * 3600_000; // 一天的毫秒数
    private static final DefaultRedisScript<Long> script = new DefaultRedisScript<>(
            "local arg = tonumber(ARGV[1])\n" +
                    "if redis.call(\"setbit\", KEYS[1], arg, 1) == 0 then\n" +
                    "   redis.call(\"EXPIRE\", KEYS[1], 90000)\n" +
                    "   return arg\n" +
                    "end\n" +
                    "local id = tonumber(redis.call(\"bitpos\",KEYS[1], 0, math.floor(tonumber(arg/8))))\n" +
                    "if id == -1 or id == 0 then\n" +
                    "   id = arg\n" +
                    "end\n" +
                    "if id <= 99999999 then\n" +
                    "    redis.call(\"setbit\", KEYS[1], id, 1)\n" +
                    "else\n" +
                    "    return 0\n" +
                    "end\n" +
                    "redis.call(\"EXPIRE\", KEYS[1], 90000)\n" +
                    "return id", Long.class);

    public Long generate() {
        return buildDate(10 + LocalTime.now().getMinute() % 10);
    }

    private long buildDate(long pre) {
        return buildRandomId(pre * 10000 + (System.currentTimeMillis() - START_TIME) / DAY_LIMITER);
    }

    private long buildRandomId(long pre) {
        int capacity = 100_000_000;
        int i = RandomUtils.nextInt(20, capacity);
        Long id = stringRedisTemplate.execute(script, Collections.singletonList(BUCKET_KEY + pre), String.valueOf(i));
        if (id < 10 || id > capacity) {
            throw new RuntimeException("ID 生成异常：" + id);
        }
        return pre * capacity + id;
    }

    /*   *//**
     * 生成8位用户id.
     *//*
    public long generateUserId() {
        String key = redisIdGeneratorProperties.getKeyPrefix() + redisIdGeneratorProperties.getUserSubPrefix();
        Long increment = stringRedisTemplate.opsForValue().increment(key, RandomUtils.nextInt(1, 128));
        return (increment == null ? 1 : increment) + redisIdGeneratorProperties.getUserStartId();
    }

    *//**
     * 生成15位id.
     *
     * @param subKey 业务子Key，例如：user order
     *//*
    public long generate(String subKey, Integer count) {
        String key = redisIdGeneratorProperties.getKeyPrefix() + subKey;
        Long increment = stringRedisTemplate.opsForValue().increment(key, count);
        return (increment == null ? 1 : increment) + redisIdGeneratorProperties.getStartId();
    }

    *//**
     * 生成15位id.
     *
     * @return id.
     *//*
    public long generate() {
        return generate("", 1);
    }

    *//**
     * 生成15位id.
     *
     * @return id.
     *//*
    public long generate(int count) {
        return generate("", count);
    }

    *//**
     * 批量生成ID.
     *
     * @return id.
     *//*
    public BatchId batchGenerate(int count) {
        long id = generate(count);
        return new BatchId(id, count);
    }

    *//**
     * 批量生成ID.
     *
     * @return id.
     *//*
    public BatchId batchGenerate(String subKey, int count) {
        long id = generate(subKey, count);
        return new BatchId(id, count);
    }*/

    /**
     * 生成有序ID
     *
     * @return
     */
    public Long generateSeqID() {
        String key = SEQ_BUCKET_KEY + (System.currentTimeMillis() / DAY_LIMITER);
        Long increment = stringRedisTemplate.opsForValue().increment(key);
        if (increment < 10) {
            stringRedisTemplate.expire(key, Duration.ofHours(30));
        }
        return ((System.currentTimeMillis() - START_TIME) / DAY_LIMITER) * 100_000_000 + increment;
    }
}

package com.media.user.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.media.user.domain.ClientUserModel;
import com.media.user.domain.NewbieTaskRecordModel;
import com.media.user.dto.query.ClientUserQuery;
import com.media.user.dto.request.NewBieTaskRequest;
import com.media.user.dto.response.PointRuleResponse;
import com.media.user.enums.CompleteEnum;
import com.media.user.enums.PointEventEnums;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.UserPointClient;
import com.media.user.mapper.NewbieTaskRecordMapper;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class NewbieTaskRecordService extends ServiceImpl<NewbieTaskRecordMapper, NewbieTaskRecordModel> {



    @Autowired
    private UserPointClient userPointClient;

    @Autowired
    @Lazy
    private ClientUserService clientUserService;



    public List<PointRuleResponse> newBieTaskList(Long uid) {
        if (clientUserService.earlyBirdSwitch()) {
            return Collections.emptyList();
        }

        if(uid == null) {
            // 调用用户积分任务接口
            ApiResponse<List<PointRuleResponse>> apiResponse = userPointClient.newTaskList();
            if (apiResponse == null || apiResponse.getCode() != 200 || CollUtil.isEmpty(apiResponse.getResult())) {
                return Collections.emptyList();
            }

            apiResponse.getResult().forEach(point -> point.setStatus(CompleteEnum.UNCOMPLETED.getValue()));
            // 没有记录初始化当前用户的所有新手任务
            return apiResponse.getResult();
        }else{
            // 查询用户的任务记录
            List<NewbieTaskRecordModel> userTaskRecords = list(
                    new LambdaQueryWrapper<NewbieTaskRecordModel>().eq(NewbieTaskRecordModel::getUid, uid)
            );
            boolean empty = CollUtil.isEmpty(userTaskRecords);
            // 如果全部新手任务都领取了，就返回空
            if (!empty && userTaskRecords.stream()
                    .allMatch(records -> records.getStatus() == CompleteEnum.RECEIVED.getValue())){
                return new ArrayList<>();
            }
            // 调用用户积分任务接口
            ApiResponse<List<PointRuleResponse>> apiResponse = userPointClient.newTaskList();
            if (apiResponse == null || apiResponse.getCode() != 200 || CollUtil.isEmpty(apiResponse.getResult())) {
                return Collections.emptyList();
            }

            Map<Long, PointRuleResponse> pointEventMap = apiResponse.getResult().stream()
                    .collect(Collectors.toMap(PointRuleResponse::getId, Function.identity()));

            // 没有任何任务记录，则直接返回所有任务
            if (empty) {
                apiResponse.getResult().forEach(point -> point.setStatus(CompleteEnum.UNCOMPLETED.getValue()));
                // 没有记录初始化当前用户的所有新手任务
                saveNewBieTask(uid);
                return apiResponse.getResult();
            }

            // 将用户任务记录转换为 Map，以便快速查找
            Map<Long, NewbieTaskRecordModel> userTaskRecordMap = userTaskRecords.stream()
                    .collect(Collectors.toMap(NewbieTaskRecordModel::getEventId, Function.identity()));

            // 更新任务状态
            pointEventMap.forEach((taskId, pointRule) -> {
                pointRule.setStatus(userTaskRecordMap.get(taskId).getStatus());
            });

            return new ArrayList<>(pointEventMap.values());
        }
    }

    public void saveNewBieTask(Long uid) {
        List<NewbieTaskRecordModel> userTaskRecords = PointEventEnums.getNewbieTaskEventIds().stream()
                .map(eventId -> new NewbieTaskRecordModel()
                        .setEventId(eventId)
                        .setUid(uid)
                        .setCreatedTime(new Date())
                        .setStatus(CompleteEnum.UNCOMPLETED.getValue())
                )
                .collect(Collectors.toList());
        super.saveBatch(userTaskRecords);
    }

    public void completeNewBieTask(NewBieTaskRequest request) {
        log.info("completeNewBieTask request:{}", JSON.toJSONString(request));
        NewbieTaskRecordModel newbieTaskRecordModel = getOne(Wrappers.<NewbieTaskRecordModel>lambdaQuery().eq(NewbieTaskRecordModel::getUid, request.getUid()).
                eq(NewbieTaskRecordModel::getEventId, request.getEventId()));
        if (newbieTaskRecordModel == null) {
            saveNewBieTask(request.getUid());
            newbieTaskRecordModel = getOne(Wrappers.<NewbieTaskRecordModel>lambdaQuery().eq(NewbieTaskRecordModel::getUid, request.getUid()).
                    eq(NewbieTaskRecordModel::getEventId, request.getEventId()));
        }

        if (newbieTaskRecordModel.getStatus() == CompleteEnum.UNCOMPLETED.getValue()) {
            updateNewbieTaskStatus(request.getEventId(), request.getUid(), CompleteEnum.DONE.getValue());
        }
    }

    public BigDecimal receiveNewBieTask(NewBieTaskRequest request) {
        log.info("receiveNewBieTask request:{}", JSON.toJSONString(request));
        ApiResponse<BigDecimal> response = userPointClient.jobEvent(request);
        if (response == null || response.getCode() != 200) {
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }
        updateNewbieTaskStatus(request.getEventId(), request.getUid(), CompleteEnum.RECEIVED.getValue());
        return response.getResult();
    }

    public void updateNewbieTaskStatus(Long eventId, Long uid, Byte status) {
        update(Wrappers.<NewbieTaskRecordModel>lambdaUpdate()
                .set(NewbieTaskRecordModel::getStatus, status)
                .eq(NewbieTaskRecordModel::getEventId, eventId)
                .eq(NewbieTaskRecordModel::getUid, uid)
        );
    }

    public Map<String, Long> activeUserNum(Long uid){
        List<ClientUserModel> clientUserModels = clientUserService.queryUserInviteList(uid);
        log.info("clientUserModels:{}", JSON.toJSONString(clientUserModels));
        if (CollUtil.isEmpty(clientUserModels)){
            return Collections.emptyMap();
        }
        List<Long> uidList = clientUserModels.stream().map(ClientUserModel::getUid).toList();
        log.info("uidList:{}", JSON.toJSONString(uidList));
        ApiResponse<Map<Long, Boolean>> userActive = userPointClient.getUserActive(new ClientUserQuery(uid, uidList));
        if (userActive == null || !userActive.getSuccess() || userActive.getCode() != 200) {
            return Map.of("active", 0L, "notActive", 0L);
        }
        Map<Long, Boolean> result = userActive.getResult();

        long trueCount = result.values().stream()
                .filter(Boolean::booleanValue)
                .count();
        return Map.of("active", trueCount, "notActive", result.size() - trueCount);
    }

}

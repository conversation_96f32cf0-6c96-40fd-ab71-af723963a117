package com.media.core.utils;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * User-Agent 获取工具类
 */
@Slf4j
@Component
public class UserAgentUtil {

    /**
     * 获取当前请求的 User-Agent
     * 
     * @return User-Agent，如果获取失败返回空字符串
     */
    public String getUserAgent() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = (HttpServletRequest) requestAttributes
                .resolveReference(RequestAttributes.REFERENCE_REQUEST);
        return this.getUserAgentByRequest(request);
    }

    /**
     * 从 HttpServletRequest 中获取 User-Agent
     * 
     * @param request HTTP请求对象
     * @return User-Agent，如果获取失败返回空字符串
     */
    public String getUserAgentByRequest(HttpServletRequest request) {
        String userAgent = null;
        try {
            userAgent = request.getHeader("User-Agent");

            // 如果获取不到或为空，返回空字符串
            if (userAgent == null || userAgent.length() == 0) {
                userAgent = "";
            }

        } catch (Exception e) {
            userAgent = "";
            log.error("get user agent error", e);
        }

        return userAgent;
    }
}

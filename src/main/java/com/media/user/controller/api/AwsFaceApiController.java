package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.core.utils.aws.AwsCreateSessionResponse;
import com.media.user.dto.request.NewBieTaskRequest;
import com.media.user.service.AwsFaceService;
import com.media.user.service.UserInviteFaceRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


@RestController
@RequestMapping("/user/api/aws")
public class AwsFaceApiController {

    @Autowired
    AwsFaceService awsFaceService;

    /**
     * 创建 session
     */
    @GetMapping("/create/session")
    @RequireAuth
    public AwsCreateSessionResponse createSession(String country){
        return awsFaceService.createSession(CloudSession.getUid(), country);
    }

    /**
     * 获取 session是否有效
     */
    @PostMapping("/get/session")
    @RequireAuth
    public Float getSession(){
        return awsFaceService.getFaceResult(CloudSession.getUid());
    }


//    @Autowired
//    UserInviteFaceRecordService userInviteFaceRecordService;

//    /**
//     * TODO Test send xme
//     */
//    @PostMapping("/send")
//    public void sendXme(@Validated @RequestBody NewBieTaskRequest request){
//        // 更新用户人脸识别状态(数据库和缓存)
//        awsFaceService.updateUserFaceStatus(request.getUid(), null);
//        userInviteFaceRecordService.faceLiveSendXme(request.getUid());
//    }
//
//    /**
//     * TODO Test del face
//     */
//    @GetMapping("/delFace")
//    public void delFace(Long uid, String faceId){
//        awsFaceService.delFace(uid, faceId);
//    }
//    @GetMapping("/delCollection")
//    public void delCollection(Long uid){
//        awsFaceService.delCollection(uid);
//    }
//    @GetMapping("/collection")
//    public String getCollection(){
//        return awsFaceService.getCollection();
//    }

}

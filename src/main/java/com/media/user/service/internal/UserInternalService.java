package com.media.user.service.internal;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.media.user.constant.MediaUserConstant;
import com.media.core.constant.PageResponse;
import com.media.user.domain.ClientUserModel;
import com.media.user.domain.TwitterUserModel;
import com.media.user.domain.UserAdditionConfigModel;
import com.media.user.dto.query.ClientUserQuery;
import com.media.user.dto.query.UserInviteListQuery;
import com.media.user.dto.query.UserSearchQuery;
import com.media.user.dto.request.internal.ModifyUserInfoRequest;
import com.media.user.dto.request.internal.UpdateUserRequest;
import com.media.user.dto.request.internal.UserBatchRegisterRequest;
import com.media.user.dto.request.internal.UserRegisterRequest;
import com.media.user.dto.request.internal.UserStatusUpdateRequest;
import com.media.user.dto.response.UserInviteListResponse;
import com.media.user.dto.response.UserOverviewResponse;
import com.media.user.dto.response.internal.PreBatchRegisterUserResponse;
import com.media.user.dto.response.internal.PreRegisterUserResponse;
import com.media.user.dto.response.internal.SearchUserResponse;
import com.media.user.dto.response.internal.UserMeResponse;
import com.media.user.enums.*;
import com.media.user.es.EsClientUserService;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.UserPointClient;
import com.media.core.id.RedisIdGenerator;
import com.media.user.mapper.ClientUserMapper;
import com.media.user.mapper.TwitterUserMapper;
import com.media.core.request.ClientInfoContext;
import com.media.user.service.ClientUserService;
import com.media.user.service.UserAdditionConfigService;
import com.media.user.service.UserFollowService;
import com.media.user.service.cache.ClientUserCacheService;
import com.media.user.tools.UserImgUtils;
import com.media.user.tools.UserInviteCodeUtils;
import com.xme.xme_base_depends.models.ApiResponse;
import com.media.user.service.twitter.impl.TwitterService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserInternalService {

    @Autowired
    private ClientUserMapper clientUserMapper;


    @Autowired
    private RedisIdGenerator redisIdGenerator;

    @Autowired
    private TwitterService twitterService;

    @Autowired
    TwitterUserMapper twitterUserMapper;

    @Autowired
    UserFollowService userFollowService;

    @Autowired
    private UserAdditionConfigService userAdditionConfigService;

    @Autowired
    ClientUserService clientUserService;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @Autowired
    UserPointClient userPointClient;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    EsClientUserService esClientUserService;

    @Autowired
    UserImgUtils userImgUtils;

    @Autowired
    UserInviteCodeUtils userInviteCodeUtils;

    public PageResponse<SearchUserResponse> searchUser(UserSearchQuery query) {
        //关键字为空 则返回 空数据
        if (StringUtils.isBlank(query.getKey())) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
        if(query.getPage() == null || query.getPage() < 1) {
            query.setPage(1);
        }
        if(query.getSize() == null || query.getSize() < 1) {
            query.setSize(10);
        }

        IPage<ClientUserModel> searchUsers = getSearchUser(query);
        if (CollectionUtils.isEmpty(searchUsers.getRecords())) {
            return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
        }

        List<SearchUserResponse> list = new ArrayList<>();
        SearchUserResponse searchUserResponse = null;
        for (ClientUserModel userModel : searchUsers.getRecords()) {
            searchUserResponse = new SearchUserResponse();
            BeanUtils.copyProperties(userModel, searchUserResponse);
            searchUserResponse.setFollowersNumber(userFollowService.countFollowers(userModel.getUid()));
            searchUserResponse.setFansNumber(userFollowService.countFans(userModel.getUid()));
            searchUserResponse.setFollowState(UserFollowStateEnum.FOLLOW_STATE_0.getState());
            if(query.getUid() != null && query.getUid() > 0){
                boolean followRation = userFollowService.isFollowing(query.getUid(), userModel.getUid());
                if(followRation){
                    searchUserResponse.setFollowState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
                }
            }
            list.add(searchUserResponse);
        }
        return new PageResponse<>(query.getPage(), query.getSize(), searchUsers.getTotal(), list);
    }

    public IPage<ClientUserModel> getSearchUser(UserSearchQuery query) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(query.getKey())){
            queryWrapper.lambda().like(ClientUserModel::getNickName, query.getKey());
        }
        if(query.getUid() != null && query.getUid() > 0){
            queryWrapper.lambda().ne(ClientUserModel::getUid, query.getUid());
        }
        queryWrapper.lambda().eq(ClientUserModel::getStatus, UserStatusEnum.ENABLED.getStatus());
        queryWrapper.orderByDesc("attention");

        IPage<ClientUserModel> page = new Page<>(query.getPage(), query.getSize());
        return clientUserMapper.selectPage(page, queryWrapper);
    }


    public Map<Long, UserMeResponse> userMes(Long currentUid, List<Long> uids) {
        List<UserMeResponse> responseList = clientUserCacheService.users(uids);
        if(responseList == null || responseList.size() == 0) {
            return new HashMap<>();
        }

        Map<Long, UserMeResponse> map = new HashMap<>();
        for (UserMeResponse response : responseList){
            if(currentUid != null && currentUid.equals(response.getUid())){
                continue;
            }
            response.setFollowState(UserFollowStateEnum.FOLLOW_STATE_0.getState());
            response.setFollowedState(UserFollowStateEnum.FOLLOW_STATE_0.getState());
            //用户是否关注我
            boolean followRation = userFollowService.isFollowing(currentUid, response.getUid());
            if(followRation){
                response.setFollowState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
            }
            //我是否关注用户
            boolean followedRation = userFollowService.isFollowing(response.getUid(), currentUid);
            if(followedRation){
                response.setFollowedState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
            }
            map.put(response.getUid(), response);
        }
        return map;
    }

    public Map<Long, UserMeResponse> getUserByIds(List<Long> uids) {
        Map<Long, UserMeResponse> map = new HashMap<>();
        for (Long uid : uids){
            UserMeResponse response = clientUserCacheService.userInfo(uid);
            map.put(uid, response);
        }
        return map;
    }

    public String getTwitterIdById(Long uid) {
        try {
            QueryWrapper<TwitterUserModel> queryWrapper = new QueryWrapper<>();
            queryWrapper.and(
                    qr -> qr.lambda().eq(TwitterUserModel::getNewUid, uid)
                            .or(qrw -> qrw.eq(TwitterUserModel::getOldUid, uid)));
            TwitterUserModel twitterUserModel = twitterUserMapper.selectOne(queryWrapper);
            if (twitterUserModel == null) {
                return "";
            }
            return twitterUserModel.getTwitterId();
        }catch (Exception e){
            log.error("getTwitterIdById query exception", e.getMessage());
        }
        return "";
    }

    public PreRegisterUserResponse preRegister(UserRegisterRequest request){
//        log.info("UserInternalService.preRegister request:{}", request);

        //如果是 其他的用户则直接返回对应的用户表信息
        if(request.getThirdPlatType() == null){
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }

        if(request.getThirdPlatType() == 9){
            ClientUserModel model = this.getByUserNameOriginal(request.getUserName());
            PreRegisterUserResponse response = new PreRegisterUserResponse();
            if(model != null){
                if(model.getVirtualType() == 1){
                    boolean modifyFlag = false;
                    if(StringUtils.isNotBlank(request.getNickName()) && !model.getNickNameOriginal().equals(request.getNickName())){
                        if(request.getNickName().length()>100){
                            model.setNickName(request.getNickName().substring(0, 100));
                        }else{
                            model.setNickName(request.getNickName());
                        }
                        model.setNickNameOriginal(request.getNickName());
                        modifyFlag = true;
                    }
                    if(StringUtils.isNotBlank(request.getAvatarUrl()) && !model.getAvatarUrlOriginal().equals(request.getAvatarUrl())){
                        model.setAvatarUrl(request.getAvatarUrl());
                        model.setAvatarUrlOriginal(request.getAvatarUrl());
                        if(!request.getAvatarUrl().startsWith("https://s3.x.me") && !request.getAvatarUrl().startsWith("https://s3-video.x.me")){
                            model.setFixImageType(1);
                        }
                        modifyFlag = true;
                    }
                    if(modifyFlag) {
                        model.setUpdatedTime(new Date());
                        clientUserMapper.updateById(model);
//                        log.info("date update finish ,delete redis");
                        //删除个人信息缓存
                        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + model.getUid());
                    }
                }
                response.setUid(model.getUid());
                response.setTwitterId(request.getTwitterId());
                return response;
            }
            //不存在则创建用户并返回
            ClientUserModel entity = this.createVirtualUser(request);
            if(entity != null){
                response.setUid(entity.getUid());
                response.setTwitterId(request.getTwitterId());

                //推送用户至 ES
                esClientUserService.saveOne(entity.getUid(), entity.getNickName());
            }else{
                response.setUid(0L);
                response.setTwitterId(request.getTwitterId());
            }
            return response;
        }


        //如果是 twitter 用户则需查询 twitter 的信息并落库
        if(request.getThirdPlatType() == 1){
            //查询是否有 twitter 用户
            TwitterUserModel twitterUserModel = twitterService.getByTwitterId(request.getTwitterId());
            PreRegisterUserResponse response = new PreRegisterUserResponse();
            if(twitterUserModel != null){
                response.setUid((twitterUserModel.getNewUid() == null || twitterUserModel.getNewUid() == 0) ? twitterUserModel.getOldUid() : twitterUserModel.getNewUid());
                response.setTwitterId(twitterUserModel.getTwitterId());
                return response;
            }
            //先存储数据， 定时任务 fix info
            request.setUserName( StringUtils.isBlank(request.getUserName()) ? request.getTwitterId() : request.getUserName() );
            request.setNickName( StringUtils.isBlank(request.getNickName()) ? request.getTwitterId() : request.getNickName() );
            ClientUserModel entity = this.createVirtualUser(request);
            if(entity != null){
                TwitterUserModel saveModel = new TwitterUserModel();
                saveModel.setTwitterId(request.getTwitterId());
                saveModel.setOldUid(entity.getUid());
                saveModel.setName(request.getNickName());
                saveModel.setUsername(request.getUserName());
                saveModel.setProfileImageUrl(request.getAvatarUrl());
                saveModel.setOriginalImageUrl(request.getAvatarUrl());
                saveModel.setBind(TwitterBindStateEnum.BIND.getState());
                saveModel.setBindTime(new Date());
                saveModel.setCreatedTime(new Date());
                saveModel.setFixInfoType(1);
                saveModel.setRecommendType(0);
                twitterUserMapper.insert(saveModel);
                response.setUid(entity.getUid());
                response.setTwitterId(request.getTwitterId());

                //推送用户至 ES
                esClientUserService.saveOne(entity.getUid(), entity.getNickName());
            }else{
                response.setUid(0L);
                response.setTwitterId(request.getTwitterId());
            }
            return response;
        }
        return null;
    }


    public List<PreBatchRegisterUserResponse> preBatchRegister(List<UserBatchRegisterRequest> list){
        //获取传入的所有昵称
        List<String> usernames = list.stream().map(UserBatchRegisterRequest::getUserName).distinct().collect(Collectors.toList());
        List<ClientUserModel> existUsers = this.getBatchByUserNameOriginal(usernames);
        if(existUsers == null || existUsers.isEmpty()){
            log.info("传入的数据 {} 条，已存在 {} 条，需要插入 {} 条", usernames.size(), 0, list.size());
            //批量插入系统
            List<ClientUserModel> insertList = this.batchCreateVirtualUser(list);
            long count = clientUserMapper.insertBatch(insertList);
            log.info("insert count: {}", count);
            if(count == 0){
                return insertList.stream().map(user -> new PreBatchRegisterUserResponse(
                        0L, user.getUsernameOriginal()
                )).collect(Collectors.toList());
            }
            for(ClientUserModel userModel : insertList){
                //推送用户至 ES
                esClientUserService.saveOne(userModel.getUid(), userModel.getNickName());
            }

            return insertList.stream().map(user -> new PreBatchRegisterUserResponse(
                    user.getUid(), user.getUsernameOriginal()
            )).collect(Collectors.toList());
        }else{
            List<String> existUserNames = existUsers.stream().map(ClientUserModel::getUsernameOriginal).distinct().collect(Collectors.toList());
            list.removeIf(user -> existUserNames.contains(user.getUserName()));
            log.info("传入的数据 {} 条，已存在 {} 条，需要插入 {} 条", usernames.size(), existUserNames.size(), list.size());
            //组装返回值
            List<PreBatchRegisterUserResponse> responseExist = existUsers.stream().map(user -> new PreBatchRegisterUserResponse(
                    user.getUid(), user.getUsernameOriginal()
            )).collect(Collectors.toList());

            if(list.isEmpty()) {
                return responseExist;
            }
            //批量插入系统
            List<ClientUserModel> insertList = this.batchCreateVirtualUser(list);
            long count = clientUserMapper.insertBatch(insertList);
            log.info("insert count: {}", count);
            List<PreBatchRegisterUserResponse> responseInsert = null;
            if(count == 0){
                responseInsert = insertList.stream().map(user -> new PreBatchRegisterUserResponse(
                        0L, user.getUsernameOriginal()
                )).collect(Collectors.toList());
            }else{
                for(ClientUserModel userModel : insertList){
                    //推送用户至 ES
                    esClientUserService.saveOne(userModel.getUid(), userModel.getNickName());
                }

                responseInsert = insertList.stream().map(user -> new PreBatchRegisterUserResponse(
                        user.getUid(), user.getUsernameOriginal()
                )).collect(Collectors.toList());
            }
            //组装返回值
            responseExist.addAll(responseInsert);
            return responseExist;
        }

    }

    private List<ClientUserModel> batchCreateVirtualUser(List<UserBatchRegisterRequest> list){
        List<ClientUserModel> insertList = new ArrayList<>();
        for(UserBatchRegisterRequest request : list){
            ClientUserModel clientUserModel = new ClientUserModel();
            clientUserModel.setUsername(request.getUserName());
            if(request.getNickName().length()>100){
                clientUserModel.setNickName(request.getNickName().substring(0, 100));
            }else{
                clientUserModel.setNickName(request.getNickName());
            }
            if(request.getUserName().length()>64){
                clientUserModel.setEmail(request.getUserName().substring(0, 64));
            }else{
                clientUserModel.setEmail(request.getUserName());
            }
            clientUserModel.setType(UserTypeEnums.ROBOT.getCode());
            clientUserModel.setUid(redisIdGenerator.generate());
            Date now = new Date();
            clientUserModel.setCreatedTime(now);
            clientUserModel.setRegistrationTime(now);
            clientUserModel.setPassword("noPass");
            clientUserModel.setLanguage(ClientInfoContext.getLanguage().getValue());
            clientUserModel.setStatus(UserStatusEnum.ENABLED.getStatus());
            clientUserModel.setInviteCode(userInviteCodeUtils.makeUserInviteCode());
            clientUserModel.setAvatarUrl(request.getAvatarUrl());
            clientUserModel.setPersonIntroduce(request.getDescription());
            if (StringUtils.isBlank(request.getAvatarUrl())){
                clientUserModel.setAvatarUrl(userImgUtils.getUserAvatarUrl());
            }
            clientUserModel.setVirtualType(Byte.valueOf("1"));
            if(!request.getAvatarUrl().startsWith("https://s3.x.me") && !request.getAvatarUrl().startsWith("https://s3-video.x.me")){
                clientUserModel.setFixImageType(1);
            }
            clientUserModel.setUsernameOriginal(request.getUserName());
            clientUserModel.setNickNameOriginal(request.getNickName());
            clientUserModel.setAvatarUrlOriginal(request.getAvatarUrl());
            clientUserModel.setFirstRegistration(1);
            clientUserModel.setBackground(userImgUtils.getUserBackground());
            insertList.add(clientUserModel);
        }
        return insertList;
    }



    private ClientUserModel createVirtualUser(UserRegisterRequest request){
        try {
            ClientUserModel clientUserModel = new ClientUserModel();
            clientUserModel.setUsername(request.getUserName());
            if(request.getNickName().length()>100){
                clientUserModel.setNickName(request.getNickName().substring(0, 100));
            }else{
                clientUserModel.setNickName(request.getNickName());
            }
            if(request.getUserName().length()>64){
                clientUserModel.setEmail(request.getUserName().substring(0, 64));
            }else{
                clientUserModel.setEmail(request.getUserName());
            }
            clientUserModel.setType(UserTypeEnums.ROBOT.getCode());
            clientUserModel.setUid(redisIdGenerator.generate());
            Date now = new Date();
            clientUserModel.setCreatedTime(now);
            clientUserModel.setRegistrationTime(now);
            clientUserModel.setPassword("noPass");
            clientUserModel.setLanguage(ClientInfoContext.getLanguage().getValue());
            clientUserModel.setStatus(UserStatusEnum.ENABLED.getStatus());
            clientUserModel.setInviteCode(userInviteCodeUtils.makeUserInviteCode());
            clientUserModel.setAvatarUrl(request.getAvatarUrl());
            clientUserModel.setPersonIntroduce(request.getDescription());
            if (StringUtils.isBlank(request.getAvatarUrl())){
                clientUserModel.setAvatarUrl(userImgUtils.getUserAvatarUrl());
            }
            clientUserModel.setVirtualType(Byte.valueOf("1"));
            if(!request.getAvatarUrl().startsWith("https://s3.x.me") && !request.getAvatarUrl().startsWith("https://s3-video.x.me")){
                clientUserModel.setFixImageType(1);
            }
            clientUserModel.setUsernameOriginal(request.getUserName());
            clientUserModel.setNickNameOriginal(request.getNickName());
            clientUserModel.setAvatarUrlOriginal(request.getAvatarUrl());
            clientUserModel.setFirstRegistration(1);
            clientUserModel.setBackground(userImgUtils.getUserBackground());
            clientUserMapper.insert(clientUserModel);
            return clientUserModel;
        }catch (Exception e){
            log.error("create virtual user exception: {}", e.getMessage());
        }
        return null;
    }

    private List<ClientUserModel> getBatchByUserNameOriginal(List<String> userNames){
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("username_original", userNames);
        return clientUserMapper.selectList(queryWrapper);
    }

    private ClientUserModel getByUserNameOriginal(String userName){
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username_original", userName);
        return clientUserMapper.selectOne(queryWrapper);
    }


    /**
     * 检查当前用户是否已经绑定了 twitterId
     * @param uid
     * @param virtualType
     */
    public Integer isBindTwitter(Long uid, Byte virtualType){
        QueryWrapper<TwitterUserModel> twitterWrapper = new QueryWrapper<>();
        if(virtualType.intValue() == 0){
            twitterWrapper.lambda().eq(TwitterUserModel::getNewUid, uid);
        }else{
            twitterWrapper.lambda().eq(TwitterUserModel::getOldUid, uid);
        }
        twitterWrapper.lambda().eq(TwitterUserModel::getBind, TwitterBindStateEnum.BIND.getState());
        Long count = twitterUserMapper.selectCount(twitterWrapper);
        if(count == null || count == 0){
            return 0;
        }
        return 1;
    }


    public Map<Long, UserOverviewResponse> selectUserAdditionByUids(ClientUserQuery query) {
        Map<Long, UserMeResponse> userMeResponseMap = getUserByIds(query.getUids());
        List<UserAdditionConfigModel> userAdditionConfigModelList = userAdditionConfigService.getUserAdditionConfig();
        Map<Long, UserAdditionConfigModel> userAdditionConfigModelMap = userAdditionConfigModelList.stream().collect(Collectors.toMap(UserAdditionConfigModel::getId, Function.identity()));
        Map<Long, UserOverviewResponse> result = new HashMap<>();
        for (Long uid : query.getUids()){
            UserOverviewResponse response = getUserPointCoefficient(uid, userAdditionConfigModelMap);
            result.put(uid, response);
        }
        return result;
    }


    public UserOverviewResponse getUserPointCoefficient(Long uid,  Map<Long, UserAdditionConfigModel> userAdditionConfigModelMap){
        Map<Long,UserMeResponse> map = getUserByIds(List.of(uid));
        if (CollectionUtils.isEmpty(map)){
            return null;
        }

        BigDecimal baseAddition = BigDecimal.ZERO;
        UserMeResponse userMeResponse = map.get(uid);
        baseAddition = baseAddition.add(userAdditionConfigModelMap.get(UserAdditionConfigEnums.SIGNUP.getId()).getAddition());

        BigDecimal identityAddition = BigDecimal.ZERO;
//        if (BindTwitterEnum.BIND.getStatus().equals(userMeResponse.getIsBindTwitter())){
//            identityAddition = identityAddition.add(userAdditionConfigModelMap.get(UserAdditionConfigEnums.TWITTER.getId()).getAddition());
//        }

        UserAdditionConfigModel userAdditionConfigModel = userAdditionConfigModelMap.get(UserAdditionConfigEnums.ACTIVE.getId());
        int page = 1;
        int activeCount = 0;
        while(true){
            UserInviteListQuery query = (UserInviteListQuery) new UserInviteListQuery()
                    .setUid(uid)
                    .setPage(page)
                    .setSize(MediaUserConstant.pageSize);
            PageResponse<UserInviteListResponse> userInviteListResponsePageResponse = clientUserService.queryUserInviteList(query);
            if (CollectionUtils.isEmpty(userInviteListResponsePageResponse.getData())){
                break;
            }
            List<Long> uids = userInviteListResponsePageResponse.getData().stream().map(UserInviteListResponse::getUid).toList();
            ClientUserQuery clientUserQuery = new ClientUserQuery()
                    .setUids(uids);
            ApiResponse<Map<Long, Boolean>> response = userPointClient.getUserActive(clientUserQuery);
            if (!response.getSuccess()){
                log.error("UserInternalService.getUserPointCoefficient uid:{}, clientUserQuery:{},error:{}", uid, JSON.toJSONString(clientUserQuery), JSON.toJSONString(response));
            }
            activeCount += response.getResult().values().stream().filter(Boolean::booleanValue).toList().size();
            if (userInviteListResponsePageResponse.getData().size() < MediaUserConstant.pageSize){
                break;
            }
            page++;
        }
        BigDecimal inviteAddition = userAdditionConfigModel.getAddition().multiply(BigDecimal.valueOf(activeCount));
        return new UserOverviewResponse()
                .setUid(uid)
                .setInviteAddition(baseAddition.add(inviteAddition))
                .setIdentityAddition(baseAddition.add(identityAddition));
    }

    public Map<Long, UserMeResponse> batchUserMes(Long currentUid, List<Long> uids, boolean needFollowState, boolean needFollowedState, boolean needFollowCount) {
         return clientUserCacheService.batchUserMes(currentUid, uids, needFollowState, needFollowedState, needFollowCount);
    }

    public void modifyUserInfo(ModifyUserInfoRequest request){
        if(request == null || request.getUid() == null) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
        if(StringUtils.isEmpty(request.getNickName()) && StringUtils.isEmpty(request.getAvatarUrl()) && StringUtils.isEmpty(request.getPersonIntroduce())){
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }

        ClientUserModel clientUserModel = clientUserMapper.selectById(request.getUid());
        if(clientUserModel == null) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        if(StringUtils.isNotBlank(request.getNickName())){
            clientUserService.checkNickName(request.getNickName());
            clientUserModel.setNickName(request.getNickName());
        }
        if(StringUtils.isNotBlank(request.getPersonIntroduce())){
            clientUserModel.setPersonIntroduce(request.getPersonIntroduce());
        }
        if(StringUtils.isNotBlank(request.getAvatarUrl())){
            clientUserModel.setAvatarUrl(request.getAvatarUrl());
        }
        clientUserModel.setUpdatedTime(new Date());
        clientUserMapper.updateById(clientUserModel);

        //删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + clientUserModel.getUid());

        //推送用户至 ES
        if(StringUtils.isNotBlank(request.getNickName())){
            esClientUserService.saveOne(clientUserModel.getUid(), clientUserModel.getNickName());
        }
    }

    public void updateUserInfo(UpdateUserRequest request){
        if(request == null || request.getUid() == null) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
        if(StringUtils.isEmpty(request.getNickName()) && StringUtils.isEmpty(request.getAvatarUrl()) && StringUtils.isEmpty(request.getPersonIntroduce())){
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }

        ClientUserModel clientUserModel = clientUserMapper.selectById(request.getUid());
        if(clientUserModel == null) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        if(StringUtils.isNotBlank(request.getNickName())){
            clientUserService.checkNickName(request.getNickName());
            clientUserModel.setNickName(request.getNickName());
        }

        if(StringUtils.isNotBlank(request.getEmail())){
            clientUserService.checkEmail(request.getEmail());
            clientUserModel.setEmail(request.getEmail());
        }


        if(StringUtils.isNotBlank(request.getPersonIntroduce())){
            clientUserModel.setPersonIntroduce(request.getPersonIntroduce());
        }
        if(StringUtils.isNotBlank(request.getAvatarUrl())){
            clientUserModel.setAvatarUrl(request.getAvatarUrl());
        }
        clientUserModel.setUpdatedTime(new Date());
        clientUserMapper.updateById(clientUserModel);

        //删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + clientUserModel.getUid());

        //推送用户至 ES
        if(StringUtils.isNotBlank(request.getNickName())){
            esClientUserService.saveOne(clientUserModel.getUid(), clientUserModel.getNickName());
        }
    }
    
    /**
     * 更新用户状态（封禁/解封）
     * @param request 包含用户ID和目标状态的请求
     */
    public void updateUserStatus(UserStatusUpdateRequest request) {
        if(request == null || request.getUid() == null || request.getStatus() == null) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
        
        // 验证状态值是否合法（只允许正常或封禁状态）
        if(request.getStatus() != UserStatusEnum.ENABLED.getStatus() && 
           request.getStatus() != UserStatusEnum.BANNED.getStatus()) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
        
        // 先检查用户是否存在
        ClientUserModel clientUserModel = clientUserMapper.selectById(request.getUid());
        if(clientUserModel == null) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }
        
        // 使用UpdateWrapper直接更新用户状态
        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("uid", request.getUid())
                    .set("status", request.getStatus())
                    .set("updated_time", new Date());
        clientUserMapper.update(null, updateWrapper);
        
        // 删除个人信息缓存，强制刷新
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + clientUserModel.getUid());
        
    }
}

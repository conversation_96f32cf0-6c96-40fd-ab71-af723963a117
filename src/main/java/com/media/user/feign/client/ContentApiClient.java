package com.media.user.feign.client;
import com.media.user.dto.response.SubscriptionCountResponse;
import com.xme.xme_base_depends.models.ApiResponse;
import feign.Request;

import com.media.user.dto.request.GetSubscriptionCountRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.concurrent.TimeUnit;

@FeignClient(url = "${content.host:http://envtest-api.x.me}", value = "contentApiClient", configuration = ContentApiClient.ContentApiClientConfiguration.class)
public interface ContentApiClient {

    @Configuration
    class ContentApiClientConfiguration {
        @Bean("contentApiClientConfigurationOptions")
        public Request.Options options() {
            // Set connect timeout to 200ms and read timeout to 2000ms
            return new Request.Options(200, TimeUnit.MILLISECONDS, 500, TimeUnit.MILLISECONDS, true);
        }
    }

    @PostMapping(value = "/content/internal/web3/channel/subscription/count")
    ApiResponse<SubscriptionCountResponse> getSubscriptionCount(@RequestBody GetSubscriptionCountRequest request);

}

package com.media.user.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.media.core.auth.CloudSession;
import com.media.core.constant.KafkaTopicConstant;
import com.media.core.i18n.I18nConvert;
import com.media.core.request.ClientInfoContext;
import com.media.user.cache.UserInviteCache;
import com.media.user.constant.MediaUserConstant;
import com.media.core.exception.ApiException;
import com.media.core.i18n.LocalMessageResource;
import com.media.user.dto.request.XmeRedemptionRequest;
import com.media.user.dto.request.CoreSkyMessageRequest;
import com.media.user.dto.response.ClientUserResponse;
import com.media.user.dto.response.PopupResponse;
import com.media.user.dto.response.internal.CcyPriceReponse;
import com.media.user.enums.LanguageEnums;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.UserPointClient;
import com.media.user.mapper.ClientUserMapper;
import com.media.user.mq.KafkaMessageProducer;
import com.media.core.utils.TimeTool;
import com.media.user.service.cache.ClientUserCacheService;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

import static com.media.user.constant.MediaUserConstant.*;

@Slf4j
@Service
public class ActivityCommonService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ClientUserMapper clientUserMapper;

    @Autowired
    private UserPointClient userPointClient;

    @Autowired
    private KafkaMessageProducer kafkaMessageProducer;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @Autowired
    private CoreSkyActivityRecordService coreSkyActivityRecordService;

    @Autowired
    private UserInviteCache userInviteCache;

    @Autowired
    EnhancedI18nConvert enhancedI18nConvert;

    /**
     * CoreSky 活动配置 - 从 Apollo 读取
     * 配置格式示例：
     * {
     * "startTime": "2025-06-02 16:00:00",
     * "endTime": "2025-06-03 16:00:00"
     * }
     * 注意：配置中的时间约定为 UTC+0 时区（世界标准时间）
     */
    @Value("${coresky.config:{}}")
    private String coreSkyConfigJson;

    /**
     * 判断是否在 CoreSky 活动期间
     *
     * @param uid      用户ID
     * @param key      活动密令
     * @param clientIp 客户端IP地址
     * @return CoreSkyJoinResponse 包含提示信息
     */
    public ApiResponse coreSkyJoin(Long uid, String key, String clientIp) {
        // 使用分布式锁确保同一用户的并发请求串行处理
        String lockKey = "activity:coresky:lock:" + uid;
        RLock lock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取锁，最多等待3秒，锁过期时间30秒
            boolean locked = lock.tryLock(3, 30, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("用户 {} 获取活动锁失败，可能存在并发请求", uid);
                throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
            }

            return doCoreSkyJoin(uid, key, clientIp);
        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("CoreSky 活动参与失败，用户: {}, 错误: {}", uid, e.getMessage(), e);
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        } finally {
            // 确保锁被释放
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 执行 CoreSky 活动参与逻辑
     */
    public ApiResponse doCoreSkyJoin(Long uid, String key, String clientIp) {
        JSONObject config = parseCoreSkyConfig();
        if (config == null) {
            log.warn("CoreSky 活动配置为空");
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }

        String startTimeStr = config.getString("startTime");
        String endTimeStr = config.getString("endTime");

        if (startTimeStr == null || endTimeStr == null) {
            log.warn("CoreSky 活动配置缺少时间信息");
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }

        // 明确指定时区为 UTC+0 (世界标准时间)
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC")); // UTC+0

        Date startTime = null;
        Date endTime = null;
        try {
            startTime = sdf.parse(startTimeStr);
            endTime = sdf.parse(endTimeStr);
        } catch (ParseException e) {
            log.error("解析 CoreSky 活动时间失败: {}", e.getMessage());
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }

        Date now = new Date();

        log.info("CoreSky 活动时间检查 - 当前时间: {}, 开始时间: {}, 结束时间: {}, 时区: UTC+0",
                sdf.format(now), sdf.format(startTime), sdf.format(endTime));

        // 判断是否在活动期间
        if (now.before(startTime) || now.after(endTime)) {
            log.warn("CoreSky 活动不在进行中");
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }

        // 判断用户是否在这个时间段注册的
        if (!isUserRegisteredInTimeRange(uid, startTime, endTime)) {
            log.warn("用户 {} 不在 CoreSky 活动注册时间段内", uid);
            throw new ApiException(10061);
        }

        // 判断是否已经领取过了
        if (hasUserReceivedReward(uid)) {
            log.warn("用户 {} 已经领取过 CoreSky 活动奖励", uid);
            throw new ApiException(10061);
        }

        // 判断密令是否正确
        String expectedKey = config.getString("key");
        if (expectedKey == null || !expectedKey.equals(key)) {
            log.warn("CoreSky 活动密令不正确 - 用户: {}, 输入密令: {}", uid, key);
            throw new ApiException(10060);
        }

        // 写入领取结果到 redis（原子操作，确保幂等性）
        if (!markUserRewardReceived(uid)) {
            log.warn("用户 {} CoreSky 奖励领取状态标记失败", uid);
            throw new ApiException(10061);
        }

        try {
            // 调用发送 xme 的接口
            sendCoreSkyXmeReward(uid);
        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("CoreSky 活动参与失败，用户: {}, 错误: {}", uid, e.getMessage(), e);
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }

        try {
            // 发送 kafka 消息到站内信
            sendCoreSkyJoinSuccessMessage(uid);
        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("CoreSky 活动参与失败，用户: {}, 错误: {}", uid, e.getMessage(), e);
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }

        // 在成功返回前，记录用户参与活动的信息到数据库
        try {
            boolean recordInserted = coreSkyActivityRecordService.insertActivityRecord(uid, clientIp);
            if (recordInserted) {
                log.info("用户 {} CoreSky 活动参与记录插入成功", uid);
            } else {
                log.warn("用户 {} CoreSky 活动参与记录插入失败，但不影响主流程", uid);
            }
        } catch (Exception e) {
            log.error("用户 {} CoreSky 活动参与记录插入异常，但不影响主流程: {}", uid, e.getMessage(), e);
        }

        log.info("用户 {} CoreSky 活动参与成功", uid);
        ApiResponse response = new ApiResponse();
        response.setCode(200);
        response.setSuccess(true);
        response.setMessage(LocalMessageResource.getMessage("result.code.10062"));
        return response;
    }

    /**
     * 获取用户注册时间
     *
     * @param uid 用户ID
     * @return 用户注册时间，如果用户不存在返回 null
     */
    public Date getUserRegistrationTime(Long uid) {
        if (uid == null) {
            log.warn("获取用户注册时间失败：用户ID为空");
            return null;
        }

        try {
            ClientUserResponse user = clientUserCacheService.me(uid);
            if (user == null) {
                log.warn("用户 {} 不存在", uid);
                return null;
            }

            return user.getRegistrationTime();
        } catch (Exception e) {
            log.error("获取用户 {} 注册时间失败: {}", uid, e.getMessage());
            return null;
        }
    }

    /**
     * 检查用户是否在指定时间段内注册
     *
     * @param uid       用户ID
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return true 表示在时间段内注册，false 表示不在时间段内注册
     */
    public boolean isUserRegisteredInTimeRange(Long uid, Date startTime, Date endTime) {
        if (uid == null || startTime == null || endTime == null) {
            log.warn("检查用户注册时间段失败：参数为空");
            return false;
        }

        try {
            Date registrationTime = getUserRegistrationTime(uid);
            if (registrationTime == null) {
                return false;
            }

            boolean inRange = !registrationTime.before(startTime) && !registrationTime.after(endTime);
            log.debug("用户 {} 注册时间: {}, 时间段: {} - {}, 是否在范围内: {}",
                    uid, registrationTime, startTime, endTime, inRange);
            return inRange;
        } catch (Exception e) {
            log.error("检查用户 {} 注册时间段失败: {}", uid, e.getMessage());
            return false;
        }
    }

    /**
     * 检查用户是否已经领取过 CoreSky 活动奖励
     *
     * @param uid 用户ID
     * @return true 表示已领取，false 表示未领取
     */
    public boolean hasUserReceivedReward(Long uid) {
        if (uid == null) {
            log.warn("检查用户奖励领取状态失败：用户ID为空");
            return false;
        }

        try {
            String redisKey = getCoreSkyRewardRedisKey(uid);
            Boolean hasKey = stringRedisTemplate.hasKey(redisKey);
            boolean hasReceived = Boolean.TRUE.equals(hasKey);

            log.debug("检查用户 {} CoreSky 奖励领取状态: {}", uid, hasReceived);
            return hasReceived;
        } catch (Exception e) {
            log.error("检查用户 {} CoreSky 奖励领取状态失败: {}", uid, e.getMessage());
            return false;
        }
    }

    /**
     * 标记用户已领取 CoreSky 活动奖励
     *
     * @param uid 用户ID
     * @return true 表示标记成功，false 表示标记失败
     */
    public boolean markUserRewardReceived(Long uid) {
        if (uid == null) {
            log.warn("标记用户奖励领取状态失败：用户ID为空");
            return false;
        }

        try {
            String redisKey = getCoreSkyRewardRedisKey(uid);

            // 使用 setIfAbsent 确保幂等性，如果 key 已存在则不会覆盖
            Boolean success = stringRedisTemplate.opsForValue().setIfAbsent(redisKey, "1", 30, TimeUnit.DAYS);

            if (Boolean.TRUE.equals(success)) {
                log.info("用户 {} CoreSky 奖励领取状态标记成功", uid);
                return true;
            } else {
                log.warn("用户 {} CoreSky 奖励领取状态标记失败：可能已经领取过", uid);
                return false;
            }
        } catch (Exception e) {
            log.error("标记用户 {} CoreSky 奖励领取状态失败: {}", uid, e.getMessage());
            return false;
        }
    }

    /**
     * 发送 CoreSky 活动 XME 奖励
     *
     * @param uid 用户ID
     * @return true 表示发送成功，false 表示发送失败
     */
    private boolean sendCoreSkyXmeReward(Long uid) {
        if (uid == null) {
            log.warn("发送 CoreSky XME 奖励失败：用户ID为空");
            return false;
        }

        try {
            String xmeIdempotentId = DigestUtils.md5Hex("activity:coresky:xme:" + uid);
            XmeRedemptionRequest xmeRequest = XmeRedemptionRequest.builder()
                    .uid(uid)
                    .amount(88) // CoreSky 活动奖励 88 XME
                    .ccy("XME")
                    .network("XME")
                    .eventId(41) // CoreSky 活动事件ID
                    .idempotent(xmeIdempotentId)
                    .build();

            ApiResponse<?> xmeResponse = userPointClient.xmeRedemptionFromOP(xmeRequest);
            if (xmeResponse != null && Boolean.TRUE.equals(xmeResponse.getSuccess())) {
                log.info("Successfully sent 88 XME to user {} for CoreSky activity", uid);
                return true;
            } else {
                log.error("Failed to send XME to user {} for CoreSky activity: {}",
                        uid, xmeResponse != null ? xmeResponse.getMessage() : "null response");
                throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
            }
        } catch (ApiException e) {
            // 重新抛出 ApiException，保持原有的错误类型
            throw e;
        } catch (Exception e) {
            log.error("Error sending XME to user {} for CoreSky activity", uid, e);
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }
    }

    /**
     * 生成 CoreSky 奖励领取记录的 Redis key
     *
     * @param uid 用户ID
     * @return Redis key
     */
    private String getCoreSkyRewardRedisKey(Long uid) {
        return MediaUserConstant.CORESKY_REWARD_PREFIX + uid;
    }

    /**
     * 发送 CoreSky 活动参与成功消息到 Kafka
     *
     * @param uid 用户ID
     */
    private void sendCoreSkyJoinSuccessMessage(Long uid) {
        if (uid == null) {
            log.warn("发送 CoreSky 活动参与成功消息失败：用户ID为空");
            return;
        }

        try {
            // 使用 CoreSkyMessageRequest 格式发送消息
            CoreSkyMessageRequest messageRequest = new CoreSkyMessageRequest()
                    .setUid(uid)
                    .setAmount(new BigDecimal("88")) // CoreSky 活动奖励 88 XME
                    .setCreateTime(TimeTool.timestamp());

            String message = JSON.toJSONString(messageRequest);
            kafkaMessageProducer.sendMessage(KafkaTopicConstant.CORESKY_JOIN_SUCCESS, message);

            log.info("成功发送 CoreSky 活动参与成功消息到 Kafka，用户: {}, 消息: {}", uid, message);
        } catch (Exception e) {
            log.error("发送 CoreSky 活动参与成功消息到 Kafka 失败，用户: {}", uid, e);
        }
    }

    /**
     * 判断用户是否应该显示 CoreSky 弹窗
     * 综合判断：活动时间、用户注册时间、是否已获得奖励
     *
     * @param uid 用户ID，如果为null表示未登录用户
     * @return true 表示应该显示弹窗，false 表示不应该显示
     */
    public boolean shouldShowCoreSkyPopup(Long uid, boolean checkNewUser) {
        try {
            JSONObject config = parseCoreSkyConfig();
            if (config == null) {
                log.debug("CoreSky 活动配置为空，不显示弹窗");
                return false;
            }

            String startTimeStr = config.getString("startTime");
            String endTimeStr = config.getString("endTime");

            if (startTimeStr == null || endTimeStr == null) {
                log.debug("CoreSky 活动配置缺少时间信息，不显示弹窗");
                return false;
            }

            // 明确指定时区为 UTC+0 (世界标准时间)
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            sdf.setTimeZone(TimeZone.getTimeZone("UTC")); // UTC+0

            Date startTime = sdf.parse(startTimeStr);
            Date endTime = sdf.parse(endTimeStr);
            Date now = new Date();

            // 检查是否在活动期间
            if (now.before(startTime) || now.after(endTime)) {
                log.debug("CoreSky 活动不在进行中，不显示弹窗");
                return false;
            }

            // 如果用户未登录，只要在活动期间就显示弹窗
            if (uid == null) {
                log.debug("未登录用户，活动期间显示弹窗");
                return true;
            }

            // 检查用户注册时间是否在活动期间
            if (checkNewUser) {
                if (!isUserRegisteredInTimeRange(uid, startTime, endTime)) {
                    log.debug("用户 {} 不在 CoreSky 活动注册时间段内，不显示弹窗", uid);
                    return false;
                }
            }

            // 检查用户是否已经获得奖励
            if (hasUserReceivedReward(uid)) {
                log.debug("用户 {} 已经获得 CoreSky 奖励，不显示弹窗", uid);
                return false;
            }

            log.debug("用户 {} 符合 CoreSky 弹窗显示条件", uid);
            return true;
        } catch (Exception e) {
            log.error("判断 CoreSky 弹窗显示条件失败，用户: {}, 错误: {}", uid, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析 CoreSky 配置
     *
     * @return JSONObject 配置对象，解析失败返回 null
     */
    private JSONObject parseCoreSkyConfig() {
        try {
            if (coreSkyConfigJson == null || coreSkyConfigJson.isEmpty() || "{}".equals(coreSkyConfigJson)) {
                return null;
            }
            return JSON.parseObject(coreSkyConfigJson);
        } catch (Exception e) {
            log.error("解析 CoreSky 配置失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 查询bitcoin 价格
     *
     * @return
     */
    public String getBitcoinPriceDescription() {
        String bitcoinPrefix = "1 Bitcoin≈$";
        try {
            XmeRedemptionRequest xmeRedemptionRequest = new XmeRedemptionRequest();
            xmeRedemptionRequest.setCcy("BTC");
            ApiResponse<CcyPriceReponse> apiResponse = userPointClient.getCcyPrice(xmeRedemptionRequest);
            if (!apiResponse.getSuccess() || Objects.isNull(apiResponse.getResult()) || Objects.isNull(apiResponse.getResult().getPrice())) {
                log.error("query bitcoin price fail!");
                return Strings.concat(bitcoinPrefix, "118075.46");
            }
            return Strings.concat(bitcoinPrefix, apiResponse.getResult().getPrice().toString());
        }catch (Exception e) {
            log.error("query bitcoin price fail!");
        }
        return Strings.concat(bitcoinPrefix, "118075.46"); //
    }

    public void processDate(PopupResponse.PopupItem responseItem) {
        int type = userInviteCache.getPopupType(CloudSession.getUid());
        log.info("查询用户:{}弹窗类型:{}", CloudSession.getUid(), type);
        String title = enhancedI18nConvert.getI18nMessage(String.format(POPUP_BITCOIN_TITLE_TYPE_I18N_KEY, type), ClientInfoContext.getLanguage());
        String content = enhancedI18nConvert.getI18nMessage(String.format(POPUP_BITCOIN_CONTENT_TYPE_I18N_KEY, type), ClientInfoContext.getLanguage());
        String button = enhancedI18nConvert.getI18nMessage(String.format(POPUP_BITCOIN_BUTTON_TYPE_I18N_KEY, type), ClientInfoContext.getLanguage());
        responseItem.setTitle(title);
        responseItem.setContent(content);
        responseItem.setButtonText(button);
        responseItem.setTrackingPointId(Strings.concat(responseItem.getId(), String.valueOf(type)));
//        responseItem.setDescription(this.getBitcoinPriceDescription()); // 原型修改,不需要展示汇率
    }
}

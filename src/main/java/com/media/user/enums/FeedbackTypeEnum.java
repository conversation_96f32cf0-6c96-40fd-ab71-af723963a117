package com.media.user.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum for feedback types
 */
@Getter
@AllArgsConstructor
public enum FeedbackTypeEnum {

    /**
     * General feedback
     */
    GENERAL((byte) 1, "General feedback"),


    /**
     * Appeal
     */
    APPEAL((byte) 2, "Appeal");

    private final byte type;
    private final String description;

    /**
     * Get enum by type
     *
     * @param type type value
     * @return enum instance
     */
    public static FeedbackTypeEnum getByType(Byte type) {
        if (type == null) {
            return null;
        }
        for (FeedbackTypeEnum typeEnum : FeedbackTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}

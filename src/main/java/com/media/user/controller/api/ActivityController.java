package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.core.utils.IpUtil;
import com.media.user.dto.request.CoreSkyJoinRequest;
import com.media.user.dto.response.ActivityResponse;
import com.media.user.service.ActivityCommonService;
import com.media.user.service.ActivityConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.xme.xme_base_depends.models.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/user/api/activity")
public class ActivityController {

    @Autowired
    private ActivityCommonService activityCommonService;

    @Autowired
    private ActivityConfigService activityConfigService;

    @Autowired
    private IpUtil ipUtil;

    @GetMapping("/list")
    public ActivityResponse getActivities(HttpServletRequest request) {
        ActivityResponse response = new ActivityResponse();
        response.setActivities(activityConfigService.getActivities(request));
        return response;
    }

    /**
     * CoreSky 活动参与接口
     *
     * @param request     包含活动密令的请求
     * @param httpRequest HTTP请求对象，用于获取客户端IP
     * @return 参与结果
     */
    @RequireAuth
    @PostMapping("/coreSky/join")
    public ApiResponse coreSkyJoin(@Validated @RequestBody CoreSkyJoinRequest request,
            HttpServletRequest httpRequest) {
        Long uid = CloudSession.getUid();
        String clientIp = ipUtil.getIpAddrByRequest(httpRequest);

        log.info("CoreSky活动参与请求：用户ID={}, 客户端IP={}", uid, clientIp);

        return activityCommonService.coreSkyJoin(uid, request.getKey(), clientIp);
    }
}

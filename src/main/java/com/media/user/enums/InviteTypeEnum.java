package com.media.user.enums;

import lombok.Getter;

/**
 * 邀请类型-1:普通邀请;2:创世大使邀请;3:活动邀请;4:游戏活动邀请
 */
@Getter
public enum InviteTypeEnum {

    INVITE_TYPE_1(1, "普通邀请"),
    INVITE_TYPE_2(2, "创世大使邀请"),
    INVITE_TYPE_3(3, "活动邀请"),
    INVITE_TYPE_4(4, "游戏活动邀请");

    private Integer type;
    private String description;

    private InviteTypeEnum(Integer type, String description){
        this.type = type;
        this.description = description;
    }
}

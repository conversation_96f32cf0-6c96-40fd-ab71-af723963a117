package com.media.user.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.UserLoginLogModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface UserLoginLogMapper extends BaseMapper<UserLoginLogModel> {

    default Long findByLoginDate(Long uid, String loginDate) {
        QueryWrapper<UserLoginLogModel> wrapper = new QueryWrapper<>();
        wrapper.eq("uid", uid);
        wrapper.eq("login_time", loginDate);
        return selectCount(wrapper);
    }

    /**
     * 统计指定日期的唯一登录用户数量
     */
    @Select("SELECT COUNT(DISTINCT uid) FROM user_login_log WHERE login_time = #{date}")
    Long countUniqueUsersByDate(@Param("date") String date);

}

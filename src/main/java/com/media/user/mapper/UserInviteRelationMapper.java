package com.media.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.UserInviteRelationModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface UserInviteRelationMapper extends BaseMapper<UserInviteRelationModel> {

    @Select("select sum(invite_amount) 'sum',count(1) 'count' from user_invite_relation where invite_uid=#{inviteUid} and invite_amount_status=2 and is_push_front=0")
    Map<String, Object> countNotPush(@Param("inviteUid") Long inviteUid);

    @Select("select sum(invite_amount) 'sum',count(1) 'count' from user_invite_relation where invite_uid=#{inviteUid} and invite_amount_status=2")
    Map<String, Object> countAll(@Param("inviteUid") Long inviteUid);

    @Update("UPDATE user_invite_relation SET is_push_front=1, updated_time=#{date}  where invite_uid=#{inviteUid} and is_push_front=0 and invite_amount_status=2")
    void setPushStatus(@Param("inviteUid") Long inviteUid, @Param("date") Date date);

    @Update("UPDATE user_invite_relation SET invite_amount_status=2, updated_time=#{date}  where idempotent=#{idempotent} and invite_amount_status=1")
    void setInviteAmountStatus(@Param("idempotent") String idempotent, @Param("date") Date date);

    @Update("UPDATE user_invite_relation SET invite_amount_status=2, updated_time=#{date}  where invite_uid=#{inviteUid} and invited_uid=#{invitedUid} and invite_amount_status=9")
    void modifyInviteXmeAmountStatus(@Param("inviteUid") Long inviteUid, @Param("invitedUid") Long invitedUid, @Param("date") Date date);

    @Select("<script>select * from user_invite_relation where invited_uid in <foreach item='uid' index='index' collection='uids' open='(' separator=',' close=')'>#{uid}</foreach></script>")
    List<UserInviteRelationModel> getInviteRationXme(@Param("uids") List<Long> uids);

    /**
     * 批量查询用户邀请人数
     * @param uids 用户ID列表
     * @return 每个用户的邀请人数
     */
    @Select("<script>SELECT invite_uid, COUNT(1) as invite_count FROM user_invite_relation WHERE invite_uid IN <foreach item='uid' index='index' collection='uids' open='(' separator=',' close=')'>#{uid}</foreach> GROUP BY invite_uid</script>")
    List<Map<String, Object>> batchCountInvites(@Param("uids") List<Long> uids);
}
package com.media.user.context;

import com.media.user.dto.response.internal.RegisterCodeInfoResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 注册码上下文管理器
 * 负责管理EventBus处理器之间的注册码信息传递
 */
@Slf4j
public class RegisterCodeContext {
    
    private static final ThreadLocal<RegisterCodeInfoResponse> CONTEXT = new ThreadLocal<>();
    
    /**
     * 设置注册码信息
     */
    public static void setRegisterCodeInfo(RegisterCodeInfoResponse response) {
        CONTEXT.set(response);
        log.debug("Register code info set: {}", response != null ? response.toString() : "null");
    }
    
    /**
     * 获取注册码信息
     */
    public static RegisterCodeInfoResponse getRegisterCodeInfo() {
        RegisterCodeInfoResponse response = CONTEXT.get();
        log.debug("Register code info retrieved: {}", response != null ? response.toString() : "null");
        return response;
    }
    
    /**
     * 检查注册码是否开放
     */
    public static boolean isRegisterCodeOpen() {
        RegisterCodeInfoResponse response = getRegisterCodeInfo();
        return response != null && response.isOpen();
    }
    
    /**
     * 清理注册码上下文
     * 必须在事件处理完成后调用，避免内存泄露
     */
    public static void clear() {
        CONTEXT.remove();
        log.debug("Register code context cleared");
    }
    
    /**
     * 执行带注册码上下文的操作
     * 自动管理上下文的清理
     */
    public static <T> T execute(RegisterCodeOperation<T> operation) {
        try {
            return operation.execute();
        } catch (RuntimeException e) {
            // 直接抛出运行时异常，不包装
            throw e;
        } catch (Exception e) {
            // 只包装检查异常
            throw new RuntimeException("Register code operation failed", e);
        } finally {
            clear();
        }
    }
    
    /**
     * 注册码操作接口
     */
    @FunctionalInterface
    public interface RegisterCodeOperation<T> {
        T execute() throws Exception;
    }
} 
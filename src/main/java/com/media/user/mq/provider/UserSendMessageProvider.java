package com.media.user.mq.provider;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.media.core.constant.KafkaTopicConstant;
import com.media.core.utils.MD5Util;
import com.media.core.utils.TimeTool;
import com.media.user.dto.request.UserInviteTransferAccountRequest;
import com.media.user.dto.request.MultiCurrencyRewardRequest;
import com.media.user.enums.CcyEnums;
import com.media.user.enums.NetworkEnums;
import com.media.user.enums.PointEventEnums;
import com.media.user.mq.KafkaMessageProducer;
import com.xme.xme_base_depends.mq.message.UserBehaviorEvent;
import com.xme.xme_base_depends.mq.message.UserEventMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class UserSendMessageProvider {

    @Autowired
    KafkaMessageProducer kafkaMessageProducer;

    /**
     * @param followUid    关注人
     * @param fromUserName 关注人昵称
     * @param toUid        被关注人
     */
    @Async("threadPoolTaskExecutor")
    public void sendFollowMessage(Long followUid, String fromUserName, String fromUserAvatarUrl, Long toUid) {
        log.info("userSendMessageProvider sendFollowMessage event:{} followUid:{} toUid:{}", KafkaTopicConstant.USER_ATTENTION_EVENT, followUid, toUid);
        Map<String, Object> obj = new HashMap<>();
        obj.put("fromUid", followUid);   //关注人
        obj.put("fromUserName", fromUserName);   //关注人
        obj.put("toUid", toUid);   //被关注人
        obj.put("createTime", TimeTool.timestamp());   //时间
        obj.put("idempotent", DigestUtils.md5Hex(followUid + "-" + toUid));  //幂等值
        //TODO 兼容老消息系统
        obj.put("followName", fromUserName); //关注人名称
        obj.put("headAddress", fromUserAvatarUrl); //关注人头像地址
        kafkaMessageProducer.sendMessage(KafkaTopicConstant.USER_ATTENTION_EVENT, JSONObject.toJSONString(obj));
    }

    /**
     * @param followUid    被邀请人(注册用户)
     * @param fromUserName 被邀请人昵称
     * @param toUid        邀请人
     */
    @Async("threadPoolTaskExecutor")
    public void sendRegisterInviteMessage(Long followUid, String fromUserName, Long toUid) {
        log.info("userSendMessageProvider sendRegisterInviteMessage event:{} followUid:{} toUid:{}", KafkaTopicConstant.INVITE_USER_REGISTER_SUCCESS, followUid, toUid);
        Map<String, Object> obj = new HashMap<>();
        obj.put("fromUid", followUid);   //被邀请人
        obj.put("fromUserName", fromUserName);   //被邀请人昵称
        obj.put("toUid", toUid);   //邀请人
        obj.put("createTime", TimeTool.timestamp());   //时间
        obj.put("idempotent", DigestUtils.md5Hex(followUid + "-" + toUid));  //幂等值
        kafkaMessageProducer.sendMessage(KafkaTopicConstant.INVITE_USER_REGISTER_SUCCESS, JSONObject.toJSONString(obj));
    }

    /**
     * 发送人脸识别的消息
     *
     * @param fromUid
     * @param fromAmount
     * @param toUid
     * @param toAmount
     * @param idempotent
     */
    public void sendFaceGenesisXmeKafka(Long fromUid, Integer fromAmount, Long toUid, Integer toAmount, String idempotent) {
        log.info("userSendMessageProvider sendFaceGenesisXmeKafka fromUid:{} toUid:{}", fromUid, toUid);
        UserInviteTransferAccountRequest transferAccountRequest = new UserInviteTransferAccountRequest()
                .setEventId(PointEventEnums.LIVE_AUTH_XME.getEventId())
                .setCcy(CcyEnums.XME.name())
                .setNetwork(NetworkEnums.XME.name())
                .setFromUid(null)
                .setFromAmount(null)
                .setToUid(toUid)
                .setToAmount(new BigDecimal(toAmount))
                .setCreateTime(TimeTool.timestamp())
                .setIdempotent(idempotent);
        if (fromUid != null) {
            transferAccountRequest.setFromUid(fromUid);
        }
        if (fromAmount != null) {
            transferAccountRequest.setFromAmount(new BigDecimal(fromAmount));
        }
        kafkaMessageProducer.sendMessage(KafkaTopicConstant.FACE_GENESIS_XME, JSON.toJSONString(transferAccountRequest));
    }

    /**
     * 发送挂件消息
     *
     * @param uid
     */
    public void sendGenesisBadgeKafka(Long uid) {
        Map<String, Object> obj = new HashMap<>();
        obj.put("uid", uid);
        obj.put("createTime", TimeTool.timestamp());   //时间
        obj.put("idempotent", DigestUtils.md5Hex(uid + ""));  //幂等值
        kafkaMessageProducer.sendMessage(KafkaTopicConstant.GET_GENESIS_BADGE, JSON.toJSONString(obj));
    }


    /**
     * 获取转盘抽奖消息
     *
     * @param uid
     */
    public void sendTurntableChanceKafka(Long uid, String idempotent) {
        Map<String, Object> obj = new HashMap<>();
        obj.put("uid", uid);
        obj.put("createTime", TimeTool.timestamp());   //时间
        obj.put("idempotent", idempotent == null ? DigestUtils.md5Hex(uid + "") : idempotent);  //幂等值
        kafkaMessageProducer.sendMessage(KafkaTopicConstant.GET_TURNTABLE_CHANCE, JSON.toJSONString(obj));
    }

    /**
     * 发送预约发送奖励XME和消息
     *
     * @param fromUid
     * @param fromAmount
     * @param toUid
     * @param toAmount
     * @param idempotent
     */
    public void sendReserveXmeMessage(Long fromUid, Integer fromAmount, Long toUid, Integer toAmount, String idempotent) {
        log.info("userSendMessageProvider sendFaceGenesisXmeKafka fromUid:{} toUid:{}", fromUid, toUid);
        UserInviteTransferAccountRequest transferAccountRequest = new UserInviteTransferAccountRequest()
                .setEventId(PointEventEnums.LIVE_AUTH_XME.getEventId())
                .setCcy(CcyEnums.XME.name())
                .setNetwork(NetworkEnums.XME.name())
                .setFromUid(null)
                .setFromAmount(null)
                .setToUid(toUid)
                .setToAmount(new BigDecimal(toAmount))
                .setCreateTime(TimeTool.timestamp())
                .setIdempotent(idempotent);
        if (fromUid != null) {
            transferAccountRequest.setFromUid(fromUid);
        }
        if (fromAmount != null) {
            transferAccountRequest.setFromAmount(new BigDecimal(fromAmount));
        }
        kafkaMessageProducer.sendMessage(KafkaTopicConstant.LEAD_ORDER_REMIND, JSON.toJSONString(transferAccountRequest));
    }

    /**
     * 发送用户行为信息
     */
    public void sendUserEventMessage(UserBehaviorEvent dto, boolean isNewEvent, Date time) {
        UserEventMessage message = new UserEventMessage();
        message.setEventType(dto.getUserEventType());
        message.setEventIds(dto.getUserBehaviorEventType());
        message.setUserId(dto.getUserId());
        message.setData(dto);
        message.setEventTime(time);
        message.setNewActivity(isNewEvent);
        String uuid= MD5Util.getMD5(message.toString());
        message.setUuid(uuid);
        String sendJson=  JSON.toJSONString(message);
        log.info("send message={}",sendJson);
        kafkaMessageProducer.sendMessage(KafkaTopicConstant.USER_EVENT_NOTIFY, sendJson ,message.getUserId()+"");
    }

    /**
     * 发送多货币奖励消息（三选一认证奖励）
     *
     * @param verificationType 认证类型：1-自己认证, 2-被邀请认证
     * @param inviteType 邀请类型：1-普通邀请, 2-创世大使邀请, 3-活动邀请, 4-游戏活动邀请
     * @param activityId 活动ID（如果是活动邀请）
     * @param fromUid 邀请人UID
     * @param fromRewards 邀请人奖励Map，key为货币符号，value为数量
     * @param toUid 被邀请人/认证人UID
     * @param toRewards 被邀请人/认证人奖励Map，key为货币符号，value为数量
     * @param idempotent 幂等键
     */
    public void sendMultiCurrencyVerificationReward(Integer verificationType, Integer inviteType, Long activityId,
                                                   Long fromUid, Map<String, Integer> fromRewards,
                                                   Long toUid, Map<String, Integer> toRewards,
                                                   String idempotent) {
        log.info("sendMultiCurrencyVerificationReward verificationType:{}, inviteType:{}, fromUid:{}, toUid:{}, fromRewards:{}, toRewards:{}",
                 verificationType, inviteType, fromUid, toUid, fromRewards, toRewards);

        MultiCurrencyRewardRequest request = new MultiCurrencyRewardRequest()
                .setEventId(PointEventEnums.LIVE_AUTH_XME.getEventId())
                .setVerificationType(verificationType)
                .setInviteType(inviteType)
                .setActivityId(activityId)
                .setIdempotent(idempotent)
                .setCreateTime(TimeTool.timestamp());

        // 设置邀请人奖励
        if (fromUid != null && fromRewards != null && !fromRewards.isEmpty()) {
            MultiCurrencyRewardRequest.UserReward fromUserReward = new MultiCurrencyRewardRequest.UserReward()
                    .setUid(fromUid)
                    .setRewards(buildCurrencyRewards(fromRewards));
            request.setFromUser(fromUserReward);
        }

        // 设置被邀请人/认证人奖励
        if (toUid != null && toRewards != null && !toRewards.isEmpty()) {
            MultiCurrencyRewardRequest.UserReward toUserReward = new MultiCurrencyRewardRequest.UserReward()
                    .setUid(toUid)
                    .setRewards(buildCurrencyRewards(toRewards));
            request.setToUser(toUserReward);
        }

        kafkaMessageProducer.sendMessage(KafkaTopicConstant.MULTI_CURRENCY_VERIFICATION_REWARD, JSON.toJSONString(request));
    }

    /**
     * 构建货币奖励列表
     */
    private List<MultiCurrencyRewardRequest.CurrencyReward> buildCurrencyRewards(Map<String, Integer> rewardsMap) {
        List<MultiCurrencyRewardRequest.CurrencyReward> rewards = new ArrayList<>();

        for (Map.Entry<String, Integer> entry : rewardsMap.entrySet()) {
            String currency = entry.getKey();
            Integer amount = entry.getValue();

            if (amount != null && amount > 0) {
                // 根据货币类型设置对应的网络
                String network = getNetworkByCurrency(currency);
                rewards.add(new MultiCurrencyRewardRequest.CurrencyReward(currency, network, amount));
            }
        }

        return rewards;
    }

    /**
     * 根据货币类型获取对应的网络
     */
    private String getNetworkByCurrency(String currency) {
        switch (currency) {
            case "XME":
                return NetworkEnums.XME.name();
            case "DOGE":
                return NetworkEnums.DOGE.name();
            case "BTC":
                return "BTC"; // 直接使用字符串，因为NetworkEnums中暂时没有定义
            case "USDT":
                return "USDT"; // 直接使用字符串，因为NetworkEnums中暂时没有定义
            case "BNB":
                return "BNB"; // 直接使用字符串，因为NetworkEnums中暂时没有定义
            default:
                return NetworkEnums.XME.name(); // 默认使用XME网络
        }
    }
}

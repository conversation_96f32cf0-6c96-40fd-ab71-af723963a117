package com.media.user.dto.popup;

import lombok.Data;

import java.util.List;

@Data
public class PopupConfig {
    private List<PopupConfigItem> popups;

    @Data
    public static class PopupConfigItem {
        private String id;
        private String titleKey;       // i18n 资源文件中的标题键
        private String descriptionKey; // i18n 资源文件中的描述键
        private String contentKey;  // i18n 资源文件中的内容描述键
        private String buttonTextKey;  // i18n 资源文件中的按钮文字键
        private String backgroundImage; // 背景图片地址
        private String backgroundImageCn; // 背景图片地址
        private String jumpType;       // 跳转类型: native/h5/external
        private String jumpUrl;        // 跳转链接
        private String style;        // 样式
        private boolean clickable;
        private String startTime;      // 开始时间，格式：yyyy-MM-dd HH:mm:ss
        private String endTime;        // 结束时间
        private Integer status;        // 状态：1-激活，2-暂停，3-结束
        private Integer order;         // 排序
        private Integer width;         // 宽度
        private Integer height;         // 高度
        private boolean auditShowable; // 审核状态是否展示
        private boolean needLogin = false;  // 是否需要登录
        private boolean needFace = false;   // 是否需要人脸验证
        private boolean needVerify = false;   // 是否需要人脸验证
        private String minVersion;     // 最低版本要求（向后兼容，同时适用于Android和iOS）
        private String minVersionAndroid;  // Android最低版本要求
        private String minVersionIos;      // iOS最低版本要求
        private String popupType;     // 弹窗类型
        private String scene;          // 弹窗场景：home/task/profile/login 等，支持多个场景用逗号分隔，如 "home,task"
        private DisplayRule displayRule; // 显示规则
        private String highlightColor; // 高亮颜色

        @Data
        public static class DisplayRule {
            private String type;        // 规则类型：once_per_day/once_per_week/once_per_session/always/custom
            private Integer interval;   // 间隔时间（小时）
            private Integer maxTimes;   // 最大显示次数（per day/week）
            private String priority;    // 优先级：high/medium/low
        }
    }
}

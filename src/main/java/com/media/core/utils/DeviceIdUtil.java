package com.media.core.utils;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * Device ID 获取工具类
 */
@Slf4j
@Component
public class DeviceIdUtil {

    /**
     * 获取当前请求的 Device ID
     * 
     * @return Device ID，如果获取失败返回空字符串
     */
    public String getDeviceId() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = (HttpServletRequest) requestAttributes
                .resolveReference(RequestAttributes.REFERENCE_REQUEST);
        return this.getDeviceIdByRequest(request);
    }

    /**
     * 从 HttpServletRequest 中获取 Device ID
     * 
     * @param request HTTP请求对象
     * @return Device ID，如果获取失败返回空字符串
     */
    public String getDeviceIdByRequest(HttpServletRequest request) {
        String deviceId = null;
        try {
            // 1. 首先尝试从 X-Device-Fingerprint header 获取
            deviceId = request.getHeader("X-Device-Fingerprint");
            if (deviceId == null || deviceId.length() == 0 || "unknown".equalsIgnoreCase(deviceId)) {
                // 2. 如果 X-Device-Fingerprint 中没有，则取 X-Device-Id
                deviceId = request.getHeader("X-Device-Id");
            }

            // 3. 如果还是没有，返回空字符串
            if (deviceId == null || deviceId.length() == 0 || "unknown".equalsIgnoreCase(deviceId)) {
                deviceId = "";
            }

        } catch (Exception e) {
            deviceId = "";
            log.error("get device id error", e);
        }

        return deviceId;
    }
}
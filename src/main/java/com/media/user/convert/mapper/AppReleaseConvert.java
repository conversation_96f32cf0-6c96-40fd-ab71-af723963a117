package com.media.user.convert.mapper;

import com.media.user.domain.AppReleaseModel;
import com.media.user.dto.response.AppReleaseResponse;
import com.media.user.enums.LanguageEnums;
import org.springframework.stereotype.Component;

@Component
public class AppReleaseConvert {

    public static final AppReleaseConvert INSTANCE = new AppReleaseConvert();

    public AppReleaseResponse model2Response(AppReleaseModel model) {
        if (model == null) {
            return null;
        }
        
        AppReleaseResponse response = new AppReleaseResponse();
        response.setId(model.getId());
        response.setVersion(model.getVersion());
        response.setMinVersion(model.getMinVersion());
        response.setPlatformType(model.getPlatformType());
        response.setUrl(model.getUrl());
        response.setQrCode(model.getQrCode());
        response.setPackageSize(model.getPackageSize());
        response.setName(model.getEnName());
        response.setTitle(model.getEnTitle());
        response.setUpdateContent(model.getEnUpdateContent());
        response.setForceUpdate(false); // Default value
        
        return response;
    }

    public AppReleaseResponse model2ResponsePro(AppReleaseModel appReleaseModel, LanguageEnums languageEnums) {
        AppReleaseResponse response = model2Response(appReleaseModel);

        if (LanguageEnums.zh_CN.equals(languageEnums)) {
            response.setName(appReleaseModel.getZhName())
                    .setTitle(appReleaseModel.getZhTitle())
                    .setUpdateContent(appReleaseModel.getZhUpdateContent());
        } else {
            response.setName(appReleaseModel.getEnName())
                    .setTitle(appReleaseModel.getEnTitle())
                    .setUpdateContent(appReleaseModel.getEnUpdateContent());
        }
        return response;
    }
}

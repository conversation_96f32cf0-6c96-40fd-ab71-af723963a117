package com.media.user.feign.client;

import com.media.user.dto.request.PartnerAccessTokenRequest;
import com.media.user.dto.response.PartnerAccessTokenResponse;
import feign.Logger;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(url = "${partner.api.url:https://api-test.loopspace.xyz}", name = "partnerTokenClient", configuration = PartnerTokenClient.FeignConfig.class)
public interface PartnerTokenClient {
    
    /**
     * 调用下游服务获取access_token
     */
    @PostMapping(value = "/api/partner/access_token", consumes = "application/json", produces = "application/json")
    PartnerAccessTokenResponse getAccessToken(@RequestBody PartnerAccessTokenRequest request);
    
    @Configuration
    class FeignConfig {
        @Bean
        Logger.Level feignLoggerLevel() {
            return Logger.Level.FULL; // 记录完整的请求和响应信息
        }
    }
} 
package com.media.user.dto.request;

import java.io.Serializable;

import lombok.Data;

@Data
public class TelegramBindRequest implements Serializable{

    /**
     * Telegram用户ID
     */
    private Long id;
    
    /**
     * Telegram用户名
     */
    private String username;
    
    /**
     * Telegram用户昵称
     */
    private String first_name;
    
    /**
     * Telegram用户姓氏
     */
    private String last_name;

    /**
     * 用户头像
     */
    private String photo_url;

    /**
     * 认证日期时间戳
     */
    private String auth_date;

    /**
     * 数据哈希值，用于验证数据完整性
     */
    private String hash;
    
} 
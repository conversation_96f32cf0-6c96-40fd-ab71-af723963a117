package com.media.core.log;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
public class RequestLog {

    private String traceId;

    private String url;

    private String method;

    private Map<String, String> params;

    private Map<String, String> headers;

    private Object body;

    private ResponseLog responseLog;

    private boolean error = false;

}

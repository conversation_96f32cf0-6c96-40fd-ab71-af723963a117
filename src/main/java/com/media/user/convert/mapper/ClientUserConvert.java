package com.media.user.convert.mapper;

import com.media.user.domain.ClientUserModel;
import com.media.user.dto.request.ClientUserRequest;
import com.media.user.dto.response.ClientUserResponse;
import com.media.user.dto.response.internal.UserMeResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface ClientUserConvert {

    ClientUserConvert INSTANCE = Mappers.getMapper(ClientUserConvert.class);

    ClientUserResponse clientUser2Response(ClientUserModel clientUserModel);

    ClientUserModel req2Model(ClientUserRequest request);
}

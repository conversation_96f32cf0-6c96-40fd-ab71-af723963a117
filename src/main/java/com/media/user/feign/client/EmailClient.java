package com.media.user.feign.client;

import com.media.user.dto.request.SendEmailRequest;
import com.media.user.dto.request.SendPhoneSmsRequest;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.concurrent.TimeUnit;

@FeignClient(url = "${email.url}", value = "emialClient", configuration = EmailClient.EmailClientConfiguration.class)
public interface EmailClient {

    @PostMapping(value = "/mail/send", headers = {"user-agent=Chrome/69.0.3497.81 Safari/537.36","accept-language=zh-CN,zh"})
    void sendEmail(@RequestBody  SendEmailRequest request);

    @PostMapping(value = "/mail/html/send", headers = {"user-agent=Chrome/69.0.3497.81 Safari/537.36","accept-language=zh-CN,zh"})
    void sendEmailHtml(@RequestBody  SendEmailRequest request);

    @PostMapping(value = "/mail/phone/send", headers = {"user-agent=Chrome/69.0.3497.81 Safari/537.36","accept-language=zh-CN,zh"})
    void sendPhoneCode(@RequestBody SendPhoneSmsRequest request);

    /**
     * 通过模板发送短信
     *
     * @param request 发送短信请求，包含手机号、模板变量等
     */
    @PostMapping(value = "/mail/phone/send/template", headers = {"user-agent=Chrome/69.0.3497.81 Safari/537.36","accept-language=zh-CN,zh"})
    void sendPhoneCodeByTemplate(@RequestBody SendPhoneSmsRequest request);

    @Configuration
    class EmailClientConfiguration {
        @Bean("emailClientOptions")
        public Request.Options options() {
            // Set connect timeout to 50ms and read timeout to 200ms
            return new Request.Options(50, TimeUnit.MILLISECONDS, 1000, TimeUnit.MILLISECONDS, true);
        }
    }
}

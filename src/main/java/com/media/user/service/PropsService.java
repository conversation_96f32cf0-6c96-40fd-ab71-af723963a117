package com.media.user.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.user.constant.MediaUserConstant;
import com.media.user.convert.mapper.PropsConvert;
import com.media.user.domain.PropsModel;
import com.media.user.domain.UserFamilyMemberModel;
import com.media.user.dto.query.PropsQuery;
import com.media.user.dto.response.PropsResponse;
import com.media.user.enums.PropsTypeEnums;
import com.media.user.enums.UserStatusEnum;
import com.media.user.mapper.PropsMapper;
import com.media.core.request.ClientInfoContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;


@Slf4j
@Service
public class PropsService {

    @Autowired
    private PropsMapper propsMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    @Lazy
    private UserFamilyService userFamilyService;

    public PropsModel getPropsModelByType(Integer type) {
        String key = MediaUserConstant.PROPS_PREFIX+type;
        String value = stringRedisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(value)){
            QueryWrapper<PropsModel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("type", type);
            PropsModel propsModel = propsMapper.selectOne(queryWrapper);
            if (propsModel == null){
                return null;
            }
            stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(propsModel));
            return propsModel;
        }
        return JSON.parseObject(value, PropsModel.class);
    }

    public PropsResponse getPropsByType(PropsQuery query) {
        PropsModel propsModel = getPropsModelByType(query.getType());
        if (propsModel == null){
            return null;
        }
        PropsResponse propsResponse = PropsConvert.INSTANCE.model2ResponsePro(propsModel, ClientInfoContext.getLanguage());

        if (PropsTypeEnums.FAMILY_CARD.getCode().equals(propsModel.getType())){
            UserFamilyMemberModel userFamilyMemberModel = userFamilyService.getUserFamilyMember(query.getUid(), UserStatusEnum.ENABLED.getStatus());
            if (userFamilyMemberModel == null){
                BigDecimal price = propsResponse.getPrice().multiply(MediaUserConstant.KING_DIViDE);
                propsResponse.setPrice(price);
            }
        }

        return propsResponse;
    }

}

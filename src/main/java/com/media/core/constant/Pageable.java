package com.media.core.constant;

/**
 * 分页参数.
 *
 * <AUTHOR>
 */
public class Pageable {

    /**
     * 金额排序.
     */
    public static String PRICE = "price";

    /**
     * 金额排序.
     */
    public static String AMOUNT = "amount";

    /**
     * 更新时间排序.
     */
    public static String UPDATED_TIME = "updatedTime";

    /**
     * 创建时间排序.
     */
    public static String CREATED_TIME = "createdTime";

    /**
     * ID 排序 .
     */
    public static String ID = "id";

    public static String DESC = "desc";

    public static String ASC = "asc";

    /**
     * 页码 默认 1.
     */
    private Integer page;

    /**
     * 每页大小 默认 20.
     */
    private Integer size;

    private String orderBy;

    private String sort;

    /**
     * 限制最大值
     */
    private boolean limitSize = true;

    public Integer getSize() {
        if (limitSize && (size == null || size > 10000 || size < 1)) {
            return 20;
        }
        return size;
    }

    public Pageable setSize(Integer size) {
        this.size = size;
        return this;
    }

    public Pageable setPage(Integer page) {
        this.page = page;
        return this;
    }

    public Integer getPage() {
        if (page == null || page < 1) {
            return 1;
        }
        return page;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public Object setOrderBy(String orderBy) {
        this.orderBy = orderBy;
        return this;
    }

    public String getSort() {
        return sort;
    }

    public Object setSort(String sort) {
        this.sort = sort;
        return this;
    }

    public boolean isLimitSize() {
        return limitSize;
    }

    public Pageable setLimitSize(boolean limitSize) {
        this.limitSize = limitSize;
        return this;
    }

}

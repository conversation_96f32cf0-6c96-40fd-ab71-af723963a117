package com.media.user.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.media.core.constant.KafkaTopicConstant;
import com.media.user.service.UserBehaviorEventService;
import com.xme.xme_base_depends.consts.consts;
import com.xme.xme_base_depends.enums.UserBehaviorEventEnum;
import com.xme.xme_base_depends.mq.message.UserEventMessage;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.Set;
import java.util.UUID;

@Component
@Slf4j
public class UserEventConsumer {

    @Autowired
    private UserBehaviorEventService userBehaviorEventService;

    @KafkaListener(topics = KafkaTopicConstant.USER_EVENT_NOTIFY, groupId = KafkaTopicConstant.MEDIA_USER_EVENT_GROUP, concurrency = "5")
    public void userEventNotify(String message, Acknowledgment ack) {
        if (message == null || message.trim().isEmpty()) {
            log.error("Received null or empty message");
            ack.acknowledge();
            return;
        }
        Optional<UserEventMessage> userEventMessageOpt = Optional.empty();
        try {
            userEventMessageOpt = Optional.ofNullable(JSON.parseObject(message, UserEventMessage.class));
        } catch (Exception e) {
            log.error("Received user event message error", e);
            ack.acknowledge();
        }
        try {
            MDC.put(consts.LogRequestIdKey, UUID.randomUUID().toString());
            if (userEventMessageOpt.isEmpty()) {
                log.error("Failed to parse message to UserEventMessage, message: {}", message);
                ack.acknowledge(); // 确认消息以避免重复消费
                return;
            }
            UserEventMessage userEventMessage = userEventMessageOpt.get();
            if (!isValidUserEventMessage(userEventMessage)) {
                log.error("Invalid UserEventMessage, message: {}", message);
                ack.acknowledge();
                return;
            }
            if (userEventMessage.isNewActivity()) {
                log.warn("is new Activity, message: {}", message);
                ack.acknowledge();
                return;
            }
            Long userId = userEventMessage.getUserId();
            log.info("userEventNotify uid:{}, message:{}", userId, message);
            userEventMessage.getEventIds().forEach(eventId -> processSingleEvent(userEventMessage, eventId));
            ack.acknowledge();
        } catch (Exception e) {
            log.error("userEventNotify message:{} error", message, e);
        } finally {
            MDC.remove(consts.LogRequestIdKey);
        }
    }

    /**
     * 验证UserEventMessage的完整性
     *
     * @param userEventMessage 用户事件消息
     * @return 是否有效
     */
    private boolean isValidUserEventMessage(UserEventMessage userEventMessage) {
        if (userEventMessage == null) {
            return false;
        }
        Set<UserBehaviorEventEnum> eventIds = userEventMessage.getEventIds();
        if (eventIds == null || eventIds.isEmpty()) {
            log.warn("userEventNotify eventIds is null or empty, uid:{}", userEventMessage.getUserId());
            return false;
        }
        return true;
    }

    /**
     * 处理单个事件
     *
     * @param userEventMessage 用户事件消息
     * @param eventId          事件ID
     */
    private void processSingleEvent(UserEventMessage userEventMessage, UserBehaviorEventEnum eventId) {
        switch (eventId) {
            case REGISTERED:
                userBehaviorEventService.handlerRegistered(userEventMessage);
                break;
            case FACE_FINISH:
                userBehaviorEventService.handlerFaceFinish(userEventMessage);
                break;
            case PHONE_FINISH:
                userBehaviorEventService.handlerPhoneFinish(userEventMessage);
                break;
            case EMAIL_FINISH:
                userBehaviorEventService.handlerEmailFinish(userEventMessage);
                break;
            default:
                log.error("userEventNotify eventId:{} not support", eventId);
        }
    }
}

package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class UserOverviewResponse {

    private Long uid;

    /**
     * xme数量
     */
    private BigDecimal xmeAmount;

    /**
     * 邀请加成
     */
    private BigDecimal inviteAddition;

    /**
     * 身份加成
     */
    private BigDecimal identityAddition;

    /**
     * 当天基础积分数量
     */
    private BigDecimal basePointCount;
}

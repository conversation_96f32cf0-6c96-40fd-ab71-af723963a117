# 弹窗系统使用说明

## 概述

弹窗系统采用服务端配置下发 + 客户端控制显示的设计，确保灵活性和性能的平衡。

## 设计理念

### 服务端职责
- 提供弹窗配置下发
- 进行基础过滤（时间、版本、登录状态、审核状态等）
- 提供统计记录接口

### 客户端职责  
- 根据显示规则控制弹窗显示时机
- 管理本地显示状态（localStorage/SharedPreferences）
- 根据用户交互和页面状态决定是否显示

## API接口

### 1. 获取弹窗列表
```
GET /user/api/system/popup/list?scene={scene}
```

#### 参数
- `scene`（可选）：弹窗场景，如 `home`、`task`、`profile`、`login`

#### 响应示例
```json
[
    {
        "id": "welcome_popup",
        "title": "欢迎使用X.ME",
        "description": "开始您的数字资产之旅",
        "icon": "https://s3.x.me/images/popup/welcome_icon.png",
        "backgroundImage": "https://s3.x.me/images/popup/welcome_bg.png",
        "jumpType": "native",
        "jumpUrl": "xme://taskCenter",
        "clickable": true,
        "status": 1,
        "order": 10,
        "needLogin": false,
        "needFace": false,
        "scene": "home",
        "shouldShow": true,
        "displayRule": {
            "type": "once_per_day",
            "interval": 24,
            "maxTimes": 1,
            "priority": "high"
        }
    }
]
```

### 2. 记录弹窗显示
```
GET /user/api/system/popup/record?popupId={popupId}
```

用于统计分析，客户端在显示弹窗时调用。

## 配置格式

### Apollo配置Key
```
popup.config
```

### 配置结构
```json
{
    "popups": [
        {
            "id": "unique_popup_id",
            "titleKey": "popup.title.key",
            "descriptionKey": "popup.description.key", 
            "icon": "图标URL",
            "backgroundImage": "背景图URL",
            "jumpType": "native|h5|external",
            "jumpUrl": "跳转链接",
            "clickable": true,
            "startTime": "2025-01-01 00:00:00",
            "endTime": "2025-12-31 23:59:59",
            "status": 1,
            "order": 10,
            "auditShowable": true,
            "needLogin": false,
            "needFace": false,
            "minVersion": "1.5.0",
            "scene": "home",
            "displayRule": {
                "type": "once_per_day",
                "interval": 24,
                "maxTimes": 1,
                "priority": "high"
            }
        }
    ]
}
```

### 字段说明

#### 基础字段
- `id`: 弹窗唯一标识
- `titleKey`/`descriptionKey`: 国际化键名
- `icon`: 图标URL
- `backgroundImage`: 背景图URL
- `jumpType`: 跳转类型
  - `native`: 原生页面跳转
  - `h5`: H5页面
  - `external`: 外部链接
- `jumpUrl`: 跳转链接
- `clickable`: 是否可点击

#### 时间控制
- `startTime`/`endTime`: 生效时间范围
- `status`: 状态（1-激活，2-暂停，3-结束）

#### 条件控制
- `needLogin`: 是否需要登录
- `needFace`: 是否需要人脸验证  
- `minVersion`: 最低版本要求
- `auditShowable`: 审核状态下是否显示
- `scene`: 显示场景

#### 显示规则 (displayRule)
- `type`: 规则类型
  - `once_per_day`: 每天最多一次
  - `once_per_week`: 每周最多一次  
  - `once_per_session`: 每次会话最多一次
  - `always`: 总是显示
  - `custom`: 自定义间隔
- `interval`: 间隔时间（小时）
- `maxTimes`: 最大显示次数
- `priority`: 优先级（high/medium/low）

## 客户端实现建议

### 1. 存储结构
```javascript
// localStorage示例
const popupStorage = {
    "welcome_popup": {
        "lastShown": 1736236800000,  // 上次显示时间戳
        "showCount": 1,              // 显示次数
        "dayCount": "2025-01-08:1"   // 当天显示次数
    }
}
```

### 2. 显示规则判断
```javascript
function shouldShowPopup(popup, storage) {
    const rule = popup.displayRule;
    if (!rule || rule.type === 'always') return true;
    
    const record = storage[popup.id] || {};
    const now = Date.now();
    const lastShown = record.lastShown || 0;
    
    switch (rule.type) {
        case 'once_per_day':
            const daysPassed = Math.floor((now - lastShown) / (24 * 60 * 60 * 1000));
            return daysPassed >= 1;
            
        case 'once_per_week':
            const weeksPassed = Math.floor((now - lastShown) / (7 * 24 * 60 * 60 * 1000));
            return weeksPassed >= 1;
            
        case 'once_per_session':
            return !record.sessionShown;
            
        case 'custom':
            const hoursPassed = Math.floor((now - lastShown) / (60 * 60 * 1000));
            return hoursPassed >= (rule.interval || 24);
            
        default:
            return true;
    }
}
```

### 3. 记录显示
```javascript
function recordPopupShown(popupId, storage) {
    const now = Date.now();
    const today = new Date().toISOString().split('T')[0];
    
    storage[popupId] = {
        lastShown: now,
        showCount: (storage[popupId]?.showCount || 0) + 1,
        sessionShown: true,
        dayCount: `${today}:${(storage[popupId]?.dayCount?.split(':')[1] || 0) + 1}`
    };
    
    // 调用服务端记录接口
    fetch(`/user/api/system/popup/record?popupId=${popupId}`);
}
```

## 使用场景示例

### 1. 首页欢迎弹窗（每天一次）
```json
{
    "id": "daily_welcome",
    "scene": "home",
    "displayRule": {
        "type": "once_per_day",
        "priority": "high"
    }
}
```

### 2. 任务提醒弹窗（每周一次）  
```json
{
    "id": "weekly_task_reminder",
    "scene": "task",
    "displayRule": {
        "type": "once_per_week",
        "priority": "medium"
    }
}
```

### 3. 紧急通知（总是显示）
```json
{
    "id": "urgent_notice",
    "scene": "home",
    "displayRule": {
        "type": "always",
        "priority": "high"
    }
}
```

### 4. 活动推广（自定义间隔）
```json
{
    "id": "promotion_popup",
    "scene": "home", 
    "displayRule": {
        "type": "custom",
        "interval": 72,
        "priority": "low"
    }
}
```

## 优势

1. **性能优化**: 服务端不维护复杂状态，减少Redis压力
2. **灵活控制**: 客户端可结合页面状态、用户交互等因素控制显示  
3. **离线友好**: 客户端可以离线判断是否显示
4. **降低延迟**: 减少服务端计算时间
5. **易于调试**: 客户端显示逻辑透明可控 
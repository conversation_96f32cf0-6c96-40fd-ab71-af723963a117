package com.media.user.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.UserLoginLogModel;
import com.media.user.domain.UserLoginLogVersionModel;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface UserLoginLogVersionMapper extends BaseMapper<UserLoginLogVersionModel> {

    /**
     * 查询大于指定ID的新增日志
     */
    @Select("""
    SELECT l.id, l.uid, l.version
    FROM (
        SELECT uid, id, version
        FROM user_login_log_version 
        WHERE version = #{version} AND id > #{lastId} 
        ORDER BY id ASC
    ) l
    INNER JOIN client_user c ON l.uid = c.uid AND c.registration_time < #{time}
    """)
    List<UserLoginLogVersionModel> findNewLogsAfterId(@Param("lastId") Long lastId, @Param("version") String version, @Param("time") String time);



    @Insert(" INSERT IGNORE INTO user_login_log_version \n" +
            "            (uid, version, created_time, updated_time)\n" +
            "        VALUES \n" +
            "            (#{uid}, #{version}, #{createdTime}, #{updatedTime})")
    int insertIgnore(UserLoginLogVersionModel loginLog);
}

package com.media.user.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.media.core.constant.KafkaTopicConstant;
import com.media.user.constant.MediaUserConstant;
import com.media.user.enums.PointEventEnums;
import com.media.user.mq.dto.XmeReceiveResponse;
import com.media.user.service.UserInviteFaceRecordService;
import com.media.user.service.UserInviteRelationService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class XmeReceiveStatusConsumer {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    UserInviteRelationService userInviteRelationService;

    @Autowired
    UserInviteFaceRecordService userInviteFaceRecordService;

    @KafkaListener(topics = KafkaTopicConstant.XME_RECEIVE_STATUS, groupId = KafkaTopicConstant.USER_GROUP, concurrency = "5")
    public void xmeReceiveStatus(String message, Acknowledgment ack) {
        XmeReceiveResponse xmeReceiveResponse = JSON.parseObject(message, XmeReceiveResponse.class);
        log.info("XmeReceiveStatusConsumer.xmeReceiveStatus id:{}, message:{}", xmeReceiveResponse.getUid(), message);
        RLock rLock = redissonClient.getLock(MediaUserConstant.XME_RECEIVE_STATUS + xmeReceiveResponse.getIdempotent() + xmeReceiveResponse.getUid());
        try {
            rLock.lock(5000, TimeUnit.MILLISECONDS);
            if(Objects.equals(xmeReceiveResponse.getEventId(), PointEventEnums.INVITE_XME.getEventId())){  //邀请 xme 的到账
                log.info("modify invite xme status");
                userInviteRelationService.modifyStatus(xmeReceiveResponse);
            }else if(Objects.equals(xmeReceiveResponse.getEventId(), PointEventEnums.LIVE_AUTH_XME.getEventId())){  //人脸识别的 xme 的到账
                log.info("modify face xme status");
                userInviteFaceRecordService.modifyStatus(xmeReceiveResponse);
            }
            ack.acknowledge();
        }catch (Exception e) {
            ack.acknowledge();
        } finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }
}

package com.media.user.feign.client;

import com.media.user.feign.vo.IpCityResponse;
import com.media.user.feign.vo.IpCountryResponse;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.concurrent.TimeUnit;

@FeignClient(url = "${ipCountry.url}", value = "ipCountryClient", configuration = IpCountryClient.IpCountryClientConfiguration.class)
public interface IpCountryClient {

    @GetMapping(value = "/ip", headers = {"user-agent=Chrome/69.0.3497.81 Safari/537.36","accept-language=zh-CN,zh"})
    IpCountryResponse getIpCountryInfo(@RequestParam String ip);

    @GetMapping(value = "/ip/city", headers = {"user-agent=Chrome/69.0.3497.81 Safari/537.36","accept-language=zh-CN,zh"})
    IpCityResponse getIpCityInfo(@RequestParam String ip);
    
    @Configuration
    class IpCountryClientConfiguration {
        @Bean("ipCountryClientOptions")
        public Request.Options options() {
            // Set connect timeout to 50ms and read timeout to 100ms
            return new Request.Options(100, TimeUnit.MILLISECONDS, 1000, TimeUnit.MILLISECONDS, true);
        }
    }
}

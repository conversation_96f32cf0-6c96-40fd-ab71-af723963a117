package com.media.user.enums;

import lombok.Getter;

/**
 * 欢迎语
 */
@Getter
public enum WelcomeMessageEnum {
    WELCOME_MESSAGE_EN_US("en-US", "Get News, \n Share Ideas, \n & Earn Rewards!"),
    WELCOME_MESSAGE_ZH_TW("zh-TW", "在這裡獲取最新、最全的資訊，一起分享互動並獲得獎勳！"),
    WELCOME_MESSAGE_ZH_CN("zh-CN", "在这里获取最新、最全的资讯，一起分享互动并获取奖励！"),
    WELCOME_MESSAGE_FR_FR("fr-FR", "Obtenez des nouvelles, partagez des idées et gagnez des récompenses !"),
    WELCOME_MESSAGE_DE_DE("de-DE", "Holen Sie sich Nachrichten, teilen Sie Ideen und verdienen Sie Belohnungen!"),
    WELCOME_MESSAGE_RU_RU("ru-RU", "Получайте новости, делитесь идеями и зарабатывайте награды!"),
    WELCOME_MESSAGE_ES_ES("es-ES", "¡Obtén noticias, comparte ideas y gana recompensas!"),
    WELCOME_MESSAGE_JA_JP("ja-JP", "ニュースを取得し、アイデアを共有して、報酬を得よう！"),
    WELCOME_MESSAGE_TH_TH("th-TH", "รับข่าวสาร แบ่งปันไอเดีย และรับรางวัล!"),
    WELCOME_MESSAGE_VI_VN("vi-VN", "Nhận tin tức, chia sẻ ý tưởng và kiếm phần thưởng!"),
    WELCOME_MESSAGE_KO_KR("ko-KR", "뉴스 받기, 아이디어 공유, 보상 받기!"),
    WELCOME_MESSAGE_ID_ID("id-ID", "Dapatkan berita, bagikan ide, dan dapatkan hadiah!"),
    WELCOME_MESSAGE_HI_IN("hi-IN", "समाचार प्राप्त करें, विचार साझा करें, और पुरस्कार अर्जित करें!"),
    WELCOME_MESSAGE_UR_PK("ur-PK", "خبریں حاصل کریں، خیالات شیئر کریں، اور انعامات حاصل کریں!"),
    WELCOME_MESSAGE_TR_TR("tr-TR", "Haberleri al, fikirleri paylaş ve ödüller kazan!"),
    WELCOME_MESSAGE_AR_SA("ar-SA", "احصل على الأخبار، وشارك الأفكار، وحقق المكافآت!"),
    WELCOME_MESSAGE_FA_IR("fa-IR", "اخبار بگیرید، ایده\u200Cها را به اشتراک بگذارید و جوایز کسب کنید!"),
    WELCOME_MESSAGE_PT_PT("pt-PT", "Receba notícias, compartilhe ideias e ganhe recompensas!"),
    WELCOME_MESSAGE_BN_BD("bn-BD", "খবর পান, আইডিয়া শেয়ার করুন, এবং পুরস্কার জিতুন!"),
    ;

    private final String language;

    private final String content;

    WelcomeMessageEnum(String language, String content) {
        this.language = language;
        this.content = content;
    }

    public static String getContent(String language) {
        for (WelcomeMessageEnum welcomeMessageEnum : WelcomeMessageEnum.values()) {
            if (welcomeMessageEnum.getLanguage().equals(language)) {
                return welcomeMessageEnum.getContent();
            }
        }
        return "";
    }
}

package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("activity_invite_relation")
@Data
@Accessors(chain = true)
public class ActivityInviteRelationModel extends Model<ActivityInviteRelationModel> {

    @TableId(value = "relation_id", type = IdType.AUTO)
    private Long relationId;

    @TableField("email")
    private String email;

    @TableField("invite_code")
    private String inviteCode;

    @TableField("created_time")
    private Date createdTime;

    @TableField("country_code")
    private String countryCode;

    @TableField("phone_prefix")
    private String phonePrefix;

    @TableField("phone")
    private String phone;

    /**
     * UTM来源标识，用于统计邀请来源渠道
     */
    @TableField("utm_source")
    private String utmSource;


}
package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 用户关注成员响应DTO
 * 用于返回用户关注的成员信息
 */
@Data
@Accessors(chain = true)
public class UserFollowingMemberResponse {

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 个人介绍
     */
    private String personIntroduce;

    /**
     * 头像URL
     */
    private String avatarUrl;

    /**
     * 关注状态
     * 0: 未关注, 1: 已关注
     */
    private Byte followState;

    /**
     * 被关注状态
     * 0: 未被关注, 1: 已被关注
     */
    private Byte followedState;

    /**
     * 粉丝数量
     */
    private Long fansCount;

    /**
     * 创世大使挂件
     * 0: 无, 1: 有
     */
    private Integer genesisBadge;

}
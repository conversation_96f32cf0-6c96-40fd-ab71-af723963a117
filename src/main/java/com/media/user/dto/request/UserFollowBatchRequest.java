package com.media.user.dto.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true)
public class UserFollowBatchRequest {

    private List<Long> uids;

    private boolean newbieTask;

    public List<Long> getUids() {
        return uids;
    }

    public void setUids(List<Long> uids) {
        this.uids = uids;
    }

    public boolean getNewbieTask() {
        return newbieTask;
    }

    public void setNewbieTask(boolean newbieTask) {
        this.newbieTask = newbieTask;
    }
}

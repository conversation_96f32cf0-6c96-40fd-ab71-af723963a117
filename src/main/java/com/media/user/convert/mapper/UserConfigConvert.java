package com.media.user.convert.mapper;

import com.media.user.domain.UserConfigModel;
import com.media.user.dto.response.UserConfigResponse;
import com.media.user.enums.LanguageEnums;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserConfigConvert {

    UserConfigConvert INSTANCE = Mappers.getMapper(UserConfigConvert.class);

//    default UserConfigResponse model2Response(UserConfigModel userConfigModel, LanguageEnums languageEnums){
//        UserConfigResponse userConfigResponse = new UserConfigResponse()
//                .setId(userConfigModel.getId());
//        if (LanguageEnums.zh_CN.equals(languageEnums) || LanguageEnums.zh_TW.equals(languageEnums)){
//            userConfigResponse.setUserAgreement(userConfigModel.getZhUserAgreement())
//                    .setUserAgreementTitle(userConfigModel.getZhUserAgreementTitle())
//                    .setPrivacyAgreement(userConfigModel.getZhPrivacyAgreement())
//                    .setPrivacyAgreementTitle(userConfigModel.getZhPrivacyAgreementTitle())
//                    .setAboutUs(userConfigModel.getZhAboutUs());
//        }else {
//            userConfigResponse.setUserAgreement(userConfigModel.getEnUserAgreement())
//                    .setUserAgreementTitle(userConfigModel.getEnUserAgreementTitle())
//                    .setPrivacyAgreement(userConfigModel.getEnPrivacyAgreement())
//                    .setPrivacyAgreementTitle(userConfigModel.getEnPrivacyAgreementTitle())
//                    .setAboutUs(userConfigModel.getEnAboutUs());
//        }
//
//        return userConfigResponse;
//    }
}

package com.media.user.scheduler;


import com.media.user.mapper.ClientUserMapper;
import com.media.user.mapper.TwitterUserMapper;
import com.media.core.config.twitter.TwitterConfiguration;
import com.media.user.service.twitter.impl.TwitterService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Deprecated
@Slf4j
@Component
public class TwitterInfoFixScheduler {

    @Autowired
    TwitterUserMapper twitterUserMapper;

    @Autowired
    ClientUserMapper clientUserMapper;

    @Autowired
    private TwitterConfiguration twitterConfiguration;

    @Autowired
    private TwitterService twitterService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 定时修复 twitter 的信息，每5分钟跑一次，每一次更新 100 条信息
     * access token 后期改矩阵，可以用免费的 token
     */
    @XxlJob("robotTwitterInfoFix")
    public void twitterInfoFix() {
//        List<TwitterUserModel> list =  twitterUserMapper.getNeedFixInfo100();
//        log.info("need fix size: {}", list.size());
//        String accessToken = twitterConfiguration.getAccessToken();
//        log.info("accessToken: {}", accessToken);
//
//        for(TwitterUserModel twitterUserModel : list) {
//            log.info("twitterId: {}", twitterUserModel.getTwitterId());
//            try{
//                TwitterUser twitterUser = twitterService.twitterUserByTwitterId(twitterUserModel.getTwitterId(), accessToken);
//                log.info("twitterUser: {}", JSONObject.toJSON(twitterUser));
//                //获取到信息则进行更新
//                if(twitterUser != null) {
//                    twitterUserModel.setName(twitterUser.getName());
//                    twitterUserModel.setUsername(twitterUser.getUsername());
//                    twitterUserModel.setDescription(twitterUser.getDescription());
//                    twitterUserModel.setSubscriptionType(twitterUserModel.getSubscriptionType());
//                    twitterUserModel.setMostRecentTweetId(twitterUserModel.getMostRecentTweetId());
//                    twitterUserModel.setVerifiedType(twitterUserModel.getVerifiedType());
//                    twitterUserModel.setVerified(twitterUser.isVerified()?1:0);
//                    twitterUserModel.set_protected(twitterUser.is_protected()?1:0);
//                    twitterUserModel.setReceivesYourDm(twitterUser.isReceivesYourDm()?1:0);
//                    twitterUserModel.setRecommendType(0);
//                    twitterUserModel.setFixInfoType(0);
//                    twitterUserModel.setCreatedAt(twitterUser.getCreatedAt());
//                    twitterUserModel.setUpdatedTime(new Date());
//                    twitterUserModel.setOriginalImageUrl(twitterUser.getProfileImageUrl());
//                    if(twitterUser.getPublicMetrics() != null) {
//                        twitterUserModel.setFollowersCount(twitterUser.getPublicMetrics().getFollowersCount());
//                        twitterUserModel.setFollowingCount(twitterUser.getPublicMetrics().getFollowingCount());
//                        twitterUserModel.setTweetCount(twitterUser.getPublicMetrics().getTweetCount());
//                        twitterUserModel.setListedCount(twitterUser.getPublicMetrics().getListedCount());
//                        twitterUserModel.setLikeCount(twitterUser.getPublicMetrics().getLikeCount());
//                    }
//                    //上传 twitter 的头像至 s3
//                    String imageUrl = ImageUpS3Util.upload(twitterUserModel.getOriginalImageUrl());
//                    if(StringUtils.isNotBlank(imageUrl)){
//                        twitterUserModel.setProfileImageUrl(imageUrl);
//                        Long uid = (twitterUserModel.getNewUid() == null || twitterUserModel.getNewUid() == 0) ? twitterUserModel.getOldUid() : twitterUserModel.getNewUid();
//                        ClientUserModel userModel = clientUserMapper.selectById(uid);
//                        if(userModel != null && userModel.getVirtualType() == 1) {
//                            userModel.setEmail(twitterUser.getUsername());
//                            userModel.setNickName(twitterUser.getName());
//                            userModel.setAvatarUrl(twitterUserModel.getProfileImageUrl());
//                            clientUserMapper.updateById(userModel);
//
//                            //删除个人信息缓存
//                            stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + uid);
//
//                        }
//                    }
//                    twitterUserMapper.updateById(twitterUserModel);
//                }
//
//            }catch (Exception e){
//                log.error("twitterId:{}, exception:{}, fix type 9", twitterUserModel.getTwitterId(), e.getMessage());
//                twitterUserModel.setFixInfoType(9);  //标记成 9 不处理了
//                twitterUserMapper.updateById(twitterUserModel);
//            }
//
//        }

    }

}

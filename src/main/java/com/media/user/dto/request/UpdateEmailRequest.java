package com.media.user.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class UpdateEmailRequest {

    private Long uid;

//    @Size(min = 8, max = 16)
//    @NotBlank
//    private String password;

    @NotBlank
    @Size(max = 6)
    private String emailCode;

    @Email
    @NotBlank
    private String newEmail;

    @NotBlank
    @Size(max = 6)
    private String newEmailCode;
}

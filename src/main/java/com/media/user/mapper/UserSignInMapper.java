package com.media.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.UserSignInModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;


@Mapper
public interface UserSignInMapper extends BaseMapper<UserSignInModel> {

    @Select("select * from user_sign_in where uid=#{uid} and sign_date=#{signDate}")
    UserSignInModel existsByUidAndSignDate(@Param("uid") Long uid, @Param("signDate") String signDate);

    @Select("select count(1) from user_sign_in where uid=#{uid} and sign_date=#{signDate}")
    Long countSignToday(@Param("uid") Long uid, @Param("signDate") String signDate);

}
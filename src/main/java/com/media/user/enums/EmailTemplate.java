package com.media.user.enums;

import lombok.Getter;

import java.util.Set;

/**
 * 邮件
 */
@Getter
public enum EmailTemplate {
    /**
     * 验证码
     */
    VERIFICATION_CODE("verification_code_subject", "verification_code"),
    REGISTER_EMAIL("register_email_subject", "register_email_content"),
    ;

    private final String template;
    private final String subject;

    EmailTemplate(String subject, String template) {
        this.subject = subject;
        this.template = template;
    }


    public static EmailTemplate getEmailTemplate(BusinessTypeEnum businessTypeEnum) {
        Set<BusinessTypeEnum> set = Set.of(BusinessTypeEnum.FORGET_PASSWORD_CODE,
                BusinessTypeEnum.SIGNUP,
                BusinessTypeEnum.UPDATE_PASSWORD_CODE,
                BusinessTypeEnum.UPDATE_EMAIL_OLD_CODE,
                BusinessTypeEnum.UPDATE_EMAIL_NEW_CODE,
                BusinessTypeEnum.EMAIL_BIND_CODE,
                BusinessTypeEnum.EMAIL_VERIFY_CODE
        );
        if (set.contains(businessTypeEnum)){
            return VERIFICATION_CODE;
        } else if (BusinessTypeEnum.REGISTER_EMAIL.equals(businessTypeEnum)){
            return REGISTER_EMAIL;
        }
        return null;
    }
}

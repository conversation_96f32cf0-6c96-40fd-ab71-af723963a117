package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.exception.ApiException;
import com.media.core.exception.CustomizeApiException;
import com.media.core.request.ClientInfoContext;
import com.media.user.domain.ClientUserModel;
import com.media.user.dto.request.SendPhoneCodeRequest;
import com.media.user.enums.LanguageEnums;
import com.media.user.enums.PhoneVerifyCodeTypeEnum;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.service.ClientUserService;
import com.media.user.service.PhoneService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/user/api/phone")
public class PhoneSendCodeApiController {

    @Autowired
    PhoneService phoneService;

    @Autowired
    ClientUserService clientUserService;

    /**
     * 发送手机验证码
     * @param request
     */
    @PostMapping("/sendCode")
    public void sendCode(@Validated @RequestBody SendPhoneCodeRequest request){
        Long uid = null;
        PhoneVerifyCodeTypeEnum typeEnum = PhoneVerifyCodeTypeEnum.getEnumFromCode(request.getBusinessType());
        if(typeEnum == null){
            throw new ApiException(MediaUserExceptionCodeApi.BUSINESS_TYPE_NOT_EXIST);
        }

        if (PhoneVerifyCodeTypeEnum.UPDATE_PASSWORD_CODE.equals(typeEnum)
                || PhoneVerifyCodeTypeEnum.PHONE_VERIFY_CODE.equals(typeEnum)
                || PhoneVerifyCodeTypeEnum.PHONE_BIND_CODE.equals(typeEnum)){
            if (!CloudSession.isLogin()){
                throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
            }
            uid = CloudSession.getUid();
            ClientUserModel clientUserResponse = clientUserService.selectUserById(uid);
            if(!PhoneVerifyCodeTypeEnum.PHONE_BIND_CODE.equals(typeEnum) && StringUtils.isBlank(clientUserResponse.getPhone())){
                throw new ApiException(MediaUserExceptionCodeApi.PHONE_NOT_EXIST);
            }
            if(!PhoneVerifyCodeTypeEnum.PHONE_BIND_CODE.equals(typeEnum)) {
                request.setCountryCode(clientUserResponse.getCountryCode());
                request.setPhonePrefix(clientUserResponse.getPhonePrefix());
                request.setPhone(clientUserResponse.getPhone());
            }
        }
        phoneService.sendCodeByTemplate(uid, request);
    }

    @PostMapping("/checkCode")
    public void checkCode(@Validated @RequestBody SendPhoneCodeRequest request){
        if(StringUtils.isEmpty(request.getCode())){
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_CODE_ERROR);
        }
        Long uid = null;
        PhoneVerifyCodeTypeEnum typeEnum = PhoneVerifyCodeTypeEnum.getEnumFromCode(request.getBusinessType());
        if(typeEnum == null){
            throw new ApiException(MediaUserExceptionCodeApi.BUSINESS_TYPE_NOT_EXIST);
        }
        if (PhoneVerifyCodeTypeEnum.UPDATE_PASSWORD_CODE.equals(typeEnum)
                || PhoneVerifyCodeTypeEnum.PHONE_VERIFY_CODE.equals(typeEnum)
                || PhoneVerifyCodeTypeEnum.PHONE_BIND_CODE.equals(typeEnum)){
            if (!CloudSession.isLogin()){
                throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
            }
            uid = CloudSession.getUid();
            ClientUserModel clientUserResponse = clientUserService.selectUserById(uid);
            if(!PhoneVerifyCodeTypeEnum.PHONE_BIND_CODE.equals(typeEnum) && StringUtils.isBlank(clientUserResponse.getPhone())){
                throw new ApiException(MediaUserExceptionCodeApi.PHONE_NOT_EXIST);
            }
            if(!PhoneVerifyCodeTypeEnum.PHONE_BIND_CODE.equals(typeEnum)) {
                request.setCountryCode(clientUserResponse.getCountryCode());
                request.setPhonePrefix(clientUserResponse.getPhonePrefix());
                request.setPhone(clientUserResponse.getPhone());
            }
        }
        phoneService.verifyPhoneCode(uid, request.getCountryCode(), request.getPhonePrefix(), request.getPhone(), request.getBusinessType(), request.getCode());
    }


}

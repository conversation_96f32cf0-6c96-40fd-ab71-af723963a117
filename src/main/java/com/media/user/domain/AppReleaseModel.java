package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("app_release")
@Data
@Accessors(chain = true)
public class AppReleaseModel extends Model<AppReleaseModel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("en_name")
    private String enName;

    @TableField("zh_name")
    private String zhName;

    @TableField("en_title")
    private String enTitle;

    @TableField("zh_title")
    private String zhTitle;

    @TableField("version")
    private String version;

    @TableField("min_version")
    private String minVersion;

    @TableField("platform_type")
    private String platformType;

    @TableField("url")
    private String url;

    @TableField("qr_code")
    private String qrCode;

    @TableField("enable")
    private Byte enable;

    @TableField("package_size")
    private Long packageSize;

    @TableField("en_update_content")
    private String enUpdateContent;

    @TableField("zh_update_content")
    private String zhUpdateContent;

    @TableField("created_by")
    private Long createdBy;

    @TableField("updated_by")
    private Long updatedBy;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;
}
package com.media.user.enums;

import lombok.Getter;

import java.util.Set;

/**
 * 邮件
 */
@Getter
public enum PhoneVerifyTemplate {
    /**
     * 验证码
     */
    VERIFICATION_CODE("verification_code_subject", "verification_code"),
    ;

    private final String template;
    private final String subject;

    PhoneVerifyTemplate(String subject, String template) {
        this.subject = subject;
        this.template = template;
    }


    public static PhoneVerifyTemplate getTemplate(PhoneVerifyCodeTypeEnum phoneVerifyCodeTypeEnum) {
        Set<PhoneVerifyCodeTypeEnum> set = Set.of(PhoneVerifyCodeTypeEnum.FORGET_PASSWORD_CODE,
                PhoneVerifyCodeTypeEnum.SIGNUP,
                PhoneVerifyCodeTypeEnum.PHONE_LOGIN_CODE,
                PhoneVerifyCodeTypeEnum.UPDATE_PASSWORD_CODE,
                PhoneVerifyCodeTypeEnum.PHONE_BIND_CODE,
                PhoneVerifyCodeTypeEnum.PHONE_VERIFY_CODE);
        if (set.contains(phoneVerifyCodeTypeEnum)){
            return VERIFICATION_CODE;
        }
        return null;
    }
}

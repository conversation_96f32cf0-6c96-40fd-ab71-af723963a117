package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("client_user")
@Data
@Accessors(chain = true)
public class ClientUserModel extends Model<ClientUserModel> {

    @TableId(value = "uid", type = IdType.AUTO)
    private Long uid;

    @TableField("username")
    private String username;

    @TableField("email")
    private String email;

    /**
     * 是否邮箱验证(0:否;1:是- 默认为:0)
     */
    @TableField("email_verify")
    private Integer emailVerify;

    @TableField("email_verify_time")
    private Date emailVerifyTime;

    @TableField("country_code")
    private String countryCode;

    @TableField("phone_prefix")
    private String phonePrefix;

    @TableField("phone")
    private String phone;
    /**
     * 是否手机号验证(0:否;1:是- 默认为:0)
     */
    @TableField("phone_verify")
    private Integer phoneVerify;

    @TableField("phone_verify_time")
    private Date phoneVerifyTime;

    /**
     * 第一注册方式-1:邮箱;2:手机号;
     */
    @TableField("first_registration")
    private Integer firstRegistration;

    @TableField("password")
    private String password;

    //0:原始加密 1:md5加密
    @TableField("password_version")
    private Byte passwordVersion;

    @TableField("language")
    private String language;

    @TableField("ip_address")
    private String ipAddress;

    @TableField("avatar_url")
    private String avatarUrl;

    @TableField("background")
    private String background;

    @TableField("invite_code")
    private String inviteCode;

    @TableField("nick_name")
    private String nickName;

    @TableField("person_introduce")
    private String personIntroduce;

    @TableField("status")
    private Byte status;

    @TableField("invite_uid")
    private Long inviteUid;

    @TableField("type")
    private Byte type;

    /**
     * 关注度
     */
    @TableField("attention")
    private Integer attention;

    /**
     * 是否是虚拟用户(0:否;1:是- 默认为:0)
     */
    @TableField("virtual_type")
    private Byte virtualType;

    /**
     * 是新手用户(0:否;1:是- 默认为:1)
     */
    @TableField("is_newer")
    private Byte isNewer;

    /**
     * 是否推送给了推荐系统(0:否;1:是- 默认为:0)
     */
    @TableField("is_recommend")
    private Byte isRecommend;

    @TableField("source_type")
    private Byte sourceType;

    @TableField("registration_time")
    private Date registrationTime;

    @TableField("last_login_time")
    private Date lastLoginTime;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;

    @TableField("username_original")
    private String usernameOriginal;

    @TableField("nick_name_original")
    private String nickNameOriginal;

    @TableField("avatar_url_original")
    private String avatarUrlOriginal;

    /** 是否定时跑数据 ：0:不跑数;1:跑数;9:不处理 - 默认: 0 */
    @TableField("fix_image_type")
    private int fixImageType;

    //创世大使挂件 0:无;1:有
    @TableField("genesis_badge")
    private Integer genesisBadge;

    //获取创世大使挂件时间
    @TableField("genesis_badge_time")
    private Long genesisBadgeTime;

    @TableField("face_liveness_status")
    private int faceLivenessStatus;

    @TableField("face_liveness_at")
    private Date faceLivenessAt;

    @TableField("face_liveness_id")
    private String faceLivenessId;

    /**
     * 渠道ID
     */
    @TableField("channel_id")
    private Long channelId;

    /**
     * UTM来源标识
     */
    @TableField("utm_source")
    private String utmSource;

    /**
     * UTM活动标识
     */
    @TableField("utm_campaign")
    private String utmCampaign;
}

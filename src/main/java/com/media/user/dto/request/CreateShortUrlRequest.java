package com.media.user.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建短链接请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateShortUrlRequest {
    /**
     * 原始URL
     */
    @JsonProperty("url")
    private String originalUrl;
    
    /**
     * 过期时间（秒）
     */
    @JsonProperty("expires_in")
    private Integer expiresIn;
    
    /**
     * 创建者
     */
    @JsonProperty("created_by")
    private String createdBy;
}

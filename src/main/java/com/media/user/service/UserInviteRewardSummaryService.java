package com.media.user.service;

import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.common.multidata.IMultiDataAccess;
import com.media.common.multidata.RetrieveIdUtils;
import com.media.common.utils.RedisObjAccess;
import com.media.core.config.SystemConfig;
import com.media.user.domain.UserInviteRewardSummaryModel;
import com.media.user.mapper.UserInviteRewardSummaryMapper;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.media.common.utils.RedisObjAccess.of;
import static com.media.core.constant.UserRedisKey.USER_INVITE_REWARD_TOTAL;
import static java.util.concurrent.TimeUnit.DAYS;

/**
 * 用户邀请奖励汇总服务
 */
@Slf4j
@Service
public class UserInviteRewardSummaryService {

    @Autowired
    private UserInviteRewardSummaryMapper userInviteRewardSummaryMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    SystemConfig systemConfig;

    private RedisObjAccess<Long, List<UserInviteRewardSummaryModel>> inviteTotalRewardCache;

    @PostConstruct
    private void init() {
        // 邀请总奖励缓存
        inviteTotalRewardCache = of(stringRedisTemplate, USER_INVITE_REWARD_TOTAL::of, DAYS.toMillis(5), new TypeReference<>() {});
    }

    /**
     * 增加用户奖励汇总（原子操作）
     *
     * @param uid      用户ID
     * @param currency 货币类型
     * @param amount   奖励数量
     */
    @Transactional
    public void incrementRewardSummary(Long uid, String currency, Long amount) {
        if (uid == null || currency == null || amount == null || amount <= 0) {
            return;
        }
        try {
            inviteTotalRewardCache.delete(uid);
            int rows = userInviteRewardSummaryMapper.incrementRewardSummary(uid, currency, amount);
            log.debug("增加用户奖励汇总: uid={}, currency={}, amount={}, affected_rows={}", uid, currency, amount, rows);
        } catch (Exception e) {
            log.error("增加用户奖励汇总失败: uid={}, currency={}, amount={}", uid, currency, amount, e);
            throw e;
        }
    }

    public List<UserInviteRewardSummaryModel> getUserRewardSummary(Long uid) {
        return MapUtils.getObject(batchGetUserRewardSummary(Collections.singletonList(uid)), uid);
    }

    public Map<Long, List<UserInviteRewardSummaryModel>> batchGetUserRewardSummary(Collection<Long> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return Collections.emptyMap();
        }
        return RetrieveIdUtils.get(uids, inviteTotalRewardCache, IMultiDataAccess.getOnly(this::batchGetUserRewardSummaryForDb));
    }

    public Map<Long, List<UserInviteRewardSummaryModel>> batchGetUserRewardSummaryForDb(Collection<Long> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return Collections.emptyMap();
        }
        Map<String, Integer> inviteReward = systemConfig.getInviteReward();
        QueryWrapper<UserInviteRewardSummaryModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(UserInviteRewardSummaryModel::getUid, uids).gt(UserInviteRewardSummaryModel::getTotalAmount, 0);
        List<UserInviteRewardSummaryModel> allSummaries = userInviteRewardSummaryMapper.selectList(queryWrapper);
        Map<Long, List<UserInviteRewardSummaryModel>> groupedSummaries = allSummaries.stream().collect(Collectors.groupingBy(UserInviteRewardSummaryModel::getUid));
        Map<Long, List<UserInviteRewardSummaryModel>> result = new HashMap<>();
        for (Map.Entry<Long, List<UserInviteRewardSummaryModel>> entry : groupedSummaries.entrySet()) {
            Long uid = entry.getKey();
            List<UserInviteRewardSummaryModel> summaries = entry.getValue();
            List<UserInviteRewardSummaryModel> sortedSummaries = summaries.stream().sorted((s1, s2) -> {
                String c1 = s1.getCurrency();
                String c2 = s2.getCurrency();
                // 1. XME 最高优先级
                if ("XME".equals(c1) && !"XME".equals(c2)) return -1;
                if (!"XME".equals(c1) && "XME".equals(c2)) return 1;
                // 2. inviteReward 包含的币种次优先级
                boolean isInviteReward1 = inviteReward != null && inviteReward.containsKey(c1);
                boolean isInviteReward2 = inviteReward != null && inviteReward.containsKey(c2);
                if (isInviteReward1 && !isInviteReward2) return -1;
                if (!isInviteReward1 && isInviteReward2) return 1;
                // 3. 其余按金额从大到小排序
                return s2.getTotalAmount().compareTo(s1.getTotalAmount());
            }).toList();
            result.put(uid, sortedSummaries);
        }
        log.info("批量获取用户奖励汇总: uids={}, userCount={}, totalRecords={}", uids.size(), result.size(), allSummaries.size());
        return result;
    }
} 
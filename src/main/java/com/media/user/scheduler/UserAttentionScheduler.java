package com.media.user.scheduler;


import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.media.user.domain.ClientUserModel;
import com.media.user.dto.query.UserAttentionCount;
import com.media.user.enums.UserStatusEnum;
import com.media.user.mapper.ClientUserMapper;
import com.media.user.mapper.UserFollowRelationMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Deprecated
@Slf4j
@Component
public class UserAttentionScheduler {

    @Autowired
    UserFollowRelationMapper followRelationMapper;

    @Autowired
    ClientUserMapper clientUserMapper;



    /**
     * 用户关注度的计算 - 计算一天之内发生关注行为和被关注行为的用户，更新用户的粉丝人数
     */
    @XxlJob("robotCalcUserAttention")
    public void calcUserAttention() {

        //真实用户
//        List<UserAttentionCount> listNormal = followRelationMapper.listTodayNormalUser();
//        if(!listNormal.isEmpty()){
//            for(UserAttentionCount count : listNormal){
//                UpdateWrapper<ClientUserModel> updateUserWrapper = new UpdateWrapper<>();
//                updateUserWrapper.lambda()
//                        .eq(ClientUserModel::getUid, count.getUid())
//                        .eq(ClientUserModel::getStatus, UserStatusEnum.ENABLED.getStatus())
//                        .set(ClientUserModel::getAttention, count.getCount());
//                clientUserMapper.update(null, updateUserWrapper);
//            }
//        }

        //预注册用户
//        List<UserAttentionCount> listVirtual = followRelationMapper.listTodayVirtualUser();
//        if(!listVirtual.isEmpty()){
//            for(UserAttentionCount count : listVirtual){
//                UpdateWrapper<ClientUserModel> updateUserWrapper = new UpdateWrapper<>();
//                updateUserWrapper.lambda()
//                        .eq(ClientUserModel::getUid, count.getUid())
//                        .eq(ClientUserModel::getStatus, UserStatusEnum.ENABLED.getStatus())
//                        .set(ClientUserModel::getAttention, count.getCount());
//                clientUserMapper.update(null, updateUserWrapper);
//            }
//        }

    }


}

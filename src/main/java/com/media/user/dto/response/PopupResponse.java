package com.media.user.dto.response;

import lombok.Data;

import java.util.List;

@Data
public class PopupResponse {
    private List<PopupItem> popups;

    @Data
    public static class PopupItem {
        private String id;              // 弹窗ID
        private String title;           // 弹窗标题
        private String description;     // 弹窗描述
        private String content;     // 文本
        private String buttonText;     // 按钮描述
        private String backgroundImage; // 背景图片地址
        private String jumpType;        // 跳转类型: native/h5/external
        private String jumpUrl;         // 跳转链接
        private String style;         // 样式
        private Integer width;         // 宽度
        private Integer height;    //高度
        private boolean clickable;      // 是否可点击
        private Integer status;         // 弹窗状态
        private Integer order;          // 排序
        private boolean needLogin;      // 是否需要登录
        private boolean needFace;       // 是否需要人脸验证
        private boolean needVerify;       // 是否需要人脸验证
        private String scene;           // 弹窗场景
        private boolean shouldShow;     // 是否应该显示（基于显示规则计算）
        private String minVersion;      // 最低版本要求（通用）
        private String minVersionAndroid; // Android最低版本要求
        private String minVersionIos;    // iOS最低版本要求
        private DisplayRuleInfo displayRule; // 显示规则信息
        private String trackingPointId; // 埋点Id
        private String highlightColor; // 高亮颜色

        @Data
        public static class DisplayRuleInfo {
            private String type;        // 规则类型：once_per_day/once_per_week/once_per_session/always/custom
            private Integer interval;   // 间隔时间（小时），客户端用于计算显示间隔
            private Integer maxTimes;   // 最大显示次数（per day/week），客户端控制
            private String priority;    // 优先级：high/medium/low，客户端用于排序
        }
    }
}

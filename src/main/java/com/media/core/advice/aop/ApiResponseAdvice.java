package com.media.core.advice.aop;

import com.media.core.advice.config.ApiResponseProperties;
import com.media.core.exception.ExceptionCode;
import com.media.user.dto.rongCloud.RongCloudCallbackResponse;
import com.xme.xme_base_depends.models.ApiResponse;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestControllerAdvice
public class ApiResponseAdvice implements ResponseBodyAdvice<Object> {

    @Autowired
    private ApiResponseProperties properties;

    @Autowired
    private MeterRegistry meterRegistry;

    // 从Apollo配置中获取排除的错误码
    @Value("${service.availability.excluded_codes:400,404}")
    private String excludedCodesStr;

    // 从Apollo配置中获取排除的路径
    @Value("${service.availability.excluded_paths:/health,/actuator,/swagger}")
    private String excludedPathsStr;

    // 默认排除的错误码
    private List<Integer> excludedCodes;

    // 默认排除的路径
    private List<String> excludedPaths;

    @Override
    public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
        // 如果返回值类型是RongCloudCallbackResponse，则不包装
        return !returnType.getParameterType().equals(RongCloudCallbackResponse.class);
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
                                 Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                 ServerHttpRequest request, ServerHttpResponse response) {
        String type = selectedContentType.getType();
        if (!type.contains("text")) {
            // 确保配置已加载
            initExcludedConfig();

            // 获取请求路径
            String path = request.getURI().getPath();
            String method = request.getMethod().toString();

            // 如果路径在排除列表中，直接返回
            if (isPathExcluded(path)) {
                return processResponse(body, selectedContentType, request);
            }

            // 处理错误码和成功状态
            int errorCode = 0;
            boolean isSuccess = true;

            if (body instanceof ExceptionCode) {
                ExceptionCode exceptionCode = (ExceptionCode) body;
                errorCode = exceptionCode.getCode();
                isSuccess = false;

                // 记录错误码指标
                recordErrorCode(errorCode, path, method);
            } else if (body instanceof ApiResponse) {
                ApiResponse<?> apiResponse = (ApiResponse<?>) body;
                if (!apiResponse.getSuccess()) {
                    errorCode = apiResponse.getCode();
                    isSuccess = true;
                    // 记录错误码指标
                    recordErrorCode(errorCode, path, method);
                }
            }

            // 判断是否需要计入可用性
            boolean excluded = isCodeExcluded(errorCode);
            String status = (isSuccess || excluded) ? "success" : "failure";

            // 记录服务可用性指标
            recordServiceAvailability(status, errorCode, path, method);
        }

        return processResponse(body, selectedContentType, request);
    }

    /**
     * 初始化排除配置
     */
    private void initExcludedConfig() {
        // 懒加载方式，只在第一次使用时初始化
        if (excludedCodes == null) {
            excludedCodes = parseExcludedCodes(excludedCodesStr);
        }

        if (excludedPaths == null) {
            excludedPaths = parseExcludedPaths(excludedPathsStr);
        }
    }

    /**
     * 解析排除的错误码
     */
    private List<Integer> parseExcludedCodes(String codesStr) {
        if (!StringUtils.hasText(codesStr)) {
            return Arrays.asList(400, 404); // 默认值
        }

        return Arrays.stream(codesStr.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .map(Integer::parseInt)
                .collect(Collectors.toList());
    }

    /**
     * 解析排除的路径
     */
    private List<String> parseExcludedPaths(String pathsStr) {
        if (!StringUtils.hasText(pathsStr)) {
            return Arrays.asList("/health", "/actuator", "/swagger"); // 默认值
        }

        return Arrays.stream(pathsStr.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
    }

    /**
     * 处理响应体
     */
    private Object processResponse(Object body, MediaType selectedContentType, ServerHttpRequest request) {
        if (body instanceof ExceptionCode) {
            return body;
        }
        if (body instanceof ApiResponse) {
            return body;
        }
        String type = selectedContentType.getType();
        if (type.contains("text")) {
            return body;
        }
        if (request.getURI().getPath().contains("/actuator")) {
            return body;
        }
        if (request.getURI().getPath().startsWith(properties.getApiPrefix())) {
            return ApiResponse.success(body);
        }
        return body;
    }

    /**
     * 检查路径是否被排除
     */
    private boolean isPathExcluded(String path) {
        for (String excludedPath : excludedPaths) {
            if (path.startsWith(excludedPath)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查错误码是否被排除
     */
    private boolean isCodeExcluded(int code) {
        return excludedCodes.contains(code);
    }

    /**
     * 记录服务可用性指标
     */
    private void recordServiceAvailability(String status, int errorCode, String path, String method) {
        List<Tag> tags = new ArrayList<>();
        tags.add(Tag.of("status", status));
        tags.add(Tag.of("error_code", String.valueOf(errorCode)));
        tags.add(Tag.of("path", path));
        tags.add(Tag.of("method", method));

        Counter.builder("service_availability_total")
                .description("服务可用性请求总数统计")
                .tags(tags)
                .register(meterRegistry)
                .increment();
    }

    /**
     * 记录错误码指标
     */
    private void recordErrorCode(int errorCode, String path, String method) {
        List<Tag> tags = new ArrayList<>();
        tags.add(Tag.of("error_code", String.valueOf(errorCode)));
        tags.add(Tag.of("path", path));
        tags.add(Tag.of("method", method));

        Counter.builder("service_error_codes_total")
                .description("服务错误码统计")
                .tags(tags)
                .register(meterRegistry)
                .increment();
    }
}

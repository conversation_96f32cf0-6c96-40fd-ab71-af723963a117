package com.media.core.utils;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5Util {

    // 将字符串转换为MD5哈希值
    public static String getMD5(String input) {
        try {
            // 获取MD5算法实例
            MessageDigest md = MessageDigest.getInstance("MD5");

            // 执行加密
            byte[] messageDigest = md.digest(input.getBytes());

            // 将字节数组转换为十六进制表示
            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0'); // 补零
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    // 测试方法
    public static void main(String[] args) {
        String text = "HelloWorld";
        String md5Hash = getMD5(text);
        System.out.println("MD5 Hash: " + md5Hash);
    }
}
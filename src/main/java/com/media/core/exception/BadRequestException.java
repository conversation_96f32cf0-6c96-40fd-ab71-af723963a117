package com.media.core.exception;


import org.springframework.http.HttpStatus;

public class BadRequestException extends BaseException {

    public BadRequestException() {
        this(HttpStatus.BAD_REQUEST.getReasonPhrase());
    }

    public BadRequestException(String message) {
        this(HttpStatus.BAD_REQUEST.value(), message, null);
    }

    public BadRequestException(String message, Integer code) {
        this(HttpStatus.BAD_REQUEST.value(), message, code);
    }

    public BadRequestException(Integer status, String message) {
        this(status, message, null);
    }

    public BadRequestException(Integer status, String message, Integer code) {
        super(status, message, code);
    }

    public BadRequestException(Throwable cause) {
        super(cause);
        setStatus(HttpStatus.BAD_REQUEST.value());
    }

}

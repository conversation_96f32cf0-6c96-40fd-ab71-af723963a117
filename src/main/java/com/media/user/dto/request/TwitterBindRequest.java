package com.media.user.dto.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class TwitterBindRequest {

    private String plat;
    private String accessToken;
    private String refreshToken;
    private String[] scopes;
    private Date expireAt;
//    private String thumbnailImage;
//    private String name;
//    private String screenName;

}

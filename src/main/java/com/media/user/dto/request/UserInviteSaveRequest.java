package com.media.user.dto.request;

import jakarta.validation.constraints.Email;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UserInviteSaveRequest {


    private String countryCode;

    private String phonePrefix;

    private String phone;

    @Email
    private String email;

    private String inviteCode;

    /**
     * 谷歌人机校验 token
     */
    String responseToken;

    /**
     * UTM来源标识，用于统计邀请来源渠道
     */
    private String utmSource;
}
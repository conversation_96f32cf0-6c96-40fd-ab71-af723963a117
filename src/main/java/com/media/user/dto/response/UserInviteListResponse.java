package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class UserInviteListResponse {


    private Long uid;


    private String avatarUrl;


    private String nickName;

    /**
     * 邀请时间
     */
    private Long inviteTime;

    /**
     * 邀请奖励数量
     */
    private Integer inviteXmeAmount;


    /**
     * 是否是活跃用户
     */
    private Boolean active;

    /**
     * 是否已经推送过 push
     */
    private Boolean needPush;

    private Integer inviteAmountStatus;

    /**
     * 邀请并且人脸识别以后给邀请人奖励数量
     */
    private Integer inviteFaceXmeAmount;

    //活体认证状态(0未认证,1已认正)
    private Integer faceLivenessStatus;

    //创世大使挂件 0:无;1:有
    private Integer genesisBadge;


}

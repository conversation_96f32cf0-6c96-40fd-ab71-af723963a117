package com.media.user.utils;

import java.util.Random;

public class NickUtils {

        private static final String[] ADJECTIVES = {
                "Happy", "Brave", "Smart", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>",
                "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>tle", "<PERSON><PERSON><PERSON>", "<PERSON>itty", "<PERSON><PERSON>", "<PERSON>zy",
                "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>lly", "<PERSON>", "<PERSON><PERSON>", "B<PERSON>bly", "Glowing", "<PERSON>any",
                "<PERSON><PERSON><PERSON>", "<PERSON>less", "Grumpy", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
                "<PERSON>punky", "Quirky", "<PERSON><PERSON>y", "<PERSON>nazzy", "<PERSON>iddy", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
                "<PERSON><PERSON>", "<PERSON>lee<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
                "<PERSON>idy", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>iggly", "<PERSON><PERSON>", "Vivid", "<PERSON><PERSON>", "<PERSON><PERSON>",
                "<PERSON><PERSON>py", "Snuggly", "Rowdy", "Goofy"
        };

        private static final String[] ANIMALS = {
                "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>",
                "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>",
                "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
                "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
                "<PERSON>", "<PERSON><PERSON>", "<PERSON>mp", "Seahorse", "Whale", "Lizard", "Parrot",
                "Flamingo", "Raccoon", "Badger", "Beaver", "Sloth", "Stingray", "Snail",
                "Ferret", "Hedgehog", "Pelican", "Lemur", "Antelope", "Bat", "Mole",
                "Armadillo", "Goat", "Donkey", "Hamster", "Toad", "Weasel", "Camel", "Turkey"
        };

        private static final Random RANDOM = new Random();

        public static String GenerateFriendlyNickname() {
            String adjective = ADJECTIVES[RANDOM.nextInt(ADJECTIVES.length)];
            String animal = ANIMALS[RANDOM.nextInt(ANIMALS.length)];
            int number = 100 + RANDOM.nextInt(900); // 避免过小的数字，比如000、1等

            return adjective + "-" + animal + "-" + number;
        }

        // Optional: 带前缀（可支持用户类型等）
        public static String generateFriendlyNicknameWithPrefix(String prefix) {
            return prefix + "-" + GenerateFriendlyNickname();
        }

}

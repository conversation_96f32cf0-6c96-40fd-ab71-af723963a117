package com.media.core.utils.bee.push.vo.rtn;

import lombok.Data;

@Data
public class AdminMessageTemplate {

    private Integer id;
    private String name;
    private Boolean inlayFlag;
    private String[] inUrl;
    private String outUrl;
    private Boolean jumpType;
    private String browserOpenType;
    private Boolean dappFlag;
    private String messageLvType;
    private String titleZh;
    private String contentZh;
    private String titleEn;
    private String contentEn;
    private Integer useCnt;
    private Long createDate;
    private Long updateDate;
    private Boolean effect;
    private String messagePartition;
    private String messageType;
    private Integer messageSubTypeId;
    private String properties;
    private String jumpParams;

}

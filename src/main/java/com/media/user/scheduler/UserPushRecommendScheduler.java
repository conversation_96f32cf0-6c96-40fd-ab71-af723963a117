package com.media.user.scheduler;

import com.media.user.feign.client.RecommendClient;
import com.media.user.mapper.ClientUserMapper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@Component
public class UserPushRecommendScheduler {

    @Autowired
    RecommendClient recommendClient;

    @Autowired
    ClientUserMapper clientUserMapper;

    public static Boolean isPushing = false;

    /**
     * 推送未推送给推荐系统的用户
     */
    @XxlJob("robotUserPushRecommend")
    public void userPushRecommend() {
        if (isPushing.booleanValue()) {
            log.info("robotUserPushRecommend is in pushing state!");
            return;
        }
        synchronized (isPushing) {
            if (isPushing.booleanValue()) {
                log.info("robotUserPushRecommend is in pushing state!");
                return;
            }
            isPushing = true;
        }
        log.info("robotUserPushRecommend starting");
        try {
            List<Long> uids = clientUserMapper.needPushRecommend();
            if(uids != null || uids.size() > 0) {
                List<Long> updateUids = new ArrayList<>();
                for (Long uid : uids) {
                    log.info("robotUserPushRecommend pushing uid={}", uid);
                    Integer value = recommendClient.recommendSend(uid + "");
                    if(value == 1) {
                        updateUids.add(uid);
                    }
                }
                //批量修改推送状态
                if(updateUids != null && updateUids.size() > 0) {
                    clientUserMapper.modifyPushRecommend(updateUids);
                }
            }
        } catch (Exception e) {
            log.info("robotUserPushRecommend is error!  - {}", e.getMessage());
        } finally {
            isPushing = false;
        }
    }


}

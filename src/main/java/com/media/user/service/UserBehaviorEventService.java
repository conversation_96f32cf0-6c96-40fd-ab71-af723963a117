package com.media.user.service;

import com.alibaba.fastjson.JSON;
import com.media.user.enums.VerificationScenarioEnum;
import com.xme.xme_base_depends.mq.message.UserEventMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UserBehaviorEventService {

    @Autowired
    private UserInviteFaceRecordService userInviteFaceRecordService;

    public void handlerRegistered(UserEventMessage userEventMessage) {
        log.info("handlerRegistered start uid:{} event:{} ...", userEventMessage.getUserId(), JSON.toJSONString(userEventMessage));
    }

    public void handlerFaceFinish(UserEventMessage userEventMessage) {
        log.info("handlerFaceFinish start uid:{} event:{} ...", userEventMessage.getUserId(), JSON.toJSONString(userEventMessage));
        userInviteFaceRecordService.saveMultiScenarioRewardRecord(userEventMessage.getUserId(), VerificationScenarioEnum.FACE);
    }

    public void handlerPhoneFinish(UserEventMessage userEventMessage) {
        log.info("handlerPhoneFinish start uid:{} event:{} ...", userEventMessage.getUserId(), JSON.toJSONString(userEventMessage));
        userInviteFaceRecordService.saveMultiScenarioRewardRecord(userEventMessage.getUserId(),VerificationScenarioEnum.PHONE);
    }

    public void handlerEmailFinish(UserEventMessage userEventMessage) {
        log.info("handlerEmailFinish start uid:{} event:{} ...", userEventMessage.getUserId(), JSON.toJSONString(userEventMessage));
        userInviteFaceRecordService.saveMultiScenarioRewardRecord(userEventMessage.getUserId(),VerificationScenarioEnum.EMAIL);
    }
}

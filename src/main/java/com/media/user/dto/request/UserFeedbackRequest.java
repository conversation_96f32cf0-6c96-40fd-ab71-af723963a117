package com.media.user.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class UserFeedbackRequest{

    private Long uid;

    private String contract;

    private String imageList;

    @NotBlank
    @Size(max = 2000)
    private String content;

    /**
     * Feedback type:
     * 1 - General feedback
     * 2 - Appeal
     * 默认为1（普通反馈），兼容老版本
     */
    private Byte type = 1;
}

package com.media.core.constant;


import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ClientInfo {

    /**
     * app包名.
     */
    private String bundleId;

    /**
     * 设备ID.
     */
    private String deviceId;

    /**
     * 设备指纹.
     */
    private String fingerprint;


    /**
     * 设备类型.
     */
    private String deviceModel;
    /**
     * 客户端版本.
     */
    private String version;

    /**
     * 系统版本.
     */
    private String osVersion;

    /**
     * 客户端时间戳.
     */
    private String timestamp;

    /**
     * 客户端语言.
     */
    private String language;

    /**
     * 时区偏移
     */
    private String timezoneValue;

    /**
     * 商户KEY
     */
    private String brandKey;

    /**
     * 客户端类型
     */
    private String platformType;

    /**
     * 渠道
     */
    private String channel;

}

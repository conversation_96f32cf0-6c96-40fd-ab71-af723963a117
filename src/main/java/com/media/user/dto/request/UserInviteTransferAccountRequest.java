package com.media.user.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class UserInviteTransferAccountRequest {

    @NotNull
    private Long eventId;

    private Byte type;

    private String ccy;

    private String network;

    // 邀请人 uid
    private Long fromUid;

    private BigDecimal fromAmount;
    // 被邀请人 uid
    private Long toUid;

    private BigDecimal toAmount;
    /**
     * 幂等
     */
    private String idempotent;

    private Long createTime;
}

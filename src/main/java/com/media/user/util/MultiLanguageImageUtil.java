package com.media.user.util;

import com.media.user.enums.LanguageEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Map;

/**
 * 多语言图片工具类
 * 支持19种语言的图片选择逻辑
 */
@Slf4j
public class MultiLanguageImageUtil {

    /**
     * 根据语言获取对应的图片URL
     * 
     * @param imageMap 多语言图片映射
     * @param language 当前语言
     * @param defaultImage 默认图片（向后兼容）
     * @return 对应语言的图片URL
     */
    public static String getImageByLanguage(Map<String, String> imageMap, LanguageEnums language, String defaultImage) {
        if (imageMap == null || imageMap.isEmpty()) {
            return defaultImage;
        }

        if (language == null) {
            return getDefaultImageFromMap(imageMap, defaultImage);
        }

        String languageCode = language.getValue();
        
        // 1. 精确匹配当前语言
        String image = imageMap.get(languageCode);
        if (StringUtils.hasText(image)) {
            return image;
        }

        // 2. 尝试匹配语言前缀（如 zh-CN -> zh）
        String languagePrefix = languageCode.split("-")[0];
        for (Map.Entry<String, String> entry : imageMap.entrySet()) {
            if (entry.getKey().startsWith(languagePrefix + "-") && StringUtils.hasText(entry.getValue())) {
                return entry.getValue();
            }
        }

        // 3. 简化回退逻辑：中文系列回退到zh-CN，其他语言回退到en-US
        String fallbackImage = getSimpleFallbackImage(imageMap, language);
        if (StringUtils.hasText(fallbackImage)) {
            return fallbackImage;
        }

        // 4. 使用默认图片
        return getDefaultImageFromMap(imageMap, defaultImage);
    }

    /**
     * 简化的回退逻辑
     * 中文系列回退到zh-CN，其他所有语言回退到en-US
     */
    private static String getSimpleFallbackImage(Map<String, String> imageMap, LanguageEnums language) {
        // 中文系列回退到简体中文
        if (isChinese(language)) {
            return imageMap.get("zh-CN");
        }

        // 其他所有语言回退到英语
        return imageMap.get("en-US");
    }

    /**
     * 从图片映射中获取默认图片
     * 优先级：en-US > zh-CN > 第一个可用的图片
     */
    private static String getDefaultImageFromMap(Map<String, String> imageMap, String defaultImage) {
        if (imageMap == null || imageMap.isEmpty()) {
            return defaultImage;
        }

        // 优先使用英语
        String englishImage = imageMap.get("en-US");
        if (StringUtils.hasText(englishImage)) {
            return englishImage;
        }

        // 其次使用中文
        String chineseImage = imageMap.get("zh-CN");
        if (StringUtils.hasText(chineseImage)) {
            return chineseImage;
        }

        // 使用第一个可用的图片
        for (String image : imageMap.values()) {
            if (StringUtils.hasText(image)) {
                return image;
            }
        }

        return defaultImage;
    }

    /**
     * 判断是否为中文语言
     */
    private static boolean isChinese(LanguageEnums language) {
        String code = language.getValue();
        return code.startsWith("zh-");
    }



    /**
     * 获取支持的主要语言列表（用于配置参考）
     */
    public static String[] getSupportedLanguages() {
        return new String[]{
            "en-US",    // 英语（美国）
            "zh-CN",    // 中文（简体）
            "zh-TW",    // 中文（繁体）
            "ja-JP",    // 日语
            "ko-KR",    // 韩语
            "de-DE",    // 德语
            "fr-FR",    // 法语
            "es-ES",    // 西班牙语
            "it-IT",    // 意大利语
            "ru-RU",    // 俄语
            "pt-PT",    // 葡萄牙语
            "ar-SA",    // 阿拉伯语
            "hi-IN",    // 印地语
            "th-TH",    // 泰语
            "vi-VN",    // 越南语
            "tr-TR",    // 土耳其语
            "id-ID",    // 印尼语
            "fa-IR",    // 波斯语
            "bn-BD"     // 孟加拉语
        };
    }
}

package com.media.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.media.core.id.RedisIdGenerator;
import com.media.user.domain.*;
import com.media.user.dto.query.UserInviteListQuery;
import com.media.user.dto.response.ClientUserResponse;
import com.media.user.enums.InvitePushFrontEnum;
import com.media.user.mapper.*;
import com.media.user.mq.dto.XmeReceiveResponse;
import com.media.user.service.cache.ClientUserCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserInviteRelationService {

    @Autowired
    UserInviteRelationMapper userInviteRelationMapper;

    @Autowired
    UserInviteFaceRecordMapper userInviteFaceRecordMapper;

    @Autowired
    RedisIdGenerator redisIdGenerator;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Value("${user.invite.count.cache.timeout:1800}")
    private long inviteCountCacheTimeout; // 默认缓存30分钟（1800秒）

    private static final String INVITE_COUNT_CACHE_KEY_PREFIX = "user:invite:count:cache:";
    private static final String INVITE_COUNT_BATCH_LOCK_PREFIX = "user:invite:count:lock:batch:";
    private static final int LOCK_TIMEOUT = 10; // 锁超时时间（秒）


    public IPage<UserInviteRelationModel> listInvitePage(UserInviteListQuery query) {
        QueryWrapper<UserInviteRelationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("invite_uid", query.getUid());
        queryWrapper.orderByDesc("created_time");

        IPage<UserInviteRelationModel> page = new Page<>(query.getPage(), query.getSize());
        return userInviteRelationMapper.selectPage(page, queryWrapper);
    }

    public Long count(Long uid){
        QueryWrapper<UserInviteRelationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("invite_uid", uid);
        return userInviteRelationMapper.selectCount(queryWrapper);
    }

    /**
     * 保存邀请关系（兼容方法）
     * @param inviteUid 邀请人的UID
     * @param uid 被邀请人的UID
     * @param inviteType 邀请类型
     * @param inviteAmount 邀请奖励金额
     * @param inviteAmountSymbol 邀请奖励符号
     * @param amountStatus 奖励状态
     * @param idempotent 幂等标识
     * @return 邀请关系模型
     */
    public UserInviteRelationModel saveInviteRelation(Long inviteUid, Long uid, Integer inviteType, Integer inviteAmount, String inviteAmountSymbol, Integer amountStatus, String idempotent){
        return saveInviteRelation(inviteUid, uid, inviteType, null, inviteAmount, inviteAmountSymbol, amountStatus, idempotent);
    }

    /**
     * 保存邀请关系（支持活动ID）
     * @param inviteUid 邀请人的UID
     * @param uid 被邀请人的UID
     * @param inviteType 邀请类型-1:普通邀请;2:创世大使邀请;3:活动邀请;4:游戏活动邀请
     * @param activityId 活动ID（活动邀请时必填，其他情况为null）
     * @param inviteAmount 邀请奖励金额
     * @param inviteAmountSymbol 邀请奖励符号
     * @param amountStatus 奖励状态
     * @param idempotent 幂等标识
     * @return 邀请关系模型
     */
    public UserInviteRelationModel saveInviteRelation(Long inviteUid, Long uid, Integer inviteType, Long activityId, Integer inviteAmount, String inviteAmountSymbol, Integer amountStatus, String idempotent){
        try {
            UserInviteRelationModel userInviteRelationModel = new UserInviteRelationModel();
            userInviteRelationModel.setRelationId(redisIdGenerator.generate());
            userInviteRelationModel.setInviteUid(inviteUid);
            userInviteRelationModel.setInvitedUid(uid);
            userInviteRelationModel.setInviteType(inviteType);
            userInviteRelationModel.setActivityId(activityId);
            userInviteRelationModel.setInviteAmountStatus(amountStatus);
            userInviteRelationModel.setInviteAmount(inviteAmount);
            userInviteRelationModel.setInviteAmountSymbol(inviteAmountSymbol);
            userInviteRelationModel.setIdempotent(idempotent);
            userInviteRelationModel.setIsPushFront(InvitePushFrontEnum.INVITE_PUSH_FRONT_0.getStatus());
            userInviteRelationModel.setCreatedTime(new Date());
            userInviteRelationMapper.insert(userInviteRelationModel);

            return userInviteRelationModel;
        }catch (Exception e){
            log.error("save invite relation error: {}", e.getMessage());
        }
        return null;
    }

    // 查询未通知给前端的邀请消息
    public Map<String, Object> queryNoPushRation(Long uid){
        ClientUserResponse clientUserResponse = clientUserCacheService.me(uid);
        if(clientUserResponse == null){
            Map<String, Object> result = new HashMap<>();
            result.put("sum", "0XME");
            result.put("count", 0);
            return result;
        }
        Map<String, Object> countMap = userInviteRelationMapper.countNotPush(uid);
        BigDecimal sum = new BigDecimal(String.valueOf(countMap.get("sum") == null ? 0 : countMap.get("sum")));
        Integer count = Integer.parseInt(countMap.get("count").toString());
        if(count > 0){
            //将本次弹窗的数据设置成已弹窗
            userInviteRelationMapper.setPushStatus(uid, new Date());
        }
        Map<String, Object> result = new HashMap<>();
        result.put("sum", sum + "XME");
        result.put("count", count);
        return result;
    }

    // 查询未通知给前端的邀请消息
    public Map<String, Object> queryInviteRationCount(Long uid){
        ClientUserResponse clientUserResponse = clientUserCacheService.me(uid);
        if(clientUserResponse == null){
            Map<String, Object> result = new HashMap<>();
            result.put("sum", "0XME");
            result.put("count", 0);
            return result;
        }
        Map<String, Object> countMap = userInviteRelationMapper.countAll(uid);
        BigDecimal sum = new BigDecimal(String.valueOf(countMap.get("sum") == null ? 0 : countMap.get("sum")));
        Integer count = Integer.parseInt(countMap.get("count").toString());
        Map<String, Object> result = new HashMap<>();
        result.put("sum", sum + "XME");
        result.put("count", count);
        return result;
    }

    /**
     * 更新状态为已发送
     * @param xmeReceiveResponse
     */
    public void modifyStatus(XmeReceiveResponse xmeReceiveResponse){
        userInviteRelationMapper.setInviteAmountStatus(xmeReceiveResponse.getIdempotent(), new Date());
    }

    /**
     * 批量获取用户邀请人数，带缓存
     * @param uids 用户ID列表
     * @return 每个用户ID对应的邀请人数Map
     */
    public Map<Long, Long> batchCountInvites(List<Long> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return new HashMap<>();
        }

        Map<Long, Long> result = new HashMap<>();
        List<Long> needToQueryUids = new ArrayList<>();

        // 初始化结果，默认所有用户邀请人数为0
        for (Long uid : uids) {
            result.put(uid, 0L);
        }

        // 准备批量获取缓存的键
        List<String> cacheKeys = new ArrayList<>();
        Map<String, Long> keyToUidMap = new HashMap<>();
        for (Long uid : uids) {
            String cacheKey = INVITE_COUNT_CACHE_KEY_PREFIX + uid;
            cacheKeys.add(cacheKey);
            keyToUidMap.put(cacheKey, uid);
        }

        // 批量获取缓存
        List<String> cachedCounts = stringRedisTemplate.opsForValue().multiGet(cacheKeys);

        // 处理缓存结果
        if (cachedCounts != null) {
            for (int i = 0; i < cacheKeys.size(); i++) {
                String cacheKey = cacheKeys.get(i);
                String cachedCount = cachedCounts.get(i);
                Long uid = keyToUidMap.get(cacheKey);

                if (cachedCount != null) {
                    // 缓存命中，直接使用缓存的值
                    try {
                        result.put(uid, Long.valueOf(cachedCount));
                    } catch (NumberFormatException e) {
                        log.error("Invalid cached invite count for uid {}: {}", uid, cachedCount);
                        needToQueryUids.add(uid);
                    }
                } else {
                    // 缓存未命中，需要从数据库查询
                    needToQueryUids.add(uid);
                }
            }
        } else {
            // 如果批量获取失败，则所有UID都需要从数据库查询
            needToQueryUids.addAll(uids);
        }

        // 如果所有用户都命中缓存，直接返回结果
        if (needToQueryUids.isEmpty()) {
            return result;
        }

        // 获取分布式锁，防止缓存雪崩
       // String batchLockKey = INVITE_COUNT_BATCH_LOCK_PREFIX + UUID.randomUUID().toString();
       // boolean lockAcquired = false;
        try {
            /**
            lockAcquired = stringRedisTemplate.opsForValue().setIfAbsent(batchLockKey, "1", LOCK_TIMEOUT, TimeUnit.SECONDS);
            if (lockAcquired == null || !lockAcquired) {
                log.warn("Failed to acquire lock for batch invite count query, using direct DB query");
            }*/

            // 批量查询需要从数据库获取的用户邀请人数
            List<Map<String, Object>> countResults = userInviteFaceRecordMapper.batchCountInvites(needToQueryUids);

            // 处理查询结果
            Map<String, String> cacheMap = new HashMap<>();

            if (!CollectionUtils.isEmpty(countResults)) {
                for (Map<String, Object> countResult : countResults) {
                    Long uid = Long.valueOf(countResult.get("from_uid").toString());
                    Long count = Long.valueOf(countResult.get("invite_count").toString());
                    result.put(uid, count);

                    // 准备批量缓存数据
                    String cacheKey = INVITE_COUNT_CACHE_KEY_PREFIX + uid;
                    cacheMap.put(cacheKey, count.toString());
                }
            }

            // 对于没有邀请记录的用户，也将结果（0）加入批量缓存
            for (Long uid : needToQueryUids) {
                if (result.get(uid) == 0) {
                    String cacheKey = INVITE_COUNT_CACHE_KEY_PREFIX + uid;
                    cacheMap.put(cacheKey, "0");
                }
            }

            // 批量设置缓存
            if (!cacheMap.isEmpty()) {
                try {
                    // 使用批量操作设置缓存
                    stringRedisTemplate.opsForValue().multiSet(cacheMap);

                    // 为每个缓存键设置过期时间
                    for (String key : cacheMap.keySet()) {
                        stringRedisTemplate.expire(key, inviteCountCacheTimeout, TimeUnit.SECONDS);
                    }

                    log.info("Batch set {} invite count cache entries", cacheMap.size());
                } catch (Exception e) {
                    log.error("Error batch setting invite count cache: {}", e.getMessage(), e);
                }
            }
        } catch (Exception e) {
            log.error("Error batch counting invites for uids: {}, error: {}", needToQueryUids, e.getMessage(), e);
        } finally {
            // 释放锁
           /** if (lockAcquired != null && lockAcquired) {
                stringRedisTemplate.delete(batchLockKey);
            }*/
        }

        return result;
    }

    /**
     * 清除用户邀请人数缓存
     * @param uid 用户ID
     */
    public void clearInviteCountCache(Long uid) {
        if (uid == null) {
            return;
        }

        String cacheKey = INVITE_COUNT_CACHE_KEY_PREFIX + uid;
        stringRedisTemplate.delete(cacheKey);
        log.info("Cleared invite count cache for uid: {}", uid);
    }

    /**
     * 批量清除用户邀请人数缓存
     * @param uids 用户ID列表
     */
    public void batchClearInviteCountCache(List<Long> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return;
        }

        List<String> cacheKeys = new ArrayList<>();
        for (Long uid : uids) {
            if (uid != null) {
                cacheKeys.add(INVITE_COUNT_CACHE_KEY_PREFIX + uid);
            }
        }

        if (!cacheKeys.isEmpty()) {
            stringRedisTemplate.delete(cacheKeys);
            log.info("Batch cleared invite count cache for {} uids", cacheKeys.size());
        }
    }
}

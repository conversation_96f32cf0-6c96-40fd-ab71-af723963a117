package com.media.user.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.BeeUserModel;
import com.media.user.domain.ClientUserModel;
import com.media.user.dto.request.*;
import com.media.user.dto.response.AuthTokenBeeResponse;
import com.media.user.dto.response.AuthTokenResponse;
import com.media.user.enums.PasswordVersionEnum;
import com.media.user.enums.PlatformEnums;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.mapper.BeeUserModelMapper;
import com.media.user.mapper.ClientUserMapper;
import com.media.core.request.ClientInfoContext;
import com.media.core.utils.PasswordUtils;
import com.media.core.utils.bee.login.BeeUserRtn;
import com.media.core.utils.bee.login.RSAUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Map;
import java.util.Objects;

/**
 * bee 快捷登录
 */
@Slf4j
@Service
public class BeeAuthService {

    @Autowired
    ClientUserService clientUserService;

    @Autowired
    UserRegisterService userRegisterService;

    @Autowired
    BeeUserModelMapper beeUserModelMapper;

    @Autowired
    ClientUserMapper clientUserMapper;

    @Value(value = "${bee.login.privateKey}")
    private String beeLoginPrivateKey;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    PasswordUtils passwordUtils;

    @Autowired
    SmsService smsService;

    @Transactional(rollbackFor = Exception.class)
    public AuthTokenBeeResponse auth(BeeAuthRequest beeAuthRequest) {
        if(StringUtil.isBlank(beeAuthRequest.getSign())){
            throw new ApiException(MediaUserExceptionCodeApi.NO_PERMISSION);
        }

        BeeUserRtn entity = this.tokenInfo(beeAuthRequest.getSign());
        log.info("bee user pojo: {}" , entity);
        log.info("input beeUid:{}, tokenInfo email:{}", entity.getAccessToken(), entity.getEmail());
        return this.authLogin(entity, beeAuthRequest.getInviteCode());
    }

    /**
     * 登录逻辑
     * @param entity
     * @return
     */
    private AuthTokenBeeResponse authLogin(BeeUserRtn entity, String inviteCode) {
        AuthTokenBeeResponse authTokenBeeResponse = new AuthTokenBeeResponse();
        authTokenBeeResponse.setIsSetEmail(0);

        //检查subId是否存在用户
        QueryWrapper<BeeUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bee_uid", entity.getAccessToken());
        BeeUserModel queryExist = beeUserModelMapper.selectOne(queryWrapper);
        //如果不为空则查询用户进行登录
        if (queryExist != null) {
            log.info("this bee user [{}-{}-{}] already exist, now login.", queryExist.getBeeUid(), queryExist.getUid(), queryExist.getEmail());
            UserLoginRequest request = new UserLoginRequest();
            request.setEmail(queryExist.getEmail());
            AuthTokenResponse authTokenResponse =  clientUserService.loginNoPass(request, "bee", PlatformEnums.get(ClientInfoContext.get().getPlatformType()).getCode());
            BeanUtils.copyProperties(authTokenResponse, authTokenBeeResponse);
            //如果是自定义邮箱，则需要设置邮箱
            if(queryExist.getEmail().startsWith("BEE_") && queryExist.getEmail().endsWith("@bee.cn")){
                authTokenBeeResponse.setIsSetEmail(1);
            }
            return authTokenBeeResponse;
        }

        //传入的邮箱为空
        if(StringUtil.isEmpty(entity.getEmail())){
            entity.setEmail("BEE_" + entity.getAccessToken() + "@bee.cn");
        }else{
            log.info("this bee user [{}-{}] not exist，need register", entity.getAccessToken(), entity.getEmail());
            //如果不存在，则检查邮箱对应的用户是否存在，如果不存在则注册，如果存在则报错
            clientUserService.checkEmail(entity.getEmail());
        }

        log.info("this bee user [{}-{}] not exist，check email passed，keep register", entity.getAccessToken(), entity.getEmail());
        //创建三方用户,然后注册client用户，然后返回登录信息
        ClientUserRequest request = new ClientUserRequest();
        request.setSourceType(PlatformEnums.get(ClientInfoContext.get().getPlatformType()).getCode());
        request.setEmail(entity.getEmail());
        request.setInviteCode(inviteCode);
        request.setNickName(entity.getNickname());
        request.setUsername("bee_" + entity.getAccessToken());
        request.setPassword(System.currentTimeMillis()+"");//随机生成一个密码
        AuthTokenResponse authTokenResponse = userRegisterService.register(request);

        log.info("this bee user [{}-{}] not exist，finish register，uid={}", entity.getAccessToken(), entity.getEmail(), authTokenResponse.getUserId());

        //从token解析 Uid
        try {
            Date now = new Date();
            BeeUserModel beeUserModel = new BeeUserModel();
            beeUserModel.setBeeUid(entity.getAccessToken());
            beeUserModel.setUid(authTokenResponse.getUserId());
            beeUserModel.setEmail(entity.getEmail());
            beeUserModel.setNickName(entity.getNickname());
            beeUserModel.setAddress(entity.getAddress());
            beeUserModel.setAppId(entity.getAppId());
            beeUserModel.setClientInfo(entity.getClientInfo());
            beeUserModel.setCreatedTime(now);
            beeUserModelMapper.insert(beeUserModel);
            log.info("this bee user [{}-{}] not exist，finish register，and save bee user data.", entity.getAccessToken(), entity.getEmail());
        }catch (Exception e){
            log.error("save bee user error:{}-{} - {}", entity.getAccessToken(), entity.getEmail(), e.getMessage());
        }

        BeanUtils.copyProperties(authTokenResponse, authTokenBeeResponse);
        //如果是自定义邮箱，则需要设置邮箱
        if(request.getEmail().startsWith("BEE_") && request.getEmail().endsWith("@bee.cn")){
            authTokenBeeResponse.setIsSetEmail(1);
        }
        return authTokenBeeResponse;
    }

    /**
     * 根据签名获取用户信息
     * @param rsaSign
     * @return
     */
    private BeeUserRtn tokenInfo(String rsaSign){
        try {
            String d = RSAUtils.decryptDataOnJava(rsaSign, beeLoginPrivateKey);
            log.info("data decrypted by private key: {}",d);
            Map<String, Object> dataFormSignature = RSAUtils.getDataFormSignature(d);
            log.info("data decrypted and recovered: {}",dataFormSignature);
            return JSONObject.parseObject(JSONObject.toJSONString(dataFormSignature), BeeUserRtn.class);
        }catch (Exception e){
            log.error("bee.auth token info error:{}", e);
            throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
        }
    }




    /**
     * bee 更新个人邮箱
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBeeEmail(UpdateBeeEmailRequest request){
        BeeUserRtn entity = this.tokenInfo(request.getSign());

        QueryWrapper<BeeUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bee_uid", entity.getAccessToken());
        BeeUserModel queryExist = beeUserModelMapper.selectOne(queryWrapper);
        if(queryExist == null){
            throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
        }

        if(!Objects.equals(queryExist.getUid(), request.getUid())){
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        ClientUserModel clientUserModel = clientUserMapper.selectById(request.getUid());
        if(clientUserModel == null){
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        //检查填入的邮箱是否存在
        clientUserService.checkEmail(request.getEmail());

        // TODO 暂时先不验证新邮箱
//        smsService.verifyEmailCode(request.getNewEmail(), BusinessTypeEnum.UPDATE_EMAIL_NEW_CODE, request.getNewEmailCode());


        // 验证密码
//        boolean result = passwordUtils.checkPassword(request.getPassword(), clientUserModel.getPassword());
//        if (!result){
//            throw new ApiException(MediaUserExceptionCodeApi.OLD_PASSWORD_ERROR);
//        }
        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ClientUserModel::getUid, request.getUid())
                .set(ClientUserModel::getEmail, request.getEmail())
                .set(ClientUserModel::getPassword, passwordUtils.bcryptHash(request.getPassword(), PasswordVersionEnum.PASSWORD_VERSION_1.getState()))
                .set(ClientUserModel::getUpdatedTime, new Date());
        clientUserMapper.update(updateWrapper);

        //修改 bee 邮箱
        queryExist.setEmail(request.getEmail());
        beeUserModelMapper.updateById(queryExist);

        //标记验证码失效
//        smsService.signEmailCodeExpire(clientUserModel.getEmail(), BusinessTypeEnum.UPDATE_EMAIL_OLD_CODE);
//        smsService.signEmailCodeExpire(request.getEmail(), BusinessTypeEnum.UPDATE_EMAIL_NEW_CODE);

        //发送注册成功邮件 - 异步推送
        smsService.sendRegisterMessage(request.getEmail(), queryExist.getNickName(), ClientInfoContext.getLanguage());

        //删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + request.getUid());

    }



}

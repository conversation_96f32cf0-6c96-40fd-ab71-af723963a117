package com.media.user.dto.request;

import jakarta.validation.constraints.Email;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ActivityReservationRequest {

    private String countryCode;

    private String phonePrefix;

    private String phone;

    @Email
    private String email;

    /**
     * 活动类型，可以用于区分不同活动
     */
    private String activityType;

    /**
     * 谷歌人机校验 token
     */
    private String responseToken;
}

-- 定期同步用户邀请奖励汇总表数据
-- 建议在低峰期（如凌晨）执行，确保数据一致性

-- 创建临时表来存放重新计算的汇总数据
CREATE TEMPORARY TABLE temp_reward_summary AS
SELECT 
    from_uid as uid,
    from_amount_symbol as currency,
    SUM(from_amount) as total_amount,
    COUNT(1) as total_count,
    MAX(updated_time) as last_updated_time,
    MIN(created_time) as created_time
FROM user_invite_face_record 
WHERE from_uid IS NOT NULL 
    AND from_amount IS NOT NULL 
    AND from_amount > 0
    AND from_amount_symbol IS NOT NULL
    AND amount_status = 2
GROUP BY from_uid, from_amount_symbol

UNION ALL

SELECT 
    from_uid as uid,
    from_amount_symbol_2 as currency,
    SUM(from_amount_2) as total_amount,
    COUNT(1) as total_count,
    MAX(updated_time) as last_updated_time,
    MIN(created_time) as created_time
FROM user_invite_face_record 
WHERE from_uid IS NOT NULL 
    AND from_amount_2 IS NOT NULL 
    AND from_amount_2 > 0
    AND from_amount_symbol_2 IS NOT NULL
    AND amount_status = 2
GROUP BY from_uid, from_amount_symbol_2;

-- 合并相同用户和货币的记录
CREATE TEMPORARY TABLE temp_reward_summary_final AS
SELECT 
    uid,
    currency,
    SUM(total_amount) as total_amount,
    SUM(total_count) as total_count,
    MAX(last_updated_time) as last_updated_time,
    MIN(created_time) as created_time
FROM temp_reward_summary
GROUP BY uid, currency;

-- 比较并找出差异
SELECT 
    '数据差异检查' as check_type,
    COUNT(*) as diff_count
FROM (
    -- 在临时表中但不在汇总表中的记录
    SELECT uid, currency FROM temp_reward_summary_final
    WHERE NOT EXISTS (
        SELECT 1 FROM user_invite_reward_summary urs 
        WHERE urs.uid = temp_reward_summary_final.uid 
        AND urs.currency = temp_reward_summary_final.currency
    )
    
    UNION ALL
    
    -- 在汇总表中但不在临时表中的记录
    SELECT uid, currency FROM user_invite_reward_summary
    WHERE NOT EXISTS (
        SELECT 1 FROM temp_reward_summary_final trsf
        WHERE trsf.uid = user_invite_reward_summary.uid 
        AND trsf.currency = user_invite_reward_summary.currency
    )
    
    UNION ALL
    
    -- 数量不一致的记录
    SELECT urs.uid, urs.currency 
    FROM user_invite_reward_summary urs
    JOIN temp_reward_summary_final trsf ON urs.uid = trsf.uid AND urs.currency = trsf.currency
    WHERE urs.total_amount != trsf.total_amount OR urs.total_count != trsf.total_count
) as diff_data;

-- 如果需要修复差异，可以执行以下语句：
-- 1. 删除汇总表中的所有数据
-- DELETE FROM user_invite_reward_summary;

-- 2. 从临时表重新插入数据
-- INSERT INTO user_invite_reward_summary (uid, currency, total_amount, total_count, last_updated_time, created_time)
-- SELECT uid, currency, total_amount, total_count, last_updated_time, created_time
-- FROM temp_reward_summary_final;

-- 同步完成后的验证查询
SELECT 
    '同步后验证' as check_type,
    COUNT(DISTINCT uid) as unique_users,
    COUNT(DISTINCT currency) as unique_currencies,
    COUNT(*) as total_records,
    SUM(total_amount) as total_rewards
FROM user_invite_reward_summary;

-- 清理临时表
DROP TEMPORARY TABLE temp_reward_summary;
DROP TEMPORARY TABLE temp_reward_summary_final; 
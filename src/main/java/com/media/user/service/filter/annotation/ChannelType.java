package com.media.user.service.filter.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 频道类型注解
 * 用于标识 Filter 适用的频道类型
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ChannelType {
    
    /**
     * 适用的频道类型数组
     * 支持多个频道类型
     */
    String[] value();
    
    /**
     * 是否启用该 Filter
     * 默认为 true
     */
    boolean enabled() default true;
} 
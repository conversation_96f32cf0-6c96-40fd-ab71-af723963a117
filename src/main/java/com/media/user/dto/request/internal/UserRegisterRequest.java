package com.media.user.dto.request.internal;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UserRegisterRequest {

    /**
     * 1:twitter; 9:other
     */
    private Integer thirdPlatType;

    private String twitterId;

    private String nickName;

    private String userName;

    private String avatarUrl;

    private String description;

}
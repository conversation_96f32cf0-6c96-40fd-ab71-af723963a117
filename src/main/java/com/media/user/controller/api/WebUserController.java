package com.media.user.controller.api;

import com.media.core.exception.ApiException;
import com.media.core.request.ClientInfoContext;
import com.media.user.constant.MediaUserConstant;
import com.media.user.dto.request.ClientUserRequest;
import com.media.user.dto.request.WebSendCodeRequest;
import com.media.user.dto.request.SendPhoneCodeRequest;
import com.media.user.dto.response.AuthTokenResponse;
import com.media.user.enums.BusinessTypeEnum;
import com.media.user.enums.PlatformEnums;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.service.BlockPuzzleCaptchaService;
import com.media.user.service.PhoneService;
import com.media.user.service.SmsService;
import com.media.user.service.UserRegisterService;
import com.media.user.service.UserSecretService;
import com.media.user.utils.NickUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/user/api/web")
public class WebUserController {

    @Autowired
    private UserRegisterService userRegisterService;

    @Autowired
    private PhoneService phoneService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private BlockPuzzleCaptchaService blockPuzzleCaptchaService;

    @Value("${register.captcha.web.enabled:true}")
    private boolean captchaEnabled;

    /**
     * web 注册入口,必定是web注册入口
     */
    @PostMapping("/register")
    public AuthTokenResponse webRegister(@Validated @RequestBody ClientUserRequest request, HttpServletRequest httpRequest) {
        // 自动生成英文友好昵称（零依赖）
        if (StringUtils.isBlank(request.getNickName())) {
            request.setNickName(NickUtils.GenerateFriendlyNickname());
        }

        request.setSourceType(PlatformEnums.WEB.getCode());

        // 使用新的完整注册流程
        UserRegisterService.RegisterResult result = userRegisterService.registerWithVerification(request);
        return result.getAuthTokenResponse();
    }

    /**
     * web注册场景验证码发送
     */
    @PostMapping("/sendCode")
    public void webSendCode(@RequestBody WebSendCodeRequest request) {
        // 可选：人机校验
        if (captchaEnabled) {
            blockPuzzleCaptchaService.verification(request.getResponseToken());
        }

        if ("email".equalsIgnoreCase(request.getType())) {
            if (StringUtils.isBlank(request.getEmail())) {
                throw new ApiException(MediaUserExceptionCodeApi.EMAIL_NOT_EXIST);
            }
            smsService.sendEmailCode(null, request.getEmail(), BusinessTypeEnum.getEnumFromCode(request.getBusinessType()), ClientInfoContext.getLanguage());
        } else if ("phone".equalsIgnoreCase(request.getType())) {
            if (StringUtils.isBlank(request.getCountryCode()) || StringUtils.isBlank(request.getPhonePrefix()) || StringUtils.isBlank(request.getPhone())) {
                throw new ApiException(MediaUserExceptionCodeApi.PHONE_NOT_EXIST);
            }
            SendPhoneCodeRequest phoneCodeRequest = new SendPhoneCodeRequest()
                    .setCountryCode(request.getCountryCode())
                    .setPhonePrefix(request.getPhonePrefix())
                    .setPhone(request.getPhone())
                    .setBusinessType(BusinessTypeEnum.getEnumFromCode(request.getBusinessType()).getValue());
            phoneService.sendCodeByTemplate(null, phoneCodeRequest);
        } else {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
    }
}

package com.media.user.utils;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.util.StringUtils;

import java.util.Objects;

public class CallbackSignatureVerifier {

    /**
     * 验证回调签名
     * @param appSecret 应用密钥
     * @param nonce 随机数
     * @param timestamp 时间戳
     * @param signature 待验证的签名
     * @return 验证结果
     */
    public static boolean verifySignature(String appSecret, String nonce, String timestamp, String signature) {
        // 参数校验
        if (!StringUtils.hasText(appSecret)){
            throw new IllegalArgumentException("AppSecret cannot be empty");
        }
        if (!StringUtils.hasText(nonce) || nonce.length() > 18)
            throw new IllegalArgumentException("Nonce is invalid");
        if (!StringUtils.hasText(timestamp))
            throw new IllegalArgumentException("Timestamp cannot be empty");
        if (!StringUtils.hasText(signature))
            throw new IllegalArgumentException("Signature cannot be empty");

        // 生成本地签名
        String localSignature = generateSignature(appSecret, nonce, timestamp);

        // 比较签名
        return Objects.equals(localSignature, signature);
    }

    /**
     * 生成签名
     * @param appSecret 应用密钥
     * @param nonce 随机数
     * @param timestamp 时间戳
     * @return SHA1签名
     */
    public static String generateSignature(String appSecret, String nonce, String timestamp) {
        String concatenated = appSecret + nonce + timestamp;
        return DigestUtils.sha1Hex(concatenated);
    }
}
package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("share_content")
@Data
@Accessors(chain = true)
public class ShareContentModel extends Model<ShareContentModel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 文案语言(zh-CN:中文,en-US:英文)
     */
    @TableField("content_language")
    private String contentLanguage;

    /**
     * 文案语言(A,B,C)
     */
    @TableField("content_type")
    private String contentType;

    /**
     * 文案内容
     */
    @TableField("content_detail")
    private String contentDetail;

    /**
     * 文案状态，0：不可用，1：可用
     */
    @TableField("status")
    private Byte status;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;

    @TableField("created_by")
    private Long createdBy;

    @TableField("updated_by")
    private Long updatedBy;

}
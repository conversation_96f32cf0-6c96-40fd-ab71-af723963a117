package com.media.user.controller.internal;


import com.media.user.service.ClientUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/user/internal/early/")
public class EarlyBirdInternalController {

    @Autowired
    private ClientUserService clientUserService;

    @PostMapping("bird")
    public Boolean bird() {
        return clientUserService.earlyBirdSwitch();
    }
}

package com.media.user.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.function.Function.identity;

@Getter
public enum ImExceptionEnums {

    SUCCESS(200),// 发送成功
    FAIL(10001),//发送失败
    DAILY_MESSAGE_LIMIT_PER_USER(10002),//今日对此非互关用户发送消息已达上限(最多3条)
    DAILY_RECEIVER_LIMIT(10003), //已达到今日非互关用户消息限制(最多10人)
    SPECIAL_SCENARIO_BLOCKED(10004);//特殊场景不发送消息

    private final Integer code;

    ImExceptionEnums(Integer code) {
        this.code = code;
    }

    private static final Map<Integer, ImExceptionEnums> MAP = Arrays.stream(values()).collect(Collectors.toMap(ImExceptionEnums::getCode, identity()));

    public static ImExceptionEnums form(Integer code) {
        return MAP.get(code);
    }
}

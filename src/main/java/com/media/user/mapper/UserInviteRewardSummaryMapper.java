package com.media.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.UserInviteRewardSummaryModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * 用户邀请奖励汇总 Mapper
 */
@Mapper
public interface UserInviteRewardSummaryMapper extends BaseMapper<UserInviteRewardSummaryModel> {

    /**
     * 原子性增加用户某货币的奖励汇总
     * @param uid 用户ID
     * @param currency 货币类型
     * @param amount 增加的数量
     * @return 影响行数
     */
    @Update("INSERT INTO user_invite_reward_summary (uid, currency, total_amount, total_count, last_updated_time, created_time) " +
            "VALUES (#{uid}, #{currency}, #{amount}, 1, NOW(), NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "total_amount = total_amount + #{amount}, " +
            "total_count = total_count + 1, " +
            "last_updated_time = NOW()")
    int incrementRewardSummary(@Param("uid") Long uid, 
                              @Param("currency") String currency, 
                              @Param("amount") Long amount);
} 
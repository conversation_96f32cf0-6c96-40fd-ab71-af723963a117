package com.media.core.utils;

import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 生成唯一的用户 username
 */
public class UsernameGenerator {

    private static final AtomicLong COUNTER = new AtomicLong(System.currentTimeMillis());

    // 英文字符集，用于生成随机字符
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    public static String generateUniqueUsername() {
        long timestamp = COUNTER.incrementAndGet();  // 线程安全的时间戳
        String randomPart = generateRandomString(6);  // 生成6个随机字符

        return randomPart + "_" + timestamp;  // 时间戳 + 随机字符部分，确保唯一性
    }

    // 生成指定长度的随机字符串
    public static String generateRandomString(int length) {
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            int index = random.nextInt(CHARACTERS.length());
            sb.append(CHARACTERS.charAt(index));
        }

        return sb.toString();
    }

}

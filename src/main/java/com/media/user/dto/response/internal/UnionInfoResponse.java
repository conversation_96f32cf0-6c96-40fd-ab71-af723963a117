package com.media.user.dto.response.internal;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UnionInfoResponse implements Serializable {
    /**
     * 联盟ID，唯一主键
     */
    private Long id;
    /**
     * 联盟名称
     */
    private String name;
    /**
     * 联盟口号
     */
    private String slogan;
    /**
     * 联盟邀请码
     */
    private String inviteCode;
    /**
     * 创建人用户ID
     */
    private Long creatorId;
    /**
     * 联盟用户数
     */
    private Integer userNum;
    /**
     * 任务状态，-2：自动解散，-1：审核不通过，0：审核中，1：人数不足，2：组建成功
     */
    private Byte status;
    /**
     * 审核反馈结果
     */
    private String auditFeedback;
    /**
     * 记录创建时间
     */
    private Date createTime;
    /**
     * 记录更新时间
     */
    private Date updateTime;
} 
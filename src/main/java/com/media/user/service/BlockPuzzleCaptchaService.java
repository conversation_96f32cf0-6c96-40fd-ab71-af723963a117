
package com.media.user.service;

import com.media.core.exception.ApiException;
import com.media.user.exception.MediaCaptchaExceptionCodeApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BlockPuzzleCaptchaService{

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    protected static String REDIS_SECOND_CAPTCHA_KEY = "RUNNING:CAPTCHA:second-%s";

    public void verification(String captchaVerification) {
        log.info("captchaVerification:{}", captchaVerification);
        if(StringUtils.isBlank(captchaVerification)) {
            throw new ApiException(MediaCaptchaExceptionCodeApi.API_CAPTCHA_INVALID);
        }
        try {
            String codeKey = String.format(REDIS_SECOND_CAPTCHA_KEY, captchaVerification);
            if (!stringRedisTemplate.hasKey(codeKey)) {
                throw new ApiException(MediaCaptchaExceptionCodeApi.API_CAPTCHA_INVALID);
            }
            //二次校验取值后，即刻失效
            stringRedisTemplate.delete(codeKey);
        } catch (Exception e) {
            log.error("验证码坐标解析失败", e);
            throw new ApiException(MediaCaptchaExceptionCodeApi.API_CAPTCHA_INVALID);
        }
    }



}

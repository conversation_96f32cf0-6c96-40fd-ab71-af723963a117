package com.media.user.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.media.user.constant.MediaUserConstant;
import com.media.user.convert.mapper.UserDevicePushConvert;
import com.media.user.domain.*;
import com.media.user.dto.request.DeviceInfoRequest;
import com.media.user.dto.response.ClientUserResponse;
import com.media.user.dto.response.UserPinInfoResponse;
import com.media.user.enums.DeletedEnum;
import com.media.user.enums.LanguageEnums;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.core.id.RedisIdGenerator;
import com.media.user.mapper.UserDevicePushMapper;
import com.media.user.mapper.UserPinConfigMapper;
import com.media.user.mapper.UserPinMessageMapper;
import com.media.user.mapper.UserPinTimesMapper;
import com.media.user.service.cache.ClientUserCacheService;
import com.media.core.utils.bee.push.BeeClient;
import com.media.core.utils.bee.push.vo.param.OriginalPushParam;
import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import io.github.engagelab.api.PushApi;
import io.github.engagelab.bean.push.PushParam;
import io.github.engagelab.bean.push.PushResult;
import io.github.engagelab.bean.push.message.notification.NotificationMessage;
import io.github.engagelab.bean.push.to.To;
import io.github.engagelab.enums.Platform;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toMap;

@Service
@Slf4j
public class UserDevicePushService {

    @Autowired
    private UserDevicePushMapper userDevicePushMapper;

    @Autowired
    private RedisIdGenerator redisIdGenerator;

    @Autowired
    private PushApi pushApi;

    @Autowired
    private UserPinTimesMapper userPinTimesMapper;

    @Autowired
    private UserPinConfigMapper userPinConfigMapper;

    @Autowired
    private UserPinMessageMapper userPinMessageMapper;

    @Lazy
    @Autowired
    private ClientUserService clientUserService;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @Autowired
    BeeClient beeClient;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Transactional(rollbackFor = Exception.class)
    public void userPushBatch(Long from, List<Long> uids){
        ClientUserResponse clientUserResponse =  clientUserCacheService.me(from);
        if(clientUserResponse == null){
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        UserPinInfoResponse userPinInfoResponse = getUserPinInfo(from);
        if (userPinInfoResponse.getTimes() >= userPinInfoResponse.getMaxTimes()){
            throw new ApiException(MediaUserExceptionCodeApi.USER_PINS_USED);
        }

        List<UserPinMessageModel> listPinMessage = listUserPinMessage();
        if (listPinMessage == null || listPinMessage.isEmpty()){
            return;
        }

        Map<String, UserPinMessageModel> mapListPinMessage = listPinMessage.stream().collect(toMap(UserPinMessageModel::getLanguage, pinMessage -> pinMessage));

        //如果推送过了 就不推送了
        List<Long> needPushUids = new ArrayList<>();
        for(Long uid : uids){
            String cacheKey = MediaUserConstant.USER_PIN_STATUS + uid;
            String value = stringRedisTemplate.opsForValue().get(cacheKey);
            if (StringUtils.isNotBlank(value)) {
                continue;
            }
            needPushUids.add(uid);
        }

        if (needPushUids.isEmpty()){
            return;
        }

        //get select users
        List<ClientUserResponse> listUser = clientUserService.selectListUserByIds(needPushUids);
        Map<String, List<ClientUserResponse>> mapListUser = listUser.stream().collect(Collectors.groupingBy(ClientUserResponse::getLanguage));

        //按照语言设置需要推送的 registrationId
        Map<String, List<String>> mapRegistrationId = new HashMap<>();
        List<String> registrationIdList = null;
        for(String language : mapListUser.keySet()){
            registrationIdList = new ArrayList<>();
            List<ClientUserResponse> users = mapListUser.get(language);
            List<Long> mapUserIds = users.stream().map(ClientUserResponse::getUid).toList();
            registrationIdList.addAll(mapUserIds.stream().map(String::valueOf).collect(Collectors.toList()));
            mapRegistrationId.put(language, registrationIdList);
        }
        BigDecimal amount = new BigDecimal("600");

        //发送 push
        for(String language : mapRegistrationId.keySet()){
            UserPinMessageModel userPinMessageModel = mapListPinMessage.get(language);
            //默认搞英文
            if(userPinMessageModel == null){
                userPinMessageModel = mapListPinMessage.get(LanguageEnums.en_US.getValue());
            }
            String content = buildSubject(userPinMessageModel.getContent(),Map.of("nickName", clientUserResponse.getNickName(), "amount", amount) );

            log.info("registrationId:{}, language:{}, title:{}, content:{}", mapRegistrationId.get(language),language, userPinMessageModel.getTitle(), content);
            //极光推送
            //pushMessage(mapRegistrationId.get(language), userPinMessageModel.getTitle(), content);

            //bee 推送
            beePushMessage(mapRegistrationId.get(language), userPinMessageModel.getTitle(), content);

            //创建 pin 的推送缓存
            for(String uid : mapRegistrationId.get(language)){
                String cacheKey = MediaUserConstant.USER_PIN_STATUS + uid;
                stringRedisTemplate.opsForValue().set(cacheKey, "1", 5, TimeUnit.HOURS);
            }

        }

        updateUserPinTimes(from);
    }

    private void beePushMessage(List<String> registrationIdList, String title, String content){
        if(registrationIdList != null && registrationIdList.size() > 0){
            //将 list 按照 500 分组
            List<List<String>> groupedLists = splitList(registrationIdList, 500);
            for(List<String> userIds : groupedLists){
                OriginalPushParam pushParam = new OriginalPushParam();
                pushParam.setUserId(userIds);
                pushParam.setTitle(title);
                pushParam.setMessage(content);
                beeClient.originalPush(pushParam);
            }
        }

    }


    /**
     * 将一个 List 按照指定大小进行分组
     */
    private List<List<String>> splitList(List<String> originalList, int groupSize) {
        List<List<String>> groupedLists = new ArrayList<>();
        int totalGroups = (int) Math.ceil((double) originalList.size() / groupSize);
        for (int i = 0; i < totalGroups; i++) {
            int start = i * groupSize;
            int end = Math.min(start + groupSize, originalList.size());
            List<String> group = new ArrayList<>(originalList.subList(start, end));
            groupedLists.add(group);
        }
        return groupedLists;
    }


    //极光推送
    private void pushMessage(List<String> registrationIdList, String title, String content){
        PushParam param = new PushParam();
        PushParam.Body body = new PushParam.Body();
        // 通知内容
        NotificationMessage.Android android = new NotificationMessage.Android();
        android.setAlert(content);
        android.setTitle(title);


        NotificationMessage.IOS ios = new NotificationMessage.IOS();
        ios.setAlert(content);


        NotificationMessage notificationMessage = new NotificationMessage();
        notificationMessage.setAlert(content);
        notificationMessage.setAndroid(android);
        notificationMessage.setIos(ios);

        body.setNotification(notificationMessage);


        // 目标人群
        To to = new To();
        to.setRegistrationIdList(registrationIdList);
        // 指定目标
        param.setTo(to);
        // 或者发送所有人
//            param.setTo(ApiConstants.To.ALL);

        // 指定平台
        body.setPlatform(Arrays.asList(Platform.android, Platform.ios));
        // 或者发送所有平台
        // param.setPlatform(ApiConstants.Platform.ALL);

        // 发送
        param.setBody(body);

        log.info("UserDevicePushService.userPushBatch param:{}", JSON.toJSONString(param));
        PushResult pushResult = pushApi.push(param);
        log.info("UserDevicePushService.userPushBatch pushResult:{}", pushResult);
    }


    public List<UserPinMessageModel> listUserPinMessage(){
        QueryWrapper<UserPinMessageModel> queryWrapper = new QueryWrapper<>();
        return userPinMessageMapper.selectList(queryWrapper);
    }

    public UserPinMessageModel getUserPinMessage(){
        QueryWrapper<UserPinMessageModel> queryWrapper = new QueryWrapper<>();
        List<UserPinMessageModel> userPinMessageModels = userPinMessageMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(userPinMessageModels)){
            return null;
        }
        int index = RandomUtils.nextInt(0, userPinMessageModels.size()-1);
        return userPinMessageModels.get(index);
    }

    @Transactional(rollbackFor = Exception.class)
    public void  deviceBind(DeviceInfoRequest request){
        QueryWrapper<UserDevicePushModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", request.getUid());
        queryWrapper.eq("registration_id", request.getRegistrationId());
        queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
        boolean exist = userDevicePushMapper.exists(queryWrapper);
        if (exist){
            return;
        }
        UserDevicePushModel userDevicePushModel = UserDevicePushConvert.INSTANCE.req2Model(request);
        userDevicePushModel.setId(redisIdGenerator.generate());
        userDevicePushModel.setCreatedTime(new Date());
        userDevicePushMapper.insert(userDevicePushModel);
    }

    public UserPinInfoResponse getUserPinInfo(Long uid){
        QueryWrapper<UserPinConfigModel> queryWrapper = new QueryWrapper<>();
        UserPinConfigModel userPinConfigModel = userPinConfigMapper.selectOne(queryWrapper);
        UserPinInfoResponse userPinInfoResponse = new UserPinInfoResponse()
                .setUid(uid)
                .setMaxTimes(userPinConfigModel.getMaxTimes());
        String dateStr = DateFormatUtils.format(new Date(), MediaUserConstant.dateFormat);
        QueryWrapper<UserPinTimesModel> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("uid", uid);
        queryWrapper1.eq("created_date", dateStr);
        UserPinTimesModel userPinTimesModel = userPinTimesMapper.selectOne(queryWrapper1);
        if (userPinTimesModel == null){
            userPinInfoResponse.setTimes(0);
        }else {
            userPinInfoResponse.setTimes(userPinTimesModel.getTimes());
        }
        return userPinInfoResponse;
    }

    public void updateUserPinTimes(Long uid){
        String dateStr = DateFormatUtils.format(new Date(), MediaUserConstant.dateFormat);
        QueryWrapper<UserPinTimesModel> queryWrapper1 = new QueryWrapper<>();
        queryWrapper1.eq("uid", uid);
        queryWrapper1.eq("created_date", dateStr);
        UserPinTimesModel userPinTimesModel = userPinTimesMapper.selectOne(queryWrapper1);
        if (userPinTimesModel == null){
            UserPinTimesModel insert = new UserPinTimesModel()
                    .setId(redisIdGenerator.generate())
                    .setTimes(1)
                    .setCreatedDate(dateStr)
                    .setCreatedTime(new Date())
                    .setUid(uid);
            userPinTimesMapper.insert(insert);
        }else {

            UpdateWrapper<UserPinTimesModel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                    .eq(UserPinTimesModel::getId, userPinTimesModel.getId())
                    .set(UserPinTimesModel::getTimes, userPinTimesModel.getTimes()+1)
                    .set(UserPinTimesModel::getUpdatedTime, new Date());
            userPinTimesMapper.update(updateWrapper);
        }
    }

    private String buildSubject(String template, Map<String, Object> data) {
        try {
            Configuration configuration = new Configuration(Configuration.VERSION_2_3_23);
            // 渲染标题
            StringTemplateLoader templateLoader = new StringTemplateLoader();

            templateLoader.putTemplate("subject", template); // template = 虚拟名称, 用来当作获取静态文件的key
            configuration.setTemplateLoader(templateLoader);
            Template subjectTemplate = configuration.getTemplate("subject", "utf-8");
            return FreeMarkerTemplateUtils.processTemplateIntoString(subjectTemplate, data);
        } catch (Exception e) {
            log.error("UserDevicePushService.buildSubject err::{}", e.getMessage());
        }
        return null;
    }

}
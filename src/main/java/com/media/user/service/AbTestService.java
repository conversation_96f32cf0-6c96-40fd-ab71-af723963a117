package com.media.user.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Maps;
import com.media.user.feign.client.AbClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AbTestService {

    @Autowired
    private AbClient abClient;

    public List<String> abTestForDevice(String deviceId,Long uid) {
        if (StringUtils.isBlank(deviceId)) {
            return Collections.emptyList();
        }
        Map<String, Object> queryUserMap = new HashMap<>();
        queryUserMap.put("key", "userId");
        Map<String, Object> attrsMap = new HashMap<>();
        if (uid == null || uid == 0) {
            attrsMap.put("userId",  String.format("guest-%d", stringToHash(deviceId)));
        }else {
            attrsMap.put("userId", String.valueOf(uid));
        }

        queryUserMap.put("attrs", attrsMap);
        String valueStr = JSON.toJSONString(queryUserMap);
        return abClient.toggleVariations(Base64.encodeBase64String(valueStr.getBytes()));
    }

    public static long stringToHash(String s) {
        final int FNV_32_PRIME = 0x01000193; // 16777619
        int hash = 0x811c9dc5;              // FNV offset basis 2166136261

        for (int i = 0; i < s.length(); i++) {
            hash ^= s.charAt(i);
            hash *= FNV_32_PRIME;
        }

        // 转成有符号的 long（与 Go 中 int64(fnv32) 一致）
        return Integer.toUnsignedLong(hash);
    }


    public Map<String, Object> GetAbConfigInfo(String deviceId,Long uid) {
        Map<String, Object> abConfigMap = Maps.newHashMap();
        abConfigMap.put("toggleVariations", abTestForDevice(deviceId,uid));
        return abConfigMap;
    }
}

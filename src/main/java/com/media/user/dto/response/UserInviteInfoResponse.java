package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class UserInviteInfoResponse {

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 邀请xme数量(普通)
     */
    private Integer inviteXmeAmount;

    // 被邀请奖励(普通)
    private Integer invitedXmeAmount;

    // 邀请奖励(创世大使)
    private Integer inviteGenesisXmeAmount;

    // 被邀请奖励(创世大使)
    private Integer invitedGenesisXmeAmount;

    /**
     * 邀请链接
     */
    private String inviteUrl;

    /**
     * 邀请数量
     */
    private Long inviteCount;

    private Integer level2MinNum;

    /**
     * 邀请列表页标题
     */
    private String inviteTitle;

    /**
     * 邀请列表页字标题
     */
    private String inviteSubTitle;
}

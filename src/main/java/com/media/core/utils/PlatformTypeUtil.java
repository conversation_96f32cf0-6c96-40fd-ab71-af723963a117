package com.media.core.utils;

import com.media.core.constant.ClientHeaderConstant;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * 平台类型获取工具类
 */
@Slf4j
@Component
public class PlatformTypeUtil {

    /**
     * 获取当前请求的平台类型
     * 
     * @return 平台类型，如果获取失败返回空字符串
     */
    public String getPlatformType() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = (HttpServletRequest) requestAttributes
                .resolveReference(RequestAttributes.REFERENCE_REQUEST);
        return this.getPlatformTypeByRequest(request);
    }

    /**
     * 从 HttpServletRequest 中获取平台类型
     * 
     * @param request HTTP请求对象
     * @return 平台类型，如果获取失败返回空字符串
     */
    public String getPlatformTypeByRequest(HttpServletRequest request) {
        String platformType = null;
        try {
            platformType = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_PLATFORM_TYPE);

            // 如果获取不到或为空，返回空字符串
            if (platformType == null || platformType.length() == 0) {
                platformType = "";
            }

        } catch (Exception e) {
            platformType = "";
            log.error("get platform type error", e);
        }

        return platformType;
    }
}

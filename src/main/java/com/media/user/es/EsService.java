package com.media.user.es;

import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

@Service
@Slf4j
public class EsService {

    @Value("${es.prefix}")
    private String esPrefix;

    @Autowired
    RestClient restClient;

    public String getRequestBody(String url) throws Exception {
        Request request = new Request("GET", url);
        //request.setJsonEntity("{ \"field\": \"value\" }"); // 设置请求体为 JSON 文档
        Response response = restClient.performRequest(request);
        return new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
    }

    // 单条记录保存：/dev_user_client_user/_doc/doc_id
    public String postRequestBody(String url, String body) throws Exception {
        Request request = new Request("POST", url);
        request.setJsonEntity(body); // 设置请求体为 JSON 文档
        Response response = restClient.performRequest(request);
        return new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
    }

    public String putRequestBody(String url, String body) throws Exception {
        Request request = new Request("PUT", url);
        request.setJsonEntity(body);
        Response response = restClient.performRequest(request);
        return new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
    }

    public String deleteRequestBody(String url) throws Exception {
        Request request = new Request("DELETE", url);
        Response response = restClient.performRequest(request);
        return new String(response.getEntity().getContent().readAllBytes(), StandardCharsets.UTF_8);
    }

    public String createIndex(String indexName, String body) throws Exception {
        return putRequestBody("/" + createIndexName(indexName.replace("-", "_").toLowerCase()), body);
    }

    public String deleteIndex(String indexName) throws Exception {
        return deleteRequestBody("/" + createIndexName(indexName.replace("-", "_").toLowerCase()));
    }


    public boolean isExistIndex(String indexName) {
        boolean isExist = false;
        try {
            getRequestBody("/" + createIndexName(indexName.replace("-", "_").toLowerCase()));
            isExist = true;
        }catch (Exception e){
            if(e.getMessage().contains("no such index")){
                log.info("index not exist : {}", indexName);
            }
            isExist = false;
        }
        return isExist;
    }


    public String createIndexName(String name) {
        return esPrefix + name;
    }


}

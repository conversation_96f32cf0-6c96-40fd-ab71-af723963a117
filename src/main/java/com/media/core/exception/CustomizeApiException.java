package com.media.core.exception;

import org.springframework.http.HttpStatus;

/**
 * 自定义统一异常.
 *
 * <AUTHOR>
public class CustomizeApiException extends BaseException {

    private static final long serialVersionUID = 1L;

    public CustomizeApiException() {
    }

    public CustomizeApiException(Integer code) {
        this(code, null);
    }


    public CustomizeApiException(Integer code, String message) {
        setCode(code);
        setMessage(message);
        setStatus(HttpStatus.BAD_REQUEST.value());
    }

}

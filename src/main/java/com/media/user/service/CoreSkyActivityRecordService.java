package com.media.user.service;

import com.media.user.domain.CoreSkyActivityRecordModel;
import com.media.user.mapper.CoreSkyActivityRecordMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * CoreSky活动参与记录Service
 */
@Slf4j
@Service
public class CoreSkyActivityRecordService {

    @Autowired
    private CoreSkyActivityRecordMapper coreSkyActivityRecordMapper;

    /**
     * 插入CoreSky活动参与记录
     *
     * @param uid      用户ID
     * @param clientIp 客户端IP地址
     * @return 是否插入成功
     */
    public boolean insertActivityRecord(Long uid, String clientIp) {
        try {
            if (uid == null) {
                log.warn("插入CoreSky活动记录失败：用户ID为空");
                return false;
            }

            CoreSkyActivityRecordModel record = new CoreSkyActivityRecordModel()
                    .setUid(uid)
                    .setClientIp(clientIp)
                    .setCreatedTime(new Date());

            int inserted = coreSkyActivityRecordMapper.insert(record);

            if (inserted > 0) {
                log.info("插入CoreSky活动参与记录成功：用户ID={}, IP={}, 记录ID={}",
                        uid, clientIp, record.getId());
                return true;
            } else {
                log.warn("插入CoreSky活动参与记录失败：用户ID={}, IP={}", uid, clientIp);
                return false;
            }
        } catch (Exception e) {
            log.error("插入CoreSky活动参与记录异常：用户ID={}, IP={}, 错误: {}",
                    uid, clientIp, e.getMessage(), e);
            return false;
        }
    }
}
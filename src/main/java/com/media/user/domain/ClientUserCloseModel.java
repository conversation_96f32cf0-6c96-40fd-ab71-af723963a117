package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("client_user_close")
@Data
@Accessors(chain = true)
public class ClientUserCloseModel extends Model<ClientUserCloseModel> {

    @TableId(value = "uid", type = IdType.AUTO)
    private Long uid;

    @TableField("email")
    private String email;

    @TableField("password")
    private String password;

    @TableField("language")
    private String language;

    @TableField("avatar_url")
    private String avatarUrl;

    @TableField("invite_code")
    private String inviteCode;

    @TableField("nick_name")
    private String nickName;

    @TableField("person_introduce")
    private String personIntroduce;

    @TableField("status")
    private Byte status;

    @TableField("invite_uid")
    private Long inviteUid;

    @TableField("type")
    private Byte type;

    /**
     * 关注度
     */
    @TableField("attention")
    private Integer attention;

    /**
     * 是否是虚拟用户(0:否;1:是- 默认为:0)
     */
    @TableField("virtual_type")
    private Byte virtualType;

    /**
     * 是新手用户(0:否;1:是- 默认为:1)
     */
    @TableField("is_newer")
    private Byte isNewer;

    /**
     * 是否推送给了推荐系统(0:否;1:是- 默认为:0)
     */
    @TableField("is_recommend")
    private Byte isRecommend;

    @TableField("source_type")
    private Byte sourceType;

    @TableField("registration_time")
    private Date registrationTime;

    @TableField("last_login_time")
    private Date lastLoginTime;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;
}
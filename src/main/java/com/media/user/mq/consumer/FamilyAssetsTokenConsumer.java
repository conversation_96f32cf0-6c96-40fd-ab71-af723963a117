package com.media.user.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.media.user.constant.MediaUserConstant;
import com.media.user.dto.response.FamilyAssetsTokenResponse;
import com.media.user.service.UserFamilyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class FamilyAssetsTokenConsumer {

    @Autowired
    private UserFamilyService userFamilyService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

//    @KafkaListener(topics = KafkaTopicConstant.FAMILY_ASSETS_TOKEN, groupId = KafkaTopicConstant.USER_GROUP)
    public void updateFamilyAssetsToken(String message, Acknowledgment ack) {
        log.info("FamilyAssetsTokenConsumer.updateFamilyAssetsToken message:{}", message);
        // 如果设置成功，表示这是第一次请求，执行业务逻辑
        FamilyAssetsTokenResponse familyAssetsTokenResponses = JSON.parseObject(message, FamilyAssetsTokenResponse.class);

        if (stringRedisTemplate.opsForValue().setIfAbsent(MediaUserConstant.FAMILY_ASSETS_TOKEN_REPEAT_PREFIX + familyAssetsTokenResponses.getUid() + ":" + familyAssetsTokenResponses.getCreatedDate(), familyAssetsTokenResponses.getUid().toString(), 12, TimeUnit.HOURS)) {
            userFamilyService.updateFamilyAssetsToken(familyAssetsTokenResponses);
        }
        ack.acknowledge();
    }
}

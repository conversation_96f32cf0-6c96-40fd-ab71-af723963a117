package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.user.service.*;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/user/api/user/sign")
public class UserSignTaskApiController {

    @Autowired
    UserSignTaskService userSignTaskService;

    @Autowired
    private RedissonClient redissonClient;

    @GetMapping("/rule")
//    @RequireAuth
    public Map<String, Object> signRule(){
        Long uid = null;
        if(CloudSession.isLogin()){
            uid = CloudSession.getUid();
        }
        return userSignTaskService.getSignTaskRule(uid);
    }


    @PostMapping("/day")
    @RequireAuth
    public void signDay(){
        Long uid = CloudSession.getUid();
        RLock rLock = redissonClient.getLock("user:sign:task:" + uid);
        try {
            rLock.lock(3000, TimeUnit.MILLISECONDS);
            userSignTaskService.signIn(uid);
        }finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

}

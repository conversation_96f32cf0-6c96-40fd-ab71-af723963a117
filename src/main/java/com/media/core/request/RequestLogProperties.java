package com.media.core.request;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@Accessors(chain = true)
@ConfigurationProperties(prefix = "request.log")
@Component
public class RequestLogProperties {

    /**
     * 开启拦截.
     */
    private Boolean enable = true;

    /**
     * 输出到日志.
     */
    private Boolean print = false;

    /**
     * 输出到skywalking.
     */
    private Boolean skywalking = true;

    /**
     * 记录responseLog
     */
    private Boolean responseLog = false;

    /**
     * 忽略路径.
     */
    private List<String> ignorePaths = new ArrayList<>();


    /**
     * 拦截路径.
     */
    private List<String> paths = new ArrayList<>();

    /**
     * 忽略参数,例如password
     */
    private Set<String> ignoreQueryNames = new HashSet<>(
    );


}

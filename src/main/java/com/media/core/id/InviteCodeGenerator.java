package com.media.core.id;

import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

@Component
public class InviteCodeGenerator {

    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";  // 大写字母和数字

    // 使用 ConcurrentHashMap 来保证线程安全，同时存储已生成的邀请码
    private ConcurrentHashMap<String, Boolean> generatedCodes = new ConcurrentHashMap<>();

    // 生成一个不重复的邀请码
    public String generateInviteCode(int n) {
        String inviteCode;
        // 采用自旋重试机制，直到生成不重复的邀请码
        do {
            inviteCode = generateRandomCode(n);
        } while (generatedCodes.putIfAbsent(inviteCode, Boolean.TRUE) != null);  // 如果返回非空，说明已经存在

        return inviteCode;
    }

    // 随机生成一个 5 位的数字+字母邀请码
    private String generateRandomCode(int n) {
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < n; i++) {
            int randomIndex = ThreadLocalRandom.current().nextInt(CHARACTERS.length());
            code.append(CHARACTERS.charAt(randomIndex));
        }
        return code.toString();
    }

}
package com.media.core.utils;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class GoogleUtils {

    @Value("${google.recaptcha.key:6Ld5ANcqAAAAALuCXfFahBQ7Z-1j84cxlaG0orZc}")
    String secret;

    public boolean googleRecaptcha(String responseToken) {
        try {
            String url = "https://www.recaptcha.net/recaptcha/api/siteverify";
            FormBody formBody = new FormBody.Builder()
                    .add("secret", secret)
                    .add("response", responseToken)
                    .build();
            okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();
            //  构建Request对象
            Request request = new Request.Builder()
                    .addHeader("Content-Type", "application/x-www-form-urlencoded")
                    .post(formBody)
                    .url(url)
                    .build();
            Response signRsp = client.newCall(request).execute();
            JSONObject jsonObject = new Gson().fromJson(signRsp.body().charStream(), JSONObject.class);
            return jsonObject.getBoolean("success");
        }catch (Exception e){
            log.error("recaptcha error", e);
        }
        return false;
    }

}

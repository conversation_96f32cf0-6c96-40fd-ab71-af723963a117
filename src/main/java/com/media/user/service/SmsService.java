package com.media.user.service;

import com.google.common.eventbus.EventBus;
import com.media.core.constant.BeanConstant;
import com.media.core.exception.ApiException;
import com.media.core.utils.RedisUtils;
import com.media.user.constant.MediaUserConstant;
import com.media.user.dto.request.SendEmailRequest;
import com.media.user.enums.BusinessTypeEnum;
import com.media.user.enums.EmailTemplate;
import com.media.user.enums.LanguageEnums;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.EmailClient;
import com.xme.xme_base_depends.enums.UserBehaviorEventEnum;
import com.xme.xme_base_depends.mq.message.UserFinishEvent;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 发送邮件
 */
@Service
public class SmsService {


    private static final Logger log = LoggerFactory.getLogger(SmsService.class);
    @Autowired
    RedisUtils redisUtils;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private EmailClient emailClient;

    @Autowired
    private Configuration configuration;

    @Value("${super.code.ignoreEmail:false}")
    private boolean ignoreEmail;

    @Autowired
    @Qualifier(value = BeanConstant.USER_EVENT_BUS_BEAN)
    private EventBus userEventBus;


    public void sendEmailCode(Long uid, String email, BusinessTypeEnum businessTypeEnum, LanguageEnums language) {
        //增加验证码的拦截频率  1分钟发一次， 每天最多发送 10次
        String redisLimitKey = getMailCodeLimitRateRedisKey(uid, email, businessTypeEnum.getValue());
        if (stringRedisTemplate.hasKey(redisLimitKey)) {
            throw new ApiException(MediaUserExceptionCodeApi.EMAIL_CODE_LIMIT_RATE_ERROR);
        }
        String redisCountKey = getMailCodeLimitCountRedisKey(uid, email, businessTypeEnum.getValue());
        Long count = redisUtils.getCount(redisCountKey);
        if (count >= 10) {
            throw new ApiException(MediaUserExceptionCodeApi.EMAIL_CODE_LIMIT_COUNT_ERROR);
        }

        stringRedisTemplate.opsForValue().set(redisLimitKey, "1", 1, TimeUnit.MINUTES);
        redisUtils.incrementCount(redisCountKey, 24L, TimeUnit.HOURS);

        if (language == null) {
            language = LanguageEnums.en_US;
        }

        int code = RandomUtils.nextInt(100000, 999999);

        String key = getEmailCodeKey(uid, email, businessTypeEnum);

        /**
         * 验证码五分钟过期
         */
        stringRedisTemplate.opsForValue().set(key, String.valueOf(code), 15, TimeUnit.MINUTES);

        // 发mq
        SendEmailRequest request = buildEmailRequest(email, EmailTemplate.getEmailTemplate(businessTypeEnum), language, Map.of("code", String.valueOf(code), "time", "5"));
        log.info("SmsService.sendEmailCode request: {}", request.getTo());

        try {
            emailClient.sendEmail(request);
        } catch (Exception e) {
            log.error("email send code error: {}", e.getMessage());
            throw new ApiException(MediaUserExceptionCodeApi.SEND_CODE_ERROR);
        }
    }


    /**
     * 发送注册成功邮件
     *
     * @param email
     */
    @Async("threadPoolTaskExecutor")
    public void sendRegisterMessage(String email, String nickName, LanguageEnums language) {
        log.info("SmsService.sendRegisterMessage request: {} - {}", email, nickName);
        if (language == null) {
            language = LanguageEnums.en_US;
        }
        try {
            SendEmailRequest request = buildEmailRequest(email, EmailTemplate.REGISTER_EMAIL, language, Map.of("nickName", nickName));
            emailClient.sendEmailHtml(request);
        } catch (Exception e) {
            log.info("SmsService.buildEmailRequest error: {}", e.getMessage());
        }
    }

    private SendEmailRequest buildEmailRequest(String email, EmailTemplate emailTemplate, LanguageEnums language, Map<String, Object> data) {
        try {
            SendEmailRequest request = new SendEmailRequest().setTo(email);
            // 渲染标题
            Template subjectTemplate = configuration.getTemplate(language.name() + "/" + emailTemplate.getSubject() + ".ftlh");
            String subjectContent = FreeMarkerTemplateUtils.processTemplateIntoString(subjectTemplate, data);
            request.setSubject(subjectContent);

            Template template = configuration.getTemplate(language.name() + "/" + emailTemplate.getTemplate() + ".ftlh");
            String content = FreeMarkerTemplateUtils.processTemplateIntoString(template, data);
            request.setContent(content);
            return request;
        } catch (Exception e) {
            log.info("SmsService.buildEmailRequest error: {}", e.getMessage());
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }
    }


    private String getEmailCodeKey(Long uid, String email, BusinessTypeEnum businessTypeEnum) {
        if (uid == null || uid == 0) {
            return MediaUserConstant.SMS_MAIL_CODE_PREFIX + businessTypeEnum.getValue() + ":" + email;
        }
        return MediaUserConstant.SMS_MAIL_CODE_PREFIX + businessTypeEnum.getValue() + ":" + uid + ":" + email;
    }

    private String getMailCodeLimitRateRedisKey(Long uid, String email, Integer businessType) {
        if (uid == null) {
            return MediaUserConstant.SMS_MAIL_CODE_LIMIT_RATE_PREFIX + businessType + ":" + email;
        }
        return MediaUserConstant.SMS_MAIL_CODE_LIMIT_RATE_PREFIX + businessType + ":" + uid + ":" + email;
    }

    private String getMailCodeLimitCountRedisKey(Long uid, String email, Integer businessType) {
        if (uid == null) {
            return MediaUserConstant.SMS_MAIL_CODE_LIMIT_COUNT_PREFIX + businessType + ":" + email;
        }
        return MediaUserConstant.SMS_MAIL_CODE_LIMIT_COUNT_PREFIX + businessType + ":" + uid + ":" + email;
    }


    public void verifyEmailCode(Long uid, String email, BusinessTypeEnum businessTypeEnum, String emailCode) {
        if (ignoreEmail && MediaUserConstant.SUPER_CODE.equals(emailCode)) {
            return;
        }
        String key = getEmailCodeKey(uid, email, businessTypeEnum);
        boolean exist = stringRedisTemplate.hasKey(key);
        if (!exist) {
            throw new ApiException(MediaUserExceptionCodeApi.EMAIL_CODE_EXPIRE);
        }
        String code = stringRedisTemplate.opsForValue().get(key);
        if (!emailCode.equals(code)) {
            throw new ApiException(MediaUserExceptionCodeApi.EMAIL_CODE_ERROR);
        }
    }

    /**
     * 标记 key 失效
     *
     * @param email
     * @param businessTypeEnum
     */
    public void signEmailCodeExpire(Long uid, String email, BusinessTypeEnum businessTypeEnum) {
        String key = getEmailCodeKey(uid, email, businessTypeEnum);
        stringRedisTemplate.delete(key);
    }

    /**
     * TODO test 上线需关掉
     *
     * @param uid
     * @param email
     * @param businessType
     * @return
     */
    public String getEmailCode(Long uid, String email, Integer businessType) {
//        businessType 1, 2, 9 不需要 uid
//        3， 4， 5, 7, 8 需要 uid
        if (businessType == 1 || businessType == 2 || businessType == 9) {
            uid = null;
        }
        return stringRedisTemplate.opsForValue().get(getEmailCodeKey(uid, email, BusinessTypeEnum.getEnumFromCode(businessType)));
    }

}

package com.media.user.utils;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class RequestHeaderUtils {

    /**
     * 提取所有请求头
     *
     * @param request HttpServletRequest
     * @return 请求头Map
     */
    public static Map<String, String> extractAllHeaders(HttpServletRequest request) {
        Map<String, String> headers = new HashMap<>();
        if (request == null) {
            log.warn("HttpServletRequest is null");
            return headers;
        }
        Enumeration<String> headerNames = request.getHeaderNames();
        if (headerNames != null) {
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                String headerValue = request.getHeader(headerName);
                if (StringUtils.hasText(headerValue)) {
                    headers.put(headerName, headerValue);
                }
            }
        }
        return headers;
    }

    /**
     * 提取指定请求头
     *
     * @param request HttpServletRequest
     * @param headerNames 请求头名称数组
     * @return 请求头Map
     */
    public static Map<String, String> extractHeaders(HttpServletRequest request, String... headerNames) {
        Map<String, String> headers = new HashMap<>();
        
        if (request == null || headerNames == null) {
            return headers;
        }

        for (String headerName : headerNames) {
            String headerValue = request.getHeader(headerName);
            if (StringUtils.hasText(headerValue)) {
                headers.put(headerName, headerValue);
            }
        }

        return headers;
    }

    /**
     * 获取单个请求头值
     *
     * @param request HttpServletRequest
     * @param headerName 请求头名称
     * @return 请求头值
     */
    public static String getHeader(HttpServletRequest request, String headerName) {
        if (request == null || !StringUtils.hasText(headerName)) {
            return null;
        }
        return request.getHeader(headerName);
    }

    /**
     * 检查请求头是否存在
     *
     * @param request HttpServletRequest
     * @param headerName 请求头名称
     * @return 是否存在
     */
    public static boolean hasHeader(HttpServletRequest request, String headerName) {
        if (request == null || !StringUtils.hasText(headerName)) {
            return false;
        }
        String headerValue = request.getHeader(headerName);
        return StringUtils.hasText(headerValue);
    }
} 
package com.media.core.utils;

import java.time.LocalDateTime;

import java.time.format.DateTimeFormatter;

public class TimeTool {

    public static long timestamp() {
        return System.currentTimeMillis() / 1000;
    }



    public static boolean isTimeValid(String startTimeStr,String endTimeStr) {
        boolean isTimeValid = true;
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 检查开始时间
        if (startTimeStr != null && !startTimeStr.isEmpty()) {
            try {
                LocalDateTime startTime = LocalDateTime.parse(startTimeStr, formatter);
                if (now.isBefore(startTime)) {
                    isTimeValid = false;
                }
            } catch (Exception e) {
                return false;
            }
        }

        // 检查结束时间
        if (endTimeStr != null && !endTimeStr.isEmpty()) {
            try {
                LocalDateTime endTime = LocalDateTime.parse(endTimeStr, formatter);
                if (now.isAfter(endTime)) {
                    return false;
                }
            } catch (Exception e) {
               return false;
            }
        }
        return isTimeValid;
    }
}

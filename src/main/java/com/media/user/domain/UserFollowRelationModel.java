package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("user_follow_relation")
@Data
@Accessors(chain = true)
public class UserFollowRelationModel extends Model<UserFollowRelationModel> {

    @TableId(value = "relation_id", type = IdType.AUTO)
    private Long relationId;

    /** 关注者用户ID */
    @TableField("follow_uid")
    private Long followUid;

    /** 被关注者预用户ID */
    @TableField("followed_old_uid")
    private Long followedOldUid;

    /** 被关注者真实用户ID */
    @TableField("followed_new_uid")
    private Long followedNewUid;

    /** 关注状态(0:未关注;1:已关注) */
    @TableField("follow_state")
    private Byte followState;

    /** 关注时间 */
    @TableField("follow_time")
    private Date followTime;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;
}
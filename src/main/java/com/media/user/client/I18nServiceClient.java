package com.media.user.client;

import com.media.user.dto.i18n.I18nTranslationResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * i18n翻译服务客户端
 */
@FeignClient(
    name = "i18n-service",
    url = "${i18n.service.url:https://cdn-api.x.me}",
    configuration = I18nServiceClientConfig.class
)
public interface I18nServiceClient {

    /**
     * 获取翻译数据
     *
     * @param namespace 命名空间
     * @param version 版本号
     * @return 翻译响应
     */
    @GetMapping("/i18n/api/v1/translations/user")
    I18nTranslationResponse getTranslations(
        @RequestParam("namespace") String namespace,
        @RequestParam("version") String version
    );
}

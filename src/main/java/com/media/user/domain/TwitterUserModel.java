package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2018-11-09
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("twitter_user")
@Data
@Accessors(chain = true)
public class TwitterUserModel extends Model<ThirdPlatformUserModel> {

    @TableId(value = "twitter_id", type = IdType.AUTO)
    private String twitterId;

    /** 预注册用户绑定 uid */
    @TableField("old_uid")
    private Long oldUid;

    /** 注册用户绑定真实 uid */
    @TableField("new_uid")
    private Long newUid;

    @TableField("username")
    private String username;

    @TableField("name")
    private String name;

    /** 头像链接 s3 */
    @TableField("profile_image_url")
    private String profileImageUrl;

    /** twitter 的图片地址 */
    @TableField("original_image_url")
    private String originalImageUrl;

    /** 个人介绍 */
    @TableField("description")
    private String description;

    /** 是否绑定(0:未绑定;1:已绑定;2:取消绑定) */
    @TableField("bind")
    private Byte bind;

    /** 绑定时间 */
    @TableField("bind_time")
    private Date bindTime;

    @TableField("subscription_type")
    private String subscriptionType;

    @TableField("most_recent_tweet_id")
    private String mostRecentTweetId;

    @TableField("followers_count")
    private int followersCount;

    @TableField("following_count")
    private int followingCount;

    @TableField("tweet_count")
    private int tweetCount;

    @TableField("listed_count")
    private int listedCount;

    @TableField("like_count")
    private int likeCount;

    /** 0:false;1:true */
    @TableField("verified")
    private int verified;

    /** 0:不推荐;1:推荐 - 默认: 0 */
    @TableField("recommend_type")
    private int recommendType;

    /** 是否定时跑数据 ：0:不跑数;1:跑数;9:不处理 - 默认: 0 */
    @TableField("fix_info_type")
    private int fixInfoType;

    @TableField("verified_type")
    private String verifiedType;

    /** 0:false;1:true */
    @TableField("protected")
    private int _protected;

    /** 0:false;1:true */
    @TableField("receives_your_dm")
    private int receivesYourDm;

    @TableField("auth_token")
    private String authToken;

    @TableField("auth_token_secret")
    private String authTokenSecret;

    @TableField("access_token")
    private String accessToken;

    @TableField("refresh_token")
    private String refreshToken;

    @TableField("scopes")
    private String scopes;

    @TableField("expire_at")
    private Date expireAt;

    @TableField("created_at")
    private Date createdAt;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;

    /** 是否已关注官方账号 0:未关注 1:已关注 */
    @TableField("followed_official")
    private Integer followedOfficial;
}

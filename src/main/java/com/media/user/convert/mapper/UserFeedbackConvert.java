package com.media.user.convert.mapper;

import com.media.user.domain.UserFeedbackModel;
import com.media.user.dto.request.UserFeedbackRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserFeedbackConvert {

    UserFeedbackConvert INSTANCE = Mappers.getMapper(UserFeedbackConvert.class);

    UserFeedbackModel req2Model(UserFeedbackRequest request);
}

package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("user_family_day_assets")
@Data
@Accessors(chain = true)
public class UserFamilyDayAssetsModel extends Model<UserFamilyDayAssetsModel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "uid")
    private Long uid;

    @TableField(value = "family_id")
    private Long familyId;

    @TableField("created_date")
    private String createdDate;

    @TableField("member_point_total")
    private BigDecimal memberPointTotal;

    @TableField("points_bonus")
    private BigDecimal pointsBonus;

    @TableField("deleted")
    private Byte deleted;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;
}
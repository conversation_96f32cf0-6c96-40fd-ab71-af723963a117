package com.media.user.convert.mapper;


import com.media.user.domain.WebsiteCustomerModel;
import com.media.user.dto.request.WebsiteCustomerRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface WebsiteCustomerConvert {

    WebsiteCustomerConvert INSTANCE = Mappers.getMapper(WebsiteCustomerConvert.class);

    WebsiteCustomerModel req2Model(WebsiteCustomerRequest request);
}

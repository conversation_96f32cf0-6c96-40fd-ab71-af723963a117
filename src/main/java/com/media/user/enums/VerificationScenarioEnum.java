package com.media.user.enums;

import lombok.Getter;

/**
 * 认证场景枚举
 */
@Getter
public enum VerificationScenarioEnum {
    
    FACE(1, "人脸认证"),
    PHONE(2, "手机认证"),
    EMAIL(3, "邮箱认证"),
    THREE_IN_ONE(4, "三合一认证");

    private final Integer code;
    private final String desc;

    VerificationScenarioEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据编码获取枚举
     */
    public static VerificationScenarioEnum getByCode(Integer code) {
        for (VerificationScenarioEnum scenario : values()) {
            if (scenario.getCode().equals(code)) {
                return scenario;
            }
        }
        return null;
    }

    /**
     * 根据faceType判断场景
     * @param faceType 1-自己认证(三合一), 2-被邀请认证(人脸)
     */
    public static VerificationScenarioEnum getByFaceType(Integer faceType) {
        if (faceType == 1) {
            return THREE_IN_ONE;
        } else if (faceType == 2) {
            return FACE;
        }
        return FACE; // 默认人脸认证
    }
} 
package com.media.user.controller.api;

import com.alibaba.fastjson.JSONObject;
import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.core.constant.PageResponse;
import com.media.user.dto.query.UserFollowingMemberQuery;
import com.media.user.dto.request.*;
import com.media.user.dto.response.UserFollowingMemberResponse;
import com.media.user.service.UserFollowService;
import com.media.core.utils.MD5Util;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/user/api/user/follow")
public class UserFollowApiController {


    @Autowired
    UserFollowService userFollowService;

    @Autowired
    RedissonClient redissonClient;

    /**
     * 批量关注用户
     * @return
     */
    @PostMapping("/batch")
    @RequireAuth
    public void batchFollow(@Validated @RequestBody UserFollowBatchRequest request){
        String param = MD5Util.getMD5(JSONObject.toJSONString(request.getUids()));
        RLock rLock = redissonClient.getLock("user:follow:batch:follow:" + param);
        try {
            rLock.lock(3000, TimeUnit.MILLISECONDS);
            log.info("batch follow: {} - {}", CloudSession.getUid(), request.getUids());
            userFollowService.batchFollow(CloudSession.getUid(), request);
        }finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }


    /**
     * 取消用户关注
     * @return
     */
    @PostMapping("/cancel")
    @RequireAuth
    public void cancelFollow(@Validated @RequestBody UserFollowBatchRequest request){
        String param = MD5Util.getMD5(JSONObject.toJSONString(request.getUids()));
        RLock rLock = redissonClient.getLock("user:follow:batch:cancel:" + param);
        try {
            rLock.lock(3000, TimeUnit.MILLISECONDS);
            log.info("batch cancel: {} - {}", CloudSession.getUid(), request.getUids());
            userFollowService.batchCancel(CloudSession.getUid(), request);
        }finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    /**
     * 我的关注列表
     * @return
     */
    @PostMapping("/list")
    @RequireAuth
    public PageResponse<UserFollowingMemberResponse> userFollowList(@RequestBody UserFollowingMemberQuery query){
        query.setUid(CloudSession.getUid());
        return userFollowService.getUserFollowMemberPage(query);
    }


    /**
     * 我的粉丝
     * @return
     */
    @PostMapping("/fans")
    @RequireAuth
    public PageResponse<UserFollowingMemberResponse> userFans(@RequestBody UserFollowingMemberQuery query){
        query.setUid(CloudSession.getUid());
        return userFollowService.getFansPage(query);
    }

}

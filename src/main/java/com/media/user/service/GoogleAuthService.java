package com.media.user.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.user.domain.ThirdPlatformUserModel;
import com.media.user.dto.request.ClientUserRequest;
import com.media.user.dto.request.GoogleAuthAndroidRequest;
import com.media.user.dto.request.UserLoginRequest;
import com.media.user.dto.response.AuthTokenResponse;
import com.media.user.dto.response.GoogleAuthResponse;
import com.media.user.enums.PlatformEnums;
import com.media.user.enums.PlatformTypeEnum;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.GoogleAuthClient;
import com.media.user.feign.vo.GoogleUserInfo;
import com.media.core.id.RedisIdGenerator;
import com.media.user.mapper.ThirdPlatformUserModelMapper;
import com.media.core.request.ClientInfoContext;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Base64;
import java.util.Date;

/**
 * 谷歌快捷登录
 */
@Slf4j
@Service
public class GoogleAuthService {

    @Autowired
    ClientUserService clientUserService;

    @Autowired
    UserRegisterService userRegisterService;

    @Autowired
    GoogleAuthClient googleAuthClient;

    @Autowired
    ThirdPlatformUserModelMapper thirdPlatformUserModelMapper;

    @Autowired
    private RedisIdGenerator redisIdGenerator;

    @Value(value = "${google.auth.client_id}")
    private String googleAuthClientId;

    @Value(value = "${google.auth.client_secret}")
    private String googleAuthClientSecret;

    @Value(value = "${google.auth.redirect_uri}")
    private String googleAuthRedirectUri;

    @Transactional(rollbackFor = Exception.class)
    public AuthTokenResponse authWeb(String idToken, String inviteCode) {
        ThirdPlatformUserModel entity = this.googleTokenInfoByIdToken(idToken);
        log.info("google auth web pojo: {}" , entity);
        return this.authLogin(entity, inviteCode);
    }


    @Transactional(rollbackFor = Exception.class)
    public AuthTokenResponse authAndroid(GoogleAuthAndroidRequest googleAuthAndroidRequest) {
        if(StringUtil.isBlank(googleAuthAndroidRequest.getAccessToken())){
            throw new ApiException(MediaUserExceptionCodeApi.NO_PERMISSION);
        }
        ThirdPlatformUserModel entity = this.googleTokenInfo(googleAuthAndroidRequest.getAccessToken());
        log.info("google auth android pojo: {}" , entity);
        log.info("input id:{}, tokenInfo subId:{}", googleAuthAndroidRequest.getId(), entity.getThirdId());
        if(!googleAuthAndroidRequest.getId().equals(entity.getThirdId())){
            throw new ApiException(MediaUserExceptionCodeApi.NO_PERMISSION);
        }
        entity.setEmail(googleAuthAndroidRequest.getEmail());
        entity.setDisplayName(googleAuthAndroidRequest.getDisplayName());
        entity.setPhotoUrl(googleAuthAndroidRequest.getPhotoUrl());
        return this.authLogin(entity, googleAuthAndroidRequest.getInviteCode());
    }

    /**
     * 登录逻辑
     * @param entity
     * @return
     */
    private AuthTokenResponse authLogin(ThirdPlatformUserModel entity, String inviteCode) {
        //检查subId是否存在用户
        QueryWrapper<ThirdPlatformUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("third_id", entity.getThirdId());
        ThirdPlatformUserModel queryExist = thirdPlatformUserModelMapper.selectOne(queryWrapper);
        //如果不为空则查询用户进行登录
        if (queryExist != null) {
            log.info("this account[{}-{}-{}]already exist, login", queryExist.getThirdId(), queryExist.getUid(), queryExist.getEmail());
            UserLoginRequest request = new UserLoginRequest();
            request.setEmail(queryExist.getEmail());
            return clientUserService.loginNoPass(request, "google", PlatformEnums.get(ClientInfoContext.get().getPlatformType()).getCode());
        }

        log.info("this account[{}-{}]not exist，need register", entity.getThirdId(), entity.getEmail());
        //如果不存在，则检查邮箱对应的用户是否存在，如果不存在则注册，如果存在则报错
        clientUserService.checkEmail(entity.getEmail());

        log.info("this account[{}-{}]not exist，email checked，keep going register ", entity.getThirdId(), entity.getEmail());
        //创建三方用户,然后注册client用户，然后返回登录信息
        ClientUserRequest request = new ClientUserRequest();
        request.setSourceType(PlatformEnums.get(ClientInfoContext.get().getPlatformType()).getCode());
        request.setEmail(entity.getEmail());
        request.setInviteCode(inviteCode);
        request.setUsername("google_" + entity.getThirdId());
        request.setPassword(System.currentTimeMillis()+"");//随机生成一个密码
        AuthTokenResponse authTokenResponse = userRegisterService.register(request);

        log.info("this account[{}-{}]not exist，register finished，uid={}", entity.getThirdId(), entity.getEmail(), authTokenResponse.getUserId());

        //从token解析 Uid
        Date now = new Date();
        entity.setId(redisIdGenerator.generate());
        entity.setUid(authTokenResponse.getUserId());
        entity.setCreatedTime(now);
        entity.setPlatformType(PlatformTypeEnum.GOOGLE.getCode());
        entity.setCreatedBy(authTokenResponse.getUserId());
        thirdPlatformUserModelMapper.insert(entity);
        log.info("this account[{}-{}]not exist，register finished，save third login info", entity.getThirdId(), entity.getEmail());
        return authTokenResponse;
    }


    /**
     * 获取google的唯一标识和邮箱地址
     * @param code
     * @return
     */
    private ThirdPlatformUserModel googleAuth(String code){
        try {
            GoogleAuthResponse response = googleAuthClient.checkToken(code, googleAuthClientId, googleAuthClientSecret, googleAuthRedirectUri, "authorization_code");
            String token = response.getId_token();
            String[] chunks = token.split("\\.");
            Base64.Decoder decoder = Base64.getUrlDecoder();
            String payload = new String(decoder.decode(chunks[1]));
            JSONObject jsonObject = JSONObject.parseObject(payload);
            ThirdPlatformUserModel thirdPlatformUserModel = new ThirdPlatformUserModel();
            thirdPlatformUserModel.setThirdId(jsonObject.getString("sub"));
            thirdPlatformUserModel.setEmail(jsonObject.getString("email"));
            thirdPlatformUserModel.setEmailVerified(jsonObject.getBoolean("email_verified") ? Byte.valueOf("1") : Byte.valueOf("0"));
            System.out.println("sub:" + jsonObject.get("sub"));
            System.out.println("email:" + jsonObject.get("email"));
            System.out.println("email_verified:" + jsonObject.get("email_verified"));
            return thirdPlatformUserModel;
        }catch (Exception e){
            log.error("GoogleAuthService.auth error:{}", e);
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }
    }

    /**
     * 获取google的唯一标识和邮箱地址
     * @param idToken
     * @return
     */
    private ThirdPlatformUserModel googleTokenInfoByIdToken(String idToken){
        try {
            JSONObject response = googleAuthClient.tokenInfoByIdToken(idToken);
            GoogleUserInfo googleUserInfo = JSONObject.parseObject(response.toJSONString(), GoogleUserInfo.class);
            if(!googleAuthClientId.equals(googleUserInfo.getAzp())){
                throw new ApiException(MediaUserExceptionCodeApi.NO_PERMISSION);
            }
            ThirdPlatformUserModel thirdPlatformUserModel = new ThirdPlatformUserModel();
            thirdPlatformUserModel.setThirdId(googleUserInfo.getSub());
            thirdPlatformUserModel.setEmail(googleUserInfo.getEmail());
            thirdPlatformUserModel.setEmailVerified("true".equals(googleUserInfo.getEmail_verified()) ? Byte.valueOf("1") : Byte.valueOf("0"));
            return thirdPlatformUserModel;
        }catch (Exception e){
            log.error("GoogleAuthService.auth error:{}", e);
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }
    }



    /**
     * 获取google的唯一标识和邮箱地址
     * @param accessToken
     * @return
     */
    private ThirdPlatformUserModel googleTokenInfo(String accessToken){
        try {
            JSONObject jsonObject = googleAuthClient.tokenInfo(accessToken);
            ThirdPlatformUserModel thirdPlatformUserModel = new ThirdPlatformUserModel();
            thirdPlatformUserModel.setThirdId(jsonObject.getString("sub"));
            thirdPlatformUserModel.setEmail(jsonObject.getString("email"));
            thirdPlatformUserModel.setEmailVerified(jsonObject.getBoolean("email_verified") ? Byte.valueOf("1") : Byte.valueOf("0"));
            System.out.println("sub:" + jsonObject.get("sub"));
            System.out.println("email:" + jsonObject.get("email"));
            System.out.println("email_verified:" + jsonObject.get("email_verified"));
            return thirdPlatformUserModel;
        }catch (Exception e){
            log.error("GoogleAuthService.auth token info error:{}", e);
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }
    }


}

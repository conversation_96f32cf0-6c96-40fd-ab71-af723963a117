package com.media.user.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 敏感词检测响应
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SensitiveWordDetectionResponse {

    /**
     * 是否包含敏感词
     */
    private Boolean hasSensitiveWords;

    /**
     * 敏感词列表
     */
    private List<String> sensitiveWords;

    /**
     * 敏感词位置信息
     */
    private List<SensitiveWordPosition> positions;

    /**
     * 总数量
     */
    private Integer totalCount;

    /**
     * 唯一数量
     */
    private Integer uniqueCount;

    /**
     * 消息
     */
    private String message;

    /**
     * 检测语言
     */
    private String detectionLanguage;

    /**
     * 语言置信度
     */
    private Double languageConfidence;

    /**
     * 检测到的语言
     */
    private String detectedLanguage;
} 
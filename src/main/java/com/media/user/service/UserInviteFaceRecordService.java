package com.media.user.service;

import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.common.utils.RedisObjAccess;
import com.media.core.config.SystemConfig;
import com.media.core.id.RedisIdGenerator;
import com.media.core.utils.DeviceIdUtil;
import com.media.core.utils.IpUtil;
import com.media.user.cache.UserInviteCache;
import com.media.user.domain.ClientUserModel;
import com.media.user.domain.UserInviteFaceRecordModel;
import com.media.user.domain.UserInviteRelationModel;
import com.media.user.domain.UserInviteRewardSummaryModel;
import com.media.user.dto.popup.PopupUserInviteInfo;
import com.media.user.dto.request.CompleteTaskRequest;
import com.media.user.dto.request.SetFriendRelationRequest;
import com.media.user.dto.request.TaskLogRequest;
import com.media.user.dto.response.ClientUserResponse;
import com.media.user.dto.response.CompleteTaskResponse;
import com.media.user.enums.*;
import com.media.user.feign.client.MediaTaskClient;
import com.media.user.feign.client.RiskClient;
import com.media.user.mapper.ClientUserMapper;
import com.media.user.mapper.UserInviteFaceRecordMapper;
import com.media.user.mapper.UserInviteRelationMapper;
import com.media.user.mq.dto.XmeReceiveResponse;
import com.media.user.mq.provider.UserSendMessageProvider;
import com.media.user.service.cache.ClientUserCacheService;
import com.xme.xme_base_depends.models.ApiResponse;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.media.common.utils.RedisObjAccess.of;
import static com.media.core.constant.UserRedisKey.RECENT_USER_REWARD_LIST;
import static com.media.core.constant.UserRedisKey.USER_INVITE_REWARD_TOTAL;
import static java.util.concurrent.TimeUnit.DAYS;

@Slf4j
@Service
public class UserInviteFaceRecordService {

    @Autowired
    UserInviteFaceRecordMapper userInviteFaceRecordMapper;

    @Autowired
    UserInviteRelationMapper userInviteRelationMapper;

    @Autowired
    ClientUserMapper clientUserMapper;

    @Autowired
    RedisIdGenerator redisIdGenerator;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @Autowired
    SystemConfig systemConfig;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    UserSendMessageProvider userSendMessageProvider;

    @Autowired
    private UserInviteRelationService userInviteRelationService;

    @Autowired
    private MediaTaskClient mediaTaskClient;

    @Autowired
    private RiskClient riskClient;

    @Autowired
    private DeviceIdUtil deviceIdUtil;

    @Autowired
    private IpUtil ipUtil;

    @Autowired
    private UserInviteRewardSummaryService userInviteRewardSummaryService;

    @Autowired
    private UserInviteCache userInviteCache;

    private RedisObjAccess<Long, List<UserInviteRewardSummaryModel>> inviteTotalRewardCache;

    @PostConstruct
    private void init() {
        // 邀请总奖励缓存
        inviteTotalRewardCache = of(stringRedisTemplate, USER_INVITE_REWARD_TOTAL::of, DAYS.toMillis(5), new TypeReference<>() {});
    }

    // 活体认证发送 xme
    public void faceLiveSendXme(Long uid) {
        UserInviteRelationModel userInviteRelationInfo = getUserInviteRelationInfo(uid);;
        Integer faceXmeAmount = systemConfig.getFaceXmeAmount();
        Integer inviteXmeAmount = systemConfig.getInviteXmeAmount();
        Integer invitedXmeAmount = systemConfig.getInvitedXmeAmount();
        // 无邀请关系，直接发送 xme
        if (userInviteRelationInfo == null || userInviteRelationInfo.getInviteUid() == null) {
            Integer faceType = 1;
            String idempotent = DigestUtils.md5Hex(uid + "-" + faceType + "-" + PointEventEnums.LIVE_AUTH_XME.getEventId());
            boolean flag = this.saveInviteFaceRecord(faceType, null, null, null, uid, faceXmeAmount, "XME", null, null, null, null, RewardTypeEnum.VERIFICATION.getType(), idempotent);
            log.info("saveInviteFaceRecord no invite uid:{},faceType:{}, saveFlag:{} ", uid, faceType, flag);
            // 发送 kafka 消息
            if (flag) {
                userSendMessageProvider.sendFaceGenesisXmeKafka(null, null, uid, faceXmeAmount, idempotent);
            }
        } else {
            // 有邀请关系，邀请人和被邀请人都发送 xme
            Integer faceType = 2;
            Long fromUid = userInviteRelationInfo.getInviteUid();
            userInviteRelationService.clearInviteCountCache(fromUid);
            String idempotent = DigestUtils.md5Hex(fromUid + "-" + uid + "-" + faceType + "-" + PointEventEnums.LIVE_AUTH_XME.getEventId());
            Integer amountInvited = faceXmeAmount + invitedXmeAmount;
            boolean flag = this.saveInviteFaceRecord(faceType, fromUid, inviteXmeAmount, "XME", uid, amountInvited
                    , "XME", null, null, null, null,
                    RewardTypeEnum.OLD_INVITE.getType(), idempotent);
            log.info("saveInviteFaceRecord invite uid:{},faceType:{}, saveFlag:{} ", uid, faceType, flag);
            // 发布 kafka 消息
            if (flag) {
                userSendMessageProvider.sendFaceGenesisXmeKafka(fromUid, inviteXmeAmount, uid, amountInvited, idempotent);
            }
        }
    }

    // 三选一认证奖励发送（手机、邮箱、人脸三选一完成时的奖励）
    public void saveMultiScenarioRewardRecord(Long uid, VerificationScenarioEnum scenario) {
        if (!systemConfig.isNewInviteMode()) {
            if (VerificationScenarioEnum.FACE.equals(scenario)) {
                //老的逻辑保持不变
                faceLiveSendXme(uid);
            }
            return;
        }
        UserInviteRelationModel userInviteRelationModel = getUserInviteRelationInfo(uid);
        if (userInviteRelationModel == null || userInviteRelationModel.getInviteUid() == null) {
            Integer inviteType = 1; // 普通邀请类型
            // 获取三选一认证奖励
            Map<String, Integer> verificationRewards = systemConfig.getMultiCurrencyReward(inviteType, RewardRuleEnum.VERIFICATION);
            Integer faceType = 1; // 认证类型：1-自己认证
            String baseIdempotent = DigestUtils.md5Hex(uid + "-" + faceType + "-" + PointEventEnums.LIVE_AUTH_XME.getEventId());
            // 保存多货币认证奖励记录
            boolean flag = saveMultiCurrencyVerificationRecord(faceType, uid, verificationRewards, RewardTypeEnum.VERIFICATION.getType(), baseIdempotent);
            log.info("saveMultiCurrencyVerificationRecord no invite uid:{}, faceType:{}, saveFlag:{}, rewards:{}", uid, faceType, flag, verificationRewards);
            if (flag) {
                // 发送新的多货币奖励消息
                userSendMessageProvider.sendMultiCurrencyVerificationReward(
                        faceType,           // 认证类型：1-自己认证
                        inviteType,         // 邀请类型：1-普通邀请
                        null,               // 活动ID（普通邀请无活动ID）
                        null,               // 邀请人UID（无邀请关系时为null）
                        null,               // 邀请人奖励（无邀请关系时为null）
                        uid,                // 被邀请人/认证人UID
                        verificationRewards, // 被邀请人/认证人奖励
                        baseIdempotent
                );
                // 根据配置决定是否发送老的kafka消息
                if (systemConfig.isSendLegacyFaceXmeMessage()) {
                    Integer xmeAmount = verificationRewards.getOrDefault("XME", 0);
                    userSendMessageProvider.sendFaceGenesisXmeKafka(null, null, uid, xmeAmount, baseIdempotent);
                }
            }
        } else {
            // 有邀请关系，邀请人获得邀请奖励，被邀请人获得认证奖励+被邀请奖励
            Integer faceType = 2; // 认证类型：2-被邀请认证
            Long fromUid = userInviteRelationModel.getInviteUid();
            Integer inviteType = userInviteRelationModel.getInviteType() != null ? userInviteRelationModel.getInviteType() : 1; // 普通邀请
            //只记录普通邀请
            if (!InviteTypeEnum.INVITE_TYPE_1.getType().equals(inviteType)) {
                return;
            }
            userInviteRelationService.clearInviteCountCache(fromUid);
            inviteTotalRewardCache.delete(fromUid);
            // 获取多货币奖励配置
            Map<String, Integer> inviteRewards = systemConfig.getMultiCurrencyReward(inviteType, RewardRuleEnum.INVITE); // 邀请人奖励
            Map<String, Integer> invitedRewards = systemConfig.getMultiCurrencyReward(inviteType, RewardRuleEnum.INVITED); // 被邀请人奖励
            // 合并被邀请人的认证奖励和被邀请奖励
            Map<String, Integer> toRewards = new HashMap<>();
            // 再添加被邀请奖励
            for (Map.Entry<String, Integer> entry : invitedRewards.entrySet()) {
                String currency = entry.getKey();
                Integer amount = entry.getValue();
                toRewards.put(currency, toRewards.getOrDefault(currency, 0) + amount);
            }
            String baseIdempotent = DigestUtils.md5Hex(fromUid + "-" + uid + "-" + faceType + "-" + PointEventEnums.LIVE_AUTH_XME.getEventId());
            // 保存多货币奖励记录
            boolean flag = saveMultiCurrencyVerificationRecord(faceType, fromUid, uid, inviteRewards, toRewards, RewardTypeEnum.NEW_INVITE.getType(), baseIdempotent);
            log.info("saveMultiCurrencyVerificationRecord with invite uid:{}, faceType:{}, saveFlag:{}, fromRewards:{}, toRewards:{}", uid, faceType, flag, inviteRewards, toRewards);
            if (flag) {
                // 发送新的多货币奖励消息
                Long activityId = userInviteRelationModel.getActivityId(); // 获取活动ID
                userSendMessageProvider.sendMultiCurrencyVerificationReward(
                        faceType,           // 认证类型：2-被邀请认证
                        inviteType,         // 邀请类型
                        activityId,         // 活动ID（如果是活动邀请）
                        fromUid,            // 邀请人UID
                        inviteRewards,      // 邀请人奖励
                        uid,                // 被邀请人/认证人UID
                        toRewards,          // 被邀请人/认证人奖励
                        baseIdempotent
                );
                // 根据配置决定是否发送老的kafka消息
                if (systemConfig.isSendLegacyFaceXmeMessage()) {
                    Integer fromXmeAmount = inviteRewards.getOrDefault("XME", 0);
                    Integer toXmeAmount = toRewards.getOrDefault("XME", 0);
                    userSendMessageProvider.sendFaceGenesisXmeKafka(fromUid, fromXmeAmount, uid, toXmeAmount, baseIdempotent);
                }
            }
        }
    }

    private UserInviteRelationModel getUserInviteRelationInfo(Long uid) {
        QueryWrapper<UserInviteRelationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserInviteRelationModel::getInvitedUid, uid);
        return userInviteRelationMapper.selectOne(queryWrapper);
    }

    /**
     * 保存多货币三选一认证奖励记录（无邀请关系）
     */
    private boolean saveMultiCurrencyVerificationRecord(Integer faceType, Long toUid, Map<String, Integer> toRewards, Integer rewardType, String baseIdempotent) {
        return saveMultiCurrencyVerificationRecord(faceType, null, toUid, new HashMap<>(), toRewards, rewardType, baseIdempotent);
    }

    /**
     * 保存多货币三选一认证奖励记录（有邀请关系）
     */
    private boolean saveMultiCurrencyVerificationRecord(Integer faceType, Long fromUid, Long toUid, Map<String, Integer> fromRewards, Map<String, Integer> toRewards, Integer rewardType, String baseIdempotent) {
        Set<String> allCurrencies = new HashSet<>();
        allCurrencies.addAll(fromRewards.keySet());
        allCurrencies.addAll(toRewards.keySet());
        if (allCurrencies.isEmpty()) {
            log.error("No currencies configured for verification reward, using default XME");
            return false;
        }
        // 按优先级排序货币：XME优先，然后BTC，然后其他
        List<String> sortedCurrencies = allCurrencies.stream().sorted((c1, c2) -> {
            if ("XME".equals(c1) && !"XME".equals(c2)) return -1;
            if (!"XME".equals(c1) && "XME".equals(c2)) return 1;
            if ("BTC".equals(c1) && !"BTC".equals(c2)) return -1;
            if (!"BTC".equals(c1) && "BTC".equals(c2)) return 1;
            return c1.compareTo(c2);
        }).toList();
        // 获取主要货币（第一种货币，确保是XME）
        String primaryCurrency = sortedCurrencies.get(0);
        Integer fromPrimaryAmount = fromRewards.getOrDefault(primaryCurrency, 0);
        Integer toPrimaryAmount = toRewards.getOrDefault(primaryCurrency, 0);
        // 获取第二种货币（确保是BTC，如果存在的话）
        String secondaryCurrency = null;
        Integer fromSecondaryAmount = null;
        Integer toSecondaryAmount = null;
        if (sortedCurrencies.size() > 1) {
            secondaryCurrency = sortedCurrencies.get(1);
            fromSecondaryAmount = fromRewards.getOrDefault(secondaryCurrency, 0);
            toSecondaryAmount = toRewards.getOrDefault(secondaryCurrency, 0);
        }
        return saveInviteFaceRecord(faceType, fromUid, fromPrimaryAmount, primaryCurrency, toUid, toPrimaryAmount, primaryCurrency, fromSecondaryAmount, secondaryCurrency, toSecondaryAmount, secondaryCurrency, rewardType, baseIdempotent);
    }

    public boolean saveInviteFaceRecord(Integer faceType, Long fromUid, Integer fromAmount, String fromAmountSymbol, Long toUid, Integer toAmount, String toAmountSymbol, Integer fromAmount2, String fromAmountSymbol2, Integer toAmount2, String toAmountSymbol2, Integer rewardType, String idempotent) {
        try {
            UserInviteFaceRecordModel inviteFaceRecordModel = new UserInviteFaceRecordModel();
            inviteFaceRecordModel.setRecordId(redisIdGenerator.generate());
            inviteFaceRecordModel.setFaceType(faceType);
            inviteFaceRecordModel.setFromUid(fromUid);
            // 第一种货币
            inviteFaceRecordModel.setFromAmount(fromAmount);
            inviteFaceRecordModel.setFromAmountSymbol(fromAmountSymbol);
            inviteFaceRecordModel.setToUid(toUid);
            inviteFaceRecordModel.setToAmount(toAmount);
            inviteFaceRecordModel.setAmountSymbol(toAmountSymbol);
            // 第二种货币（可选）
            if (fromAmount2 != null || toAmount2 != null) {
                inviteFaceRecordModel.setFromAmount2(fromAmount2);
                inviteFaceRecordModel.setFromAmountSymbol2(fromAmountSymbol2);
                inviteFaceRecordModel.setToAmount2(toAmount2);
                inviteFaceRecordModel.setAmountSymbol2(toAmountSymbol2);
            }
            inviteFaceRecordModel.setRewardType(rewardType);
            inviteFaceRecordModel.setAmountStatus(InviteAmountStatusEnum.INVITE_AMOUNT_STATUS_1.getStatus());
            inviteFaceRecordModel.setIdempotent(idempotent);
            inviteFaceRecordModel.setIsFromPushFront(InvitePushFrontEnum.INVITE_PUSH_FRONT_0.getStatus());
            inviteFaceRecordModel.setIsToPushFront(InvitePushFrontEnum.INVITE_PUSH_FRONT_0.getStatus());
            inviteFaceRecordModel.setCreatedTime(new Date());
            userInviteFaceRecordMapper.insert(inviteFaceRecordModel);
            // 实时更新汇总表
            if (fromAmount != null && fromAmount > 0) {
                userInviteRewardSummaryService.incrementRewardSummary(fromUid, fromAmountSymbol, fromAmount.longValue());
            }
            if (fromAmount2 != null && fromAmount2 > 0 && fromAmountSymbol2 != null) {
                userInviteRewardSummaryService.incrementRewardSummary(fromUid, fromAmountSymbol2, fromAmount2.longValue());
            }
            // 弹窗数据记录
            setPopRecordList(fromUid, fromAmount, fromAmountSymbol, toUid, fromAmount2, fromAmountSymbol2);
            // 记录最近获取到奖励的用户
            addRecentRewardUser(fromUid);
            return true;
        } catch (DuplicateKeyException e1) {
            //主键冲突异常，说明已经发过了
            log.warn("Duplicate key fromUid:{} toUid:{} for idempotent: {}", fromUid, toUid, idempotent);
        } catch (Exception e) {
            log.error("save invite face record error: {}", e.getMessage());
        }
        return false;
    }

    private void addRecentRewardUser(Long fromUid) {
        try {
            if (fromUid == null) {
                return;
            }
            String key = RECENT_USER_REWARD_LIST.of();
            long timestamp = System.currentTimeMillis();
            stringRedisTemplate.opsForZSet().add(key, fromUid.toString(), timestamp);
            Long size = stringRedisTemplate.opsForZSet().size(key);
            if (size != null && size > 2000) {
                stringRedisTemplate.opsForZSet().removeRange(key, 0, size - 2001);
            }
            log.debug("添加用户 {} 到最近奖励ZSET，时间戳: {}", fromUid, timestamp);
        } catch (Exception e) {
            log.error("添加用户到最近奖励ZSET失败: uid={}, error={}", fromUid, e.getMessage(), e);
        }
    }

    private void setPopRecordList(Long fromUid, Integer fromAmount, String fromAmountSymbol, Long toUid, Integer fromAmount2, String fromAmountSymbol2) {
        if (fromUid != null && fromUid > 0) {
            PopupUserInviteInfo popupUserInviteInfo = new PopupUserInviteInfo();
            popupUserInviteInfo.setHostUid(fromUid);
            popupUserInviteInfo.setInviteUid(toUid);
            List<PopupUserInviteInfo.RewardInfo> rewardList = new ArrayList<>();
            if (fromAmount != null && fromAmount > 0) {
                PopupUserInviteInfo.RewardInfo rewardInfo = new PopupUserInviteInfo.RewardInfo();
                rewardInfo.setCcy(fromAmountSymbol);
                rewardInfo.setPrice(BigDecimal.valueOf(fromAmount));
                rewardList.add(rewardInfo);
            }
            if (fromAmount2 != null && fromAmount2 > 0) {
                PopupUserInviteInfo.RewardInfo rewardInfo = new PopupUserInviteInfo.RewardInfo();
                rewardInfo.setCcy(fromAmountSymbol2);
                rewardInfo.setPrice(BigDecimal.valueOf(fromAmount2));
                rewardList.add(rewardInfo);
            }
            popupUserInviteInfo.setRewardInfoList(rewardList);
            userInviteCache.setInviteUserCache(popupUserInviteInfo);
        }
    }

    // 查询未通知给前端的邀请消息（兼容方法，只返回第一种货币）
    public Map<String, Object> queryNoPushRationFromUid(Long uid) {
        ClientUserResponse clientUserResponse = clientUserCacheService.me(uid);
        if (clientUserResponse == null) {
            Map<String, Object> result = new HashMap<>();
            result.put("sum", "0XME");
            result.put("count", 0);
            return result;
        }
        Map<String, Object> countMap = userInviteFaceRecordMapper.countNotPushFromUid(uid);
        BigDecimal sum = new BigDecimal(String.valueOf(countMap.get("sum") == null ? 0 : countMap.get("sum")));
        Integer count = Integer.parseInt(countMap.get("count").toString());
        if (count > 0) {
            Date now = new Date();
            // 将本次弹窗的数据设置成已弹窗
            userInviteFaceRecordMapper.setPushStatusFromUid(uid, now);
            // 将邀请表对应的数据也设置成已弹窗
            userInviteRelationMapper.setPushStatus(uid, now);
        }
        Map<String, Object> result = new HashMap<>();
        result.put("sum", sum + "XME");
        result.put("count", count);
        return result;
    }

    /**
     * 查询未通知给前端的邀请消息（支持多货币）
     *
     * @param uid 用户ID
     * @return 按币种分组的奖励统计
     */
    public Map<String, Object> queryNoPushRationFromUidMultiCurrency(Long uid) {
        ClientUserResponse clientUserResponse = clientUserCacheService.me(uid);
        if (clientUserResponse == null) {
            Map<String, Object> result = new HashMap<>();
            result.put("currencies", new ArrayList<>());
            result.put("totalCount", 0);
            return result;
        }

        List<Map<String, Object>> currencyList = userInviteFaceRecordMapper.countNotPushFromUidByCurrency(uid);
        int totalCount = 0;
        List<Map<String, Object>> currencies = new ArrayList<>();

        for (Map<String, Object> currencyData : currencyList) {
            String currency = (String) currencyData.get("currency");
            BigDecimal sum = new BigDecimal(String.valueOf(currencyData.get("sum") == null ? 0 : currencyData.get("sum")));
            Integer count = Integer.parseInt(currencyData.get("count").toString());

            if (count > 0) {
                Map<String, Object> currencyInfo = new HashMap<>();
                currencyInfo.put("currency", currency);
                currencyInfo.put("sum", sum + currency);
                currencyInfo.put("count", count);
                currencies.add(currencyInfo);
                totalCount += count;
            }
        }

        if (totalCount > 0) {
            Date now = new Date();
            // 将本次弹窗的数据设置成已弹窗
            userInviteFaceRecordMapper.setPushStatusFromUid(uid, now);
            // 将邀请表对应的数据也设置成已弹窗
            userInviteRelationMapper.setPushStatus(uid, now);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("currencies", currencies);
        result.put("totalCount", totalCount);
        return result;
    }

    /**
     * 更新状态为已发送
     *
     * @param xmeReceiveResponse
     */
    public void modifyStatus(XmeReceiveResponse xmeReceiveResponse) {
        QueryWrapper<UserInviteFaceRecordModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("idempotent", xmeReceiveResponse.getIdempotent());
        UserInviteFaceRecordModel recordModel = userInviteFaceRecordMapper.selectOne(queryWrapper);
        if (recordModel != null) {
            userInviteFaceRecordMapper.setInviteAmountStatus(xmeReceiveResponse.getIdempotent(), new Date());
        }

    }

    /**
     * 根据邀请人查询是否有人脸识别并且有积分发放
     *
     * @param fromUid
     */
    public Map<Long, Integer> queryByFromUid(Long fromUid) {
        QueryWrapper<UserInviteFaceRecordModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("from_uid", fromUid);
        List<UserInviteFaceRecordModel> list = userInviteFaceRecordMapper.selectList(queryWrapper);
        if (list == null) {
            return new HashMap<>();
        }

        return list.stream()
                // .filter(record -> record.getAmountStatus() == 2) // 过滤 statue == 2
                .collect(Collectors.toMap(
                        UserInviteFaceRecordModel::getToUid, // Key: id
                        UserInviteFaceRecordModel::getFromAmount // Value: statue
                ));

    }

    public UserInviteFaceRecordModel getInviteXmeStatus(Long fromUid, Long toUid) {
        try {
            QueryWrapper<UserInviteFaceRecordModel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("from_uid", fromUid);
            queryWrapper.eq("to_uid", toUid);
            UserInviteFaceRecordModel userInviteFaceRecord = userInviteFaceRecordMapper.selectOne(queryWrapper);
            return userInviteFaceRecord;
        } catch (Exception e) {
            log.error("getInviteXmeStatus:{}", e.getMessage());
        }
        return null;
    }

    public List<UserInviteFaceRecordModel> getInviteListXmeStatus(Long fromUid, List<Long> toUid) {
        try {
            QueryWrapper<UserInviteFaceRecordModel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("from_uid", fromUid);
            queryWrapper.in("to_uid", toUid);
            return userInviteFaceRecordMapper.selectList(queryWrapper);
        } catch (Exception e) {
            log.error("getInviteXmeStatus:{}", e.getMessage());
        }
        return null;
    }

    // 触发三选一任务完成
    @Async
    public void handleFirstVerification(Long uid) {
        log.info("handleFirstVerification:{}", uid);

        // 重新查询用户信息，获取最新的认证状态
        ClientUserModel user = clientUserMapper.selectById(uid);
        if (user == null) {
            log.warn("用户不存在，无法检查认证状态, uid: {}", uid);
            return;
        }
        if (user.getInviteUid() != null && user.getInviteUid() != 0) {
            // 异步处理所有外部服务调用（好友关系 + 任务 + 风控）
            if (!systemConfig.isNewInviteMode()) {
                if (user.getFaceLivenessStatus() != 1) {
                    return;
                }
            }
            if (isFirstVerification(user)) {
                sendAllTasksAsync(uid, user.getInviteUid());
            }
        }
    }

    /**
     * 检查用户是否为首次完成认证（手机、邮箱、人脸三选一），如果是则触发相应任务
     *
     * @param user 用户
     */
    private boolean isFirstVerification(ClientUserModel user) {
        try {

            // 检查三种认证状态：手机认证、邮箱认证、人脸认证
            boolean phoneVerified = user.getPhoneVerify() != null && user.getPhoneVerify() == 1;
            boolean emailVerified = user.getEmailVerify() != null && user.getEmailVerify() == 1;
            boolean faceVerified = user.getFaceLivenessStatus() == 1;

            // 计算已完成的认证数量
            int verifiedCount = 0;
            if (phoneVerified) verifiedCount++;
            if (emailVerified) verifiedCount++;
            if (faceVerified) verifiedCount++;

            // 如果恰好完成了第一个认证（三选一），则触发任务
            if (verifiedCount == 1) {
                log.info("用户 {} 首次完成认证（手机:{}, 邮箱:{}, 人脸:{}），触发相关任务",
                        user.getUid(), phoneVerified, emailVerified, faceVerified);
                return true;
            } else {
                log.debug("用户 {} 认证状态（手机:{}, 邮箱:{}, 人脸:{}），已完成{}个认证，无需触发任务",
                        user.getUid(), phoneVerified, emailVerified, faceVerified, verifiedCount);
            }
        } catch (Exception e) {
            log.error("检查用户首次认证状态失败, uid: {}, error: {}", user.getUid(), e.getMessage(), e);
        }
        return false;
    }

    /**
     * 异步处理所有外部服务调用（好友关系 + 任务 + 风控）
     *
     * @param uid       用户ID
     * @param inviteUid 邀请人ID
     */
    @Async("threadPoolTaskExecutor")
    public void sendAllTasksAsync(Long uid, Long inviteUid) {
        // 设置好友关系
        setFriendRelation(uid, inviteUid);

        // 发送首次邀请人任务
        sendFirstInviteTask(inviteUid);

        // 发送完成邀请新人的任务
        sendInviteNewUserTask(inviteUid);
    }

    /**
     * 发送首次邀请人任务
     */
    private void sendFirstInviteTask(Long inviteUid) {
        try {
            CompleteTaskRequest request = new CompleteTaskRequest();
            request.setUid(inviteUid);
            request.setTaskCode("10003"); // 首次邀请任务编码
            request.setCompleteTime(System.currentTimeMillis());
            ApiResponse<CompleteTaskResponse> response = mediaTaskClient.completeTask(request);
            if (response != null && response.getSuccess() && response.getResult() != null) {
                log.info("发送首次邀请任务成功, uid:{}, taskCode:10003, getEpValue:{}", inviteUid,
                        response.getResult().getGetEpValue());
            } else {
                log.info("发送首次邀请任务成功, uid:{}, taskCode:10003, 无EP值返回", inviteUid);
            }
        } catch (Exception e) {
            log.error("发送首次邀请任务失败, uid:{}, taskCode:10003, error:{}", inviteUid,
                    e.getMessage(), e);
        }
    }

    /**
     * 发送邀请新人任务
     */
    private void sendInviteNewUserTask(Long inviteUid) {
        try {
            CompleteTaskRequest request = new CompleteTaskRequest();
            request.setUid(inviteUid);
            request.setTaskCode("20006"); // 邀请新人任务编码
            request.setCompleteTime(System.currentTimeMillis());
            ApiResponse<CompleteTaskResponse> response = mediaTaskClient.completeTask(request);
            if (response != null && response.getSuccess() && response.getResult() != null) {
                log.info("发送邀请新人任务成功, uid:{}, taskCode:20006, getEpValue:{}", inviteUid,
                        response.getResult().getGetEpValue());
            } else {
                log.info("发送邀请新人任务成功, uid:{}, taskCode:20006, 无EP值返回", inviteUid);
            }
        } catch (Exception e) {
            log.error("发送邀请新人任务失败, uid:{}, taskCode:20006, error:{}", inviteUid,
                    e.getMessage(), e);
        }
    }

    /**
     * 提交风控完成任务
     */
    private void sendRiskTaskLog(Long uid, Long inviteUid) {
        try {
            String deviceId = deviceIdUtil.getDeviceId();
            String ip = ipUtil.getIpAddr();
            TaskLogRequest taskLogRequest = new TaskLogRequest();
            taskLogRequest.setUser_id(uid);
            taskLogRequest.setService("media-user");
            taskLogRequest.setPath("/user/api/aws/get/session");
            taskLogRequest.setType("20006");
            taskLogRequest.setDevice_id(deviceId);
            taskLogRequest.setIp(ip);
            Map<String, Object> other = new HashMap<>();
            other.put("uid", uid);
            other.put("inviteUid", inviteUid);
            taskLogRequest.setOther(other);
            riskClient.createTaskLog(taskLogRequest);
            log.info("提交风控完成任务成功, uid:{}, inviteUid:{}", uid, inviteUid);
        } catch (Exception e) {
            log.error("提交风控完成任务失败, uid:{}, taskCode:20006, error:{}", uid, e.getMessage(), e);
        }
    }

    /**
     * 设置好友关系
     */
    private void setFriendRelation(Long uid, Long inviteUid) {
        try {
            SetFriendRelationRequest setFriendRelationRequest = new SetFriendRelationRequest();
            setFriendRelationRequest.setUid(inviteUid);
            setFriendRelationRequest.setBeUid(uid);
            ApiResponse<Boolean> setFriendRelationResponse = mediaTaskClient
                    .setFriendRelation(setFriendRelationRequest);
            log.info("设置好友关系成功, uid:{}, inviteUid:{}, setFriendRelationResponse:{}", uid, inviteUid,
                    setFriendRelationResponse);
        } catch (Exception e) {
            log.error("设置好友关系失败, uid:{}, inviteUid:{}, error:{}", uid, inviteUid, e.getMessage(), e);
        }
    }


    public List<UserInviteFaceRecordModel> getInviteRecordList(Long uid, Integer page, Integer size) {
        if (uid == null || page == null || size == null || page <= 0 || size <= 0) {
            return Collections.emptyList();
        }
        int offset = (page - 1) * size;
        LambdaQueryWrapper<UserInviteFaceRecordModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInviteFaceRecordModel::getFromUid, uid)
                .eq(UserInviteFaceRecordModel::getRewardType, RewardTypeEnum.NEW_INVITE.getType())
                .orderByDesc(UserInviteFaceRecordModel::getCreatedTime) // 按创建时间倒序
                .last("LIMIT " + offset + ", " + size);
        return userInviteFaceRecordMapper.selectList(queryWrapper);
    }

    public List<UserInviteFaceRecordModel> getRecentRecordList(Integer size) {
        if (size == null || size <= 0) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<UserInviteFaceRecordModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(
                UserInviteFaceRecordModel::getRewardType, RewardTypeEnum.NEW_INVITE.getType()
        ).orderByDesc(UserInviteFaceRecordModel::getCreatedTime).last("LIMIT " + size);
        return userInviteFaceRecordMapper.selectList(queryWrapper);
    }


    public Long countInviteRecordTotal(Long uid) {
        if (uid == null) {
            return 0L;
        }
        LambdaQueryWrapper<UserInviteFaceRecordModel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserInviteFaceRecordModel::getRewardType, RewardTypeEnum.NEW_INVITE.getType()).eq(UserInviteFaceRecordModel::getFromUid, uid);
        return userInviteFaceRecordMapper.selectCount(queryWrapper);
    }


}

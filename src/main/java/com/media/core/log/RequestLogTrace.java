package com.media.core.log;

public class RequestLogTrace {

    private RequestLogTrace() {
    }

    private static final ThreadLocal<RequestLog> INSTANCE = new ThreadLocal<>();

    public static RequestLog getRequestLog() {
        return INSTANCE.get();
    }

    public static void setRequestLog(RequestLog requestLog) {
        INSTANCE.set(requestLog);
    }

    public static void clear() {
        INSTANCE.remove();
    }

}

package com.media.user.enums;

import lombok.Getter;

/**
 * 早鸟截止日期文案
 */
@Getter
public enum EarlyBirdEndDateEnum {

    EARLY_BIRD_CONTENT_EN_US("en-US", "March ${day}"),
    EARLY_BIRD_CONTENT_ZH_TW("zh-TW", "3月${day}日"),
    EARLY_BIRD_CONTENT_ZH_CN("zh-CN", "3月${day}日"),
    EARLY_BIRD_CONTENT_FR_FR("fr-FR", "Mars ${day}"),
    EARLY_BIRD_CONTENT_DE_DE("de-DE", "März ${day}"),
    EARLY_BIRD_CONTENT_RU_RU("ru-RU", "Март ${day}"),
    EARLY_BIRD_CONTENT_ES_ES("es-ES", "Marzo ${day}"),
    EARLY_BIRD_CONTENT_J<PERSON>_JP("ja-<PERSON>", "3月${day}日"),
    EARLY_BIRD_CONTENT_TH_TH("th-TH", "มีนาคม ${day}"),
    EARLY_BIRD_CONTENT_VI_VN("vi-VN", "Tháng 3 ${day}"),
    EARLY_BIRD_CONTENT_KO_KR("ko-KR", "3월${day}일"),
    EARLY_BIRD_CONTENT_ID_ID("id-ID", "Maret ${day}"),
    EARLY_BIRD_CONTENT_HI_IN("hi-IN", "मार्च ${day}"),
    EARLY_BIRD_CONTENT_UR_PK("ur-PK", "مارچ ${day}"),
    EARLY_BIRD_CONTENT_TR_TR("tr-TR", "Mart ${day}"),
    EARLY_BIRD_CONTENT_AR_SA("ar-SA", "مارس ${day}"),
    EARLY_BIRD_CONTENT_FA_IR("fa-IR", "مارس ${day}"),
    EARLY_BIRD_CONTENT_PT_PT("pt-PT", "Março ${day}"),
    EARLY_BIRD_CONTENT_BN_BD("bn-BD", "মার্চ ${day}"),
    ;

    private final String language;

    private final String content;

    EarlyBirdEndDateEnum(String language, String content) {
        this.language = language;
        this.content = content;
    }

    public static String getContent(String language) {
        for (EarlyBirdEndDateEnum contentEarlyBirdEnum : EarlyBirdEndDateEnum.values()) {
            if (contentEarlyBirdEnum.getLanguage().equals(language)) {
                return contentEarlyBirdEnum.getContent();
            }
        }
        return "";
    }
}

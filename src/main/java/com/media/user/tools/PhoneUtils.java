package com.media.user.tools;

import com.media.core.exception.ApiException;
import com.media.user.enums.SupportPhonePrefixEnum;
import com.media.user.exception.MediaUserExceptionCodeApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PhoneUtils {

    /**
     * 校验手机号的有效性
     * @param countryCode
     * @param phone
     */
    public void checkPhoneRegex(String countryCode, String phonePrefix, String phone) {
        log.info("phone check:{}-{}-{}", countryCode, phonePrefix, phone);
        if(StringUtils.isBlank(countryCode) || StringUtils.isBlank(phonePrefix)){
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_COUNTRY_CODE_ERROR);
        }
        SupportPhonePrefixEnum prefixEnum = SupportPhonePrefixEnum.getPhonePrefixEnum(countryCode, phonePrefix);
        if(prefixEnum == null){
            log.error("phone check regex config error: {}-{}", countryCode, phonePrefix);
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_REGEX_ERROR);
        }

        //手机号格式有误，请检查
        if(!prefixEnum.getPattern().matcher(phonePrefix + phone).matches()){
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_REGEX_ERROR);
        }
    }
}

package com.media.user.scheduler;

import com.media.user.service.ActivityReservationService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class ActivityReservationScheduler {
    @Autowired
    ActivityReservationService activityReservationService;
    /**
     * 推送未推送给推荐系统的用户
     */
    @XxlJob("activityReservationReward")
    public void activityReservationReward() {
        int processedCount = activityReservationService.processReservationsWithoutUid("", 2000);
        log.info("Successfully processed {} reservations", processedCount);
    }
}

package com.media.user.dto.request;


import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DeviceInfoRequest {

    /**
     * 注册id
     */
    @NotBlank
    @Size(max=128)
    private String registrationId;

    /**
     * 设备类型
     */
    @NotBlank
    @Size(max=64)
    private String deviceType;

    /**
     * 平台类型 ANDROID ｜ IOS
     */
    @NotBlank
    @Size(max=64)
    private String platformType;

    /**
     * 系统版本
     */
    @NotBlank
    @Size(max=64)
    private String systemVersion;

    /**
     * 用户ID
     */
    private Long uid;


}

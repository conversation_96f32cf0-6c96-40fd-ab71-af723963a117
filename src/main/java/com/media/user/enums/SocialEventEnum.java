package com.media.user.enums;

import lombok.Getter;

/**
 * 社交事件 eventId
 */
@Getter
public enum SocialEventEnum {

    EVENT_7(7, "绑定twitter账号"),
    EVENT_8(8, "通过身份认证"),
    EVENT_9(9, "关注官方twitter账号"),
    EVENT_10(10, "加入TG社群"),
    ;

    private Integer eventId;
    private String eventName;

    private SocialEventEnum(Integer eventId, String eventName) {
        this.eventId = eventId;
        this.eventName = eventName;
    }
}

package com.media.core.auth;

import com.alibaba.fastjson.JSON;
import com.media.core.request.ClientInfoContext;
import com.media.user.dto.request.UserAuthRefreshTokenRequest;
import com.media.user.dto.response.AuthTokenBaseResponse;
import com.media.user.dto.response.AuthTokenResponse;
import com.media.core.exception.ApiException;
import com.media.user.enums.PlatformEnums;
import com.media.user.service.UserLoginLogService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.concurrent.TimeUnit;

import static com.media.user.constant.MediaUserConstant.TOKEN_ID_PREFIX;

/**
 * 权限AOP切面
 * @version 1.0
 */
@Aspect
@Slf4j
@RequiredArgsConstructor
@Order(1)
@Component
public class ControllerAOP {

//    @Autowired
//    private  RequestMappingHandlerMapping mapping;

    private static final String USER_ID_HEADER = "X-User-ID";

    @Autowired
    UserLoginLogService userLoginLogService;

    private static final int BEAR_LENGTH = 7;

    @Pointcut(value = "execution(* com.media.*.controller..*.*(..))")
    public void cloud() {
    }

    @Pointcut(value = "@within(org.springframework.web.bind.annotation.RestController)")
    public void controller() {
    }

    @Around(value = "cloud() && controller()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        Throwable throwable = null;
        Object res = null;
        try {
            //CloudSession设置值
            // 从请求头中获取用户ID
            String userIdStr = request.getHeader(USER_ID_HEADER);
            if (StringUtils.isNotBlank(userIdStr)) {
                initSession(request);
            }

            String method = request.getMethod();
            // 非网络过来的 不处理
            if(StringUtils.isBlank(method)){
                return joinPoint.proceed();
            }

            MethodSignature sign = (MethodSignature) joinPoint.getSignature();

            // RequireAuth 先不加入auth api管理  先走如下逻辑
            RequireAuth annotation1 = joinPoint.getTarget().getClass().getAnnotation(RequireAuth.class);
            RequireAuth annotation = sign.getMethod().getAnnotation(RequireAuth.class);
            if((annotation1 != null || annotation != null) && !CloudSession.isLogin()){
                throw new ApiException(401);
            }
            try {
                res = joinPoint.proceed();
                return res;
            }catch (Throwable t) {
                throwable = t;
                throw t;
            }
        } finally {
            CloudSession.clear();
        }
    }

    private void initSession(HttpServletRequest request){
        String cloudToken = request.getHeader("Authorization");
        if (cloudToken == null || cloudToken.length() < BEAR_LENGTH) {
            cloudToken = null;
        }else {
            cloudToken = cloudToken.substring(BEAR_LENGTH);
        }
        if (StringUtils.isBlank(cloudToken)) {
            return;
        }

        String userIdStr = request.getHeader(USER_ID_HEADER);
        if (StringUtils.isBlank(userIdStr)) {
            return;
        }
        Long userId = Long.parseLong(userIdStr);
        CloudSession session = new CloudSession()
                .setUid(userId)
                .setToken(cloudToken);

        //记录访问日志
        userLoginLogService.loginLog(userId, "platform-login", PlatformEnums.get(ClientInfoContext.get().getPlatformType()).getCode());

        if (session != null) {
            CloudSession.set(session);
        }
    }
}

package com.media.core.constant;

import com.google.common.base.Preconditions;
import com.media.common.utils.EnumUtils;
import com.media.common.utils.KeyUtil;

/**
 * 统一 Redis key 的生成，方便使用，避免冲突
 */
public enum UserRedisKey {
    USER_INVITE_REWARD_TOTAL("invite:reward:total:summery:"),
    RECENT_USER_REWARD_LIST("recent:reward:list"),
    ;


    static {
        EnumUtils.checkDuplicate(values(), v -> v.prefix);
    }

    private final String prefix;

    UserRedisKey(String prefix) {
        Preconditions.checkArgument(prefix != null && !prefix.isEmpty());
        this.prefix = prefix;
    }

    public String getPrefix() {
        return prefix;
    }

    public String of(Object... suffixes) {
        return KeyUtil.newKey(prefix, ":", suffixes);
    }

    public byte[] ofBytes(Object... suffixes) {
        return of(suffixes).getBytes();
    }
}
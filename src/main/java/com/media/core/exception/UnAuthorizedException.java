package com.media.core.exception;


import org.springframework.http.HttpStatus;

public class UnAuthorizedException extends BaseException {

    public UnAuthorizedException() {
        this(HttpStatus.UNAUTHORIZED.getReasonPhrase());
    }

    public UnAuthorizedException(String message) {
        this(HttpStatus.UNAUTHORIZED.value(), message, null);
    }

    public UnAuthorizedException(String message, Integer code) {
        this(HttpStatus.UNAUTHORIZED.value(), message, code);
    }

    public UnAuthorizedException(Integer status, String message) {
        this(status, message, null);
    }

    public UnAuthorizedException(Integer status, String message, Integer code) {
        super(status, message, code);
    }

    public UnAuthorizedException(Throwable cause) {
        super(cause);
        setStatus(HttpStatus.UNAUTHORIZED.value());
    }

}

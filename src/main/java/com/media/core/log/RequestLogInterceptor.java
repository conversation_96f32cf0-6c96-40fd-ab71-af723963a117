//package com.media.user.log;
//
//import com.media.user.constant.ClientHeaderConstant;
//import com.media.user.constant.ClientInfo;
//import com.media.user.enums.PlatformEnums;
//import com.media.user.request.ClientInfoContext;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletResponse;
//import lombok.extern.slf4j.Slf4j;
//
//import org.springframework.util.StringUtils;
//import org.springframework.web.servlet.HandlerInterceptor;
//
//import java.net.InetAddress;
//import java.util.Enumeration;
//import java.util.LinkedHashMap;
//import java.util.Map;
//import java.util.UUID;
//
//@Slf4j
//public class RequestLogInterceptor implements HandlerInterceptor {
//
//    private final RequestLogProperties requestLogProperties;
//
//    private static String ADDRESS = "127.0.0.1";
//
//    static {
//        try {
//            ADDRESS = InetAddress.getLocalHost().getHostAddress();
//        } catch (Exception e) {
//            log.warn(e.getMessage());
//        }
//    }
//
//    public RequestLogInterceptor(RequestLogProperties requestLogProperties) {
//        this.requestLogProperties = requestLogProperties;
//    }
//
//
//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
//        if (!requestLogProperties.getEnable()) {
//            return true;
//        }
//        String requestURI = request.getRequestURI();
//        String serverUrl = "http://" + ADDRESS + ":" + request.getLocalPort();
//        if (requestURI != null) {
//            serverUrl = serverUrl + requestURI;
//        }
//
//        Map<String, String> headers = new LinkedHashMap<>();
//        headers.put("content-type", "application/x-www-form-urlencoded;charset=UTF-8");
//        Enumeration<String> headerNames = request.getHeaderNames();
//        while (headerNames.hasMoreElements()) {
//            String name = headerNames.nextElement();
//            String value = request.getHeader(name);
//            headers.put(name, value);
//        }
//
//
//        Map<String, String> params = new LinkedHashMap<>();
//        Enumeration<String> parameterNames = request.getParameterNames();
//        while (parameterNames.hasMoreElements()) {
//            String name = parameterNames.nextElement();
//            String value = "";
//            if (!requestLogProperties.getIgnoreQueryNames().contains(name)) {
//                value = request.getParameter(name);
//            }
//            params.put(name, value);
//        }
//
////        String traceId = TraceContext.traceId();
//        String traceId = UUID.randomUUID().toString();
//        log.info("com.media.user.log.RequestLogInterceptor.preHandle traceId:{}", traceId);
//        RequestLog requestLog = new RequestLog()
//                .setUrl(serverUrl)
//                .setMethod(request.getMethod())
//                .setParams(params)
//                .setHeaders(headers)
//                .setTraceId(traceId);
//        if (StringUtils.hasLength(traceId)) {
//            response.addHeader("TRACE-ID", traceId);
//        }
//
//        if (requestLogProperties.getResponseLog()) {
//            requestLog.setResponseLog(new ResponseLog());
//        }
//        RequestLogTrace.setRequestLog(requestLog);
//
//        // 公用请求头信息.
//        String bundle = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_BUNDLE_ID);
//        String deviceId = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_DEVICE_ID);
//        String deviceModel = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_DEVICE_MODEL);
//        String timestamp = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_TIMESTAMP);
//        String platformType = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_PLATFORM_TYPE);
//        String osVersion = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_SYSTEM_VERSION);
//        String version = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_VERSION);
//        String language = request.getHeader(ClientHeaderConstant.ACCEPT_LANGUAGE);
//        String timezone = request.getHeader(ClientHeaderConstant.TIMEZONE);
//        String brandKey = request.getHeader(ClientHeaderConstant.METALPHA_BRAND_KEY);
//
//
//        ClientInfo clientInfo = new ClientInfo()
//                .setBundleId(bundle)
//                .setDeviceId(deviceId)
//                .setDeviceModel(deviceModel)
//                .setTimestamp(timestamp)
//                .setPlatformType(StringUtils.isEmpty(platformType)?"DEFAULT":PlatformEnums.get(platformType).name())
//                .setOsVersion(osVersion)
//                .setVersion(version)
//                .setTimezoneValue(timezone)
//                .setBrandKey(brandKey)
//                .setLanguage(language);
//        ClientInfoContext.set(clientInfo);
//        return true;
//    }
//
//    @Override
//    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
//        if (!requestLogProperties.getEnable()) {
//            return;
//        }
//        RequestLog requestLog = RequestLogTrace.getRequestLog();
//        if (requestLog != null) {
//            String details = RequestLogFormat.formatRequestRestClient(requestLog);
//
//            if (requestLog.getResponseLog() != null) {
//                ResponseLog responseLog = requestLog.getResponseLog();
//                StringBuilder sb = new StringBuilder("\r\n\r\nResponse:\r\n\r\n");
//                if (responseLog.getBody() != null) {
//                    sb.append(RequestLogFormat.toJsonString(responseLog.getBody()));
//                }
//                details = details + sb;
//            }
//
//
////            if (requestLogProperties.getSkywalking()) {
////                String accessToken = request.getHeader("authorization");
////                if (accessToken != null) {
////                    ActiveSpan.tag("Authorization", request.getHeader("authorization"));
////                }
////                ActiveSpan.info(details);
////            }
//
//            if (requestLogProperties.getPrint() || requestLog.isError()) {
//                log.info("\r\n" + details);
//            }
//
//        }
//        RequestLogTrace.clear();
//        ClientInfoContext.clear();
//    }
//
//}

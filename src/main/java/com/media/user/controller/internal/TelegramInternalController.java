package com.media.user.controller.internal;

import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.media.user.dto.response.internal.TelegramUserResponse;
import com.media.user.service.TelegramUserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/user/internal/telegram")
@Slf4j
public class TelegramInternalController {

    @Resource
    private TelegramUserService telegramUserService;

    @GetMapping("/queryTelegramUserByTelegramUserId")
    public List<TelegramUserResponse> queryTelegramUserByTelegramUserId(@RequestParam Long telegramUserId) {
        log.info("queryTelegramUserByTelegramUserId, telegramUserId:{}", telegramUserId);
        return telegramUserService.queryTelegramUserByTelegramUserId(telegramUserId);
    }

}

package com.media.core.utils;

import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

/**
 * Box ID 获取工具类
 */
@Slf4j
@Component
public class BoxIdUtil {

    /**
     * 获取当前请求的 Box ID
     * 
     * @return Box ID，如果获取失败返回空字符串
     */
    public String getBoxId() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = (HttpServletRequest) requestAttributes
                .resolveReference(RequestAttributes.REFERENCE_REQUEST);
        return this.getBoxIdByRequest(request);
    }

    /**
     * 从 HttpServletRequest 中获取 Box ID
     * 
     * @param request HTTP请求对象
     * @return Box ID，如果获取失败返回空字符串
     */
    public String getBoxIdByRequest(HttpServletRequest request) {
        String boxId = null;
        try {
            // 直接从 boxid header 获取
            boxId = request.getHeader("boxid");

            // 如果获取不到或为空，返回空字符串
            if (boxId == null || boxId.length() == 0) {
                boxId = "";
            }

        } catch (Exception e) {
            boxId = "";
            log.error("get box id error", e);
        }

        return boxId;
    }
}

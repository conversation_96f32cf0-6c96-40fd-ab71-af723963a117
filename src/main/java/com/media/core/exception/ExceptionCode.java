package com.media.core.exception;


import jakarta.servlet.http.HttpServletResponse;

public class ExceptionCode {

    private static final ExceptionCode SUCCESS = new ExceptionCode(HttpServletResponse.SC_OK, "Successful");

    private static final ExceptionCode FAILURE = new ExceptionCode(HttpServletResponse.SC_BAD_REQUEST, "Business Exception");

    private static final ExceptionCode UN_AUTHORIZED = new ExceptionCode(HttpServletResponse.SC_UNAUTHORIZED, "Unauthorized");

    private static final ExceptionCode FORBIDDEN = new ExceptionCode(HttpServletResponse.SC_FORBIDDEN, "Forbidden");

    private static final ExceptionCode NOT_FOUND = new ExceptionCode(HttpServletResponse.SC_NOT_FOUND, "404 Not Found");

    private static final ExceptionCode MESSAGE_NOT_READABLE = new ExceptionCode(HttpServletResponse.SC_BAD_REQUEST, "Message Cann't be Read");

    private static final ExceptionCode METHOD_NOT_SUPPORTED = new ExceptionCode(HttpServletResponse.SC_METHOD_NOT_ALLOWED, "Method Is Not Support");

    private static final ExceptionCode MEDIA_TYPE_NOT_SUPPORTED = new ExceptionCode(HttpServletResponse.SC_UNSUPPORTED_MEDIA_TYPE, "Media Type Is Not Supported");

    private static final ExceptionCode PARAM_MISS = new ExceptionCode(HttpServletResponse.SC_BAD_REQUEST, "Missing Required Parameter");

    private static final ExceptionCode PARAM_TYPE_ERROR = new ExceptionCode(HttpServletResponse.SC_BAD_REQUEST, "Parameter Type Mismatch");

    private static final ExceptionCode PARAM_BIND_ERROR = new ExceptionCode(HttpServletResponse.SC_BAD_REQUEST, "Parameter Binding Error");

    private static final ExceptionCode PARAM_VALID_ERROR = new ExceptionCode(HttpServletResponse.SC_BAD_REQUEST, "Paramater Validation Error");

    private static final ExceptionCode INTERNAL_SERVER_ERROR = new ExceptionCode(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Internal Server Error");

    /**
     * http响应状态码.
     */
    private final int status;

    /**
     * 错误信息.
     */
    private final String message;

    /**
     * 业务code码
     */
    private Integer code;

    /**
     * 参数
     */
    private Object[] args;

    private String param;

    private Exception exception;

    public ExceptionCode(String message) {
        this.status = 200;
        this.message = message;
    }

    public ExceptionCode(int status, String message) {
        this.status = status;
        this.message = message;
        this.code = status;
    }

    public ExceptionCode(int status, String message, Integer code) {
        this.status = status;
        this.message = message;
        this.code = code;
    }


    public static ExceptionCode success() {
        return SUCCESS;
    }

    public static ExceptionCode failure() {
        return FAILURE;
    }

    public static ExceptionCode unAuthorized() {
        return UN_AUTHORIZED;
    }

    public static ExceptionCode forbidden() {
        return FORBIDDEN;
    }

    public static ExceptionCode notFound() {
        return NOT_FOUND;
    }

    public static ExceptionCode messageNotReadable() {
        return MESSAGE_NOT_READABLE;
    }

    public static ExceptionCode methodNotSupported() {
        return METHOD_NOT_SUPPORTED;
    }

    public static ExceptionCode mediaTypeNotSupported() {
        return MEDIA_TYPE_NOT_SUPPORTED;
    }

    public static ExceptionCode paramMiss() {
        return PARAM_MISS;
    }

    public static ExceptionCode paramTypeError() {
        return PARAM_TYPE_ERROR;
    }

    public static ExceptionCode paramBindError() {
        return PARAM_BIND_ERROR;
    }

    public static ExceptionCode paramValidError() {
        return PARAM_VALID_ERROR;
    }

    public static ExceptionCode internalServerError() {
        return INTERNAL_SERVER_ERROR;
    }

    public int status() {
        return status;
    }

    public String getMessage() {
        return message;
    }

    public Integer getCode() {
        return code;
    }

    public Exception getException() {
        return exception;
    }

    public void setException(Exception exception) {
        this.exception = exception;
    }

    public Object[] getArgs() {
        return args;
    }

    public void setArgs(Object[] args) {
        this.args = args;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }
}

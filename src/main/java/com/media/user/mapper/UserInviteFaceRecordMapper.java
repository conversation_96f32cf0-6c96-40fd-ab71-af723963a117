package com.media.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.UserInviteFaceRecordModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface UserInviteFaceRecordMapper extends BaseMapper<UserInviteFaceRecordModel> {

    // 原有方法保持兼容（只统计第一种货币）
    @Select("select sum(from_amount) 'sum',count(1) 'count' from user_invite_face_record where from_uid=#{uid} and amount_status=2 and is_from_push_front=0")
    Map<String, Object> countNotPushFromUid(@Param("uid") Long uid);

    // 新增：按币种统计未推送的奖励（支持多货币）
    @Select("SELECT " +
            "from_amount_symbol as currency, " +
            "SUM(from_amount) as sum, " +
            "COUNT(1) as count " +
            "FROM user_invite_face_record " +
            "WHERE from_uid=#{uid} AND amount_status=2 AND is_from_push_front=0 AND from_amount IS NOT NULL " +
            "GROUP BY from_amount_symbol " +
            "UNION ALL " +
            "SELECT " +
            "from_amount_symbol_2 as currency, " +
            "SUM(from_amount_2) as sum, " +
            "COUNT(1) as count " +
            "FROM user_invite_face_record " +
            "WHERE from_uid=#{uid} AND amount_status=2 AND is_from_push_front=0 AND from_amount_2 IS NOT NULL " +
            "GROUP BY from_amount_symbol_2")
    List<Map<String, Object>> countNotPushFromUidByCurrency(@Param("uid") Long uid);

    @Update("UPDATE user_invite_face_record SET is_from_push_front=1, updated_time=#{date}  where from_uid=#{uid} and amount_status=2 and is_from_push_front=0")
    void setPushStatusFromUid(@Param("uid") Long uid, @Param("date") Date date);

    @Update("UPDATE user_invite_face_record SET amount_status=2, updated_time=#{date}  where idempotent=#{idempotent} and amount_status=1")
    void setInviteAmountStatus(@Param("idempotent") String idempotent, @Param("date") Date date);

    /**
     * 批量查询用户邀请人数
     * @param uids 用户ID列表
     * @return 每个用户的邀请人数
     */
    @Select("<script>SELECT from_uid, COUNT(1) as invite_count FROM user_invite_face_record WHERE from_uid IN <foreach item='uid' index='index' collection='uids' open='(' separator=',' close=')'>#{uid}</foreach> GROUP BY from_uid</script>")
    List<Map<String, Object>> batchCountInvites(@Param("uids") List<Long> uids);

    /**
     * 获取用户邀请奖励总和，按币种分组
     * @param uid 用户ID
     * @return 币种和对应的总奖励数量
     */
    @Select("SELECT " +
            "from_amount_symbol as currency, " +
            "SUM(from_amount) as total_amount " +
            "FROM user_invite_face_record " +
            "WHERE from_uid=#{uid} AND amount_status=2 AND from_amount IS NOT NULL " +
            "GROUP BY from_amount_symbol " +
            "UNION ALL " +
            "SELECT " +
            "from_amount_symbol_2 as currency, " +
            "SUM(from_amount_2) as total_amount " +
            "FROM user_invite_face_record " +
            "WHERE from_uid=#{uid} AND amount_status=2 AND from_amount_2 IS NOT NULL " +
            "GROUP BY from_amount_symbol_2")
    List<Map<String, Object>> getTotalInviteAmountByCurrency(@Param("uid") Long uid);

    /**
     * 按from_uid聚合后获取指定数量的记录
     * @param count 要获取的数量
     * @return 聚合后的用户邀请记录
     */
    @Select("SELECT " +
            "from_uid, " +
            "SUM(from_amount_2) as total_amount_2, " +
            "MAX(from_amount_symbol_2) as from_amount_symbol_2, " +
            "MAX(created_time) as latest_time " +
            "FROM user_invite_face_record " +
            "WHERE amount_status = 2 AND from_amount_2 IS NOT NULL " +
            "GROUP BY from_uid " +
            "ORDER BY latest_time DESC " +
            "LIMIT #{count}")
    List<Map<String, Object>> getAggregatedInviteRecords(@Param("count") Integer count);

}
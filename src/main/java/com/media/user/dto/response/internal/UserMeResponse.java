package com.media.user.dto.response.internal;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors
public class UserMeResponse {

    private Long uid;

    private String username;

    private String countryCode;

    private String phonePrefix;

    private String phone;

    private Integer emailVerify;

    private Integer phoneVerify;

    private String email;

    private String language;

    private String avatarUrl;

    private String background;

    private String inviteCode;

    private String nickName;

    private String personIntroduce;

    private Byte status;

    private Integer level;

    // 关注数
    private Long followersNumber;

    // 粉丝数
    private Long fansNumber;

    // 是否(0:否; 1:是)
    private Integer isBindTwitter;

    // 是否是虚拟用户(0:否;1:是- 默认为:0)
    private Byte virtualType;

    // 是否(0:否; 1:是)
    private Byte isNewer;

    // 创世大使挂件 0:无;1:有
    private Integer genesisBadge;

    private Long genesisBadgeTime;

    // 活体认证状态（0未认证，1已认证）
    private int faceLivenessStatus;

    private Date registrationTime;

    private Date lastLoginTime;

    private Date createdTime;

    private Date updatedTime;
    /**
     * 用户是否关注我
     */
    private Byte followState;
    /**
     * 我是否关注用户
     */
    private Byte followedState;

    /**
     * 是否为官方用户 (0:否; 1:是)
     */
    private Integer isOfficial;
}

package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 人脸识别发xme详情信息
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("user_invite_face_record")
@Data
@Accessors(chain = true)
public class UserInviteFaceRecordModel extends Model<UserInviteFaceRecordModel> {

    @TableId(value = "record_id", type = IdType.AUTO)
    private Long recordId;

    /**
     * 邀请类型-1:自己认证;2:被邀请认证, 如果是 1，则只存 toUid 相关的信息
     */
    @TableField("face_type")
    private Integer faceType;

    @TableField("from_uid")
    private Long fromUid;

    @TableField("from_amount")
    private Integer fromAmount;

    @TableField("from_amount_symbol")
    private String fromAmountSymbol;

    @TableField("to_uid")
    private Long toUid;

    @TableField("to_amount")
    private Integer toAmount;

    @TableField("amount_symbol")
    private String amountSymbol;

    @TableField("from_amount_2")
    private Integer fromAmount2;

    @TableField("from_amount_symbol_2")
    private String fromAmountSymbol2;

    @TableField("to_amount_2")
    private Integer toAmount2;

    @TableField("amount_symbol_2")
    private String amountSymbol2;

    /**
     * 
     * @see com.media.user.enums.RewardTypeEnum
     */
    @TableField("reward_type")
    private Integer rewardType;

    /**
     * 代币发送状态-0:初始化;1:已发送待确认;2:已确认
     */
    @TableField("amount_status")
    private Integer amountStatus;

    /**
     * 幂等值 唯一
     */
    @TableField("idempotent")
    private String idempotent;
    /**
     * fromUid 是否推送给用户端-0:未推送;1:已推送
     */
    @TableField("is_from_push_front")
    private Integer isFromPushFront;

    /**
     * toUid 是否推送给用户端-0:未推送;1:已推送
     */
    @TableField("is_to_push_front")
    private Integer isToPushFront;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;


}

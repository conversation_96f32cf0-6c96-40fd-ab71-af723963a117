package com.media.core.exception;


import org.springframework.http.HttpStatus;

public class ForbiddenException extends BaseException {

    public ForbiddenException() {
        this(HttpStatus.FORBIDDEN.getReasonPhrase());
    }

    public ForbiddenException(String message) {
        this(HttpStatus.FORBIDDEN.value(), message, null);
    }

    public ForbiddenException(String message, Integer code) {
        this(HttpStatus.FORBIDDEN.value(), message, code);
    }

    public ForbiddenException(Integer status, String message) {
        this(status, message, null);
    }

    public ForbiddenException(Integer status, String message, Integer code) {
        super(status, message, code);
    }

    public ForbiddenException(Throwable cause) {
        super(cause);
        setStatus(HttpStatus.FORBIDDEN.value());
    }

}

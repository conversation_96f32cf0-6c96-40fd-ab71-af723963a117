package com.media.user.eventbus;


import com.google.common.eventbus.EventBus;
import com.media.core.constant.BeanConstant;
import com.media.user.eventbus.handlers.RegisterEventSyncHandler;
import com.media.user.eventbus.handlers.UserEventKafkaNotifyHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class EventBusConfig {

    @Autowired
    private RegisterEventSyncHandler userEventHandler;
    @Autowired
    private UserEventKafkaNotifyHandler userEventKafkaNotifyHandler;

    @Bean(name = BeanConstant.USER_EVENT_BUS_BEAN)
    public EventBus syncEventBus() {

        EventBus bus = new EventBus((exception,context)->{
            if (exception.getCause() instanceof RuntimeException) {
                throw (RuntimeException) exception.getCause();
            }
            throw new RuntimeException(exception.getCause());
        }); // 默认同步
        bus.register(userEventHandler);
        bus.register(userEventKafkaNotifyHandler);
        return bus;
    }

}

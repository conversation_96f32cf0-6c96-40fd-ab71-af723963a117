package com.media.user.util;

import com.media.user.enums.LanguageEnums;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 多语言图片工具类测试
 */
class MultiLanguageImageUtilTest {

    @Test
    void testGetImageByLanguage_ExactMatch() {
        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("en-US", "english-image.png");
        imageMap.put("zh-CN", "chinese-image.png");
        imageMap.put("ja-JP", "japanese-image.png");

        String result = MultiLanguageImageUtil.getImageByLanguage(
            imageMap, LanguageEnums.zh_CN, "default.png");
        
        assertEquals("chinese-image.png", result);
    }

    @Test
    void testGetImageByLanguage_LanguagePrefixMatch() {
        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("en-US", "english-image.png");
        imageMap.put("zh-CN", "chinese-image.png");

        // zh-TW should fallback to zh-CN
        String result = MultiLanguageImageUtil.getImageByLanguage(
            imageMap, LanguageEnums.zh_TW, "default.png");
        
        assertEquals("chinese-image.png", result);
    }

    @Test
    void testGetImageByLanguage_FallbackToEnglish() {
        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("en-US", "english-image.png");
        imageMap.put("fr-FR", "french-image.png");

        // Korean should fallback to English
        String result = MultiLanguageImageUtil.getImageByLanguage(
            imageMap, LanguageEnums.ko_KR, "default.png");
        
        assertEquals("english-image.png", result);
    }

    @Test
    void testGetImageByLanguage_FallbackToChinese() {
        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("zh-CN", "chinese-image.png");
        imageMap.put("fr-FR", "french-image.png");

        // Korean should fallback to Chinese when no English available
        String result = MultiLanguageImageUtil.getImageByLanguage(
            imageMap, LanguageEnums.ko_KR, "default.png");
        
        assertEquals("chinese-image.png", result);
    }

    @Test
    void testGetImageByLanguage_FallbackToDefault() {
        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("fr-FR", "french-image.png");

        String result = MultiLanguageImageUtil.getImageByLanguage(
            imageMap, LanguageEnums.ko_KR, "default.png");
        
        assertEquals("french-image.png", result);
    }

    @Test
    void testGetImageByLanguage_EmptyMap() {
        Map<String, String> imageMap = new HashMap<>();

        String result = MultiLanguageImageUtil.getImageByLanguage(
            imageMap, LanguageEnums.en_US, "default.png");
        
        assertEquals("default.png", result);
    }

    @Test
    void testGetImageByLanguage_NullMap() {
        String result = MultiLanguageImageUtil.getImageByLanguage(
            null, LanguageEnums.en_US, "default.png");
        
        assertEquals("default.png", result);
    }

    @Test
    void testGetImageByLanguage_NullLanguage() {
        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("en-US", "english-image.png");
        imageMap.put("zh-CN", "chinese-image.png");

        String result = MultiLanguageImageUtil.getImageByLanguage(
            imageMap, null, "default.png");
        
        assertEquals("english-image.png", result); // Should fallback to English
    }

    @Test
    void testGetImageByLanguage_GermanFallbackToEnglish() {
        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("de-DE", "german-image.png");
        imageMap.put("en-US", "english-image.png");

        // de-AT should fallback to de-DE (language prefix match)
        String result = MultiLanguageImageUtil.getImageByLanguage(
            imageMap, LanguageEnums.de_AT, "default.png");

        assertEquals("german-image.png", result);
    }

    @Test
    void testGetImageByLanguage_FrenchFallbackToEnglish() {
        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("en-US", "english-image.png");
        imageMap.put("zh-CN", "chinese-image.png");

        // fr-CA should fallback to en-US (no fr-FR available)
        String result = MultiLanguageImageUtil.getImageByLanguage(
            imageMap, LanguageEnums.fr_CA, "default.png");

        assertEquals("english-image.png", result);
    }

    @Test
    void testGetImageByLanguage_SpanishFallbackToEnglish() {
        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("en-US", "english-image.png");
        imageMap.put("zh-CN", "chinese-image.png");

        // es-MX should fallback to en-US (no es-ES available)
        String result = MultiLanguageImageUtil.getImageByLanguage(
            imageMap, LanguageEnums.es_MX, "default.png");

        assertEquals("english-image.png", result);
    }

    @Test
    void testGetImageByLanguage_JapaneseFallbackToEnglish() {
        Map<String, String> imageMap = new HashMap<>();
        imageMap.put("en-US", "english-image.png");
        imageMap.put("zh-CN", "chinese-image.png");

        // ja-JP should fallback to en-US (non-Chinese language)
        String result = MultiLanguageImageUtil.getImageByLanguage(
            imageMap, LanguageEnums.ja_JP, "default.png");

        assertEquals("english-image.png", result);
    }

    @Test
    void testGetSupportedLanguages() {
        String[] languages = MultiLanguageImageUtil.getSupportedLanguages();
        
        assertEquals(19, languages.length);
        assertEquals("en-US", languages[0]);
        assertEquals("zh-CN", languages[1]);
        assertEquals("bn-BD", languages[18]);
    }
}

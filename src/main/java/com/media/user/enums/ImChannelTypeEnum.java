package com.media.user.enums;

import lombok.Getter;
import org.apache.commons.collections4.MapUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.function.Function.identity;

@Getter
public enum ImChannelTypeEnum {
    PERSON("PERSON"), PERSONS("PERSONS"), GROUP("GROUP"), DEFAULT("DEFAULT");
    private final String type;

    ImChannelTypeEnum(String type) {
        this.type = type;
    }

    private static final Map<String, ImChannelTypeEnum> MAP = Arrays.stream(values()).collect(Collectors.toMap(ImChannelTypeEnum::getType, identity()));

    public static ImChannelTypeEnum form(String type) {
        return MapUtils.getObject(MAP, type, DEFAULT);
    }
}

package com.media.user.service;

import com.media.core.utils.IpUtil;
import com.media.user.feign.client.IpCountryClient;
import com.media.user.feign.vo.IpCountryResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * IP地址位置服务
 * 提供IP地址相关的功能，如判断IP所属国家
 */
@Slf4j
@Service
public class IpLocationService {

    @Autowired
    private IpCountryClient ipCountryClient;

    @Autowired
    IpUtil ipUtil;

    /**
     * 判断IP地址是否为中国IP
     *
     * @param ip IP地址
     * @return 如果是中国IP返回true，否则返回false
     */
    public boolean isChineseIp(String ip) {
        if (StringUtils.isBlank(ip)) {
            return false;
        }

        try {
            IpCountryResponse response = ipCountryClient.getIpCountryInfo(ip);
            return response != null
                    && response.getCode() == 200
                    && response.getResult() != null
                    && "CN".equalsIgnoreCase(response.getResult().getCountry_code());
        } catch (Exception e) {
            log.error("Check Chinese IP error for IP: {}", ip, e);
            return false;
        }
    }

    /**
     * 判断IP地址是否为中国IP
     *
     * @param request request请求
     * @return 如果是中国IP返回true，否则返回false
     */
    public boolean isChineseIp(HttpServletRequest request) {
        String userIP = ipUtil.getIpAddrByRequest(request);
        if (StringUtils.isBlank(userIP)) {
            return false;
        }

        try {
            IpCountryResponse response = ipCountryClient.getIpCountryInfo(userIP);
            return response != null
                    && response.getCode() == 200
                    && response.getResult() != null
                    && "CN".equalsIgnoreCase(response.getResult().getCountry_code());
        } catch (Exception e) {
            log.error("Check Chinese IP error for IP: {}", userIP, e);
            return false;
        }
    }
}

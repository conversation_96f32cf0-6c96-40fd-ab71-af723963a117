package com.media.core.constant;

public interface KafkaTopicConstant {

    /**
     * 用户消费组
     */
    String USER_GROUP = "user_group";

    /**
     * 用户通知消费组
     */
    String MEDIA_USER_EVENT_GROUP = "media_user_event_group";

    /**
     * 获取转盘抽奖消息
     */
    String GET_TURNTABLE_CHANCE = "get_turntable_chance";

    /**
     * 获取挂件消息
     */
    String GET_GENESIS_BADGE = "get_genesis_badge";

    /**
     * 发送关注消息
     */
    String USER_ATTENTION_EVENT = "user_attention_event";

    /**
     * 人脸识别获得xme
     */
    String FACE_GENESIS_XME = "face_genesis_xme";

    /**
     * xme到账状态通知
     */
    String XME_RECEIVE_STATUS = "xme_receive_status";

    /**
     * 用户行为通知(注册、人脸、手机验证，邮箱验证）
     */
    String USER_EVENT_NOTIFY = "user_event_notify";

    /**
     * 被邀请用户注册成功发送消息
     */
    String INVITE_USER_REGISTER_SUCCESS = "invite_user_register_success";

    /**
     * 客户端冷启动事件
     */
    String CLIENT_COLD_START = "client_cold_start";

    /**
     * 发送活动预约的提醒消息
     */
    String LEAD_ORDER_REMIND = "lead_order_remind";

    /**
     * CoreSky 活动奖励XME消息
     */
    String CORESKY_JOIN_SUCCESS = "coresky_award_xme";

    /**
     * 多货币奖励消息（三选一认证奖励）
     */
    String MULTI_CURRENCY_VERIFICATION_REWARD = "multi_currency_verification_reward";

    /**
     * 用户强更升级消息
     */
    String PUSH_MESSAGE_NORMAL = "push_message_normal";

}

package com.media.user.enums;

import lombok.Getter;

@Getter
public enum SignInJobEventEnums {

    /**
     * 签到任务第一天
     */
    SIGN_IN_ONE(22L, 1),
    /**
     * 签到任务第二天
     */
    SIGN_IN_TWO(23L, 2),
    /**
     * 签到第三天
     */
    SIGN_IN_THREE(24L, 3),
    /**
     * 签到第四天
     */
    SIGN_IN_FOUR(25L, 4),

    /**
     * 签到第五天
     */
    SIGN_IN_FIVE(26L, 5),
    /**
     * 签到第六天
     */
    SIGN_IN_SIX(27L, 6),
    /**
     * 签到第七天
     */
    SIGN_IN_SEVEN(28L, 7)
    ;

    private final Long eventId;
    private final Integer sortNumber;

    private SignInJobEventEnums(Long eventId, Integer sortNumber) {
        this.eventId = eventId;
        this.sortNumber = sortNumber;
    }

    public static SignInJobEventEnums getEnumsByEventId(Long eventId){
        for (SignInJobEventEnums pointEventEnums : SignInJobEventEnums.values()){
            if (pointEventEnums.getEventId().equals(eventId)){
                return pointEventEnums;
            }
        }
        return null;
    }

    public static SignInJobEventEnums getEnumsBySortNum(Integer sortNumber){
        for (SignInJobEventEnums pointEventEnums : SignInJobEventEnums.values()){
            if (pointEventEnums.getSortNumber().equals(sortNumber)){
                return pointEventEnums;
            }
        }
        return null;
    }
}

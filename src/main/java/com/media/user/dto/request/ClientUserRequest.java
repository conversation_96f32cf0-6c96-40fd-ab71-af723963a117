package com.media.user.dto.request;

import com.media.core.constant.ValidatorRegexpConstant;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ClientUserRequest {


    private String countryCode;

    private String phonePrefix;

    private String phone;

    @Pattern(regexp = ValidatorRegexpConstant.USERNAME_REGEX, message = "{result.code.10035}")
    private String username;

    @Email
    private String email;

    @Pattern(regexp = ValidatorRegexpConstant.PASSWORD_REGEX, message = "{result.code.10020}")
    @NotBlank
    private String password;

    @Size(max = 6)
    private String emailCode;

    @Size(max = 6)
    private String phoneCode;

    @Size(max = 500)
    private String avatarUrl;

    @Size(max = 10, message = "{result.code.10046}")
    private String inviteCode;

    @Size(max = 32)
    private String nickName;

    @Size(max = 600)
    private String personIntroduce;

    private Byte sourceType;

    /**
     * 钱包 签名 参数
     */
    private String signature;

    private String message;

    private String address;

    private String channel;

    private String channelInviteCode;

    /**
     * 谷歌人机校验 token
     */
    String responseToken;

    /**
     * 扩展字段
     */
    private String extra;

    /**
     * UTM来源标识
     */
    private String utmSource;

    /**
     * UTM活动标识
     */
    private String utmCampaign;
}

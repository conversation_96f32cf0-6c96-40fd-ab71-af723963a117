package com.media.user.dto.response.internal;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors
public class SearchUserResponse {

    private Long uid;

    private String email;

    private String language;


    private String avatarUrl;


    private String inviteCode;


    private String nickName;


    private String personIntroduce;


    private Byte status;

    private Integer level;

    //关注数
    private Long followersNumber;

    //粉丝数
    private Long fansNumber;

    //是否(0:否; 1:是)
    private Integer isBindTwitter;

    // 是否是虚拟用户(0:否;1:是- 默认为:0)
    private Byte virtualType;

    private Date registrationTime;


    private Date lastLoginTime;

    //创世大使挂件 0:无;1:有
    private Integer genesisBadge;

    private Date createdTime;


    private Date updatedTime;

    private Byte followState;
}

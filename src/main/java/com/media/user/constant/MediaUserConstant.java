package com.media.user.constant;

import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;

public interface MediaUserConstant {

    /**
     * 邮件验证码前缀
     */
    String SMS_MAIL_CODE_PREFIX = "sms:code:";

    /**
     * 邮件验验证码 - 发送时间间隔
     */
    String SMS_MAIL_CODE_LIMIT_RATE_PREFIX = "sms:code:limitRate:";

    /**
     * 邮件验验证码 - 发送次数
     */
    String SMS_MAIL_CODE_LIMIT_COUNT_PREFIX = "sms:code:limitCount:";

    /**
     * 短信验证码前缀
     */
    String SMS_PHONE_CODE_PREFIX = "phone:code:";

    /**
     * 短信验证码 - 发送时间间隔
     */
    String SMS_PHONE_CODE_LIMIT_RATE_PREFIX = "phone:code:limitRate:";

    /**
     * 短信验证码 - 发送次数
     */
    String SMS_PHONE_CODE_LIMIT_COUNT_PREFIX = "phone:code:limitCount:";

    /**
     * 邀请码长度
     */
    Integer INVITE_CODE_LENGTH = 6;

    /**
     * 用户名长度
     */
    Integer NICK_NAME_LENGTH = 10;

    /**
     * 密码长度
     */
    Integer PASSWORD_LENGTH = 16;

    /**
     * 用户邀请前缀
     */
    String USER_INVITE_REDIS_KEY_PREFIX = "user:invite:";

    /**
     * 用户邀请排行榜
     */
    String USER_INVITE_RANKING_REDIS_KEY = "user:invite:ranking";

    /**
     * 用户个人信息前缀
     */
    String USER_PERSONINFO_REDIS_KEY_PREFIX = "user:personInfo:";
    /**
     * 用户的 following list
     */
    String USER_FOLLOWING_PREFIX = "user:following:list:";
    /**
     * 用户的 fans count
     */
    String USER_FANS_PREFIX = "user:fans:list:";

    /**
     * 用户的登录日志
     */
    String USER_LOGIN_LOG_PREFIX = "user:login_log:";

    /**
     * 用户登录错误次数
     */
    String USER_LOGIN_ERROR_TIMES_PREFIX = "user:login:error:times:";

    /**
     * 用户token前缀
     */
    String TOKEN_ID_PREFIX = "auth:users:token:";

    /**
     * 用户bin五小时只能pin一次
     */
    String USER_BIN_LIMIT = "user:bin:limit:";

    /**
     * 记录用户是否已经被发送过 pin
     */
    String USER_PIN_STATUS = "user:pin:push:status:";

    /**
     * 超时时间
     */
    Long timeout = 30L;

    /**
     * 时间类型
     */
    TimeUnit timeUnit = TimeUnit.DAYS;

    /**
     * 用户协议配置
     */
    String USER_CONFIG = "user.config";

    /**
     * 用户加权配置
     */
    String USER_ADDITION_CONFIG = "user:addition:config";

    /**
     * 日期时间格式
     */
    String dateTimeFormat = "yyyy-MM-dd HH:mm:ss";

    /**
     * 日期格式
     */
    String dateFormat = "yyyy-MM-dd";

    /**
     * 二级最多拥有的人数
     */
    Integer L2_MAX_NUM = 2000;

    /**
     * 二级花了钱的最大人数
     */
    Integer L2_VIP_MAX_NUM = 2000;

    /**
     * 根ID
     */
    Long rootId = 0l;

    /**
     * 创建家族的起始人数
     */
    Integer L2_MIN_NUM = 2;

    Integer ZERO = 0;

    /**
     * 更新家族资产锁
     */
    String FAMILY_ASSETS_PREFIX = "family:assets:";
    /**
     * xme的状态更新
     */
    String XME_RECEIVE_STATUS = "xme:receive:status:";

    /**
     * 接口幂等处理
     */
    String FAMILY_ASSETS_REPEAT_PREFIX = "family:assets:repeat:";

    /**
     * 用户资产接口幂等处理
     */
    String FAMILY_ASSETS_TOKEN_REPEAT_PREFIX = "family:assets:token:repeat:";

    /**
     * 更新家族资产代币锁
     */
    String FAMILY_ASSETS_TOKEN_PREFIX = "family:assets:token:";

    /**
     * 道具
     */
    String PROPS_PREFIX = "props:";

    Long longZero = 0L;

    Integer pageSize = 1000;

    String USER_ADDRESS = "user:address:";

    String USER_RECEIVE_LIST = "user:receive:list";

    /**
     * 给前族长分成
     */
    BigDecimal KING_DIViDE = new BigDecimal("0.5");

    /**
     * 直接上级分成
     */
    BigDecimal DIRECT_DIViDE = new BigDecimal("0.1");

    String USER_FAMILY_PREFIX = "user_family_";

    /**
     * CoreSky 活动奖励领取记录前缀
     */
    String CORESKY_REWARD_PREFIX = "coresky:reward:";

    String SUPER_CODE = "666666";

    String IM_MSG_DAILY_PREFIX = "msg:daily:";

    String IM_NONFOLLOW_DAILY_PREFIX = "nonfollow:daily:";

    String TASK_CODE_PHONE_VERIFY = "10005";
    String TASK_CODE_EMAIL_VERIFY = "10006";

    String USER_INVITE_POPUP_TYPE = "user:invite:popup:type:%s";

    String USER_INVITE_LIST = "user:invite:list:%s";

    /**
     * btc弹窗国际化文案key
     */
    String POPUP_BITCOIN_TITLE_TYPE_I18N_KEY = "popup.bitcoin.title.type%s";
    String POPUP_BITCOIN_CONTENT_TYPE_I18N_KEY = "popup.bitcoin.content.type%s";
    String POPUP_BITCOIN_BUTTON_TYPE_I18N_KEY = "popup.bitcoin.button.type%s";
    String POPUP_BITCOIN_INVITE_USER_NOTICE_TITLE_I18N_KEY = "popup.bitcoin.invite.user.notice.title";
    String POPUP_BITCOIN_INVITE_USER_NOTICE_CONTENT_I18N_KEY = "popup.bitcoin.invite.user.notice.content";

    /**
     * 奖励到账通知文案
     */
    String REWARD_RECEIVED_MESSAGE_I18N_KEY = "reward.received.message";

    /**
     * 邀请标题文案
     */
    String INVITE_TITLE_I18N_KEY = "invite.title";

    /**
     * 邀请副标题文案
     */
    String INVITE_SUBTITLE_I18N_KEY = "invite.subtitle";

    /**
     * IM 内容提示文案
     */
    String IM_CONTENT_TIPS_I18N_KEY = "im.content.tip.%d";

    /**
     * 强更奖励用户去重
     */
    String LOGIN_VERSION_CHECK = "login:version:check:";

    /**
     * 强更奖励定时任务ID
     */
    String LAST_PROCESSED_ID_KEY = "last:processed:id:key";
}

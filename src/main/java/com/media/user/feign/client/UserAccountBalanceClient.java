package com.media.user.feign.client;

import com.media.user.dto.request.GetUserBalanceRequest;
import com.media.user.dto.request.SignupRequest;
import com.media.user.dto.request.UserTransferAccountRequest;
import com.xme.xme_base_depends.models.ApiResponse;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.TimeUnit;

@FeignClient(url = "${point.url:http://envtest-api.x.me}", value = "userAccountBalanceClient", configuration = UserAccountBalanceClient.UserAccountBalanceClientConfiguration.class)
public interface UserAccountBalanceClient {

    @Configuration
    class UserAccountBalanceClientConfiguration {
        @Bean("userAccountBalanceClientOptions")
        public Request.Options options() {
            // Set connect timeout to 200ms and read timeout to 2000ms
            return new Request.Options(200, TimeUnit.MILLISECONDS, 2000, TimeUnit.MILLISECONDS, true);
        }
    }

    @PostMapping(value = "/internal/account/create")
    ApiResponse createUserAccountBalance(@RequestBody SignupRequest request);

    /**
     * 转账
     *
     * @param request
     */
    @PostMapping("/internal/account/transfer")
    ApiResponse<Long> transferAccount(@RequestBody UserTransferAccountRequest request);

    /**
     * 批量转账
     */
    @PostMapping("/internal/account/transferBatch")
    ApiResponse<Long> transferBatch(@RequestBody List<UserTransferAccountRequest> requests);

    /**
     * 获取用户的挖XME的总量 废弃
     */
    @PostMapping("/internal/account/obtainToken")
    ApiResponse<BigDecimal> obtainToken(@RequestBody SignupRequest request);

    /**
     * 获取用户的挖XME的总量
     */
    @PostMapping("/internal/account/obtainBalance")
    ApiResponse<BigDecimal> obtainBalance(@RequestBody SignupRequest request);

    /**
     * 获取用户当日的mint
     */
    @PostMapping("/internal/account/userEveryDayMint")
    ApiResponse<BigDecimal> userEveryDayMint();


    /**
     * 获取用户的balance总数量
     */
    @PostMapping("/internal/account/getTotalUSDT")
    ApiResponse<BigDecimal> getTotalUSDT(@RequestBody GetUserBalanceRequest balanceRequest);


}

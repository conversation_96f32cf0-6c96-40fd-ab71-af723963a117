package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("bee_user")
@Data
@Accessors(chain = true)
public class BeeUserModel extends Model<BeeUserModel> {

    @TableId(value = "bee_uid")
    private String beeUid;
    /**
     * 用户ID
     */
    @TableField("uid")
    private Long uid;
    /**
     * 邮箱用户名
     */
    @TableField("email")
    private String email;
    /**
     * 头像链接
     */
    @TableField("avatar_url")
    private String avatarUrl;

    /**
     * 昵称
     */
    @TableField("nick_name")
    private String nickName;

    /**
     * 钱包地址
     */
    @TableField("address")
    private String address;

    /**
     * appid
     */
    @TableField("app_id")
    private String appId;

    /**
     * appid
     */
    @TableField("client_info")
    private String clientInfo;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private Date createdTime;
    /**
     * 更新时间
     */
    @TableField("updated_time")
    private Date updatedTime;
}
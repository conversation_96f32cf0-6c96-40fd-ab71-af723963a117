package com.media.user.controller.api;


import com.media.user.service.ClientUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Deprecated
@RestController
@RequestMapping("/user/api/early")
public class EarlyBirdSwitchController {

    @Autowired
    private ClientUserService clientUserService;


    @RequestMapping("/bird")
    public Boolean earlyBirdSwitch() {
        return clientUserService.earlyBirdSwitch();
    }
}

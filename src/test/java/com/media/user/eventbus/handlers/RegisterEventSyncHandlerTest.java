package com.media.user.eventbus.handlers;

import com.alibaba.fastjson2.JSON;
import com.media.user.dto.request.internal.RegisterCodeInfoRequest;
import com.media.user.dto.response.internal.InviteCodeParseResult;
import com.media.user.dto.response.internal.RegisterCodeInfoResponse;
import com.media.user.feign.client.GameClient;
import com.xme.xme_base_depends.enums.UserBehaviorEventEnum;
import com.xme.xme_base_depends.models.ApiResponse;
import com.xme.xme_base_depends.mq.message.UserBehaviorEvent;
import com.xme.xme_base_depends.mq.message.UserEventMessage;
import com.xme.xme_base_depends.mq.message.UserRegisterMqDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@SpringBootTest
@Slf4j
public class RegisterEventSyncHandlerTest {


    @Autowired
    private RegisterEventSyncHandler registerEventSyncHandler;
//
//    @InjectMocks
//    private RegisterEventSyncHandler registerEventSyncHandler;


    @Autowired
    private GameClient gameClient;

    @Test
    public void testHandleRegisterEvent() {
        // 准备测试数据
        String json="{\n" +
                "\t\t\"avatarUrl\": \"https://s3.x.me/avatars/Avatars00-3.png\",\n" +
                "\t\t\"channelId\": 0,\n" +
                "\t\t\"createdTime\": 1753428716956,\n" +
                "\t\t\"email\": \"<EMAIL>\",\n" +
                "\t\t\"faceLivenessStatus\": 0,\n" +
                "\t\t\"inviteCode\": \"00000D001\",\n" +
                "\t\t\"ipAddress\": \"**************\",\n" +
                "\t\t\"language\": \"zh-CN\",\n" +
                "\t\t\"nickName\": \"1235ffs5d321\",\n" +
                "\t\t\"registrationTime\": 1753428716956,\n" +
                "\t\t\"sourceType\": 0,\n" +
                "\t\t\"status\": 1,\n" +
                "\t\t\"type\": 1,\n" +
                "\t\t\"uid\": 11130168903734,\n" +
                "\t\t\"userBehaviorEventType\": [\n" +
                "\t\t\t\"REGISTERED\"\n" +
                "\t\t],\n" +
                "\t\t\"userEventType\": \"REGISTER\",\n" +
                "\t\t\"userId\": 11130168903734,\n" +
                "\t\t\"username\": \"1235ffs5d321\"\n" +
                "\t}";
        UserRegisterMqDTO userRegisterMqDTO= JSON.parseObject(json, UserRegisterMqDTO.class);


        System.out.println(userRegisterMqDTO.toString());

        registerEventSyncHandler.handle(userRegisterMqDTO);



    }



@Test
    public void testGetInfo(){

        String code= "00000001";
        RegisterCodeInfoRequest request = new RegisterCodeInfoRequest().setCode(code);
        ApiResponse<RegisterCodeInfoResponse>  resp=gameClient.getRegisterCodeInfo(request);

        System.out.println(resp);
        System.out.println( resp.getResult());
    }


    @Test
    public void testParseInfo(){

        String code= "000022001";

        ApiResponse<InviteCodeParseResult>   resp=gameClient.parseInviteCode(code);

        System.out.println(resp);
        System.out.println( resp.getResult());
    }
}
package com.media.user.enums;

import lombok.Getter;

/**
 * 支持的语言 - 需要确认 TODO 暂时不用
 */
@Getter
public enum SupportLanguageEnum {

    English("美国", "en-US", "English", "英语", "English", 1, ""),
    Chinese_TW("中国", "zh-TW", "中文 (简体)", "中文（简体）", "Chinese (Simplified)", 2, ""),
    Chinese_CN("中国（繁体）", "zh-CN", "中文 (繁體)", "中文（繁体）", "Chinese (Traditional)", 3, ""),
    Japanese("日本", "fr-FR", "日本語", "日语", "Japanese", 4, ""),
    German("德国", "de-DE", "Deutsch", "德语", "German", 5, ""),
    French("法国", "ru-RU", "Français", "法语", "French", 6, ""),
    Spanish("西班牙", "es-ES", "Español", "西班牙语", "Spanish", 7, ""),
    Italian("意大利", "ja-JP", "Italiano", "意大利语", "Italian", 8, ""),
    Korean("韩国", "th-TH", "한국어", "韩语", "Korean", 9, ""),
    Russian("俄罗斯", "vi-VN", "Русский", "俄语", "Russian", 10, ""),
    Hindi("印度", "ko-KR", "हिंदी", "印地语", "Hindi", 11, ""),
    Portuguese("巴西", "id-ID", "Português", "葡萄牙语", "Portuguese", 12, ""),
//    Spanish("墨西哥", "hi-IN", "Español", "西班牙语", "Spanish", 13, ""),
    Arabic("沙特阿拉伯", "ur-PK", "العربية", "阿拉伯语", "Arabic", 14, ""),
    Thai("泰国", "tr-TR", "ไทย", "泰语", "Thai", 15, ""),
    Vietnamese("越南", "ar-SA", "Tiếng Việt", "越南语", "Vietnamese", 16, ""),
    Turkish("土耳其", "fa-IR", "Türkçe", "土耳其语", "Turkish", 17, ""),
    Indonesian("印度尼西亚", "pt-PT", "Bahasa Indonesia", "印度尼西亚语", "Indonesian", 18, ""),
    Persian("伊朗", "pt-PT", "فارسی", "波斯语", "Persian", 19, ""),
    Bengali("孟加拉国", "bn-BD", "বাংলা", "孟加拉语", "Bengali", 20, ""),
    ;

    private String country;  //国家
    private String languageName;  //语言
    private String languageLocal; //当地语种
    private String chineseName;  //中文名
    private String englishName;  //英文名
    private Integer sortBy;      //排序
    private String nationalFlagIcon;  //国旗 icon

    private SupportLanguageEnum(String country, String languageName, String languageLocal,
                                String chineseName, String englishName, Integer sortBy,
                                String nationalFlagIcon){
        this.country = country;
        this.languageName = languageName;
        this.languageLocal = languageLocal;
        this.chineseName = chineseName;
        this.englishName = englishName;
        this.sortBy = sortBy;
        this.nationalFlagIcon = nationalFlagIcon;
    }
}

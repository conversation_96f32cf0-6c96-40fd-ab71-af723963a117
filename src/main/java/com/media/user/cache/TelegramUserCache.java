package com.media.user.cache;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.media.user.domain.TelegramUserModel;
import com.media.user.mapper.TelegramUserMapper;
import jakarta.annotation.Resource;

import java.util.List;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Component
public class TelegramUserCache extends ServiceImpl<TelegramUserMapper, TelegramUserModel> {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private TelegramUserMapper telegramUserMapper;

    /**
     * 根据Telegram用户ID查询用户
     * @param telegramUserId
     * @return
     */
    public List<TelegramUserModel> getByTelegramUserId(Long telegramUserId) {
        // String key = "telegram:user:list:" + telegramUserId;
        // String value = stringRedisTemplate.opsForValue().get(key);
        // if (value != null) {
        //     return JSON.parseArray(value, TelegramUserModel.class);
        // }
        List<TelegramUserModel> telegramUsers = telegramUserMapper.getByTelegramUserId(telegramUserId);
        // if (!CollectionUtils.isEmpty(telegramUsers)) {
        //     stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(telegramUsers), 1, TimeUnit.DAYS);
        // }
        return telegramUsers;
    }

    /**
     * 根据用户ID查询Telegram用户
     * 
     * @param uid
     * @return
     */
    public List<TelegramUserModel> getByUid(Long uid) {
        // String key = "telegram:user:list:" + uid;
        // String value = stringRedisTemplate.opsForValue().get(key);
        // if (value != null) {
        //     return JSON.parseArray(value, TelegramUserModel.class);
        // }
        List<TelegramUserModel> telegramUsers = telegramUserMapper.getByUid(uid);
        // if (!CollectionUtils.isEmpty(telegramUsers)) {
        //     stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(telegramUsers), 1, TimeUnit.DAYS);
        // }
        return telegramUsers;
    }

    /**
     * 插入Telegram用户
     * 
     * @param telegramUser
     * @return
     */
    public int insertTelegramUser(TelegramUserModel telegramUser) {
        int count = telegramUserMapper.insertTelegramUser(telegramUser);
        // if (count > 0) {
        //     String key = "telegram:user:" + telegramUser.getUid() + ":" + telegramUser.getTelegramUserId();
        //     stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(telegramUser), 1, TimeUnit.DAYS);
        // }
        return count;
    }
    
}

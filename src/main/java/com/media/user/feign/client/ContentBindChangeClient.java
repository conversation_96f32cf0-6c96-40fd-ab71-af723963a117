package com.media.user.feign.client;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class ContentBindChangeClient {

    @Value(value = "${content.bindChangeUri}")
    private String contentUri;

    /**
     * 发送绑定关系变更
     */
    @Async("threadPoolTaskExecutor")
    public void sendBindChange(long oldUserId, long newUserId) {
        Map<String, Long> body = new HashMap<>();
        body.put("oldUserId", oldUserId);
        body.put("newUserId", newUserId);
        log.info("调用内容系统发送绑定关系变更。。。");
        HttpRequest httpRequest = new HttpRequest(UrlBuilder.of(contentUri + "/internal/user/change"));
        httpRequest.method(Method.POST);
        httpRequest.body(JSONObject.toJSONString(body));
        try (HttpResponse httpResponse = httpRequest.execute()) {
            if (httpResponse.isOk()) {
                String responseBoy = httpResponse.body();
                log.info("调用内容系统发送绑定关系变更: {}", responseBoy);
            }
        }
    }

}

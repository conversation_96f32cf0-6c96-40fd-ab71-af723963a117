package com.media.core.utils;

import at.favre.lib.crypto.bcrypt.BCrypt;
import com.media.user.enums.PasswordVersionEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class PasswordUtils {

    public String bcryptHash(String password, Byte passwordVersion) {
        //加密密码
        String md5Password = DigestUtils.md5Hex(password);
        if(passwordVersion.intValue() == PasswordVersionEnum.PASSWORD_VERSION_1.getState().intValue()){
            return md5Password;
        }else{
            return BCrypt.with(BCrypt.Version.VERSION_2Y).hashToString(10, md5Password.toCharArray());
        }
    }

    public boolean checkPassword(String password, String encryptPassword, Byte passwordVersion) {
        String md5Password = DigestUtils.md5Hex(password);
        if(passwordVersion.intValue() == PasswordVersionEnum.PASSWORD_VERSION_1.getState().intValue()){
            if(encryptPassword.equals(md5Password)){
                return true;
            }
            return false;
        }else{
            BCrypt.Result result = BCrypt.verifyer().verify(md5Password.toCharArray(), encryptPassword);
            if (result.verified) {
                return true;
            }
            return false;
        }
    }

}

package com.media.core.exception;


import org.springframework.http.HttpStatus;

public class ServiceException extends BaseException {

    public ServiceException() {
        this(HttpStatus.FORBIDDEN.getReasonPhrase());
    }

    public ServiceException(String message) {
        this(HttpStatus.FORBIDDEN.value(), message, null);
    }

    public ServiceException(String message, Integer code) {
        this(HttpStatus.FORBIDDEN.value(), message, code);
    }

    public ServiceException(Integer status, String message) {
        this(status, message, null);
    }

    public ServiceException(Integer status, String message, Integer code) {
        super(status, message, code);
    }

    public ServiceException(Throwable cause) {
        super(cause);
        setStatus(HttpStatus.FORBIDDEN.value());
    }

}

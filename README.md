# Media User 项目

## 项目简介

这是一个基于Spring Boot的用户管理系统，主要用于处理媒体平台的用户相关功能，包括用户注册、登录、活动参与、家族系统等。

## 核心功能

### 用户注册系统

系统提供多种注册方式，支持邮箱、手机号、钱包地址等多种注册渠道，具有完善的验证和安全机制。

#### 注册方式

1. **邮箱注册**: 邮箱 + 密码 + 邮箱验证码
2. **手机号注册**: 手机号 + 密码 + 短信验证码
3. **钱包地址注册**: 钱包地址 + 签名验证
4. **第三方登录**: Google等第三方平台自动注册

#### 注册接口

| 接口路径                           | 请求方法 | 说明                       | 控制器位置               |
| ---------------------------------- | -------- | -------------------------- | ------------------------ |
| `/user/api/client/register`        | POST     | 移动端基础注册             | ClientUserApiController  |
| `/user/api/client/v1/register`     | POST     | 移动端新版注册（含验证码） | ClientUserApiController  |
| `/user/api/web/register`           | POST     | Web端注册                  | WebUserController        |
| `/user/api/address/register`       | POST     | 钱包地址注册               | UserAddressApiController |
| `/user/api/client/register/config` | GET      | 注册配置信息               | ClientUserApiController  |

#### 核心服务类

- **UserRegisterService**: 用户注册核心业务逻辑
  - `registerWithVerification()`: 完整注册流程（含验证码校验）
  - `register()`: 基础注册方法
  - `registerByEmail()`: 邮箱注册处理
  - `registerByPhone()`: 手机号注册处理

#### 安全特性

- ✅ 图形验证码防护
- ✅ 邮箱/短信验证码验证
- ✅ 邀请码系统
- ✅ 人脸识别验证（邀请人）
- ✅ 密码bcrypt加密
- ✅ 重复注册检查
- ✅ IP地址记录
- ✅ 设备信息绑定

#### 注册流程

```mermaid
flowchart TD
    A[用户提交注册] --> B[版本检查]
    B --> C[人机验证]
    C --> D[验证码校验]
    D --> E[生成用户ID]
    E --> F[邀请码处理]
    F --> G{注册方式}
    G -->|邮箱| H[邮箱注册]
    G -->|手机号| I[手机号注册]
    H --> J[创建钱包账户]
    I --> J
    J --> K[同步到ES]
    K --> L[推荐系统]
    L --> M[登录日志]
    M --> N[设备绑定]
    N --> O[生成Token]
    O --> P[注册完成]
```

### EventBus 事件总线系统

项目采用Google Guava EventBus实现事件驱动架构，用于处理用户行为事件的异步解耦。

#### 事件总线架构

- **配置类**: `EventBusConfig.java` - 创建同步EventBus并注册处理器
- **上下文管理**: `RegisterCodeContext.java` - 管理处理器间数据传递
- **异常处理**: 统一的异常处理机制，确保系统稳定性

#### 事件处理器

| 处理器                        | 优先级    | 功能                                             | 位置               |
| ----------------------------- | --------- | ------------------------------------------------ | ------------------ |
| `RegisterEventSyncHandler`    | @Order(1) | 注册事件同步处理、游戏系统同步、**邀请消息发送** | eventbus/handlers/ |
| `UserEventKafkaNotifyHandler` | @Order(2) | 发送事件到Kafka异步队列                          | eventbus/handlers/ |

#### 支持的事件类型

- **REGISTERED**: 用户注册完成事件
- **FACE_FINISH**: 人脸验证完成事件  
- **PHONE_FINISH**: 手机验证完成事件
- **EMAIL_FINISH**: 邮箱验证完成事件

#### 事件流程

```mermaid
sequenceDiagram
    participant U as 用户行为
    participant E as EventBus
    participant S as SyncHandler
    participant K as KafkaHandler
    participant Q as Kafka队列
    participant C as 消费者
    
    U->>E: 触发事件
    E->>S: @Order(1) 同步处理
    S->>S: 验证注册码
    S->>S: 同步游戏系统
    E->>K: @Order(2) Kafka处理
    K->>Q: 发送消息
    Q->>C: 异步消费
    C->>C: 执行奖励逻辑
```

#### 事件发布点

- **注册服务**: `UserRegisterService` - 用户注册完成后发布
- **验证服务**: `UserSecretService` - 手机/邮箱验证完成后发布  
- **人脸服务**: `AwsFaceService` - 人脸识别完成后发布
- **内部接口**: `FaceInternalController` - 外部人脸验证通知

#### 技术特性

- ✅ 同步+异步双重处理机制
- ✅ ThreadLocal上下文管理
- ✅ 自动异常处理和恢复
- ✅ 事件处理器优先级控制
- ✅ Kafka消息队列集成
- ✅ 内存泄露防护

### Rongyun 融云客户端

项目实现了融云REST API的完整Feign客户端，支持私聊消息发送等功能。

#### 客户端架构

- **Feign接口**: `RongcloudClient.java` - 定义融云API接口，使用@RequestHeader处理动态请求头
- **签名处理**: 在服务层直接调用项目已有的`CallbackSignatureVerifier.generateSignature`方法
- **表单编码器**: `FormEncoder.java` - 对象转form-urlencoded格式
- **服务封装**: `RongcloudMessageService.java` - 业务逻辑封装

#### 支持的功能

- **私聊消息发送**: 支持文本消息、自定义消息类型
- **自动签名**: SHA1(AppSecret + Nonce + Timestamp)算法
- **表单编码**: 自动转换为application/x-www-form-urlencoded格式
- **错误处理**: 完善的异常处理和重试机制

#### 配置要求

在Apollo配置中心添加：
```properties
# 融云基础配置
rongcloud.host=https://api.sg-light-api.com
rongcloud.appKey=你的AppKey  
rongcloud.appSecret=你的AppSecret

# 邀请消息功能已内置在代码中，无需额外配置
```

#### 使用示例

```java
@Autowired
private RongcloudMessageService rongcloudMessageService;

// 发送私聊消息
RongcloudMessageResponse response = rongcloudMessageService.sendTextMessage(
    "发送者ID", "接收者ID", "消息内容", "额外信息");
```

#### REST API接口

- `POST /user/api/rongcloud/message/text` - 发送文本消息
- `POST /user/api/rongcloud/message/custom` - 发送自定义消息

### 风险控制客户端

项目集成了风险控制服务的Feign客户端，支持用户行为风险检测和任务日志记录。

#### 客户端架构

- **Feign接口**: `RiskClient.java` - 定义风险控制API接口
- **请求DTO**: 简洁的请求参数`LoginRiskRequest`
- **响应DTO**: 结构化的响应结果处理
- **连接配置**: 200ms连接超时，2000ms读取超时

#### 支持的功能

##### 1. 任务日志记录
- **接口路径**: `POST /risk/user/task/log`
- **功能**: 记录用户任务操作日志
- **请求参数**: `TaskLogRequest`

##### 2. 登录风险检测  
- **接口路径**: `POST /risk/login`
- **功能**: 检测用户登录行为风险
- **请求参数**: `LoginRiskRequest`
- **响应结果**: `LoginRiskResponse`

##### 3. 注册风险检测
- **接口路径**: `POST /risk/register`
- **功能**: 检测用户注册行为风险
- **请求参数**: `RegisterRiskRequest`
- **响应结果**: `RegisterRiskResponse`

##### 4. 营销风险检测
- **接口路径**: `POST /risk/marketing`
- **功能**: 检测营销活动行为风险
- **请求参数**: `MarketingRiskRequest`
- **响应结果**: `MarketingRiskResponse`

#### 数据结构

##### 登录风险请求 (LoginRiskRequest)
```java
{
    "userId": 123456,        // 用户ID
    "ip": "***********",     // IP地址
    "boxId": "device123",    // 设备盒子ID  
    "userAgent": "...",      // 用户代理
    "appVersion": "1.0.0",   // 应用版本
    "os": "Android",         // 操作系统
    "loginType": "email",    // 登录类型
    "loginValid": 1          // 登录是否有效 (0/1)
}
```

##### 登录风险响应 (LoginRiskResponse)
```java
{
    "errorCode": 0,          // 错误代码
    "action": "allow",       // 操作动作
    "title": "登录成功",      // 标题
    "content": "...",        // 内容
    "duration": 300          // 持续时间(秒)
}
```

##### 营销风险请求 (MarketingRiskRequest)
```java
{
    "userId": 123456,        // 用户ID
    "ip": "***********",     // IP地址
    "boxId": "device123",    // 设备盒子ID  
    "userAgent": "...",      // 用户代理
    "appVersion": "1.0.0",   // 应用版本
    "os": "Android",         // 操作系统
    "marketingType": "promotion", // 营销类型
    "taskId": "task456"      // 任务ID
}
```

##### 营销风险响应 (MarketingRiskResponse)
```java
{
    "errorCode": 0,          // 错误代码
    "action": "allow",       // 操作动作
    "title": "营销允许",      // 标题
    "content": "...",        // 内容
    "duration": 300          // 持续时间(秒)
}
```

#### 使用示例

```java
@Autowired
private RiskClient riskClient;

// 检测登录风险
LoginRiskRequest loginRequest = new LoginRiskRequest()
    .setUserId(123456L)
    .setIp("***********")
    .setBoxId("device123")
    .setUserAgent("Mozilla/5.0...")
    .setAppVersion("1.0.0")
    .setOs("Android")
    .setLoginType("email")
    .setLoginValid(1);

LoginRiskResponse loginResponse = riskClient.loginRisk(loginRequest);

// 检测营销风险
MarketingRiskRequest marketingRequest = new MarketingRiskRequest()
    .setUserId(123456L)
    .setIp("***********")
    .setBoxId("device123")
    .setUserAgent("Mozilla/5.0...")
    .setAppVersion("1.0.0")
    .setOs("Android")
    .setMarketingType("promotion")
    .setTaskId("task456");

MarketingRiskResponse marketingResponse = riskClient.marketingRisk(marketingRequest);
```

#### 异步风险检测服务

在 `UserRegisterService` 中提供了异步风险检测方法：

```java
// 异步检测注册风险
userRegisterService.sendRegisterRiskAsync(userId, ip, userAgent, appVersion, os, boxId, registerType);

// 异步检测营销风险  
userRegisterService.sendMarketingRiskAsync(userId, ip, userAgent, appVersion, os, boxId, marketingType, taskId);
```

#### 技术特性

- ✅ 多场景支持：登录、注册、营销风险检测
- ✅ 异步处理：UserRegisterService提供异步检测方法
- ✅ 简洁设计：单一请求类包含所有必要字段
- ✅ 链式调用支持（Lombok @Accessors）
- ✅ 超时控制：连接200ms，读取2000ms
- ✅ 参数校验：由其他服务统一处理，客户端只负责数据传输
- ✅ JSON兼容：自动转换snake_case格式以适配Go服务

## 技术栈

- Java 8+
- Spring Boot
- MyBatis Plus
- MySQL
- Redis
- Kafka
- Docker

## 项目结构

```
src/main/java/com/media/
├── core/                 # 核心模块
│   ├── auth/            # 认证相关
│   ├── config/          # 配置类
│   ├── exception/       # 异常处理
│   └── utils/           # 工具类
└── user/                # 用户模块
    ├── controller/      # 控制器
    ├── service/         # 业务逻辑
    ├── domain/          # 数据模型
    ├── mapper/          # 数据库映射
    └── dto/             # 数据传输对象
```

## 数据库表结构

### coreSky活动参与记录表 (core_sky_activity_record)

该表用于记录用户参与coreSky活动的信息。

#### 表结构

| 字段名       | 类型        | 约束       | 说明                       |
| ------------ | ----------- | ---------- | -------------------------- |
| id           | bigint(20)  | 主键，自增 | 主键ID                     |
| uid          | bigint(20)  | NOT NULL   | 用户ID                     |
| client_ip    | varchar(45) | NOT NULL   | 客户端IP地址               |
| created_time | datetime    | NOT NULL   | 参加活动时间，默认当前时间 |

#### 索引

- PRIMARY KEY: `id`
- INDEX: `idx_uid` (uid)
- INDEX: `idx_created_time` (created_time)

#### 使用说明

1. **记录活动参与**：当用户参与coreSky活动时，系统会自动记录用户ID、参与时间和IP地址
2. **查询用户参与历史**：可通过uid查询某个用户的参与记录
3. **统计分析**：可通过created_time进行时间段统计
4. **IP追踪**：记录client_ip用于安全分析和防作弊

#### 相关SQL文件

建表语句位于：`sql/20250715.sql`

```sql
-- 建表语句示例
CREATE TABLE `core_sky_activity_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` bigint(20) NOT NULL COMMENT '用户ID',
  `client_ip` varchar(45) NOT NULL COMMENT '客户端IP地址',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '参加活动时间',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='coreSky活动参与记录表';
```

## 环境配置

### 数据库配置

在 `application.yml` 中配置数据库连接信息。

### 运行说明

1. 确保MySQL服务已启动
2. 执行数据库建表脚本：`sql/20250715.sql`
3. 启动应用：`java -jar media-user.jar`

## 注意事项

- created_time字段使用UTC时间存储，表示用户参加活动的时间
- IP地址支持IPv4和IPv6格式
- 用户ID必须是有效的已注册用户ID
- 数据库字符集使用utf8mb4以支持emoji等特殊字符

## 更新历史

### 2025-01-17
- 新增风险控制客户端(RiskClient)
- 实现登录风险检测接口：`POST /risk/login`
- 新增风险控制相关DTO类：`LoginRiskRequest`, `LoginRiskResponse`
- 支持用户登录行为风险评估和检测
- 简化DTO设计：单一请求类，参数校验由风险控制服务统一处理

### 2025-07-15
- 新增coreSky活动参与记录表
- 支持记录用户活动参与信息和IP追踪
- 简化表结构：created_time同时表示参加活动时间，去掉了修改时间字段 
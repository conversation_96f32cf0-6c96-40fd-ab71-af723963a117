package com.media.core.request;



import com.media.core.constant.ClientInfo;
import com.media.user.enums.LanguageEnums;
import org.apache.commons.lang3.StringUtils;

public class ClientInfoContext {

    private ClientInfoContext() {
    }

    private static final ThreadLocal<ClientInfo> INSTANCE = new ThreadLocal<>();

    public static ClientInfo get() {
        return INSTANCE.get();
    }

    public static void set(ClientInfo c) {
        INSTANCE.set(c);
    }

    public static void clear() {
        INSTANCE.remove();
    }

    public static int getOffestMinute(){
        ClientInfo clientInfo = get();
        if(clientInfo == null) {
            return 0;
        }
        String timezoneValue = clientInfo.getTimezoneValue();
        Integer timezone = 0;
        if (StringUtils.isNotBlank(timezoneValue)) {
            double hours = Double.parseDouble(timezoneValue);
            timezone = (int) ((double)hours * 60);
        }
        return timezone;
    }

    public static LanguageEnums getLanguage(){
        ClientInfo clientInfo = get();
        if(clientInfo == null) {
            return LanguageEnums.en_US;
        }
        LanguageEnums languageEnums = LanguageEnums.get(clientInfo.getLanguage());
        if(languageEnums == null) {
            return LanguageEnums.en_US;
        }
        return languageEnums;
    }

    /**
     * 获取语言，将简体中文转化为繁体中文
     */
    public static LanguageEnums getLanguageConvert(){
        ClientInfo clientInfo = get();
        if(clientInfo == null) {
            return LanguageEnums.en_US;
        }
        LanguageEnums languageEnums = LanguageEnums.get(clientInfo.getLanguage());
        if(languageEnums == null) {
            return LanguageEnums.en_US;
        }
        if(languageEnums == LanguageEnums.zh_CN) {
            return LanguageEnums.zh_TW;
        }
        return languageEnums;
    }

}

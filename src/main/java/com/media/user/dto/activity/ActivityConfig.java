package com.media.user.dto.activity;

import lombok.Data;
import java.util.List;

@Data
public class ActivityConfig {
    private List<ActivityItem> activities;

    @Data
    public static class ActivityItem {
        private String id;
        private String titleKey;       // i18n 资源文件中的标题键
        private String descriptionKey; // i18n 资源文件中的描述键
        private String icon;
        private String jumpType;
        private String jumpUrl;
        private String backgroundImage; // 背景图片地址
        private String backgroundImageCn; // 背景图片地址
        private boolean clickable;
        private String startTime;  // 开始时间，格式：yyyy-MM-dd HH:mm:ss
        private String endTime;
        private Integer status;
        private Integer order;
        private boolean auditShowable; //审核状态是否展示
        private String minVersion;
        private boolean needLogin = false;  // 是否需要登录，默认为true
        private boolean needFace = false;  // 是否需要登录，默认为true
        private boolean needVerify = false; //是否需要三合一认证
    }
}

package com.media.user.enums;

import lombok.Getter;

/**
 * 挖矿开始日期文案
 */
@Getter
public enum MiningStartDateEnum {

    MINING_START_DATE_EN_US("en-US", "March ${day}"),
    MINING_START_DATE_ZH_TW("zh-TW", "3月${day}日"),
    MINING_START_DATE_ZH_CN("zh-CN", "3月${day}日"),
    MINING_START_DATE_FR_FR("fr-FR", "Mars ${day}"),
    MINING_START_DATE_DE_DE("de-DE", "März ${day}"),
    MINING_START_DATE_RU_RU("ru-RU", "Март ${day}"),
    MINING_START_DATE_ES_ES("es-ES", "Marzo ${day}"),
    MINING_START_DATE_JA_JP("ja-JP", "3月${day}日"),
    MINING_START_DATE_TH_TH("th-TH", "มีนาคม ${day}"),
    MINING_START_DATE_VI_VN("vi-VN", "Tháng 3 ${day}"),
    MINING_START_DATE_KO_KR("ko-KR", "3월${day}일"),
    MINING_START_DATE_ID_ID("id-ID", "Maret ${day}"),
    MINING_START_DATE_HI_IN("hi-IN", "मार्च ${day}"),
    MINING_START_DATE_UR_PK("ur-PK", "مارچ ${day}"),
    MINING_START_DATE_TR_TR("tr-TR", "Mart ${day}"),
    MINING_START_DATE_AR_SA("ar-SA", "مارس ${day}"),
    MINING_START_DATE_FA_IR("fa-IR", "مارس ${day}"),
    MINING_START_DATE_PT_PT("pt-PT", "Março ${day}"),
    MINING_START_DATE_BN_BD("bn-BD", "মার্চ ${day}"),
    ;

    private final String language;

    private final String content;

    MiningStartDateEnum(String language, String content) {
        this.language = language;
        this.content = content;
    }

    public static String getContent(String language) {
        for (MiningStartDateEnum contentEarlyBirdEnum : MiningStartDateEnum.values()) {
            if (contentEarlyBirdEnum.getLanguage().equals(language)) {
                return contentEarlyBirdEnum.getContent();
            }
        }
        return "";
    }
}

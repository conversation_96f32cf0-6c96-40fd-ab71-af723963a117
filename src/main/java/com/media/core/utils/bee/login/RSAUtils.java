package com.media.core.utils.bee.login;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.Key;
import java.security.KeyFactory;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;

/**
 * bee 登录使用的 签名方法
 */
@Slf4j
public class RSAUtils {

    /**
     * 加密算法RSA
     */
    public static final String KEY_ALGORITHM = "RSA";

    /**
     * 签名算法
     */
    public static final String SIGNATURE_ALGORITHM = "MD5withRSA";

    /**
     * 获取公钥的key
     */
    private static final String PUBLIC_KEY = "RSAPublicKey";

    /**
     * 获取私钥的key
     */
    private static final String PRIVATE_KEY = "RSAPrivateKey";

    /**
     * RSA最⼤加密明⽂⼤⼩
     */
    private static final int MAX_ENCRYPT_BLOCK = 245;

    /**
     * RSA最⼤解密密⽂⼤⼩
     */
    private static final int MAX_DECRYPT_BLOCK = 256;

    /**
     * RSA 位数 如果采⽤2048 上⾯最⼤加密和最⼤解密则须填写: 245 256
     */
    private static final int INITIALIZE_LENGTH = 2048;

    /**
      * <P>
      * 私钥解密
      * </p>
      *
      * @param encryptedData 已加密数据
      * @param privateKey 私钥(BASE64编码)
      * @return
      * @throws Exception
      */
    public static byte[] decryptByPrivateKey(byte[] encryptedData, String privateKey) throws Exception {
         byte[] keyBytes = Base64.decodeBase64(privateKey);
         PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
         KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
         Key privateK = keyFactory.generatePrivate(pkcs8KeySpec);
         Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
         cipher.init(Cipher.DECRYPT_MODE, privateK);
         int inputLen = encryptedData.length;
         ByteArrayOutputStream out = new ByteArrayOutputStream();
         int offSet = 0;
         byte[] cache;
         int i = 0;
         // 对数据分段解密
         while (inputLen - offSet > 0) {
             if (inputLen - offSet > MAX_DECRYPT_BLOCK) {
                 cache = cipher.doFinal(encryptedData, offSet,
                      MAX_DECRYPT_BLOCK);
                 } else {
                 cache = cipher.doFinal(encryptedData, offSet, inputLen -
                      offSet);
                 }
             out.write(cache, 0, cache.length);
             i++;
             offSet = i * MAX_DECRYPT_BLOCK;
         }
         byte[] decryptedData = out.toByteArray();
         out.close();
         return decryptedData;
    }



    /**
     * <p>
     * 公钥加密
     * </p>
     *
     * @param data 源数据
     * @param publicKey 公钥(BASE64编码)
     * @return
     * @throws Exception
     */
    public static byte[] encryptByPublicKey(byte[] data, String publicKey) throws Exception {
         byte[] keyBytes = Base64.decodeBase64(publicKey);
         X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(keyBytes);
         KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
         Key publicK = keyFactory.generatePublic(x509KeySpec);
         // 对数据加密
         Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
         cipher.init(Cipher.ENCRYPT_MODE, publicK);
         int inputLen = data.length;
         ByteArrayOutputStream out = new ByteArrayOutputStream();
         int offSet = 0;
         byte[] cache;
         int i = 0;
         // 对数据分段加密
         while (inputLen - offSet > 0) {
             if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                 cache = cipher.doFinal(data, offSet, MAX_ENCRYPT_BLOCK);
                 } else {
                 cache = cipher.doFinal(data, offSet, inputLen - offSet);
                 }
             out.write(cache, 0, cache.length);
             i++;
             offSet = i * MAX_ENCRYPT_BLOCK;
         }
         byte[] encryptedData = out.toByteArray();
         out.close();
         return encryptedData;
    }

    /**
     * java端公钥加密
     * @param data
     * @param PUBLICKEY
     * @return
     */
    public static String encryptedDataOnJava(String data, String PUBLICKEY) {
        try {
            data = Base64.encodeBase64String(encryptByPublicKey(data.getBytes(), PUBLICKEY));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    /**
     * java端私钥解密
     * @param data
     * @param PRIVATEKEY
     * @return
     */
    public static String decryptDataOnJava(String data, String PRIVATEKEY) {
        String temp = "";
        try {
            byte[] rs = Base64.decodeBase64(data);
            temp = new String(RSAUtils.decryptByPrivateKey(rs, PRIVATEKEY), "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return temp;
    }


    /**
     * 参数拼接的⼯具⽅法
     * @param map
     * @return
     */
    public static String getSignatureData(Map<String, Object> map){
        try {
            List<Map.Entry<String, Object>> infoIds = new ArrayList<>(map.entrySet());
            // 对所有传⼊参数按照字段名的 ASCII 码从⼩到⼤排序（字典序）
            Collections.sort(infoIds, new Comparator<Map.Entry<String, Object>>() {
                public int compare(Map.Entry<String, Object> o1, Map.Entry<String, Object> o2) {
                     return (o1.getKey()).toString().compareTo(o2.getKey());
                }
            });

            // 构造签名键值对的格式
            StringBuilder sb = new StringBuilder();
            Iterator<Map.Entry<String, Object>> it = infoIds.iterator();
            while (it.hasNext()) {
                Map.Entry<String, Object> item = it.next();
                if (item.getKey() != null || item.getKey() != "") {
                    String key = item.getKey();
                    Object val = item.getValue();
                    sb.append(key + "=" + val);
                }
                if (it.hasNext()) {
                    sb.append("&");
                }
             }
            return sb.toString();
        }catch (Exception e){
            return null;
        }
    }

    public static Map<String,Object> getDataFormSignature(String signatureData) {
        if (!StringUtils.hasText(signatureData)){
            return null;
        }

        Map<String,Object> map = new HashMap<>();
        Arrays.stream(signatureData.split("&")).forEach(ele -> {
            String[] eleArr = ele.split("=");
            if (eleArr.length == 2) {
                map.put(eleArr[0],eleArr[1]);
            }else if(eleArr.length == 1){
                map.put(eleArr[0],null);
            }
        });
        return map;
    }

 }
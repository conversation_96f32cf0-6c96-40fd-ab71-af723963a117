package com.media.user.dto.query;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ClientUserBatchQuery {
    private Long currentUid;
    private List<Long> uids;
    private Boolean needFollowState;     // 是否需要关注状态
    private Boolean needFollowedState;     // 是否需要反向关注状态
    private Boolean needFollowCount;     // 是否需要关注/粉丝数量
} 
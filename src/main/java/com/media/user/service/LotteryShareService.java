package com.media.user.service;

import com.alibaba.fastjson.JSONObject;
import com.media.core.exception.ApiException;
import com.media.core.i18n.I18nConvert;
import com.media.user.dto.request.CreateShortUrlRequest;
import com.media.user.dto.response.ClientUserResponse;
import com.media.user.dto.response.LotteryShareResponse;
import com.media.user.dto.response.ShortUrlResponse;
import com.media.user.enums.LanguageEnums;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.ShortUrlClient;
import com.media.user.service.cache.ClientUserCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;

/**
 * 彩票分享服务
 */
@Slf4j
@Service
public class LotteryShareService {

    @Autowired
    private ClientUserCacheService clientUserCacheService;

    @Autowired
    private ShareContentService shareContentService;

    @Autowired
    private ShortUrlService shortUrlService;
    
    @Value("${lottery.dailyPrizePool:10000}")
    private String dailyPrizePool;
    
    @Value("${lottery.currency:DOGE}")
    private String currency;
    
    @Value("${lottery.additionalReward:5}")
    private String additionalReward;


    /**
     * 获取彩票分享数据
     */
    public LotteryShareResponse getLotteryShareData(Long uid, LanguageEnums languageEnums) {
        // 查询用户
        ClientUserResponse clientUser = clientUserCacheService.me(uid);
        if (clientUser == null) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }
        
        // 构建响应数据
        LotteryShareResponse response = new LotteryShareResponse();
        
        // 设置总奖池金额
        response.setPrizeTotalAmount(dailyPrizePool);
        
        // 设置币种
        response.setCurrency(currency);
        
        // 设置用户邀请码
        response.setInvitationCode(clientUser.getInviteCode());
        
        // 设置分享链接（包含邀请码）

        String longUrl = String.format(shareContentService.getShareUrl(), clientUser.getInviteCode(), URLEncoder.encode(clientUser.getNickName())) + "&contentType=A&source=lottery";
        String shortUrl = shortUrlService.generateShortUrl(longUrl,Long.toString(uid));
        response.setShareUrl(shortUrl != null ? shortUrl : longUrl);

        // 设置额外奖励金额
        response.setAdditionalRewardAmount(additionalReward);
        
        // 设置分享提示标题
        response.setSharePromptTitle(I18nConvert.getI18nMessage("lottery.share.prompt.title",languageEnums));
        response.setSharePromptDescription(I18nConvert.getI18nMessage("lottery.share.prompt.description",languageEnums));
        
        return response;
    }
}
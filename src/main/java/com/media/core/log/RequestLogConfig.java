//package com.media.user.log;
//
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
//import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
//import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
//
////@ConditionalOnExpression("${request.log.enable}==true")
//@EnableConfigurationProperties(RequestLogProperties.class)
//@Configuration
//public class RequestLogConfig {
//
//    @Bean
//    public RequestLogInterceptor requestLogInterceptor(RequestLogProperties requestLogProperties) {
//        return new RequestLogInterceptor(requestLogProperties);
//    }
//
//    @Bean
//    public WebMvcConfigurer requestLogConfigWebMvcConfigurer(RequestLogInterceptor requestLogInterceptor,
//                                                             RequestLogProperties requestLogProperties) {
//        return new WebMvcConfigurer() {
//            @Override
//            public void addInterceptors(InterceptorRegistry registry) {
//                InterceptorRegistration interceptorRegistration = registry.addInterceptor(requestLogInterceptor);
//                if (!requestLogProperties.getPaths().isEmpty()) {
//                    interceptorRegistration.addPathPatterns(requestLogProperties.getPaths());
//                } else {
//                    interceptorRegistration.addPathPatterns("/**");
//                }
//                if (!requestLogProperties.getIgnorePaths().isEmpty()) {
//                    interceptorRegistration.excludePathPatterns(requestLogProperties.getIgnorePaths());
//                }
//            }
//        };
//
//    }
//
//    @Bean("requestBodyAdvicePlugin")
//    public ResponseBodyAdvicePlugin requestBodyAdvicePlugin() {
//        return new ResponseBodyAdvicePlugin();
//    }
//
////    @Bean("responseBodyAdvicePlugin")
////    public ResponseBodyAdvicePlugin responseBodyAdvicePlugin() {
////        return new ResponseBodyAdvicePlugin();
////    }
//}

package com.media.user.dto.request;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 融云私聊消息发送请求
 */
@Data
@Accessors(chain = true)
public class RongcloudMessageRequest {

    /**
     * 消息内容，JSON格式
     * 例如：{"content":"hello","extra":"helloExtra"}
     */
    private String content;

    /**
     * 发送人用户ID
     */
    private String fromUserId;

    /**
     * 接收人用户ID
     */
    private String toUserId;

    /**
     * 消息类型，例如：RC:TxtMsg
     */
    private String objectName = "RC:TxtMsg";

    /**
     * 推送内容
     */
    private String pushContent;

    /**
     * 推送数据，JSON格式
     * 例如：{"pushData":"hello"}
     */
    private String pushData;

    /**
     * 数量
     */
    private Integer count = 1;

    /**
     * 是否验证黑名单，0-不验证，1-验证
     */
    private Integer verifyBlacklist = 0;

    /**
     * 是否持久化，0-不持久化，1-持久化
     */
    private Integer isPersisted = 1;

    /**
     * 是否包含发送者，0-不包含，1-包含
     */
    private Integer isIncludeSender = 0;

    /**
     * 是否禁用推送，false-不禁用，true-禁用
     */
    private Boolean disablePush = false;

    /**
     * 是否扩展，false-不扩展，true-扩展
     */
    private Boolean expansion = false;
}
package com.media.user.service.internal;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.user.domain.TwitterUserModel;
import com.media.user.dto.request.internal.TwitterUserRecommendRequest;
import com.media.user.dto.response.internal.TwitterUserRecommendResponse;
import com.media.user.mapper.TwitterUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class TwitterUserInternalService {

    @Autowired
    TwitterUserMapper twitterUserMapper;

    public List<TwitterUserRecommendResponse> queryRecommend(TwitterUserRecommendRequest query) {
        List<TwitterUserRecommendResponse> list = new ArrayList<>();
        TwitterUserRecommendResponse recommendResponse = null;
        for(String username : query.getUsernames()){
            QueryWrapper<TwitterUserModel> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(TwitterUserModel::getUsername, username);
            TwitterUserModel twitterUserModel = twitterUserMapper.selectOne(queryWrapper);
            if(twitterUserModel != null){
                recommendResponse = new TwitterUserRecommendResponse();
                recommendResponse.setUsername(username);
                recommendResponse.setRecommendType(twitterUserModel.getRecommendType());
                list.add(recommendResponse);
            }
        }
        return list;
    }

    public void batchSet(TwitterUserRecommendRequest request){
        List<String> usernames = request.getUsernames();
        for(String username : usernames){
            QueryWrapper<TwitterUserModel> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(TwitterUserModel::getUsername, username);
            TwitterUserModel twitterUserModel = twitterUserMapper.selectOne(queryWrapper);
            if(twitterUserModel != null){
                twitterUserModel.setRecommendType(request.getRecommendType());
                twitterUserMapper.updateById(twitterUserModel);
            }
        }
    }


}

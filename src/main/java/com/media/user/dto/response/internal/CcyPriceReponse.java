package com.media.user.dto.response.internal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 查询ccy汇率
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CcyPriceReponse {

    /**
     * 币种
     */
    private String ccy;

    /**
     *
     */
    private BigDecimal priceChangePercent;

    /**
     * 价格
     */
    private BigDecimal price;

}

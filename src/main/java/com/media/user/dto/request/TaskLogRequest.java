package com.media.user.dto.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * 任务日志请求
 */
@Data
@Accessors(chain = true)
public class TaskLogRequest {

    /**
     * 用户ID
     */
    private Long user_id;

    /**
     * 服务名称
     */
    private String service;

    /**
     * 路径
     */
    private String path;

    /**
     * 类型
     */
    private String type;

    /**
     * IP地址
     */
    private String ip;

    /**
     * 设备ID
     */
    private String device_id;

    /**
     * 其他参数
     */
    private Map<String, Object> other;
} 
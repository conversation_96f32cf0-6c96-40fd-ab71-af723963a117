package com.media.user.controller.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.media.core.auth.RequireAuth;
import com.media.core.exception.ApiException;
import com.media.core.auth.CloudSession;
import com.media.user.dto.request.TelegramBindRequest;
import com.media.user.dto.response.CompleteTaskResponse;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.service.TelegramUserService;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(value = "/user/telegram", produces = "application/json;charset=UTF-8")
public class TelegramController {

    @Autowired
    private TelegramUserService telegramUserService;

    /**
     * xme用户和telegram用户进行绑定
     */
    @PostMapping("/bind")
    @RequireAuth
    public ApiResponse<CompleteTaskResponse> bind(@RequestBody TelegramBindRequest request) {
        log.info("用户绑定Telegram账号请求: {}", request);
        if (request == null) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
        // 获取当前登录用户ID
        Long uid = CloudSession.getUid();
        // 执行绑定
        CompleteTaskResponse completeTaskResponse = telegramUserService.bindTelegramUser(uid, request);
        return ApiResponse.success(completeTaskResponse, completeTaskResponse.getGetEpValue(), completeTaskResponse.getGetEpValue());
    }

}

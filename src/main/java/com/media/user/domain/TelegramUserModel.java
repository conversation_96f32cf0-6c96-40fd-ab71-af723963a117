package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("telegram_user")
@Data
@Accessors(chain = true)
public class TelegramUserModel extends Model<TelegramUserModel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 应用用户ID
     */
    @TableField("uid")
    private Long uid;
    
    /**
     * Telegram用户ID
     */
    @TableField("telegram_user_id")
    private Long telegramUserId;
    
    /**
     * Telegram用户名
     */
    @TableField("telegram_username")
    private String telegramUsername;
    
    /**
     * Telegram用户昵称
     */
    @TableField("telegram_first_name")
    private String telegramFirstName;
    
    /**
     * Telegram用户姓氏
     */
    @TableField("telegram_last_name")
    private String telegramLastName;

    /** 
     * Telegram用户头像     
     */
    @TableField("telegram_photo_url")
    private String telegramPhotoUrl;
    
    /**
     * 绑定状态 0:未绑定 1:已绑定
     */
    @TableField("status")
    private Integer status;
    
    /**
     * 创建时间
     */
    @TableField("created_time")
    private Date createdTime;
    
    /**
     * 更新时间
     */
    @TableField("updated_time")
    private Date updatedTime;
} 
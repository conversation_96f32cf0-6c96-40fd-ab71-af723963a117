package com.media.user;

import com.media.user.mapper.ClientUserCloseMapper;
import com.media.user.service.BeeAuthService;
import com.media.core.utils.bee.push.BeeClient;
import com.media.core.utils.bee.push.vo.param.OriginalPushParam;
import com.media.core.utils.bee.push.vo.rtn.AdminMessageTemplate;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;

@SpringBootTest
public class BeeTest {


    @Autowired
    BeeClient beeClient;

    @Autowired
    BeeAuthService beeAuthService;

    @Test
    public void testBeeToken() {
        //demo
        String sign = "cSzMzaHLyzavOtO46tAcehGajpLRfPVAPTxaUFqsP/Zf4dSJS5D6AcQMVtJWxSu/NZboNpYQ7Irrl30s0pbXelogfjCxjc4N/wuzPPo9Xsz7w3Zcwx0DUT49iW+xXYhK28YrnU1APvu4bFOOELdPXyOuPjnHcZHGppMHBJ3HTv+nMOUaxHNjIjaVwBRC5Hje/P6cH+DWwdRbyv3KcRlsfr/4WQGW6Zw1p/ucM/cxe0ix+bUt325r968+4HQtGDdsgIQwGJ0KZsOW/G7nGFqRVeH4+k24bYJwLPBpZvryhC2t87JZBBd5L2u/AkB3XD46lwIwLi5uEVGQyswhTYmjbg==";
        // data decrypted and recovered: {nickname=abc156, userId=62c68ec3318c7b0006e7d3fc, email=<EMAIL>, timestamp=1735030261449}

        //手机号
        sign = "mogVAJHoO47wrltsS65nO2ns3gvOYl6pyGQcvzkcznXM7R/6WSBDIMJMaCeznTpTKPlHHas5tQs9bCW62C0IGSApnZsvHM9h8uFwFchKV/1UKMYXnekaL3Yal1Yx04zAVj9dO7O7/DzDD/bzrRH2j4taU9ywaMUpB5B0+sLbGYUZrfDNcc0t6dSr4e8G6aJkHGR7Ru8VBTIfOzk/yb1Lm3Iz8QAM5dpaB2AGaQ9/xH1X6Qn1RdCdPZUg6iaRNYqQj4nFS2+akyeYyDM7gw4laC8yhgNpGnTcyFpfBA2G035TorsWYLk6RLqWx1EaCyFh1Si2yTBbumDiNYrgsiQVFbUFbWShOuKrxpTPthP7MyIwmqmaIHnX2QnXbIiyDzhL9LcJNw8Of1pwmkfxK//E8kYWzWnJJvo3qeWG3tiIbMptUr6Zv286twGJ1NXChS4Ga5GNvlASY9ArrxgqCO4Rtp79hLcK6SVaX9Mvgmw631aIap6EYBorc0ZgMY9/g5XHLapDlgL5KUSck4RP7kmnRh6uf4pdHIlGUc+ESA+rvB2Qj7z2/Y26SXXETHl5IH/2d7Fq49Zii6qFLxA1+j3bMAnxBxhQfrpQMEAMnMqM+B+fKc0zXmEq0msN4DwfDbFdTGFNXV8bwrCeUOEYJ+sJGzV/4V9lCBte40AstVQXfTQ=";
        // data decrypted and recovered: {address=******************************************, appId=xme, nickname=#Beeliever5UD2MNT, clientInfo={"l":"zh_Hans","s":"default","os":"android","a":"Bee Network","p":"network.bee.app","v":"1.26.7","b":"3358","ud":"b153bed0-c42e-11ef-ad5c-dbb6ff2b1f14"}, accessToken=676e204477ff480006d68b2c, email=null}

        sign = "Y1U2a+BQjqPnufjOsC1pFgExsunFVmMxB/Pfn4lfVpibsev9WVEIR7Gjm5sU+1jxk0LnxdcwacivbbKn/IQ+6Wvnlspo8ALsXeA+uE4siHWBi8ZMtfB9LZis+bvoC/lr9mGWpwNn/JqUS1XyXZN4wzryTfJQHV7fFxAjtwNqnBEKB2p1vKQARuIjg8VknOrJY5J6RdXKK0qcqReGGG94Hz+UPpNHeCyv31Qo6oU6q02cVwd411albdqwya+bkNXDDSAPnuHm3gX4pfDiNxk6s+JDRdyJKBkVZX1Rs6Deyf9uXj4rxRJ0m0+d/WhA7Vhp2QUjn8gXXKjphcwISqnQRznaHtvVil/+cMyiC9vZubZ67GLkbEpUaUWIh5WCA+F3wU84zQrrgreAlhREkAuLIwS4TPxnIbdhuyhgGENseLGGNu4B/Riu6bvIvuToqzXP8FSWzvwvTiX+cJZjxgAmCOJNTlHN78bNXAdlHIfeLuY9nJbFRq8Py8CEPJ9yAQjjCucSGAe+dNpW4BnloPxM8YV3A+qH3dqtIo/SFdwEvL+n1sG952bM1Sy93DJ7WpivLM0j3PUBXyrBTQKZhgHMxbUNUWoAJ85FqMt4e8YfkKwujy0pKS8A1U5JKCLO7Fw8GjkmYddVEYao8UmLhcmte0H1YBD/w1+J4iKuaLVCcBc=";
//      data decrypted and recovered: {address=******************************************, appId=xme, nickname=#Beeliever5UD2MNT, clientInfo={"l":"zh_Hans","s":"default","os":"android","a":"Bee Network","p":"network.bee.app","v":"1.26.7","b":"3358","ud":"b153bed0-c42e-11ef-ad5c-dbb6ff2b1f14"}, accessToken=676e204477ff480006d68b2c, email=<EMAIL>}

        //手机号
//        sign = "oBnwLsB7XWRvumL2+8+7hkeHtyolvcKu1JrQd+GRphifM/KOLY/LI1OFxvBhEsWdVzqZs01B9meokqmNad7WXIqNts3U4EjHlsF/1oN+0FkjS9edru/r0L0/Lp7JSP9DbBWPYmsnDbpoi2UjHjLhqQKXHvMwlxbWo7fr7k1AlFwEGwEQSbKAgn9c8f6DFU0wvesTFwnmTmx3O1xzeTa0BeTDHzk9FtA4c+F0D7RoAM9/FS3xscc3S7QKuuTSV/Qc4QTyGQYUtXjCYxoXrA5zwJTPXpfYDBpa18O0rzBC3PtdjjuHWQhT2WY1pJzwUyEOrSzHxycQLcv+IFEu5QZKwlfoJQpgY6R4JRJpqqXYZFjy1PyEq8ssIOKd1qI20qM3QtefawTo8ns3xyfzZEk9t2T1YqfFlb52WIfRvmTigMV4wm6tMhk/Zm1bSVpXPwcbLtNWPP8FzyP8uzseBWQYZG7aXxlEp+aZ5hobsLK9UDo9u42zIip0QU+CymG0VUisQG0kXR/B/sQjg4Gr4pH2Lmi/2nLR+9gqqZUL8zKMJD5D0+OutZkxDD40SS5uELkUPi55ywA0QXEuwW4h61pC0R6vDk6U0dMFCuSIpu6Jb7WgJi1s+QIRFxbjDlrtZhQkDJNDL3pPRK/i3Dj/Ok7l2WQfJQwM99kF9WQdRPH6Ago=";
        // data decrypted and recovered: {address=******************************************, appId=xme, nickname=#Beeliever5UD2MNT, clientInfo={"l":"zh_Hans","s":"default","os":"android","a":"Bee Network","p":"network.bee.app","v":"1.26.7","b":"3358","ud":"b153bed0-c42e-11ef-ad5c-dbb6ff2b1f14"}, accessToken=676e204477ff480006d68b2c, email=null}

        //邮箱: <EMAIL>
//        sign = "K1uTRn9hdP2mxdUoGE2l261v/7bvi0wlcydiBQCzVmyEqLEmwrVF8HyxcDtbTfyvnG/M71IvUDHu4TWqm0j02BpJmBNe5MM5c2fgSiU5oJA1PE/WiBcFOBub8PwHfkecKmNA1WAI/FoDjr5TVJF45DeiKfkxchVnv4ymuF5uV+0s9WOXDpvZooEGC7HG//NpNOTgaOIz9Sp9T5VsA0RL8rh6uIn6Qe9eLfy0my8MxhuFVhFqEHEz0un+xRwbkCNyD9OPOABBPwqYWVjIKqav0wfR+xlcZ6+o1pvJyXO1tAS0pfajpcD3fY9yNhNLjdS+HHl8bQfK0OwooPVhLceHw4QiN2m/FvI9NTwM0Ka/VntbkxTjsFbIuGPRJjFt0Hk3Qy1NQq9D4NRFhiePH8HIfU10l4vDfm8AlthrYOJvap1kEhKfpDOA37u82PUaoMGPU3r/3cUn16Yoxww4kGTjzHlXKQQP++A/1wJwAdEfPcUnGN18vpsikhxEWuFMiOCjwnDh7z3kVqvZd19RuzvxUzqJ+QLOrDMPW8gsoL4ySO+FpMWWAOmTuxKRwQQ2uGGv8WwZ2XYlagoqbngwuktjl/fR+Z1OHfzcvho15A6gd0aSSs6XcjC2aqGa4RE/4IHnQVG+wjPbXTcVeA6cOl7g4kqzSnjWzx73ka0qUvKuPbw=";
        // data decrypted and recovered: {address=******************************************, appId=xme, nickname=#Beeliever3FUDM2Q, clientInfo={"l":"zh_Hans","s":"default","os":"android","a":"Bee Network","p":"network.bee.app","v":"1.26.7","b":"3358","ud":"26871800-c420-11ef-88fc-bfcf56d10674"}, accessToken=676e602436145b0006cdb776, email=<EMAIL>}

        //邮箱: <EMAIL>
//        sign = "pLq2NyzaYgF3ns1+ALNz0z3Y57iE/A+uMSIZ3ethGnby0FPxOncD3emn7wq6NRGf2fllBuDJbH0Y/TQW8DAttK/FjxTEmHfHiM9vYfqsbOAmw9sfNM5P/qtYBYIFwX5dI4aWqS/7ayE3QadVGyv6RsRWzVV5HJLMfrRjLfwXXZgQlOFKAMwzFoYgC+hs3U9r+NvUU/PgjL7NJYZxjG+MqehM3MeVLltPcXxsKioi7T5VZt3d0STepQRqNMQQ6nXUfgXed+GF14zN8KhBDYf33+GAQwayV91g2LKDUEK36SWexaPjd/zPC74/5kGcDzdvQXbi37dlbo2T+8wlFtb6cJppKYwNzrZ6ILTgC+heaGneW5fl/ZLyYcIn9r20OTJj8j0v81PL1KCETaEM3zMOEvllnpo95OR7byzF5MMKo2j5y771CuNabHhnCWUujqHmzfhZ24cqIKSdsK1g/AEItfHzprekkvTSKVtnuPUylsaFuzWoM+5ZzhZ5mksK9SDNR35UeY7g/dwXXGJUggs87/ps5Ozler9gO7dfe+RUiMGQ0afeVGJxSvDHjc9pI5g38iOeeT/OSB53r8d8f/BKEa4ugjo215A+daeGVoFaC6UBZA1yvay8Y2z+22F6wUrjRTA4crUGdXHfh/pbrdeg7x4iPErNjbcL7eP8tXfwCoM=";
        // data decrypted and recovered: {address=******************************************, appId=xme, nickname=#Beeliever3FUDM2Q, clientInfo={"l":"zh_Hans","s":"default","os":"android","a":"Bee Network","p":"network.bee.app","v":"1.26.7","b":"3358","ud":"26871800-c420-11ef-88fc-bfcf56d10674"}, accessToken=676e602436145b0006cdb776, email=<EMAIL>}

//        BeeUserRtn beeUserRtn = beeAuthService.tokenInfo(sign);
//        System.out.println(beeUserRtn);
    }

    @Autowired
    ClientUserCloseMapper clientUserCloseMapper;

    @Test
    public void test() {
        List<AdminMessageTemplate> list =  beeClient.adminMessageTemplateList();
        for(AdminMessageTemplate adminMessageTemplate : list){
            System.out.println(adminMessageTemplate);
        }
    }

    @Test
    public void testOriginalPush() {
        OriginalPushParam params = new OriginalPushParam();
        List<String> userIds = new ArrayList<>();
        userIds.add("10108241576020");
        userIds.add("10108241576020");
        params.setUserId(userIds);
        params.setTitle("标题 7");
        params.setMessage("内容 7");
        beeClient.originalPush(params);
    }

    public static void main(String[] args) {
        //ar fa
//        String str = " صديق  ساعدك لكسب %s ساتوشي BTC";
//        System.out.println(String.format(str,111));
//
//        String str2 = " أدع شخصًا واحدًا آخر، s% ساتوشي BTC سيكون yours!";
//        String str3 = " أدع شخصًا واحدًا آخر،%s ساتوشي BTC سيكون yours!";
//        System.out.println(String.format(str3,111));
//      fa
//        String str5 = "=دعوت 1 نفر دیگر برای دریافت %s ساتوشی BTC!";
//        System.out.println(String.format(str5,111));
        String str6 = " دوست  به شما کمک کرده تا %s ساتوشی BTC به دست آورید";
        System.out.println(String.format(str6,111));
    }


}

package com.media.user.service;

import com.alibaba.fastjson.JSON;
import com.media.user.dto.request.RongcloudMessageRequest;
import com.media.user.dto.response.RongcloudMessageResponse;
import com.media.user.feign.client.RongcloudClient;
import com.media.user.utils.CallbackSignatureVerifier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;

/**
 * 融云消息服务
 * 封装融云API调用，提供消息发送等功能
 */
@Slf4j
@Service
public class RongcloudMessageService {

    @Autowired
    private RongcloudClient rongcloudClient;

    @Value("${rongcloud.appKey:}")
    private String appKey;

    @Value("${rongcloud.appSecret:}")
    private String appSecret;

    private static final SecureRandom RANDOM = new SecureRandom();

    /**
     * 生成融云API签名信息
     * 
     * @return 包含时间戳、随机数、签名的数组 [timestamp, nonce, signature]
     */
    private String[] generateSignatureInfo() {
        try {
            // 检查配置
            if (!StringUtils.hasText(appSecret)) {
                throw new IllegalStateException("融云AppSecret未配置");
            }

            // 生成时间戳（毫秒）
            long timestamp = System.currentTimeMillis();

            // 生成随机数
            int nonce = Math.abs(RANDOM.nextInt());

            // 使用项目中已有的签名生成方法
            String signature = CallbackSignatureVerifier.generateSignature(
                    appSecret,
                    String.valueOf(nonce),
                    String.valueOf(timestamp));

            log.debug("融云签名生成 - 时间戳: {}, 随机数: {}, 签名: {}", timestamp, nonce, signature);

            return new String[] { String.valueOf(timestamp), String.valueOf(nonce), signature };
        } catch (Exception e) {
            log.error("生成融云签名失败", e);
            throw new RuntimeException("融云签名生成失败", e);
        }
    }

    /**
     * 发送文本私聊消息
     * 
     * @param fromUserId 发送者用户ID
     * @param toUserId   接收者用户ID
     * @param content    消息内容
     * @param extra      额外信息（可选）
     * @return 发送结果
     */
    public RongcloudMessageResponse sendTextMessage(String fromUserId, String toUserId, String content, String extra) {
        try {
            // 构建消息内容JSON
            Map<String, String> messageContent = new HashMap<>();
            messageContent.put("content", content);
            if (extra != null && !extra.isEmpty()) {
                messageContent.put("extra", extra);
            }

            // 构建推送数据JSON
            Map<String, String> pushDataMap = new HashMap<>();
            pushDataMap.put("pushData", content);

            log.info("发送融云私聊消息 - 发送者: {}, 接收者: {}, 内容: {}", fromUserId, toUserId, content);

            // 生成签名信息
            String[] signatureInfo = generateSignatureInfo();

            // 调用融云API - 使用新的方法签名
            RongcloudMessageResponse response = rongcloudClient.sendPrivateMessage(
                    appKey,
                    signatureInfo[0], // timestamp
                    signatureInfo[1], // nonce
                    signatureInfo[2], // signature
                    fromUserId,
                    toUserId,
                    "RC:TxtMsg",
                    JSON.toJSONString(messageContent),
                    content,
                    JSON.toJSONString(pushDataMap),
                    1,
                    0,
                    1,
                    1,
                    false,
                    false);

            if (response.isSuccess()) {
                log.info("融云私聊消息发送成功 - 消息ID: {}, 用户ID: {}",
                        response.getFirstMessageUID(), response.getFirstUserId());
            } else {
                log.error("融云私聊消息发送失败 - 错误码: {}, 错误信息: {}",
                        response.getCode(), response.getErrorMessage());
            }

            return response;

        } catch (Exception e) {
            log.error("发送融云私聊消息异常 - 发送者: {}, 接收者: {}", fromUserId, toUserId, e);

            // 返回失败响应
            RongcloudMessageResponse errorResponse = new RongcloudMessageResponse();
            errorResponse.setCode(500);
            errorResponse.setErrorMessage("系统异常: " + e.getMessage());
            return errorResponse;
        }
    }

    /**
     * 发送自定义私聊消息
     * 
     * @param request 完整的消息请求对象
     * @return 发送结果
     */
    public RongcloudMessageResponse sendCustomMessage(RongcloudMessageRequest request) {
        try {
            log.info("发送自定义融云私聊消息 - 请求: {}", JSON.toJSONString(request));

            // 生成签名信息
            String[] signatureInfo = generateSignatureInfo();

            // 调用融云API - 使用新的方法签名
            RongcloudMessageResponse response = rongcloudClient.sendPrivateMessage(
                    appKey,
                    signatureInfo[0], // timestamp
                    signatureInfo[1], // nonce
                    signatureInfo[2], // signature
                    request.getFromUserId(),
                    request.getToUserId(),
                    request.getObjectName(),
                    request.getContent(),
                    request.getPushContent(),
                    request.getPushData(),
                    request.getCount(),
                    request.getVerifyBlacklist(),
                    request.getIsPersisted(),
                    request.getIsIncludeSender(),
                    request.getDisablePush(),
                    request.getExpansion());

            if (response.isSuccess()) {
                log.info("自定义融云私聊消息发送成功 - 消息ID: {}", response.getFirstMessageUID());
            } else {
                log.error("自定义融云私聊消息发送失败 - 错误: {}", response.getErrorMessage());
            }

            return response;

        } catch (Exception e) {
            log.error("发送自定义融云私聊消息异常", e);

            RongcloudMessageResponse errorResponse = new RongcloudMessageResponse();
            errorResponse.setCode(500);
            errorResponse.setErrorMessage("系统异常: " + e.getMessage());
            return errorResponse;
        }
    }
}
package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("user_face_recognition_log")
public class UserFaceRecognitionLog {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long uid;

    private Float confidence;

    // 识别结果: 0-失败, 1-成功
    private Integer result;

    // 失败原因: 1-人脸重复, 2-置信度不足, 3-系统错误, 4-其他
    private Integer failReason;

    // 错误码
    private Integer code;

    // 失败详情
    private String failDetail;

    private Date createdTime;
}
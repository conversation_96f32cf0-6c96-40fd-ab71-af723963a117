package com.media.core.i18n;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class I18nContextualSerializer extends JsonSerializer<String> implements ContextualSerializer {

    private List<String> texts;

    private List<String> codes;

    public I18nContextualSerializer() {

    }

    public I18nContextualSerializer(List<String> texts, List<String> codes) {
        this.texts = texts;
        this.codes = codes;
    }

    @Override
    public JsonSerializer<?> createContextual(SerializerProvider serializerProvider, BeanProperty beanProperty) throws JsonMappingException {
        if (beanProperty != null) {
            if (Objects.equals(beanProperty.getType().getRawClass(), String.class)) {
                I18n i18n = beanProperty.getAnnotation(I18n.class);
                if (i18n == null) {
                    i18n = beanProperty.getContextAnnotation(I18n.class);
                }
                List<String> texts = new ArrayList<>();
                List<String> codes = new ArrayList<>();

                if (i18n != null && i18n.value().length > 0) {
                    for (Message message : i18n.value()) {
                        String code = message.message().replace("{", "").replace("}", "");
                        texts.add(message.text());
                        codes.add(code.trim());
                    }
                    return new I18nContextualSerializer(texts, codes);
                }
            }
            return serializerProvider.findValueSerializer(beanProperty.getType(), beanProperty);
        }
        return serializerProvider.findNullValueSerializer(null);
    }

    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (!CollectionUtils.isEmpty(texts)) {
            for (int i = 0; i < texts.size(); i++) {
                String text = texts.get(i);
                String message = codes.get(i);
                value = value.replace(text, LocalMessageResource.getMessage(message));
            }
        }
        gen.writeString(value);
    }

}

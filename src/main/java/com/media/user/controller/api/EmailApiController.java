package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.request.ClientInfoContext;
import com.media.user.dto.group.ForgetPasswordGroup;
import com.media.user.dto.group.RegisterGroup;
import com.media.user.dto.group.UpdatePasswordGroup;
import com.media.user.dto.request.*;
import com.media.user.dto.response.ClientUserResponse;
import com.media.user.enums.BusinessTypeEnum;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.core.utils.ValidateUtil;
import com.media.user.service.SmsService;
import com.media.user.service.cache.ClientUserCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/user/api/email")
public class EmailApiController {

    @Autowired
    private SmsService smsService;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    /**
     * 发送邮箱验证码
     * @param request
     */
    @PostMapping("/sendCode")
    public void sendEmailCode(@Validated @RequestBody SendEmailCodeRequest request){
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getEnumFromCode(request.getBusinessType());
        request.setBusinessTypeEnum(businessTypeEnum);
        if (BusinessTypeEnum.UPDATE_PASSWORD_CODE.equals(businessTypeEnum)){
            ValidateUtil.validate(request, UpdatePasswordGroup.class);
            if (!CloudSession.isLogin()){
                throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
            }
            request.setUid(CloudSession.getUid());
        } else if (BusinessTypeEnum.FORGET_PASSWORD_CODE.equals(businessTypeEnum)) {
            ValidateUtil.validate(request, ForgetPasswordGroup.class);
        } else if (BusinessTypeEnum.SIGNUP.equals(businessTypeEnum)) {
            ValidateUtil.validate(request, RegisterGroup.class);
        }  else if (BusinessTypeEnum.EMAIL_LOGIN_CODE.equals(businessTypeEnum)) {
            ValidateUtil.validate(request, RegisterGroup.class);
        } else if (BusinessTypeEnum.EMAIL_BIND_CODE.equals(businessTypeEnum)) {
            ValidateUtil.validate(request, UpdatePasswordGroup.class);
            if (!CloudSession.isLogin()){
                throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
            }
            request.setUid(CloudSession.getUid());
        } else if (BusinessTypeEnum.EMAIL_VERIFY_CODE.equals(businessTypeEnum)) {
            ValidateUtil.validate(request, UpdatePasswordGroup.class);
            if (!CloudSession.isLogin()){
                throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
            }
            request.setUid(CloudSession.getUid());
        }else if (BusinessTypeEnum.UPDATE_EMAIL_OLD_CODE.equals(businessTypeEnum)){
            ValidateUtil.validate(request, UpdatePasswordGroup.class);
            if (!CloudSession.isLogin()){
                throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
            }
            request.setUid(CloudSession.getUid());
        } else if (BusinessTypeEnum.UPDATE_EMAIL_NEW_CODE.equals(businessTypeEnum)) {
            ValidateUtil.validate(request, RegisterGroup.class);
            if (!CloudSession.isLogin()){
                throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
            }
            request.setUid(CloudSession.getUid());
        }else {
            throw new ApiException(MediaUserExceptionCodeApi.BUSINESS_TYPE_NOT_EXIST);
        }

        log.info("send email code: {}", request.getEmail());
        if (BusinessTypeEnum.UPDATE_PASSWORD_CODE.equals(request.getBusinessTypeEnum())
                || BusinessTypeEnum.UPDATE_EMAIL_OLD_CODE.equals(request.getBusinessTypeEnum())
                || BusinessTypeEnum.EMAIL_VERIFY_CODE.equals(request.getBusinessTypeEnum())){
            ClientUserResponse clientUserResponse = clientUserCacheService.me(request.getUid());
            request.setEmail(clientUserResponse.getEmail());
        }
        smsService.sendEmailCode(request.getUid(), request.getEmail(), request.getBusinessTypeEnum(), ClientInfoContext.getLanguage());
    }

    /**
     * 校验邮箱验证码
     * @param request
     */
    @PostMapping("/checkCode")
    public void checkCode(@Validated @RequestBody SendEmailCodeRequest request){
        Long uid = null;
        if(StringUtils.isEmpty(request.getCode())){
            throw new ApiException(MediaUserExceptionCodeApi.EMAIL_CODE_ERROR);
        }
        BusinessTypeEnum businessTypeEnum = BusinessTypeEnum.getEnumFromCode(request.getBusinessType());
        request.setBusinessTypeEnum(businessTypeEnum);
        if (BusinessTypeEnum.UPDATE_PASSWORD_CODE.equals(businessTypeEnum)){
            ValidateUtil.validate(request, UpdatePasswordGroup.class);
            if (!CloudSession.isLogin()){
                throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
            }
            uid = CloudSession.getUid();
        } else if (BusinessTypeEnum.FORGET_PASSWORD_CODE.equals(businessTypeEnum)) {
            ValidateUtil.validate(request, ForgetPasswordGroup.class);
        } else if (BusinessTypeEnum.SIGNUP.equals(businessTypeEnum)) {
            ValidateUtil.validate(request, RegisterGroup.class);
        }  else if (BusinessTypeEnum.EMAIL_LOGIN_CODE.equals(businessTypeEnum)) {
            ValidateUtil.validate(request, RegisterGroup.class);
        } else if (BusinessTypeEnum.EMAIL_BIND_CODE.equals(businessTypeEnum)) {
            ValidateUtil.validate(request, UpdatePasswordGroup.class);
            if (!CloudSession.isLogin()){
                throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
            }
            uid = CloudSession.getUid();
        } else if (BusinessTypeEnum.EMAIL_VERIFY_CODE.equals(businessTypeEnum)) {
            ValidateUtil.validate(request, UpdatePasswordGroup.class);
            if (!CloudSession.isLogin()){
                throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
            }
            uid = CloudSession.getUid();
        }else if (BusinessTypeEnum.UPDATE_EMAIL_OLD_CODE.equals(businessTypeEnum)){
            ValidateUtil.validate(request, UpdatePasswordGroup.class);
            if (!CloudSession.isLogin()){
                throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
            }
            uid = CloudSession.getUid();
        } else if (BusinessTypeEnum.UPDATE_EMAIL_NEW_CODE.equals(businessTypeEnum)) {
            ValidateUtil.validate(request, RegisterGroup.class);
            if (!CloudSession.isLogin()){
                throw new ApiException(MediaUserExceptionCodeApi.UNAUTHORIZED);
            }
            uid = CloudSession.getUid();
        }else {
            throw new ApiException(MediaUserExceptionCodeApi.BUSINESS_TYPE_NOT_EXIST);
        }
        log.info("check email code: {}", request.getEmail());
        if (BusinessTypeEnum.UPDATE_PASSWORD_CODE.equals(request.getBusinessTypeEnum())
                || BusinessTypeEnum.UPDATE_EMAIL_OLD_CODE.equals(request.getBusinessTypeEnum())
                || BusinessTypeEnum.EMAIL_VERIFY_CODE.equals(request.getBusinessTypeEnum())){
            ClientUserResponse clientUserResponse = clientUserCacheService.me(request.getUid());
            request.setEmail(clientUserResponse.getEmail());
        }

        //验证邮箱验证码
        smsService.verifyEmailCode(uid, request.getEmail(), businessTypeEnum, request.getCode());

    }


}

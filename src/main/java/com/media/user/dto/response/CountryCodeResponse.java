package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CountryCodeResponse {

    private String phonePrefix;  //手机号前缀
    private String abbreviation;  //国家简拼
    private String countryName;  //国家名称
    private String countryCode;  //国家编号
    private String flag;  //国旗
    private String regex; //正则校验


    public CountryCodeResponse(String phonePrefix, String abbreviation, String countryName, String countryCode, String flag, String regex) {
        this.phonePrefix = phonePrefix;
        this.abbreviation = abbreviation;
        this.countryName = countryName;
        this.countryCode = countryCode;
        this.flag = flag;
        this.regex = regex;
    }
}
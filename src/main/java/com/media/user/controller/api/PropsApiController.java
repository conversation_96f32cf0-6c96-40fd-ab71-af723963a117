package com.media.user.controller.api;


import com.media.core.auth.CloudSession;
import com.media.user.dto.query.PropsQuery;
import com.media.user.dto.response.PropsResponse;
import com.media.user.service.PropsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/user/api/props")
public class PropsApiController {

    @Autowired
    private PropsService propsService;

    @PostMapping("")
    public PropsResponse saveWebsiteCustomer (@Validated @RequestBody PropsQuery query){
        query.setUid(CloudSession.getUid());
        return propsService.getPropsByType(query);
    }


}

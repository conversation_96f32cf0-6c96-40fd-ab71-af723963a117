package com.media.user.service;

import com.media.user.dto.request.AddUserPropProd;
import com.media.user.dto.request.AddUserPropRequest;
import com.media.user.dto.response.AddUserPropResponse;
import com.media.user.feign.client.MediaTaskClient;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 媒体任务服务
 */
@Slf4j
@Service
public class MediaTaskService {

    @Autowired
    private MediaTaskClient mediaTaskClient;

    /**
     * 给用户批量添加道具
     *
     * @param uid   用户ID
     * @param props 道具列表
     * @param idempotentId 幂等ID
     * @return 添加道具响应
     */
    public AddUserPropResponse addUserProp(Long uid, List<AddUserPropProd> props, String idempotentId) {
        try {
            AddUserPropRequest request = AddUserPropRequest.builder()
                    .uid(uid)
                    .props(props)
                    .idempotentId(idempotentId)
                    .build();

            ApiResponse<List<String>> response = mediaTaskClient.addUserProp(request);

            if (response != null && response.getSuccess() != null && Boolean.TRUE.equals(response.getSuccess())) {
                List<String> propCodes = response.getResult();
                log.info("AddUserProp success, uid: {}, props: {}, response: {}", uid, props, propCodes);
                
                // 构建响应对象
                AddUserPropResponse propResponse = new AddUserPropResponse();
                propResponse.setCode(200);
                propResponse.setMessage("success");
                propResponse.setResult(propCodes);
                propResponse.setSuccess(true);
                return propResponse;
            } else {
                log.error("AddUserProp failed, uid: {}, props: {}, response: {}", uid, props, response);
                
                // 构建失败响应
                AddUserPropResponse propResponse = new AddUserPropResponse();
                propResponse.setCode(500);
                propResponse.setMessage(response != null ? "API call failed" : "Null response");
                propResponse.setSuccess(false);
                return propResponse;
            }
        } catch (Exception e) {
            log.error("AddUserProp error, uid: {}, props: {}", uid, props, e);
            return null;
        }
    }
}

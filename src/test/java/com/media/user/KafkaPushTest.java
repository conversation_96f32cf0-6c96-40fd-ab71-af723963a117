package com.media.user;

import com.media.user.mq.provider.UserSendMessageProvider;
import com.media.user.service.ClientUserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


@SpringBootTest
public class KafkaPushTest {

    @Autowired
    UserSendMessageProvider userSendMessageProvider;

    @Autowired
    ClientUserService clientUserService;

    @Test
    public void user() {
        clientUserService.updateUserLanguage(17117921234746L, "zh-CN");
//        userSendMessageProvider.sendFollowMessage(1L, "testNickName",2L);
    }



}

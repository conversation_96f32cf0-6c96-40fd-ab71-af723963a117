package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.core.constant.PageResponse;
import com.media.core.utils.IpUtil;
import com.media.user.constant.MediaUserConstant;
import com.media.user.dto.query.ClientUserQuery;
import com.xme.xme_base_depends.models.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;
import com.media.user.dto.query.UserInviteListQuery;
import com.media.user.dto.query.UserInviteRankingQuery;
import com.media.user.dto.query.UserSearchQuery;
import com.media.user.dto.request.*;
import com.media.user.dto.response.*;
import com.media.user.model.request.InviterInfoRequest;
import com.media.user.dto.response.internal.SearchUserResponse;
import com.media.user.dto.response.internal.UserMeResponse;
import com.media.user.enums.*;
import com.media.user.es.EsClientUserService;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.core.request.ClientInfoContext;
import com.media.user.feign.client.IpCountryClient;
import com.media.user.feign.vo.IpCountryResponse;
import com.media.user.service.*;
import com.media.user.service.cache.ClientUserCacheService;
import com.media.core.utils.GoogleUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.GetMapping;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/user/api/user")
public class ClientUserApiController {

    @Autowired
    private ClientUserService clientUserService;

    @Autowired
    UserRegisterService userRegisterService;

    @Autowired
    GoogleAuthService googleAuthService;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @Autowired
    UserRecommendService userRecommendService;

    @Autowired
    AppReleaseService appReleaseService;

    @Autowired
    UserFamilyService userFamilyService;

    @Autowired
    ConfigSwitchService configSwitchService;

    @Autowired
    UserFollowService userFollowService;

    @Autowired
    EsClientUserService esClientUserService;

    @Autowired
    GoogleUtils googleUtils;

    @Autowired
    RedissonClient redissonClient;

    @Autowired
    BlockPuzzleCaptchaService blockPuzzleCaptchaService;

    @Autowired
    private UserInviteRelationService userInviteRelationService;

    @Autowired
    UserInviteFaceRecordService userInviteFaceRecordService;

    @Autowired
    ActivityInviteRelationService activityInviteRelationService;

    @Autowired
    private IpCountryClient ipCountryClient;

    @Autowired
    private ActivityReservationService activityReservationService;

    @Autowired
    private UserProfileService userProfileService;

    @Autowired
    private UserInviteService userInviteService;

    @Value("${register.captcha.enabled:true}")
    private boolean captchaEnabled;

    @Value("${official.list:}")
    private String officialListConfig;

    @Autowired
    private PhoneService phoneService;

    @Autowired
    private SmsService smsService;

    @Autowired
    private ResourceLoader resourceLoader;

    @Autowired
    UserSecretService userSecretService;

    /**
     * 查询邀请人信息
     * 用于用户注册时关联邀请人
     * 可以通过邮箱或手机号查询，优先使用邮箱
     *
     * @return 邀请人信息，包含用户ID、用户名和邀请码
     */
    @PostMapping("/inviter/info")
    public InviterInfoResponse getInviterInfo(@RequestBody InviterInfoRequest request) {

        // 首先尝试使用邮箱查询
        if (StringUtils.isNotBlank(request.getEmail())) {
            InviterInfoResponse response = activityInviteRelationService.getInviterInfoByEmail(request.getEmail());
            if (response != null) {
                return response;
            }
        }

        // 如果邮箱查询失败或未提供邮箱，尝试使用手机号查询
        if (StringUtils.isNotBlank(request.getCountryCode()) && StringUtils.isNotBlank(request.getPhonePrefix())
                && StringUtils.isNotBlank(request.getPhone())) {
            return activityInviteRelationService.getInviterInfoByPhone(request.getCountryCode(),
                    request.getPhonePrefix(), request.getPhone());
        }

        // 如果都未提供有效信息，返回空对象
        return new InviterInfoResponse();
    }

    /**
     * 获取客户端真实IP地址
     *
     * @param request HTTP请求
     * @return 客户端IP地址
     */
    private String getClientIp(HttpServletRequest request) {
        return ipUtil.getIpAddrByRequest(request);
    }

    @Autowired
    IpUtil ipUtil;

    @Value("${user.print_header:true}")
    private boolean printHeader;

    /**
     * google auth web
     */
    @PostMapping("/web/google/auth")
    public AuthTokenResponse googleAuth(@Validated @RequestBody Map<String, String> map) {
        return googleAuthService.authWeb(map.get("credential"), map.get("invite_code"));
    }

    /**
     * google auth android
     */
    @PostMapping("/android/google/auth")
    public AuthTokenResponse googleAuthAndroid(
            @Validated @RequestBody GoogleAuthAndroidRequest googleAuthAndroidRequest) {
        return googleAuthService.authAndroid(googleAuthAndroidRequest);
    }

    /**
     * 注册配置
     */
    @PostMapping("/register/config")
    public Map<String, Integer> registerConfig() {
        return userRegisterService.registerConfig();
    }

    /**
     * 注册
     *
     * @param request
     */
    @PostMapping("/register")
    public AuthTokenResponse register(@Validated @RequestBody ClientUserRequest request,
            HttpServletRequest httpRequest) {
        if (printHeader) {
            Enumeration<String> headerNames = httpRequest.getHeaderNames();
            StringBuilder headers = new StringBuilder();
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                headers.append(name).append("=").append(httpRequest.getHeader(name)).append("; ");
            }
            log.info("Register original headers: {}", headers);
        }

        String platformType = ClientInfoContext.get().getPlatformType();
        // 增加版本判断，1.0.50版本以下的接口都提示下载新版本
        if (!appReleaseService.isVersionValid()) {
            throw new ApiException(MediaUserExceptionCodeApi.VERSION_TOO_LOW);
        }

        if (captchaEnabled) {
            blockPuzzleCaptchaService.verification(request.getResponseToken());
        }

        request.setSourceType(PlatformEnums.get(platformType).getCode());
        return userRegisterService.register(request);
    }

    /**
     * 新版注册接口带短信和邮箱验证码
     *
     * @param request
     */
    @PostMapping("/v1/register")
    public ApiResponse<AuthTokenResponse> v1register(@Validated @RequestBody ClientUserRequest request,
            HttpServletRequest httpRequest) {
        if (printHeader) {
            Enumeration<String> headerNames = httpRequest.getHeaderNames();
            StringBuilder headers = new StringBuilder();
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                headers.append(name).append("=").append(httpRequest.getHeader(name)).append("; ");
            }
            log.info("Register original headers: {}", headers);
        }

        // 增加版本判断，1.0.50版本以下的接口都提示下载新版本
        if (!appReleaseService.isVersionValid()) {
            throw new ApiException(MediaUserExceptionCodeApi.VERSION_TOO_LOW);
        }

        if (captchaEnabled) {
            blockPuzzleCaptchaService.verification(request.getResponseToken());
        }

        // 设置平台类型
        String platformType = ClientInfoContext.get().getPlatformType();
        request.setSourceType(PlatformEnums.get(platformType).getCode());

        // 执行完整注册流程
        UserRegisterService.RegisterResult result = userRegisterService.registerWithVerification(request);
        return ApiResponse.success(result.getAuthTokenResponse(), result.getEpValue(), result.getEpValue());
    }

    /**
     * 更新密码
     *
     * @param request
     */
    @RequireAuth
    @PostMapping("/updatePassword")
    public void updatePassword(@Validated @RequestBody UpdatePasswordRequest request) {
        request.setUid(CloudSession.getUid());
        clientUserService.updatePassword(request);
    }

    /**
     * 忘记密码
     *
     * @param request
     */
    @PostMapping("/forget")
    public void forgetPassword(@Validated @RequestBody ForgetPasswordRequest request) {
        clientUserService.forgetPassword(request);
    }

    /**
     * 随机给一个推荐吗
     */
    @PostMapping("/push/inviteCode")
    public Map<String, String> pushInviteCode() {
        return clientUserService.pushInviteCode();
    }

    /**
     * TODO 为你推荐用户 9 人-默认马斯克和 xme
     */
    @PostMapping("/recommend")
    // @RequireAuth
    public List<UserFollowingMemberResponse> recommend(HttpServletRequest request) {
        Long uid = null;
        try {
            uid = CloudSession.getUid();
        } catch (Exception e) {
            log.info("user not login uid is null");
        }
        // 审核 app 期间返回固定的文案
        Integer check = configSwitchService.switchConfig();
        // 0 为app 提审状态
        if (check == 0) {
            log.info("app is checking");
            return userRecommendService.recommendCheck(uid);
        }

        String userIP = ipUtil.getIpAddrByRequest(request);
        log.info("recommend user ip: {}", userIP);
        if (StringUtils.isNotBlank(userIP)) {
            IpCountryResponse response = ipCountryClient.getIpCountryInfo(userIP);
            if (response != null && response.getCode() == 200
                    && response.getResult() != null
                    && "CN".equalsIgnoreCase(response.getResult().getCountry_code())) {
                return userRecommendService.recommendCN(uid);
            }
        }
        return userRecommendService.recommend(uid);
    }

    /**
     * 个人信息
     */
    @PostMapping("/me")
    @RequireAuth
    public UserMeResponse me() {
        log.info("current user info: {}", CloudSession.getUid());
        UserMeResponse userMeResponse = clientUserCacheService.userInfoForMe(CloudSession.getUid(),
                ClientInfoContext.getLanguage());

        // 设置是否为官方用户
        if (userMeResponse != null) {
            userMeResponse.setIsOfficial(isOfficialUser(CloudSession.getUid()));
        }

        return userMeResponse;
    }

    /**
     * 指定 uid 查询 个人信息
     */
    @PostMapping("/info")
    // @RequireAuth
    public UserMeResponse userInfo(@Validated @RequestBody UserSearchQuery request) {
        Long currentUid = null;
        try {
            currentUid = CloudSession.getUid();
        } catch (Exception e) {
            log.info("user not login currentUid is null");
        }
        log.info("query user info: {}", request.getUid());
        UserMeResponse userMeResponse = clientUserCacheService.userInfoForWeb(request.getUid(),
                ClientInfoContext.getLanguage());
        if (userMeResponse == null) {
            return null;
        }
        userMeResponse.setFollowState(
                userFollowService.isFollowing(currentUid, request.getUid())
                        ? UserFollowStateEnum.FOLLOW_STATE_1.getState()
                        : UserFollowStateEnum.FOLLOW_STATE_0.getState());

        userMeResponse.setFollowedState(
                userFollowService.isFollowing(request.getUid(), currentUid)
                        ? UserFollowStateEnum.FOLLOW_STATE_1.getState()
                        : UserFollowStateEnum.FOLLOW_STATE_0.getState());
        // 设置是否为官方用户
        userMeResponse.setIsOfficial(isOfficialUser(request.getUid()));
        return userMeResponse;
    }

    /**
     * 判断用户是否为官方用户
     *
     * @param uid 用户ID
     * @return 0:否; 1:是
     */
    private Integer isOfficialUser(Long uid) {
        if (StringUtils.isBlank(officialListConfig) || uid == null) {
            return 0;
        }

        try {
            // 将配置字符串按逗号分割为uid列表
            String[] officialUids = officialListConfig.split(",");
            String currentUidStr = uid.toString();

            // 检查当前用户uid是否在官方用户列表中
            for (String officialUid : officialUids) {
                if (StringUtils.isNotBlank(officialUid) && officialUid.trim().equals(currentUidStr)) {
                    return 1;
                }
            }
        } catch (Exception e) {
            log.warn("Error parsing official.list config: {}", e.getMessage());
        }

        return 0;
    }

    /**
     * 更新个人信息
     *
     * @param request
     */
    @PostMapping("/info/modify")
    @RequireAuth
    public void infoModify(@Validated @RequestBody UpdateUserInfoRequest request) {
        if (request != null) {
            if (StringUtils.isNotBlank(request.getNickName())) {
                clientUserService.checkNickName(request.getNickName());
                clientUserService.updateNickName(CloudSession.getUid(), request.getNickName());
            }
            if (StringUtils.isNotBlank(request.getPersonIntroduce())) {
                clientUserService.updatePersonIntroduce(CloudSession.getUid(), request.getPersonIntroduce());
            }
            if (StringUtils.isNotBlank(request.getAvatarUrl())) {
                clientUserService.updateAvatarUrl(CloudSession.getUid(), request.getAvatarUrl());
            }
        }
    }

    /**
     * 更新昵称 TODO 是否去掉单独接口
     *
     * @param request
     */
    @PostMapping("/nickName")
    @RequireAuth
    public void updateNickName(@Validated @RequestBody UpdateNickNameRequest request) {
        clientUserService.checkNickName(request.getNickName());
        clientUserService.updateNickName(CloudSession.getUid(), request.getNickName());
    }

    /**
     * 更新简介 TODO 是否去掉单独接口
     *
     * @param request
     */
    @PostMapping("/personIntroduce")
    @RequireAuth
    public void updatePersonIntroduce(@Validated @RequestBody UpdatePersonIntroduceRequest request) {
        clientUserService.updatePersonIntroduce(CloudSession.getUid(), request.getPersonIntroduce());
    }

    /**
     * 更新简介
     *
     * @param request
     */
    @PostMapping("/email")
    @RequireAuth
    public void updateEmail(@Validated @RequestBody UpdateEmailRequest request) {
        request.setUid(CloudSession.getUid());
        clientUserService.updateEmail(request);
    }

    /**
     * 更新头像 TODO 是否去掉单独接口
     *
     * @param request
     */
    @PostMapping("/avatarUrl")
    @RequireAuth
    public void updateAvatarUrl(@Validated @RequestBody UpdateAvatarUrlRequest request) {
        clientUserService.updateAvatarUrl(CloudSession.getUid(), request.getAvatarUrl());
    }

    /**
     * 登录
     *
     * @param loginRequest
     */
    @PostMapping("/login")
    public AuthTokenResponse login(@Validated @RequestBody UserLoginRequest loginRequest) {
        if (!appReleaseService.isVersionValid()) {
            throw new ApiException(MediaUserExceptionCodeApi.VERSION_TOO_LOW);
        }
        return clientUserService.login(loginRequest);
    }

    /**
     * 退出
     */
    @PostMapping("/logout")
    @RequireAuth
    public void logout() {
        clientUserService.logout(CloudSession.getToken());
    }

    /**
     * 用户邀请获取 xme 的弹框
     *
     * @return
     */
    @PostMapping("/invite/notice")
    @RequireAuth
    public Map<String, Object> inviteNotice() {
        // 创世结束后，弹出未弹过的
        Map<String, Object> countGenesisMap = userInviteFaceRecordService
                .queryNoPushRationFromUid(CloudSession.getUid());
        int sumCountGenesis = Integer.parseInt(countGenesisMap.get("sum").toString().replace("XME", ""));
        int countGenesis = Integer.parseInt(countGenesisMap.get("count").toString());

        // 普通需要弹的
        Map<String, Object> countNormalMap = userInviteRelationService.queryNoPushRation(CloudSession.getUid());
        int sumCountNormal = Integer.parseInt(countNormalMap.get("sum").toString().replace("XME", ""));
        int countNormal = Integer.parseInt(countNormalMap.get("count").toString());

        Map<String, Object> result = new HashMap<>();
        result.put("sum", (sumCountGenesis + sumCountNormal) + "XME");
        result.put("count", countGenesis + countNormal);
        return result;
    }

    @PostMapping("/invite/notice/popup")
    @RequireAuth
    public Map<String, Object> inviteNoticeMsg() {
        return userInviteService.inviteNoticeMsg();
    }

    /**
     * 用户邀请获取 xme 的弹框(创世大使)
     *
     * @return
     */
    @PostMapping("/invite/genesis/notice")
    @RequireAuth
    public Map<String, Object> inviteGenesisNotice() {
        return userInviteFaceRecordService.queryNoPushRationFromUid(CloudSession.getUid());
    }

    /**
     * 用户邀请获取 xme 的统计
     *
     * @return
     */
    @PostMapping("/invite/count")
    @RequireAuth
    public Map<String, Object> inviteCount() {
        return userInviteRelationService.queryInviteRationCount(CloudSession.getUid());
    }

    /**
     * 用户邀请排行分页
     *
     * @param query
     * @return
     */
    @PostMapping("/inviteRanking")
    @RequireAuth
    public PageResponse<UserInviteRankingResponse> queryUserInviteRanking(@RequestBody UserInviteRankingQuery query) {
        return clientUserService.queryUserInviteRanking(query);
    }

    /**
     * 获取用户邀请列表分页
     *
     * @param query
     * @return
     */
    @PostMapping("/inviteList")
    @RequireAuth
    public PageResponse<UserInviteListResponse> queryUserInviteList(@RequestBody UserInviteListQuery query) {
        query.setUid(CloudSession.getUid());
        return clientUserService.queryUserInviteList(query);
    }

    /**
     * 获取用户邀请信息
     *
     * @return
     */
    @PostMapping("/inviteInfo")
    // @RequireAuth
    public UserInviteInfoResponse getUserInviteInfo() {
        Long uid = null;
        if (CloudSession.isLogin()) {
            uid = CloudSession.getUid();
        }
        return clientUserService.getUserInviteInfo(uid);
    }

    /**
     * 校验昵称是否已存在
     *
     * @return
     */
    @PostMapping("/checkNickName")
    public void checkNickName(@Validated @RequestBody CheckNickNameRequest request) {
        clientUserService.checkNickName(request.getNickName());
    }

    /**
     * 校验邮箱是否已注册
     *
     * @return
     */
    @PostMapping("/checkEmail")
    public void checkEmail(@Validated @RequestBody CheckEmailRequest request) {
        clientUserService.checkEmail(request.getEmail());
    }

    /**
     * 更新用户语言
     *
     * @return
     */
    @PostMapping("/language")
    @RequireAuth
    public void updateUserLanguage() {
        log.info("update user language: uid={}, language={}", CloudSession.getUid(),
                ClientInfoContext.getLanguage().getValue());
        clientUserService.updateUserLanguage(CloudSession.getUid(), ClientInfoContext.getLanguage().getValue());
    }

    /**
     * 用户模糊搜索
     *
     * @return
     */
    @PostMapping("/search")
    // @RequireAuth
    public PageResponse<SearchUserResponse> searchUser(@RequestBody UserSearchQuery query) {
        Long uid = null;
        try {
            uid = CloudSession.getUid();
        } catch (Exception e) {
            log.info("user not login uid is null");
        }
        query.setUid(uid);
        return esClientUserService.searchUser(query);
    }

    @PostMapping("pin")
    @RequireAuth
    public void pin(@RequestBody(required = false) ClientUserQuery query) {
        log.info("user send pin message: uid={}", CloudSession.getUid());
        userFamilyService.pin(CloudSession.getUid(), query == null ? null : query.getUids());
    }

    /**
     * 标记用户成为非新手用户
     */
    @PostMapping("/sign/out/newer")
    @RequireAuth
    public void signOldMan() {
        log.info("user sign old man: uid={}", CloudSession.getUid());
        clientUserService.signOldMan(CloudSession.getUid());
    }

    // @PostMapping("/pointCoefficient")
    // @RequireAuth
    // public BigDecimal getUserPointCoefficient(){
    // return userInternalService.getUserPointCoefficient(CloudSession.getUid());
    // }

    @PostMapping("switchConfig")
    public Integer switchConfig() {
        return configSwitchService.switchConfig();
    }

    @PostMapping("/close")
    public void closeUser() {
        clientUserService.closeUser(CloudSession.getUid(), CloudSession.getToken());
    }

    /**
     * 用户邀请关系落地(活动页面使用)
     * 需要提供邮箱或手机号信息，以及邀请码
     *
     * @param request 包含邮箱/手机号和邀请码的请求
     */
    @PostMapping("/invite/bind")
    public void inviteBind(@Validated @RequestBody UserInviteSaveRequest request, HttpServletRequest httpRequest) {
        log.info("invite bind request: {}", request);

        // 验证邀请码必须存在
        if (StringUtils.isBlank(request.getInviteCode())) {
            log.warn("Invalid request: missing inviteCode");
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }

        // 验证邮箱或手机号至少有一个
        boolean hasEmail = StringUtils.isNotBlank(request.getEmail());
        boolean hasPhone = StringUtils.isNotBlank(request.getCountryCode())
                && StringUtils.isNotBlank(request.getPhonePrefix())
                && StringUtils.isNotBlank(request.getPhone());

        if (!hasEmail && !hasPhone) {
            log.warn("Invalid request: missing both email and phone");
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }

        // 获取客户端 IP 地址
        String clientIp = getClientIp(httpRequest);
        log.info("Client IP for invite bind request: {}", clientIp);

        // 使用邀请码作为锁的key，简化锁的粒度
        String lockKey = "user:invite:bind:" + request.getInviteCode();
        // 如果需要更精细的锁粒度，可以考虑使用邮箱或手机号信息
        // String requestInfo = hasEmail ? request.getEmail() :
        // (request.getCountryCode() + request.getPhonePrefix() + request.getPhone());

        // String lockKey = "user:invite:bind:" + request.getInviteCode() + ":" +
        // requestInfo;
        RLock rLock = redissonClient.getLock(lockKey);

        try {
            // 尝试获取锁，等待时间5秒，持有锁时间30秒
            boolean locked = rLock.tryLock(5, 30, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("Failed to acquire lock for invite binding: {}", request.getInviteCode());
                throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
            }

            // 调用服务处理邀请绑定
            activityInviteRelationService.inviteBind(request, clientIp);
            log.info("Successfully processed invite binding for code: {}", request.getInviteCode());
        } catch (ApiException e) {
            // 直接抛出业务异常
            throw e;
        } catch (InterruptedException e) {
            // 处理线程中断异常
            Thread.currentThread().interrupt();
            log.error("Thread interrupted while processing invite binding", e);
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        } catch (Exception e) {
            // 处理其他异常
            log.error("Error processing invite binding: {}", e.getMessage(), e);
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        } finally {
            // 释放锁
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
                log.debug("Released lock for invite binding: {}", request.getInviteCode());
            }
        }
    }

    /**
     * 活动预约接口
     * 用户可以通过手机号或邮箱进行活动预约
     * 注册后将获得道具奖励
     */
    @PostMapping("/activity/reserve")
    public void activityReserve(@Validated @RequestBody ActivityReservationRequest request,
            HttpServletRequest httpRequest) {
        String clientIp = getClientIp(httpRequest);
        log.info("activityReserve request from IP: {}, request: {}", clientIp, request);

        // 验证请求参数
        if (request == null || (StringUtils.isBlank(request.getEmail()) && StringUtils.isBlank(request.getPhone()))) {
            log.error("activityReserve missing both email and phone");
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }

        // 获取锁的key，根据邮箱或手机号
        String lockKey;
        if (StringUtils.isNotBlank(request.getEmail())) {
            lockKey = "activity:reserve:lock:email:" + request.getEmail();
        } else {
            lockKey = "activity:reserve:lock:phone:" + request.getCountryCode() + ":" + request.getPhonePrefix() + ":"
                    + request.getPhone();
        }

        // 使用分布式锁防止并发处理
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁，最多等待5秒，锁过期时间30秒
            boolean locked = lock.tryLock(5, 30, TimeUnit.SECONDS);
            if (!locked) {
                log.warn("Failed to acquire lock for activity reservation: {}", lockKey);
                throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
            }

            // 处理活动预约
            activityReservationService.reserveActivity(request, clientIp);

        } catch (ApiException e) {
            throw e;
        } catch (Exception e) {
            log.error("activityReserve error: {}", e.getMessage(), e);
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        } finally {
            // 确保锁被释放
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 手机号注册国家代码
     *
     * @return
     */
    @GetMapping("/register/countryCode")
    public List<CountryCodeResponse> listCountryCode(HttpServletRequest request) {
        // 将枚举转成新的对象列表，并根据 sort 排序
        List<CountryCodeResponse> sortedList = Arrays.stream(SupportPhonePrefixEnum.values())
                .sorted(Comparator.comparingInt(SupportPhonePrefixEnum::getSortBy)) // 按照 sort 排序
                .map(enums -> new CountryCodeResponse(enums.getPhonePrefix(), enums.getAbbreviation(),
                        enums.getLocalName(), enums.getCountryCode(), enums.getFlag(), enums.getPattern().pattern())) // 转成新的对象
                .collect(Collectors.toList());

        String loginIp = ipUtil.getIpAddrByRequest(request);
        log.info("register countryCode user ip: {}", loginIp);
        if (StringUtils.isNotBlank(loginIp)) {
            IpCountryResponse response = ipCountryClient.getIpCountryInfo(loginIp);
            if (response != null && response.getCode() == 200 && response.getResult() != null) {
                // 将指定的国家放到最前面
                String abbreviation = response.getResult().getCountry_code();
                if (StringUtils.isNotBlank(abbreviation)) {
                    log.info("move countryCode position: {}", abbreviation);
                    moveToFirstPosition(sortedList, abbreviation);
                }
            }
        }
        return sortedList;
    }

    private void moveToFirstPosition(List<CountryCodeResponse> sortedList, String targetAbbreviation) {
        for (Iterator<CountryCodeResponse> iterator = sortedList.iterator(); iterator.hasNext();) {
            CountryCodeResponse country = iterator.next();
            if (country.getAbbreviation().equals(targetAbbreviation)) {
                // 将目标国家从列表中移除
                iterator.remove();
                // 将目标国家添加到最前面
                sortedList.add(0, country);
                return;
            }
        }
    }

    /**
     * 手机号注册国家代码
     *
     * @return
     */
    @RequireAuth
    @GetMapping("/profile/me")
    public UserProfileResponse getUserProfile(HttpServletRequest request) {
        if (CloudSession.isLogin()) {
            return userProfileService.getUserProfile(CloudSession.getUid());
        }
        return new UserProfileResponse();
    }

    private int compareVersions(String version1, String version2) {
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");

        int maxLength = Math.max(v1Parts.length, v2Parts.length);

        for (int i = 0; i < maxLength; i++) {
            int v1 = (i < v1Parts.length) ? Integer.parseInt(v1Parts[i]) : 0;
            int v2 = (i < v2Parts.length) ? Integer.parseInt(v2Parts[i]) : 0;

            if (v1 > v2) {
                return 1;
            } else if (v1 < v2) {
                return -1;
            }
        }

        return 0; // Versions are equal
    }

}

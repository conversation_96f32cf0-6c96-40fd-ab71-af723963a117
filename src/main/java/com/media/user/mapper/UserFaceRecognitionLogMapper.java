package com.media.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.UserFaceRecognitionLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.Map;
import java.util.List;

@Mapper
public interface UserFaceRecognitionLogMapper extends BaseMapper<UserFaceRecognitionLog> {

    @Select("SELECT COUNT(*) FROM user_face_recognition_log WHERE created_time BETWEEN #{startTime} AND #{endTime}")
    Long countTotalAttempts(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("SELECT COUNT(*) FROM user_face_recognition_log WHERE result = 0 AND created_time BETWEEN #{startTime} AND #{endTime}")
    Long countFailedAttempts(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    @Select("SELECT fail_reason, COUNT(*) as count FROM user_face_recognition_log WHERE result = 0 AND created_time BETWEEN #{startTime} AND #{endTime} GROUP BY fail_reason")
    List<Map<String, Object>> countFailReasons(@Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
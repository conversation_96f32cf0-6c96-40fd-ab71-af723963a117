package com.media.user.service;

import com.google.common.collect.Lists;
import com.media.core.exception.ApiException;
import com.media.user.cache.TelegramUserCache;
import com.media.user.domain.TelegramUserModel;
import com.media.user.dto.request.CompleteTaskRequest;
import com.media.user.dto.request.TelegramBindRequest;
import com.media.user.dto.response.CompleteTaskResponse;
import com.media.user.dto.response.internal.TelegramUserResponse;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.MediaTaskClient;
import com.media.user.feign.client.TelegramBotClient;
import com.xme.xme_base_depends.models.ApiResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
public class TelegramUserService {

    @Resource
    private TelegramUserCache telegramUserCache;

    @Resource
    private TelegramBotClient telegramBotClient;

    @Resource
    private MediaTaskClient mediaTaskClient;

    /**
     * 绑定Telegram用户
     * @param uid
     * @param telegramUserId
     * @param telegramUsername
     * @param firstName
     * @param lastName
     * @return
     */
    public CompleteTaskResponse bindTelegramUser(Long uid, TelegramBindRequest request) {

        // 1. 验证必要参数
        if (request.getId() == null) {
            throw new ApiException(MediaUserExceptionCodeApi.TELEGRAM_USER_ID_NOT_NULL);
        }

        // 2. 验证Telegram数据的真实性
        boolean isValid = telegramBotClient.checkTelegramAuthorization(request);
        if (!isValid) {
            throw new ApiException(MediaUserExceptionCodeApi.INVALID_TELEGRAM_USER_ID);
        }

        // 验证Telegram用户ID是否存在
        boolean isValidTelegramUser = telegramBotClient.verifyTelegramUser(uid, request.getId());
        if (!isValidTelegramUser) {
            throw new ApiException(MediaUserExceptionCodeApi.INVALID_TELEGRAM_USER_ID);
        }

        List<TelegramUserModel> telegramUserListByUid = telegramUserCache.getByUid(uid);
        if (!CollectionUtils.isEmpty(telegramUserListByUid)) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_ID_ALREADY_BIND);
        }

        List<TelegramUserModel> telegramUserListByTelegramUserId = telegramUserCache.getByTelegramUserId(request.getId());
        if (!CollectionUtils.isEmpty(telegramUserListByTelegramUserId)) {
            throw new ApiException(MediaUserExceptionCodeApi.TELEGRAM_USER_ID_ALREADY_BIND);
        }

        // 进行绑定
        TelegramUserModel newBinding = new TelegramUserModel()
                .setUid(uid)
                .setTelegramUserId(request.getId())
                .setTelegramUsername(request.getUsername())
                .setTelegramFirstName(request.getFirst_name())
                .setTelegramLastName(request.getLast_name())
                .setTelegramPhotoUrl(request.getPhoto_url())
                .setStatus(1)
                .setCreatedTime(new Date())
                .setUpdatedTime(new Date());
        
        int count = telegramUserCache.insertTelegramUser(newBinding);
        // 绑定成功 完成任务
        CompleteTaskResponse completeTaskResponse = new CompleteTaskResponse();
        Integer epValue = 0;
        if (count > 0) {
            try {
                // 完成绑定任务
                CompleteTaskRequest completeTaskRequest = new CompleteTaskRequest();
                completeTaskRequest.setUid(uid);
                completeTaskRequest.setTaskCode("10009"); 
                completeTaskRequest.setCompleteTime(System.currentTimeMillis());
                ApiResponse<CompleteTaskResponse> response = mediaTaskClient.completeTask(completeTaskRequest);
                if (response != null && response.getSuccess() && response.getResult() != null) {
                    log.info("绑定Telegram用户, uid:{}, taskCode:10009, getEpValue:{}", uid, response.getResult().getGetEpValue());
                    epValue = response.getResult().getGetEpValue();
                    completeTaskResponse.setGetEpValue(epValue);
                    // 完成加入群组任务
                    completeJoinGroupTask(uid, request.getId(), telegramBotClient.getTargetGroupId());
                } else {
                    log.info("绑定Telegram用户, uid:{}, taskCode:10009, 无EP值返回", uid);
                }
            } catch (Exception e) {
                log.error("绑定Telegram用户, uid:{}, taskCode:10009, error:{}", uid, ExceptionUtils.getStackTrace(e));
            }
        }
        return completeTaskResponse;
    }

    /**
     * 绑定任务成功 判断用户是否已经在群组中，如果在群组中则自动完成加入群组任务
     * 
     * @param uid
     * @param telegramUserId
     * @param groupId
     */
    public void completeJoinGroupTask(Long uid, Long telegramUserId, String groupId) {
        CompletableFuture.runAsync(() -> {
            // 查询用户是否已经在群组中
            boolean isInTargetGroup = telegramBotClient.isUserInGroup(telegramUserId, groupId);
            if (isInTargetGroup) {
                // 完成加入群组任务
                try {
                    // 完成加入群组任务
                    CompleteTaskRequest completeTaskRequest = new CompleteTaskRequest();
                    completeTaskRequest.setUid(uid);
                    completeTaskRequest.setTaskCode("10008");
                    completeTaskRequest.setCompleteTime(System.currentTimeMillis());
                    ApiResponse<CompleteTaskResponse> response = mediaTaskClient.completeTask(completeTaskRequest);
                    if (response != null && response.getSuccess() && response.getResult() != null) {
                        log.info("完成加入群组任务, uid:{}, taskCode:10008, getEpValue:{}", uid, response.getResult().getGetEpValue());
                    } else {
                        log.info("完成加入群组任务, uid:{}, taskCode:10008, 无EP值返回", uid);
                    }
                } catch (Exception e) {
                    log.error("完成加入群组任务, telegramUserId:{}, groupId:{}, error:{}", telegramUserId, groupId, ExceptionUtils.getStackTrace(e));
                }
            }
        });
    }

    /**
     * 通过telegramUserId查询telegram绑定用户
     * 
     * @param telegramUserId
     * @return
     */
    public List<TelegramUserResponse> queryTelegramUserByTelegramUserId(Long telegramUserId) {
        if (telegramUserId == null) {
            return Collections.emptyList();
        }
        List<TelegramUserModel> telegramUsers = telegramUserCache.getByTelegramUserId(telegramUserId);
        if (CollectionUtils.isEmpty(telegramUsers)) {
            return Collections.emptyList();
        }

        List<TelegramUserResponse> telegramUserResponses = Lists.newArrayList();
        for (TelegramUserModel telegramUser : telegramUsers) {
            telegramUserResponses.add(new TelegramUserResponse()
                .setUid(telegramUser.getUid())
                .setTelegramUserId(telegramUser.getTelegramUserId()));
        }
        return telegramUserResponses;
    }


} 
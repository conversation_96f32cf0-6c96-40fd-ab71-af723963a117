package com.media.user.feign.client;

import com.media.user.dto.query.ClientUserQuery;
import com.media.user.dto.request.NewBieTaskRequest;
import com.media.user.dto.request.PointEventRequest;
import com.media.user.dto.request.RobotPointsGenerationRequest;
import com.media.user.dto.request.XmeRedemptionRequest;
import com.media.user.dto.request.internal.SignInEventRequest;
import com.media.user.dto.request.internal.UserBindChangeRequest;
import com.media.user.dto.response.PointRuleResponse;
import com.media.user.dto.response.internal.CcyPriceReponse;
import com.xme.xme_base_depends.models.ApiResponse;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@FeignClient(url = "${point.url}", value = "userPointClient", configuration = UserPointClient.UserPointClientConfiguration.class)
public interface UserPointClient {

    @Configuration
    class UserPointClientConfiguration {
        @Bean("userPointClientOptions")
        public Request.Options options() {
            // Set connect timeout to 200ms and read timeout to 2000ms
            return new Request.Options(200, TimeUnit.MILLISECONDS, 2000, TimeUnit.MILLISECONDS, true);
        }
    }


    @PostMapping(value = "/internal/point/user/signup")
    ApiResponse signupEvent(@RequestBody PointEventRequest request);

    @PostMapping(value = "/internal/point/user/invite")
    ApiResponse inviteEvent(@RequestBody PointEventRequest request);

    @PostMapping("/internal/point/generation")
    ApiResponse robotPointsGeneration(@RequestBody RobotPointsGenerationRequest request);

    @PostMapping("/internal/account/merge")
    ApiResponse sendBindChange(@RequestBody UserBindChangeRequest request);

    /**
     * 每日签到任务配置查询
     * @return
     */
    @PostMapping("/internal/point/signInJob")
    ApiResponse signInJob();

    /**
     * 推送签到请求
     * @return
     */
    @PostMapping("/internal/point/jobEvent")
    ApiResponse jobEvent(@RequestBody SignInEventRequest request);


    /**
     * 推送签到请求,eventId 默认为 1 只用来做标记
     * @return
     */
    @PostMapping("/internal/point/usersign")
    ApiResponse signJobEvent(@RequestBody SignInEventRequest request);


    @PostMapping("/internal/point/active")
    ApiResponse<Map<Long, Boolean>> getUserActive(@RequestBody ClientUserQuery query);


    @PostMapping("/internal/point/noviceJob")
    ApiResponse<List<PointRuleResponse>> newTaskList();

    @PostMapping("/internal/point/jobEvent")
    ApiResponse<BigDecimal> jobEvent(@RequestBody NewBieTaskRequest request);

    @PostMapping("/internal/account/xmeRedemptionFromOP")
    ApiResponse xmeRedemptionFromOP(@RequestBody XmeRedemptionRequest request);

    @PostMapping("/internal/account/getCcyPrice")
    ApiResponse<CcyPriceReponse> getCcyPrice(@RequestBody XmeRedemptionRequest request);

}

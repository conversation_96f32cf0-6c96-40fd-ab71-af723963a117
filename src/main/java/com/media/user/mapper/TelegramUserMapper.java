package com.media.user.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.TelegramUserModel;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface TelegramUserMapper extends BaseMapper<TelegramUserModel> {

    /**
     * 根据用户ID和Telegram用户ID查询Telegram用户
     * @param uid
     * @param telegramUserId
     * @return
     */
    default TelegramUserModel getByUidAndTelegramUserId(Long uid, Long telegramUserId) {
        return selectOne(new LambdaQueryWrapper<TelegramUserModel>()
            .eq(TelegramUserModel::getUid, uid)
            .eq(TelegramUserModel::getTelegramUserId, telegramUserId)
            .eq(TelegramUserModel::getStatus, 1)
        );
    }

    /**
     * 根据用户ID查询Telegram用户
     */
    default List<TelegramUserModel> getByUid(Long uid) {
        return selectList(new LambdaQueryWrapper<TelegramUserModel>()
            .eq(TelegramUserModel::getUid, uid)
            .eq(TelegramUserModel::getStatus, 1)
        );
    }

    /**
     * 插入Telegram用户
     * @param telegramUser
     * @return
     */
    default int insertTelegramUser(TelegramUserModel telegramUser) {
        return insert(telegramUser);
    }

    /**
     * 根据Telegram用户ID查询
     * @param telegramUserId
     * @return
     */
    default List<TelegramUserModel> getByTelegramUserId(Long telegramUserId) {
        return selectList(new LambdaQueryWrapper<TelegramUserModel>()
            .eq(TelegramUserModel::getTelegramUserId, telegramUserId)
            .eq(TelegramUserModel::getStatus, 1)
        );
    }


} 
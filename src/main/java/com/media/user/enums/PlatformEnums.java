package com.media.user.enums;

import lombok.Getter;

public enum PlatformEnums {

    ANDROID((byte)0, "ANDROID"),
    IOS((byte) 1, "IOS"),
    WEB((byte) 2, "WEB"),
    CONTENT((byte) 3, "CONTENT"),
    ANDROID_GP((byte) 4, "ANDROID-GP"),
    UNKNOWN((byte) 5, "UNKNOWN"),
    ;

    @Getter
    private Byte code;
    @Getter
    private String name;

    private PlatformEnums(Byte code, String name) {
        this.code = code;
        this.name = name;
    }

    public static PlatformEnums get(String name) {
        if (name == null) {
            return UNKNOWN;
        }
        String normalized = name.trim().toUpperCase();

        if (normalized.startsWith("ANDROID-GP")) {
            return ANDROID_GP;
        }else if (normalized.startsWith("ANDROID")) {
            return ANDROID;
        }else if (normalized.startsWith("IOS")) {
            return IOS;
        } else if (normalized.startsWith("WEB")) {
            return WEB;
        } else if (normalized.startsWith("CONTENT")) {
            return CONTENT;
        }
        return UN<PERSON><PERSON><PERSON><PERSON>;
    }
}

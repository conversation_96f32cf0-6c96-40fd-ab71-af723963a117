package com.media.user.convert.mapper;

import com.media.user.domain.UserFamilyGroupModel;
import com.media.user.dto.response.UserFamilyInfoResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserFamilyConvert {

    UserFamilyConvert INSTANCE = Mappers.getMapper(UserFamilyConvert.class);

    UserFamilyInfoResponse model2Response(UserFamilyGroupModel userFamilyGroupModel);


}

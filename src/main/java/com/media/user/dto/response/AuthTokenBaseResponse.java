package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.concurrent.TimeUnit;

@Accessors(chain = true)
@Data
public class AuthTokenBaseResponse {

    /**
     * 用户ID.
     */
    private Long userId;

    /**
     * 超时时间(需要配合timeUnit 比如  7 + TimeUnit.Day）
     */
    private Long timeout;

    /**
     * 时间类型
     */
    private TimeUnit timeUnit;

    /**
     * 过期时间（long类型时间戳）
     */
    private Long expire;
}

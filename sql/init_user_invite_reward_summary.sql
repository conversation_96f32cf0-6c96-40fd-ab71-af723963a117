-- 初始化用户邀请奖励汇总表数据
-- 从现有的 user_invite_face_record 表中统计数据并插入到 user_invite_reward_summary 表

-- 先清空汇总表（如果需要重新初始化）
-- TRUNCATE TABLE user_invite_reward_summary;

-- 插入第一种货币的汇总数据（from_amount + from_amount_symbol）
INSERT INTO user_invite_reward_summary (uid, currency, total_amount, total_count, last_updated_time, created_time)
SELECT 
    from_uid as uid,
    from_amount_symbol as currency,
    SUM(from_amount) as total_amount,
    COUNT(1) as total_count,
    MAX(updated_time) as last_updated_time,
    MIN(created_time) as created_time
FROM user_invite_face_record 
WHERE from_uid IS NOT NULL 
    AND from_amount IS NOT NULL 
    AND from_amount > 0
    AND from_amount_symbol IS NOT NULL
    AND amount_status = 2  -- 只统计已确认的记录
GROUP BY from_uid, from_amount_symbol
ON DUPLICATE KEY UPDATE 
    total_amount = VALUES(total_amount),
    total_count = VALUES(total_count),
    last_updated_time = VALUES(last_updated_time);

-- 插入第二种货币的汇总数据（from_amount_2 + from_amount_symbol_2）
INSERT INTO user_invite_reward_summary (uid, currency, total_amount, total_count, last_updated_time, created_time)
SELECT 
    from_uid as uid,
    from_amount_symbol_2 as currency,
    SUM(from_amount_2) as total_amount,
    COUNT(1) as total_count,
    MAX(updated_time) as last_updated_time,
    MIN(created_time) as created_time
FROM user_invite_face_record 
WHERE from_uid IS NOT NULL 
    AND from_amount_2 IS NOT NULL 
    AND from_amount_2 > 0
    AND from_amount_symbol_2 IS NOT NULL
    AND amount_status = 2  -- 只统计已确认的记录
GROUP BY from_uid, from_amount_symbol_2
ON DUPLICATE KEY UPDATE 
    total_amount = total_amount + VALUES(total_amount),
    total_count = total_count + VALUES(total_count),
    last_updated_time = GREATEST(last_updated_time, VALUES(last_updated_time));

-- 验证初始化结果
SELECT 
    '汇总统计' as type,
    COUNT(DISTINCT uid) as unique_users,
    COUNT(DISTINCT currency) as unique_currencies,
    COUNT(*) as total_records,
    SUM(total_amount) as total_rewards,
    SUM(total_count) as total_transactions
FROM user_invite_reward_summary;

-- 按货币查看汇总情况
SELECT 
    currency,
    COUNT(DISTINCT uid) as user_count,
    SUM(total_amount) as currency_total,
    SUM(total_count) as transaction_count,
    AVG(total_amount) as avg_per_user,
    MAX(total_amount) as max_per_user
FROM user_invite_reward_summary 
GROUP BY currency 
ORDER BY 
    CASE 
        WHEN currency = 'XME' THEN 1
        WHEN currency = 'BTC' THEN 2
        ELSE 3
    END,
    currency_total DESC;

-- 查看Top 10用户奖励情况
SELECT 
    uid,
    currency,
    total_amount,
    total_count,
    last_updated_time
FROM user_invite_reward_summary 
ORDER BY total_amount DESC 
LIMIT 10; 
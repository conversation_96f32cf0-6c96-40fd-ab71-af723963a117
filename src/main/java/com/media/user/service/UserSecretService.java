package com.media.user.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.eventbus.EventBus;
import com.media.core.constant.BeanConstant;
import com.media.core.exception.ApiException;
import com.media.core.utils.DeviceIdUtil;
import com.media.core.utils.IpUtil;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.*;
import com.media.user.dto.request.*;
import com.media.user.dto.response.CompleteTaskResponse;
import org.springframework.scheduling.annotation.Async;
import com.media.core.request.ClientInfoContext;
import com.media.user.enums.BusinessTypeEnum;
import com.media.user.enums.PhoneVerifyCodeTypeEnum;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.MediaTaskClient;
import com.media.user.feign.client.RiskClient;
import com.media.user.mapper.ClientUserMapper;
import com.media.user.tools.PhoneUtils;
import com.media.core.utils.UserAgentUtil;
import com.media.core.utils.PlatformTypeUtil;
import com.media.core.utils.BoxIdUtil;
import com.xme.xme_base_depends.enums.UserBehaviorEventEnum;
import com.xme.xme_base_depends.models.ApiResponse;
import com.xme.xme_base_depends.mq.message.UserFinishEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class UserSecretService {

    @Autowired
    private ClientUserMapper clientUserMapper;

    @Autowired
    PhoneService phoneService;

    @Autowired
    SmsService smsService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    PhoneUtils phoneUtils;

    @Autowired
    private MediaTaskClient mediaTaskClient;

    @Autowired
    private RiskClient riskClient;

    @Autowired
    private DeviceIdUtil deviceIdUtil;

    @Autowired
    private IpUtil ipUtil;

    @Autowired
    private UserAgentUtil userAgentUtil;

    @Autowired
    private PlatformTypeUtil platformTypeUtil;

    @Autowired
    private BoxIdUtil boxIdUtil;

    @Autowired
    @Qualifier(value = BeanConstant.USER_EVENT_BUS_BEAN)
    private EventBus userEventBus;

    @Autowired
    private UserInviteFaceRecordService userInviteFaceRecordService;

    /**
     * 绑定手机号接口
     * 手机号，验证码
     */
    public Integer bindPhone(Long uid, BindPhoneRequest request) {
        log.info("UserSecretService bindPhone: {}, {}", uid, request);
        // 校验手机号格式
        phoneUtils.checkPhoneRegex(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());

        // 校验验证码
        phoneService.verifyPhoneCode(uid, request.getCountryCode(), request.getPhonePrefix(), request.getPhone(),
                PhoneVerifyCodeTypeEnum.PHONE_BIND_CODE.getValue(), request.getCode());

        // 检查手机号是否已经绑定用户
        ClientUserModel user = clientUserMapper.selectByPhone(request.getCountryCode(), request.getPhonePrefix(),
                request.getPhone());
        if (user != null) {
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_ALREADY_BIND);
        }
        ClientUserModel clientUserModel = clientUserMapper.selectById(uid);
        if (StringUtils.isNotBlank(clientUserModel.getPhone())) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_PHONE_ALREADY_SET);
        }

        Date now = new Date();
        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ClientUserModel::getUid, clientUserModel.getUid())
                .set(ClientUserModel::getCountryCode, request.getCountryCode())
                .set(ClientUserModel::getPhonePrefix, request.getPhonePrefix())
                .set(ClientUserModel::getPhone, request.getPhone())
                .set(ClientUserModel::getPhoneVerify, 1)
                .set(ClientUserModel::getPhoneVerifyTime, now)
                .set(ClientUserModel::getUpdatedTime, now);
        clientUserMapper.update(updateWrapper);

        userEventBus.post(new UserFinishEvent(UserBehaviorEventEnum.PHONE_FINISH, clientUserModel.getUid()));

        // 删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + clientUserModel.getUid());

        // 标记验证码失效
        phoneService.signPhoneCodeExpire(uid, clientUserModel.getCountryCode(), clientUserModel.getPhonePrefix(),
                request.getPhone(), PhoneVerifyCodeTypeEnum.PHONE_BIND_CODE.getValue());

        // 发送完成任务
        Integer epValue = sendMediaTask(uid, MediaUserConstant.TASK_CODE_PHONE_VERIFY, "/user/api/user/bind/phone");

        // 营销风险检测
        sendMarketingRiskAsync(uid, ipUtil.getIpAddr(), userAgentUtil.getUserAgent(),
                ClientInfoContext.get().getVersion(), platformTypeUtil.getPlatformType(), boxIdUtil.getBoxId(),
                "task", MediaUserConstant.TASK_CODE_PHONE_VERIFY);

        // 如果是首次认证完成三选一任务
        userInviteFaceRecordService.handleFirstVerification(uid);

        return epValue;
    }

    /**
     * 绑定邮箱
     *
     * @param uid
     * @param request
     */
    public Integer bindEmail(Long uid, BindEmailRequest request) {
        log.info("UserSecretService bindEmail: {}, {}", uid, request);

        // 校验验证码
        smsService.verifyEmailCode(uid, request.getEmail(), BusinessTypeEnum.EMAIL_BIND_CODE, request.getCode());

        // 检查邮箱是否已经绑定用户
        ClientUserModel user = clientUserMapper.selectByEmail(request.getEmail());
        if (user != null) {
            throw new ApiException(MediaUserExceptionCodeApi.EMAIL_ALREADY_BIND);
        }

        ClientUserModel clientUserModel = clientUserMapper.selectById(uid);
        if (StringUtils.isNotBlank(clientUserModel.getEmail())) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_EMAIL_ALREADY_SET);
        }
        Date now = new Date();
        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ClientUserModel::getUid, clientUserModel.getUid())
                .set(ClientUserModel::getEmail, request.getEmail())
                .set(ClientUserModel::getEmailVerify, 1)
                .set(ClientUserModel::getEmailVerifyTime, now)
                .set(ClientUserModel::getUpdatedTime, now);
        clientUserMapper.update(updateWrapper);

        userEventBus.post(new UserFinishEvent(UserBehaviorEventEnum.EMAIL_FINISH, clientUserModel.getUid()));

        // 删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + clientUserModel.getUid());

        // 标记验证码失效
        smsService.signEmailCodeExpire(uid, request.getEmail(), BusinessTypeEnum.EMAIL_BIND_CODE);

        // 发送完成任务
        Integer epValue = sendMediaTask(uid, MediaUserConstant.TASK_CODE_EMAIL_VERIFY, "/user/api/user/bind/email");

        // 营销风险检测
        sendMarketingRiskAsync(uid, ipUtil.getIpAddr(), userAgentUtil.getUserAgent(),
                ClientInfoContext.get().getVersion(), platformTypeUtil.getPlatformType(), boxIdUtil.getBoxId(),
                "task", MediaUserConstant.TASK_CODE_EMAIL_VERIFY);

        userInviteFaceRecordService.handleFirstVerification(uid);

        return epValue;
    }

    /**
     * 验证手机号
     *
     * @param uid
     * @param code
     */
    public Integer verifyPhone(Long uid, String code) {
        log.info("UserSecretService verifyPhone: {}, {}", uid, code);

        ClientUserModel clientUserModel = clientUserMapper.selectById(uid);
        if (clientUserModel == null) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        if (StringUtils.isEmpty(clientUserModel.getPhone())) {
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_NOT_EXIST);
        }

        // 校验验证码
        phoneService.verifyPhoneCode(uid, clientUserModel.getCountryCode(), clientUserModel.getPhonePrefix(),
                clientUserModel.getPhone(), PhoneVerifyCodeTypeEnum.PHONE_VERIFY_CODE.getValue(), code);

        // 更新手机号验证状态
        Date now = new Date();
        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ClientUserModel::getUid, clientUserModel.getUid())
                .set(ClientUserModel::getPhoneVerify, 1)
                .set(ClientUserModel::getPhoneVerifyTime, now)
                .set(ClientUserModel::getUpdatedTime, now);
        clientUserMapper.update(updateWrapper);

        userEventBus.post(new UserFinishEvent(UserBehaviorEventEnum.PHONE_FINISH, clientUserModel.getUid()));

        // 删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + clientUserModel.getUid());

        // 标记验证码失效
        phoneService.signPhoneCodeExpire(uid, clientUserModel.getCountryCode(), clientUserModel.getPhonePrefix(),
                clientUserModel.getPhone(), PhoneVerifyCodeTypeEnum.PHONE_VERIFY_CODE.getValue());

        // 发送完成任务
        Integer epValue = sendMediaTask(uid, MediaUserConstant.TASK_CODE_PHONE_VERIFY, "/user/api/user/bind/phone");

        // 营销风险检测
        sendMarketingRiskAsync(uid, ipUtil.getIpAddr(), userAgentUtil.getUserAgent(),
                ClientInfoContext.get().getVersion(), platformTypeUtil.getPlatformType(), boxIdUtil.getBoxId(),
                "task", MediaUserConstant.TASK_CODE_PHONE_VERIFY);

        userInviteFaceRecordService.handleFirstVerification(uid);

        return epValue;
    }

    /**
     * 验证邮箱
     *
     * @param uid
     * @param code
     */
    public Integer verifyEmail(Long uid, String code) {
        log.info("UserSecretService verifyEmail: {}, {}", uid, code);

        ClientUserModel clientUserModel = clientUserMapper.selectById(uid);
        if (clientUserModel == null) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        if (StringUtils.isEmpty(clientUserModel.getEmail())) {
            throw new ApiException(MediaUserExceptionCodeApi.EMAIL_NOT_EXIST);
        }

        // 校验验证码
        smsService.verifyEmailCode(uid, clientUserModel.getEmail(), BusinessTypeEnum.EMAIL_VERIFY_CODE, code);

        // 更新邮箱验证状态
        Date now = new Date();
        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ClientUserModel::getUid, clientUserModel.getUid())
                .set(ClientUserModel::getEmailVerify, 1)
                .set(ClientUserModel::getEmailVerifyTime, now)
                .set(ClientUserModel::getUpdatedTime, now);
        clientUserMapper.update(updateWrapper);

        userEventBus.post(new UserFinishEvent(UserBehaviorEventEnum.EMAIL_FINISH, clientUserModel.getUid()));

        // 删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + clientUserModel.getUid());

        // 标记验证码失效
        smsService.signEmailCodeExpire(uid, clientUserModel.getEmail(), BusinessTypeEnum.EMAIL_VERIFY_CODE);

        // 发送完成任务
        Integer epValue = sendMediaTask(uid, MediaUserConstant.TASK_CODE_EMAIL_VERIFY, "/user/api/user/bind/email");

        // 营销风险检测
        sendMarketingRiskAsync(uid, ipUtil.getIpAddr(), userAgentUtil.getUserAgent(),
                ClientInfoContext.get().getVersion(), platformTypeUtil.getPlatformType(), boxIdUtil.getBoxId(),
                "task", MediaUserConstant.TASK_CODE_EMAIL_VERIFY);

        userInviteFaceRecordService.handleFirstVerification(uid);

        return epValue;
    }

    // 活体认证发送完成任务
    public Integer sendMediaTask(Long uid, String taskCode, String path) {
        log.info("sendMediaTask:{}", uid);

        Integer epValue = null;
        // 发送完成任务
        try {
            CompleteTaskRequest request = new CompleteTaskRequest();
            request.setUid(uid);
            request.setTaskCode(taskCode); // 首次邀请任务编码
            ApiResponse<CompleteTaskResponse> response = mediaTaskClient.completeTask(request);
            if (response != null && response.getSuccess() && response.getResult() != null) {
                epValue = response.getResult().getGetEpValue();
                log.info("发送完成任务成功, uid:{}, taskCode:{}, getEpValue:{}", uid, taskCode, epValue);
            } else {
                log.info("发送完成任务成功, uid:{}, taskCode:{}, 无EP值返回", uid, taskCode);
            }
        } catch (Exception e) {
            log.error("发送完成任务失败, uid:{}, taskCode:{}", uid, taskCode,
                    e.getMessage(), e);
        }

        // 提交风控完成任务
        try {
            String deviceId = deviceIdUtil.getDeviceId();
            String ip = ipUtil.getIpAddr();
            TaskLogRequest taskLogRequest = new TaskLogRequest();
            taskLogRequest.setUser_id(uid);
            taskLogRequest.setService("media-user");
            taskLogRequest.setPath(path);
            taskLogRequest.setType(taskCode);
            taskLogRequest.setDevice_id(deviceId);
            taskLogRequest.setIp(ip);
            Map<String, Object> other = new HashMap<>();
            other.put("uid", uid);
            other.put("path", path);
            other.put("taskCode", taskCode);
            taskLogRequest.setOther(other);
            riskClient.createTaskLog(taskLogRequest);
        } catch (Exception e) {
            log.error("提交风控完成任务失败, uid:{}, taskCode:{}", uid, taskCode, e.getMessage(), e);
        }

        return epValue;
    }

    /**
     * 异步执行营销风险检测
     *
     * @param userId        用户ID
     * @param ip            IP地址
     * @param userAgent     用户代理
     * @param appVersion    应用版本
     * @param os            操作系统
     * @param boxId         设备盒子ID
     * @param marketingType 营销类型
     * @param taskId        任务ID
     */
    @Async("threadPoolTaskExecutor")
    public void sendMarketingRiskAsync(Long userId, String ip, String userAgent, String appVersion, String os,
            String boxId, String marketingType, String taskId) {
        if (boxId == null) {
            log.warn("营销风险检测异步调用失败: userId={}, boxId is null", userId);
            return;
        }
        try {
            MarketingRiskRequest marketingRiskRequest = new MarketingRiskRequest();
            marketingRiskRequest.setUserId(userId);
            marketingRiskRequest.setIp(ip);
            marketingRiskRequest.setUserAgent(userAgent);
            marketingRiskRequest.setAppVersion(appVersion);
            marketingRiskRequest.setOs(os);
            marketingRiskRequest.setBoxId(boxId);
            marketingRiskRequest.setMarketingType(marketingType);
            marketingRiskRequest.setTaskId(taskId);

            riskClient.marketingRisk(marketingRiskRequest);
            log.info("营销风险检测异步调用成功: userId={}, marketingType={}, taskId={}", userId, marketingType, taskId);
        } catch (Exception e) {
            log.warn("营销风险检测异步调用失败: userId={}, marketingType={}, taskId={}, error={}",
                    userId, marketingType, taskId, e.getMessage());
        }
    }
}

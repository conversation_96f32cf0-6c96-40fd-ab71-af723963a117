package com.media.user.enums;

import com.media.user.constant.PhoneSnsTemplateConstant;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * 手机号前缀
 */
@Getter
public enum SupportPhonePrefixEnum {

    United_States ("+1","US","美国","United States","UnitedStates","",1,Pattern.compile("^\\+1\\d{10}$"),PhoneSnsTemplateConstant.en_US),
    Vietnam ("+84","VN","越南","Việt Nam","Vietnam","",2,Pattern.compile("^\\+84\\d{9,10}$"),PhoneSnsTemplateConstant.vi_VN),
    India ("+91","IN","印度","भारत (Bhārat)","India","",3,Pattern.compile("^\\+91[6-9]\\d{9}$"),PhoneSnsTemplateConstant.hi_IN),
    China ("+86","CN","中国","中国","China","",4,Pattern.compile("^\\+86(1[3-9]\\d{9})$"),PhoneSnsTemplateConstant.zh_CN),
    Indonesia ("+62","ID","印度尼西亚","Indonesia","Indonesia","",5,Pattern.compile("^\\+62\\d{9,12}$"),PhoneSnsTemplateConstant.id_ID),
    Philippines ("+63","PH","菲律宾","Pilipinas","Philippines","",6,Pattern.compile("^\\+63\\d{10}$"),PhoneSnsTemplateConstant.en_US),
    Thailand ("+66","TH","泰国","ประเทศไทย","Thailand","",7,Pattern.compile("^\\+66\\d{9}$"),PhoneSnsTemplateConstant.th_TH),
    Nigeria ("+234","NG","尼日利亚","Nigeria","Nigeria","",8,Pattern.compile("^\\+234\\d{10}$"),PhoneSnsTemplateConstant.en_US),
    Kenya ("+254","KE","肯尼亚","Kenya","Kenya","",9,Pattern.compile("^\\+254\\d{9}$"),PhoneSnsTemplateConstant.en_US),
    South_Africa ("+27","SA","南非","South Africa","SouthAfrica","",10,Pattern.compile("^\\+27\\d{9}$"),PhoneSnsTemplateConstant.en_US),
    Brazil ("+55","BR","巴西","Brasil","Brazil","",11,Pattern.compile("^\\+55\\d{10,11}$"),PhoneSnsTemplateConstant.pt_PT),
    Mexico ("+52","MX","墨西哥","México","Mexico","",12,Pattern.compile("^\\+52\\d{10}$"),PhoneSnsTemplateConstant.es_ES),
    Argentina ("+54","AR","阿根廷","Argentina","Argentina","",13,Pattern.compile("^\\+54\\d{10,11}$"),PhoneSnsTemplateConstant.es_ES),
    Pakistan ("+92","PK","巴基斯坦","پاکستان","Pakistan","",14,Pattern.compile("^\\+92\\d{10}$"),PhoneSnsTemplateConstant.ur_PK),
    Turkey ("+90","TR","土耳其","Türkiye","Turkey","",15,Pattern.compile("^\\+90\\d{10}$"),PhoneSnsTemplateConstant.tr_TR),
    South_Korea ("+82","KR","韩国","대한민국","SouthKorea","",16,Pattern.compile("^\\+82\\d{9,10}$"),PhoneSnsTemplateConstant.ko_KR),
    Russia ("+7","RU","俄罗斯","Россия","Russia","",17,Pattern.compile("^\\+7\\d{10}$"),PhoneSnsTemplateConstant.ru_RU),
    Ukraine ("+380","UA","乌克兰","Україна","Ukraine","",18,Pattern.compile("^\\+380\\d{9}$"),PhoneSnsTemplateConstant.uk_UA),
    Malaysia ("+60","MY","马来西亚","Malaysia","Malaysia","",19,Pattern.compile("^\\+60\\d{9,10}$"),PhoneSnsTemplateConstant.ms_MY),
    Colombia ("+57","CO","哥伦比亚","Colombia","Colombia","",20,Pattern.compile("^\\+57\\d{10}$"),PhoneSnsTemplateConstant.es_ES),
    United_Kingdom ("+44","GB","英国","United Kingdom","UnitedKingdom","",21,Pattern.compile("^\\+44[7]\\d{9}$"),PhoneSnsTemplateConstant.en_US),
    France ("+33","FR","法国","France","France","",22,Pattern.compile("^\\+33\\d{9}$"),PhoneSnsTemplateConstant.fr_FR),
    Germany ("+49","DE","德国","Deutschland","Germany","",23,Pattern.compile("^\\+49\\d{10,11}$"),PhoneSnsTemplateConstant.ber_DZ),
    Japan ("+81","JP","日本","日本","Japan","",24,Pattern.compile("^\\+81\\d{9,10}$"),PhoneSnsTemplateConstant.ja_JP),
    Algeria ("+213","DZ","阿尔及利亚","الجزائر","Algeria","",25,Pattern.compile("^\\+213\\d{9}$"),PhoneSnsTemplateConstant.en_US),
    Angola ("+244","AO","安哥拉","Angola","Angola","",26,Pattern.compile("^\\+244\\d{9}$"),PhoneSnsTemplateConstant.pt_PT),
    Armenia ("+374","AM","亚美尼亚","Հայաստան","Armenia","",27,Pattern.compile("^\\+374\\d{8}$"),PhoneSnsTemplateConstant.hy_AM),
    Australia ("+61","AU","澳大利亚","Australia","Australia","",28,Pattern.compile("^\\+61\\d{9}$"),PhoneSnsTemplateConstant.en_US),
    Austria ("+43","AT","奥地利","Österreich","Austria","",29,Pattern.compile("^\\+43\\d{9,10}$"),PhoneSnsTemplateConstant.de_DE),
    Azerbaijan ("+994","AZ","阿塞拜疆","Azərbaycan","Azerbaijan","",30,Pattern.compile("^\\+994\\d{9}$"),PhoneSnsTemplateConstant.az_Latn),
    Bahamas ("+242","BS","巴哈马","The Bahamas","Bahamas","",31,Pattern.compile("^\\+1242\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Bahrain ("+973","BH","巴林","البحرين","Bahrain","",32,Pattern.compile("^\\+973\\d{8}$"),PhoneSnsTemplateConstant.ar_SA),
    Bangladesh ("+880","BD","孟加拉国","বাংলাদেশ","Bangladesh","",33,Pattern.compile("^\\+880\\d{10,12}$"),PhoneSnsTemplateConstant.bn_Bangla),
    Barbados ("******","BB","巴巴多斯","Barbados","Barbados","",34,Pattern.compile("^\\+1246\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Belarus ("+375","BY","白俄罗斯","Беларусь","Belarus","",35,Pattern.compile("^\\+375\\d{9}$"),PhoneSnsTemplateConstant.be_BY),
    Belgium ("+32","BE","比利时","België","Belgium","",36,Pattern.compile("^\\+32\\d{8,9}$"),PhoneSnsTemplateConstant.nl_NL),
    Belize ("+501","BZ","伯利兹","Belize","Belize","",37,Pattern.compile("^\\+501\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Botswana ("+267","BW","博茨瓦纳","Botswana","Botswana","",38,Pattern.compile("^\\+267\\d{8}$"),PhoneSnsTemplateConstant.en_US),
    Brunei ("+673","BN","文莱","Brunei","Brunei","",39,Pattern.compile("^\\+673\\d{7}$"),PhoneSnsTemplateConstant.ms_MY),
    Bulgaria ("+359","BG","保加利亚","България","Bulgaria","",40,Pattern.compile("^\\+359\\d{8,9}$"),PhoneSnsTemplateConstant.bg_BG),
    Burkina_Faso ("+226","BF","布基纳法索","Burkina Faso","BurkinaFaso","",41,Pattern.compile("^\\+226\\d{8}$"),PhoneSnsTemplateConstant.fr_FR),
    Burundi ("+257","BI","布隆迪","Burundi","Burundi","",42,Pattern.compile("^\\+257\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Cape_Verde ("+238","CV","佛得角","Cabo Verde","CapeVerde","",43,Pattern.compile("^\\+238\\d{7}$"),PhoneSnsTemplateConstant.el_GR),
    Cameroon ("+237","CM","喀麦隆","Cameroun","Cameroon","",44,Pattern.compile("^\\+237\\d{8}$"),PhoneSnsTemplateConstant.fr_FR),
    Canada ("+1","CA","加拿大","Canada","Canada","",45,Pattern.compile("^\\+1\\d{10}$"),PhoneSnsTemplateConstant.en_US),
    Central_African_Rep ("+236","CF","中非共和国","République centrafricaine","CentralAfricanRep","",46,Pattern.compile("^\\+236\\d{7,8}$"),PhoneSnsTemplateConstant.fr_FR),
    Chad("+235","TD","查德","Tchad","Chad","",47,Pattern.compile("^\\+235\\d{8}$"),PhoneSnsTemplateConstant.fr_FR),
    Chile ("+56","CL","智利","Chile","Chile","",48,Pattern.compile("^\\+56\\d{9}$"),PhoneSnsTemplateConstant.es_ES),
    Comoros ("+269","KM","科摩罗","جزر القمر","Comoros","",49,Pattern.compile("^\\+269\\d{7}$"),PhoneSnsTemplateConstant.ar_SA),
    Congo_Republic("+242","CG","刚果（布）","République du Congo","CongoRepublic","",50,Pattern.compile("^\\+242\\d{8}$"),PhoneSnsTemplateConstant.fr_FR),
    Congo_DRC("+243","CD","刚果（金）","République Démocratique du Congo","CongoDRC","",51,Pattern.compile("^\\+243\\d{9}$"),PhoneSnsTemplateConstant.fr_FR),
    Costa_Rica ("+506","CR","哥斯达黎加","Costa Rica","CostaRica","",52,Pattern.compile("^\\+506\\d{8}$"),PhoneSnsTemplateConstant.es_ES),
    Croatia ("+385","HR","克罗地亚","Hrvatska","Croatia ","",53,Pattern.compile("^\\+385\\d{9}$"),PhoneSnsTemplateConstant.hr_HR),
    Cuba ("+53","CU","古巴","Cuba","Cuba","",54,Pattern.compile("^\\+53\\d{8}$"),PhoneSnsTemplateConstant.pt_PT),
    Cyprus ("+357","CY","塞浦路斯","Κύπρος","Cyprus","",55,Pattern.compile("^\\+357\\d{8}$"),PhoneSnsTemplateConstant.cs_CZ),
    Czech_Republic ("+420","CZ","捷克","Česko","CzechRepublic","",56,Pattern.compile("^\\+420\\d{9}$"),PhoneSnsTemplateConstant.de_DE),
    Lithuania ("+370","LT","立陶宛","Lietuva","Lithuania","",57,Pattern.compile("^\\+370\\d{8}$"),PhoneSnsTemplateConstant.lt_LT),
    Luxembourg ("+352","LU","卢森堡","Lëtzebuerg","Luxembourg","",58,Pattern.compile("^\\+352\\d{8}$"),PhoneSnsTemplateConstant.fr_FR),
    Latvia ("+371","LV","拉脱维亚","Latvija","Latvia","",59,Pattern.compile("^\\+371\\d{7,8}$"),PhoneSnsTemplateConstant.lv_LV),
    Lesotho ("+266","LS","莱索托","Lesotho","Lesotho","",60,Pattern.compile("^\\+266\\d{8}$"),PhoneSnsTemplateConstant.st_LS),
    Liberia ("+231","LR","利比里亚","Liberia","Liberia","",61,Pattern.compile("^\\+231\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Libya ("+218","LY","利比亚","ليبيا","Libya","",62,Pattern.compile("^\\+218\\d{9}$"),PhoneSnsTemplateConstant.ar_SA),
    Madagascar ("+261","MG","马达加斯加","Madagasikara","Madagascar","",63,Pattern.compile("^\\+261\\d{9}$"),PhoneSnsTemplateConstant.mg_MG),
    Maldives ("+960","MV","马尔代夫","ދިވެހިބަސް","Maldives","",64,Pattern.compile("^\\+960\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Mali ("+223","ML","马里","Mali","Mali","",65,Pattern.compile("^\\+223\\d{8}$"),PhoneSnsTemplateConstant.fr_FR),
    Malta ("+356","MT","马耳他","Malta","Malta","",66,Pattern.compile("^\\+356\\d{8}$"),PhoneSnsTemplateConstant.en_US),
    Marshall_Islands ("+692","MH","马绍尔群岛","Marshall Islands","MarshallIslands","",67,Pattern.compile("^\\+692\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Mauritania ("+222","MR","毛里塔尼亚","موريتانيا","Mauritania","",68,Pattern.compile("^\\+222\\d{8}$"),PhoneSnsTemplateConstant.ar_SA),
    Mauritius ("+230","MU","毛里求斯","Maurice","Mauritius","",69,Pattern.compile("^\\+230\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Micronesia ("+691","FM","密克罗尼西亚","Micronesia","Micronesia","",70,Pattern.compile("^\\+691\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Moldova ("+373","MD","摩尔多瓦","Moldova","Moldova","",71,Pattern.compile("^\\+373\\d{8}$"),PhoneSnsTemplateConstant.ro_RO),
    Monaco ("+377","MC","摩纳哥","Monaco","Monaco","",72,Pattern.compile("^\\+377\\d{8}$"),PhoneSnsTemplateConstant.fr_FR),
    Mongolia ("+976","MN","蒙古","Монгол Улс","Mongolia","",73,Pattern.compile("^\\+976\\d{8}$"),PhoneSnsTemplateConstant.mn_MN),
    Mozambique ("+258","MZ","莫桑比克","Moçambique","Mozambique","",74,Pattern.compile("^\\+258\\d{9}$"),PhoneSnsTemplateConstant.pt_PT),
    Myanmar ("+95","MM","缅甸","မြန်မာ","Myanmar","",75,Pattern.compile("^\\+95\\d{9,10}$"),PhoneSnsTemplateConstant.my_MM),
    Namibia ("+264","NA","纳米比亚","Namibia","Namibia","",76,Pattern.compile("^\\+264\\d{9}$"),PhoneSnsTemplateConstant.en_US),
    Nepal ("+977","NP","尼泊尔","नेपाल (Nepāl)","Nepal","",77,Pattern.compile("^\\+977\\d{10}$"),PhoneSnsTemplateConstant.ne_NP),
    Netherlands ("+31","NL","荷兰","Nederland","Netherlands","",78,Pattern.compile("^\\+31\\d{9}$"),PhoneSnsTemplateConstant.nl_NL),
    New_Zealand ("+64","NZ","新西兰","New Zealand","NewZealand","",79,Pattern.compile("^\\+64\\d{8,9}$"),PhoneSnsTemplateConstant.en_US),
    Nicaragua ("+505","NI","尼加拉瓜","Nicaragua","Nicaragua","",80,Pattern.compile("^\\+505\\d{7}$"),PhoneSnsTemplateConstant.es_ES),
    Niger ("+227","NE","尼日尔","Niger","Niger","",81,Pattern.compile("^\\+227\\d{8}$"),PhoneSnsTemplateConstant.fr_FR),
    North_Macedonia ("+389","MK","北马其顿","Северна Македонија","NorthMacedonia","",82,Pattern.compile("^\\+389\\d{8}$"),PhoneSnsTemplateConstant.mk_MK),
    Norway ("+47","NO","挪威","Norge / Noreg","Norway","",83,Pattern.compile("^\\+47\\d{8}$"),PhoneSnsTemplateConstant.nb_NO),
    Peru ("+51","PE","秘鲁","Perú","Peru","",84,Pattern.compile("^\\+51\\d{9}$"),PhoneSnsTemplateConstant.es_ES),
    Poland ("+48","PL","波兰","Polska","Poland","",85,Pattern.compile("^\\+48\\d{9}$"),PhoneSnsTemplateConstant.pl_PL),
    Portugal ("+351","PT","葡萄牙","Portugal","Portugal","",86,Pattern.compile("^\\+351\\d{9}$"),PhoneSnsTemplateConstant.pt_PT),
    Qatar ("+974","QA","卡塔尔","قطر","Qatar","",87,Pattern.compile("^\\+974\\d{8}$"),PhoneSnsTemplateConstant.ar_SA),
    Romania ("+40","RO","罗马尼亚","România","Romania","",88,Pattern.compile("^\\+40\\d{9}$"),PhoneSnsTemplateConstant.ro_RO),
    Rwanda ("+250","RW","卢旺达","Rwanda","Rwanda","",89,Pattern.compile("^\\+250\\d{9}$"),PhoneSnsTemplateConstant.rw_RW),
    Saint_Kitts_Nevis ("******","KN","圣基茨和尼维斯","Saint Kitts and Nevis","SaintKittsNevis","",90,Pattern.compile("^\\+1869\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Saint_Lucia ("******","LC","圣卢西亚","Saint Lucia","SaintLucia","",91,Pattern.compile("^\\+1758\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Saint_Vincent_Grenadines ("******","VC","圣文森特和格林纳丁斯","Saint Vincent and the Grenadines","SaintVincentGrenadines","",92,Pattern.compile("^\\+1784\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Samoa ("+685","WS","萨摩亚","Samoa","Samoa","",93,Pattern.compile("^\\+685\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    São_Tomé_Príncipe ("+239","ST","圣多美和普林西比","São Tomé e Príncipe","SãoToméPríncipe","",94,Pattern.compile("^\\+239\\d{7}$"),PhoneSnsTemplateConstant.pt_PT),
    Saudi_Arabia ("+966","SA","沙特阿拉伯","المملكة العربية السعودية","SaudiArabia","",95,Pattern.compile("^\\+966\\d{8,9}$"),PhoneSnsTemplateConstant.ar_SA),
    Serbia ("+381","RS","塞尔维亚","Србија","Serbia","",96,Pattern.compile("^\\+381\\d{9}$"),PhoneSnsTemplateConstant.sr_SP_Cyrl),
    Seychelles ("+248","SC","塞舌尔","Seychelles","Seychelles","",97,Pattern.compile("^\\+248\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Sierra_Leone ("+232","SL","塞拉利昂","Sierra Leone","SierraLeone","",98,Pattern.compile("^\\+232\\d{8}$"),PhoneSnsTemplateConstant.en_US),
    Singapore ("+65","SG","新加坡","Singapore","Singapore","",99,Pattern.compile("^\\+65\\d{8}$"),PhoneSnsTemplateConstant.en_US),
    Slovakia ("+421","SK","斯洛伐克","Slovensko","Slovakia","",100,Pattern.compile("^\\+421\\d{9}$"),PhoneSnsTemplateConstant.sk_SK),
    Slovenia ("+386","SI","斯洛文尼亚","Slovenija","Slovenia","",101,Pattern.compile("^\\+386\\d{8,9}$"),PhoneSnsTemplateConstant.sl_SI),
    Solomon_Islands ("+677","SB","所罗门群岛","Solomon Islands","SolomonIslands","",102,Pattern.compile("^\\+677\\d{6}$"),PhoneSnsTemplateConstant.en_US),
    Somalia ("+252","SO","索马里","Soomaaliya","Somalia","",103,Pattern.compile("^\\+252\\d{7,8}$"),PhoneSnsTemplateConstant.so_SO),
    South_Sudan ("+211","SS","南苏丹","South Sudan","SouthSudan","",104,Pattern.compile("^\\+211\\d{8,9}$"),PhoneSnsTemplateConstant.en_US),
    Spain ("+34","ES","西班牙","España","Spain","",105,Pattern.compile("^\\+34\\d{9}$"),PhoneSnsTemplateConstant.es_ES),
    Sri_Lanka ("+94","LK","斯里兰卡","ශ්‍රී ලංකා","SriLanka","",106,Pattern.compile("^\\+94\\d{9}$"),PhoneSnsTemplateConstant.si_LK),
    Sudan ("+249","SD","苏丹","السودان","Sudan","",107,Pattern.compile("^\\+249\\d{9}$"),PhoneSnsTemplateConstant.ar_SA),
    Suriname ("+597","SR","苏里南","Suriname","Suriname","",108,Pattern.compile("^\\+597\\d{7,8}$"),PhoneSnsTemplateConstant.nl_NL),
    Sweden ("+46","SE","瑞典","Sverige","Sweden","",109,Pattern.compile("^\\+46\\d{9}$"),PhoneSnsTemplateConstant.sv_SE),
    Switzerland ("+41","CH","瑞士","Schweiz","Switzerland","",110,Pattern.compile("^\\+41\\d{9}$"),PhoneSnsTemplateConstant.de_DE),
    Syria ("+963","SY","叙利亚","سوريا","Syria","",111,Pattern.compile("^\\+963\\d{9}$"),PhoneSnsTemplateConstant.ar_SA),
    Taiwan ("+886","TW","台湾","台灣","Taiwan","",112,Pattern.compile("^\\+886\\d{9}$"),PhoneSnsTemplateConstant.zh_TW),
    Tajikistan ("+992","TJ","塔吉克斯坦","Тоҷикистон","Tajikistan","",113,Pattern.compile("^\\+992\\d{9}$"),PhoneSnsTemplateConstant.tg_TJ),
    Timor_Leste ("+670","TL","东帝汶","Timor-Leste","TimorLeste","",114,Pattern.compile("^\\+670\\d{7,8}$"),PhoneSnsTemplateConstant.pt_PT),
    Togo ("+228","TG","多哥","Togo","Togo","",115,Pattern.compile("^\\+228\\d{8}$"),PhoneSnsTemplateConstant.fr_FR),
    Tonga ("+676","TO","汤加","Tonga","Tonga","",116,Pattern.compile("^\\+676\\d{5,6}$"),PhoneSnsTemplateConstant.to_TO),
    Trinidad_Tobago ("******","TT","特立尼达和多巴哥","Trinidad and Tobago","TrinidadTobago","",117,Pattern.compile("^\\+1868\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Tunisia ("+216","TN","突尼斯","تونس","Tunisia","",118,Pattern.compile("^\\+216\\d{8}$"),PhoneSnsTemplateConstant.ar_SA),
    Tuvalu ("+688","TV","图瓦卢","Tuvalu","Tuvalu","",119,Pattern.compile("^\\+688\\d{5}$"),PhoneSnsTemplateConstant.tvl_TV),
    Uganda ("+256","UG","乌干达","Uganda","Uganda","",120,Pattern.compile("^\\+256\\d{9}$"),PhoneSnsTemplateConstant.en_US),
    United_Arab_Emirates ("+971","AE","阿联酋","الإمارات العربية المتحدة","UnitedArabEmirates","",121,Pattern.compile("^\\+971\\d{9}$"),PhoneSnsTemplateConstant.ar_SA),
    Uruguay ("+598","UY","乌拉圭","Uruguay","Uruguay","",122,Pattern.compile("^\\+598\\d{8,9}$"),PhoneSnsTemplateConstant.es_ES),
    Uzbekistan ("+998","UZ","乌兹别克斯坦","Oʻzbekiston","Uzbekistan","",123,Pattern.compile("^\\+998\\d{9}$"),PhoneSnsTemplateConstant.uz_UZ),
    Vanuatu ("+678","VU","瓦努阿图","Vanuatu","Vanuatu","",124,Pattern.compile("^\\+678\\d{7}$"),PhoneSnsTemplateConstant.en_US),
    Venezuela ("+58","VE","委内瑞拉","Venezuela","Venezuela","",125,Pattern.compile("^\\+58\\d{10}$"),PhoneSnsTemplateConstant.es_ES),
    Yemen ("+967","YE","也门","اليمن","Yemen","",126,Pattern.compile("^\\+967\\d{8,9}$"),PhoneSnsTemplateConstant.ar_SA),
    Zambia ("+260","ZM","赞比亚","Zambia","Zambia","",127,Pattern.compile("^\\+260\\d{9}$"),PhoneSnsTemplateConstant.en_US),
    Zimbabwe ("+263","ZW","津巴布韦","Zimbabwe","Zimbabwe","",128,Pattern.compile("^\\+263\\d{9}$"),PhoneSnsTemplateConstant.en_US),
    Hongkong ("+852","HK","香港","香港","Hongkong","",129,Pattern.compile("^\\+852\\d{8}$"),PhoneSnsTemplateConstant.zh_TW),
    ;


    private String phonePrefix;  //手机号前缀
    private String abbreviation;  //国家简拼
    private String countryName;  //国家名称
    private String localName;  //当地语言
    private String countryCode;  //国家编号
    private String flag;  //国旗
    private Integer sortBy;      //排序
    private Pattern pattern;      //正则表达式
    private String snsTemplate;      //短信模板

    private SupportPhonePrefixEnum(String phonePrefix, String abbreviation, String countryName, String localName, String countryCode, String flag, Integer sortBy, Pattern pattern, String snsTemplate) {
        this.phonePrefix = phonePrefix;
        this.abbreviation = abbreviation;
        this.countryName = countryName;
        this.localName = localName;
        this.flag = flag;
        this.countryCode = countryCode;
        this.sortBy = sortBy;
        this.pattern = pattern;
        this.snsTemplate = snsTemplate;
    }

    /**
     * 根据国家获取手机号的正则表达式
     * @param countryCode
     * @return
     */
    public static SupportPhonePrefixEnum getPhonePrefixEnum(String countryCode, String phonePrefix) {
        if(StringUtils.isBlank(countryCode) || StringUtils.isBlank(phonePrefix)) {
            return null;
        }
        for (SupportPhonePrefixEnum phonePrefixEnum : SupportPhonePrefixEnum.values()) {
            if (phonePrefixEnum.getCountryCode().equals(countryCode) && phonePrefixEnum.getPhonePrefix().equals(phonePrefix)) {
                return phonePrefixEnum;
            }
        }
        return null;
    }
}

package com.media.user.service.strategy.impl;

import com.media.common.utils.JsonUtils;
import com.media.user.dto.im.RongCloudMessageData;
import com.media.user.dto.response.SensitiveWordDetectionResponse;
import com.media.user.dto.rongCloud.RongCloudCallbackDTO;
import com.media.user.enums.ImChannelTypeEnum;
import com.media.user.service.SensitiveWordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class PrivateMessageHandlerStrategy extends AbstractMessageHandlerStrategy {

    @Autowired
    private SensitiveWordService sensitiveWordService;

    private static final String MASK = "**";

    @Override
    public Map<String, Object> replaceContent(RongCloudMessageData messageData) {
        if (!validateMessageData(messageData)) return null;
        RongCloudCallbackDTO messageDTO = messageData.getMessageDTO();
        String content = messageDTO.getContent();
        SensitiveWordDetectionResponse res = sensitiveWordService.sensitiveWordDetection(content);
        if (Objects.isNull(res) || CollectionUtils.isEmpty(res.getSensitiveWords())) {
            return null;
        }
        List<String> sensitiveWords = res.getSensitiveWords();
        String newContent = sensitiveWords.stream().filter(Objects::nonNull).filter(word -> !word.isEmpty()).reduce(content, (currentContent, sensitiveWord) -> currentContent.replace(sensitiveWord, MASK));
        return JsonUtils.toMap(newContent);
    }

    private boolean validateMessageData(RongCloudMessageData messageData) {
        return Objects.nonNull(messageData) && Objects.nonNull(messageData.getMessageDTO()) && StringUtils.isNotBlank(messageData.getMessageDTO().getContent());
    }


    @Override
    public String getChannelType() {
        return ImChannelTypeEnum.PERSON.getType();
    }
}
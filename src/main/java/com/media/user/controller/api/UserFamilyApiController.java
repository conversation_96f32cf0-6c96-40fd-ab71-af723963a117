package com.media.user.controller.api;


import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.core.constant.PageResponse;
import com.media.user.dto.query.UserFamilyMemberQuery;
import com.media.user.dto.query.UserFamilySubMemberQuery;
import com.media.user.dto.request.DeviceInfoRequest;
import com.media.user.dto.response.UserFamilyInfoResponse;
import com.media.user.dto.response.UserFamilyMemberResponse;
import com.media.user.dto.response.UserFamilySubMemberResponse;
import com.media.user.enums.PropsTypeEnums;
import com.media.user.service.UserDevicePushService;
import com.media.user.service.UserFamilyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Deprecated
@RestController
@RequestMapping("/user/api/family")
public class UserFamilyApiController {

    @Autowired
    private UserFamilyService userFamilyService;

    @Autowired
    private UserDevicePushService userDevicePushService;

    @RequireAuth
    @PostMapping("/create")
    public void createUserFamily(){
        userFamilyService.createUserFamily(CloudSession.getUid(), PropsTypeEnums.FAMILY_CARD);
    }

    @RequireAuth
    @PostMapping("/pin")
    public void  familyPin(){
        userFamilyService.familyPin(CloudSession.getUid());
    }

    @RequireAuth
    @PostMapping("/push/bind")
    public void  deviceBind(@Validated @RequestBody DeviceInfoRequest request){
        request.setUid(CloudSession.getUid());
        userDevicePushService.deviceBind(request);
    }

    /**
     * 扩容
     */
    @RequireAuth
    @PostMapping("/scaling")
    public void expandCapacityUserFamilyGroup(){
        userFamilyService.expandCapacityUserFamilyGroup(CloudSession.getUid());
    }

    /**
     * 获取家族信息
     */
    @RequireAuth
    @PostMapping("/info")
    public UserFamilyInfoResponse getUserFamilyInfo(){
        return userFamilyService.getUserFamilyInfo(CloudSession.getUid());
    }

    /**
     * 获取子成员分页
     * @return
     */
    @RequireAuth
    @PostMapping("/subMember")
    public PageResponse<UserFamilySubMemberResponse> getUserFamilySubMemberPage(@RequestBody UserFamilySubMemberQuery query){
        query.setUid(CloudSession.getUid());
        return userFamilyService.getUserFamilySubMemberPage(query);
    }

    /**
     * 获取成员分页
     * @return
     */
    @RequireAuth
    @PostMapping("/member")
    public PageResponse<UserFamilyMemberResponse> getUserFamilyMemberPage(@RequestBody UserFamilyMemberQuery query){
        query.setUid(CloudSession.getUid());
        return userFamilyService.getUserFamilyMemberPage(query);
    }

}

package com.media.user.enums;

import lombok.Getter;

/**
 * 早鸟分享文案
 */
@Getter
public enum ShareContentEarlyBirdEnum {

    EARLY_BIRD_CONTENT_EN_US("en-US", "Invite Friends"),
    EARLY_BIRD_CONTENT_ZH_TW("zh-TW", "邀請好友"),
    EARLY_BIRD_CONTENT_ZH_CN("zh-CN", "邀请好友"),
    EARLY_BIRD_CONTENT_FR_FR("fr-FR", "Inviter des amis"),
    EARLY_BIRD_CONTENT_DE_DE("de-DE", "Freunde e<PERSON>laden"),
    EARLY_BIRD_CONTENT_RU_RU("ru-RU", "Пригласить друзей"),
    EARLY_BIRD_CONTENT_ES_ES("es-ES", "Invitar amigos"),
    EARLY_BIRD_CONTENT_J<PERSON>_<PERSON>("ja-<PERSON>", "友達を招待する"),
    EARLY_BIRD_CONTENT_TH_TH("th-TH", "เชิญเพื่อน"),
    EARLY_BIRD_CONTENT_VI_VN("vi-VN", "Mời bạn bè"),
    EARLY_BIRD_CONTENT_KO_KR("ko-KR", "친구 초대"),
    EARLY_BIRD_CONTENT_ID_ID("id-ID", "Undang Teman"),
    EARLY_BIRD_CONTENT_HI_IN("hi-IN", "मित्रों को आमंत्रित करें"),
    EARLY_BIRD_CONTENT_UR_PK("ur-PK", "دوستوں کو مدعو کریں"),
    EARLY_BIRD_CONTENT_TR_TR("tr-TR", "Arkadaşları Davet Et"),
    EARLY_BIRD_CONTENT_AR_SA("ar-SA", "دعوة الأصدقاء"),
    EARLY_BIRD_CONTENT_FA_IR("fa-IR", "دعوت از دوستان"),
    EARLY_BIRD_CONTENT_PT_PT("pt-PT", "Convidar Amigos"),
    EARLY_BIRD_CONTENT_BN_BD("bn-BD", "বন্ধুদের আমন্ত্রণ জানান"),
    ;

    private final String language;

    private final String content;

    ShareContentEarlyBirdEnum(String language, String content) {
        this.language = language;
        this.content = content;
    }

    public static String getContent(String language) {
        for (ShareContentEarlyBirdEnum contentEarlyBirdEnum : ShareContentEarlyBirdEnum.values()) {
            if (contentEarlyBirdEnum.getLanguage().equals(language)) {
                return contentEarlyBirdEnum.getContent();
            }
        }
        return "";
    }
}

package com.media.user.dto.popup;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PopupUserInviteInfo {

    private List<RewardInfo> rewardInfoList;

    private Long hostUid; // 邀请人id
    private Long inviteUid; // 被邀请人id

    @Data
    public static class RewardInfo {
        private String ccy;
        private BigDecimal price;
    }

    public static void main(String[] args) {
        PopupUserInviteInfo popupUserInviteInfo = new PopupUserInviteInfo();
        popupUserInviteInfo.setInviteUid(12313132123L);
        popupUserInviteInfo.setHostUid(19129108917744L);
        List<RewardInfo> list = Lists.newArrayList();
        PopupUserInviteInfo.RewardInfo rewardInfo = new PopupUserInviteInfo.RewardInfo();
        rewardInfo.setCcy("BTC");
        rewardInfo.setPrice(BigDecimal.valueOf(10));
        PopupUserInviteInfo.RewardInfo rewardInfo1 = new PopupUserInviteInfo.RewardInfo();
        rewardInfo1.setCcy("XME");
        rewardInfo1.setPrice(BigDecimal.valueOf(10));
        list.add(rewardInfo);
        list.add(rewardInfo1);
        popupUserInviteInfo.setRewardInfoList(list);
        System.out.println(JSON.toJSON(popupUserInviteInfo));
    }
}



package com.media.user.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 单个道具结构
 * {"propCode": "EP_800", "quantity": 1}
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddUserPropProd {
    /**
     * 道具代码
     */
    private String propCode;
    
    /**
     * 道具数量
     */
    private Integer quantity;
}

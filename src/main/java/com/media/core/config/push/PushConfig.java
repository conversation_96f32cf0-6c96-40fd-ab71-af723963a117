package com.media.core.config.push;

import io.github.engagelab.api.DeviceApi;
import io.github.engagelab.api.PushApi;
import io.github.engagelab.api.ScheduleApi;
import io.github.engagelab.api.StatusApi;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class PushConfig {

    // appKey and masterSecret from EngageLab console

    @Value("${push.jiguang.appKey}")
    private String appKey;

    @Value("${push.jiguang.masterSecret}")
    private String masterSecret;

    @Value("${push.jiguang.pushHost}")
    private String pushHost;
    @Bean
    public PushApi pushApi() {
        return new PushApi.Builder()
                .setHost(pushHost)
                .setAppKey(appKey)
                .setMasterSecret(masterSecret)
                .build();
    }

    @Bean
    public DeviceApi deviceApi() {
        return new DeviceApi.Builder()
                .setAppKey(appKey)
                .setMasterSecret(masterSecret)
                .build();
    }

    @Bean
    public StatusApi statusApi() {
        return new StatusApi.Builder()
                .setAppKey(appKey)
                .setMasterSecret(masterSecret)
                .build();
    }

    @Bean
    public ScheduleApi scheduleApi() {
        return new ScheduleApi.Builder()
                .setAppKey(appKey)
                .setMasterSecret(masterSecret)
                .build();
    }
}

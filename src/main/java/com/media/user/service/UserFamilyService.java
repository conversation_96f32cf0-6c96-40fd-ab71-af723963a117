package com.media.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.media.user.constant.MediaUserConstant;
import com.media.core.constant.PageResponse;
import com.media.user.domain.*;
import com.media.user.dto.query.*;
import com.media.user.dto.request.UserTransferAccountRequest;
import com.media.user.dto.response.*;
import com.media.user.enums.*;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.UserAccountBalanceClient;
import com.media.user.feign.client.UserPointClient;
import com.media.core.id.RedisIdGenerator;
import com.media.user.mapper.UserFamilyConfigMapper;
import com.media.user.mapper.UserFamilyDayAssetsMapper;
import com.media.user.mapper.UserFamilyGroupMapper;
import com.media.user.mapper.UserFamilyMemberMapper;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserFamilyService {

    @Autowired
    private UserFamilyMemberMapper userFamilyMemberMapper;

    @Autowired
    private UserFamilyGroupMapper userFamilyGroupMapper;

    @Autowired
    private UserFamilyDayAssetsMapper userFamilyDayAssetsMapper;

    @Autowired
    @Lazy
    private ClientUserService clientUserService;

    @Autowired
    private RedisIdGenerator redisIdGenerator;

    @Autowired
    private UserAccountBalanceClient userAccountBalanceClient;

    @Autowired
    private PropsService propsService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private UserFamilyConfigMapper userFamilyConfigMapper;

    @Autowired
    private UserDevicePushService userDevicePushService;
    @Autowired
    private UserPointClient userPointClient;

    public void pin(Long uid, List<Long> uids){
        String cacheKey = MediaUserConstant.USER_BIN_LIMIT + uid;

        String value = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotBlank(value)) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_PIN_LIMIT_ERROR);
        }

        List<Long> uidList = null;
        if(uids == null || uids.isEmpty()){
            List<ClientUserModel> clientUserModels = clientUserService.queryUserInviteList(uid);
            if (CollectionUtils.isEmpty(clientUserModels)) {
                return;
            }
            uidList = clientUserModels.stream().map(ClientUserModel::getUid).toList();
        }else{
            uidList = uids;
        }

        ApiResponse<Map<Long, Boolean>> userActiveResponse = userPointClient.getUserActive(new ClientUserQuery(uid, uidList));
        if (userActiveResponse == null || !Boolean.TRUE.equals(userActiveResponse.getSuccess()) || userActiveResponse.getCode() != 200) {
            return;
        }
        Map<Long, Boolean> activeStatusMap = userActiveResponse.getResult();
        List<Long> inactiveUidList = activeStatusMap.entrySet().stream()
                .filter(entry -> Boolean.FALSE.equals(entry.getValue()))
                .map(Map.Entry::getKey)
                .toList();

        if (!inactiveUidList.isEmpty()) {
            userDevicePushService.userPushBatch(uid, inactiveUidList);

            stringRedisTemplate.opsForValue().set(cacheKey, UUID.randomUUID().toString(), 1, TimeUnit.HOURS);
        }
    }

    public void familyPin(Long uid) {
        UserFamilyMemberModel userFamilyMemberModel = getUserFamilyMember(uid, UserStatusEnum.ENABLED.getStatus());
        if (userFamilyMemberModel == null) {
            throw new ApiException(MediaUserExceptionCodeApi.MEMBER_NOT_EXIST);
        }
        if (!LevelEnums.L1.getLevel().equals(userFamilyMemberModel.getUserLevel())) {
            throw new ApiException(MediaUserExceptionCodeApi.MEMBER_NOT_EXIST);
        }
        QueryWrapper<UserFamilyMemberModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pid", uid);
        queryWrapper.in("user_level", List.of(LevelEnums.L2.getLevel(), LevelEnums.L3.getLevel()));
        queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
        queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
        List<UserFamilyMemberModel> userFamilyMemberModelList = userFamilyMemberMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(userFamilyMemberModelList)) {
            throw new ApiException(MediaUserExceptionCodeApi.SUB_MEMBER_NUM_NOT_SUPPORT_EXPAND);
        }
        List<Long> uids = userFamilyMemberModelList.stream().map(UserFamilyMemberModel::getUid).toList();
        userDevicePushService.userPushBatch(uid, uids);
    }


    @Transactional(rollbackFor = Exception.class)
    public void createUserFamily(Long uid, PropsTypeEnums propsTypeEnums) {
//        ClientUserModel clientUserModel = clientUserService.selectUserById(uid);
//        if (clientUserModel == null) {
//            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
//        }
//
//        UserFamilyMemberModel userFamilyMemberModel = getUserFamilyMember(uid, UserStatusEnum.ENABLED.getStatus());
//        //查询道具
//        PropsModel propsModel = propsService.getPropsModelByType(propsTypeEnums.getCode());
//
//        List<ClientUserModel> clientUserModelList = clientUserService.queryUserListByInviteId(uid);
//        if (CollectionUtils.isEmpty(clientUserModelList)) {
//            throw new ApiException(MediaUserExceptionCodeApi.MEMBER_NUMBER_TOO_LITTLE_ERROR);
//        }
//        List<Long> uids = clientUserModelList.stream().map(ClientUserModel::getUid).toList();
//        UserFamilyMemberListQuery query1 = new UserFamilyMemberListQuery()
//                .setUids(uids)
//                .setUserLevelList(List.of(LevelEnums.L1.getLevel()));
//        List<UserFamilyMemberModel> l1 = queryUserFamilyMemberList(query1);
//        Set<Long> memberUids;
//        if (!CollectionUtils.isEmpty(l1)) {
//            memberUids = l1.stream().map(UserFamilyMemberModel::getUid).collect(Collectors.toSet());
//        } else {
//            memberUids = new HashSet<>();
//        }
//        UserFamilyConfigModel userFamilyConfigModel = queryUserFamilyConfig();
//
//        List<ClientUserModel> l2MemberUserList = clientUserModelList.stream().filter(clientUserModel1 -> !memberUids.contains(clientUserModel1.getUid())).toList();
//        Set<Long> disableUids;
//        if (l2MemberUserList.size() > propsModel.getCapacity()) {
//            disableUids = l2MemberUserList.subList(propsModel.getCapacity(), l2MemberUserList.size()).stream().map(ClientUserModel::getUid).collect(Collectors.toSet());
//        } else {
//            disableUids = new HashSet<>();
//        }
//        Integer minNum = userFamilyConfigModel.getSuperLevel2MinNum();
//        if (PropsTypeEnums.FAMILY_CARD.equals(propsTypeEnums)) {
//            minNum = userFamilyConfigModel.getLevel2MinNum();
//        }
//        if (CollectionUtils.isEmpty(l2MemberUserList) || l2MemberUserList.size() < minNum) {
//            throw new ApiException(MediaUserExceptionCodeApi.MEMBER_NUMBER_TOO_LITTLE_ERROR);
//        }
//        // 查询三级用户
//        List<Long> l2Uids = l2MemberUserList.stream().map(ClientUserModel::getUid).toList();
//        List<ClientUserModel> l3ClientUserList = clientUserService.queryUserListByInviteIds(l2Uids);
//        List<ClientUserModel> l3MemberList = new ArrayList<>();
//        Set<Long> disableUidsL3 = new HashSet<>();
//        if (!CollectionUtils.isEmpty(l3ClientUserList)) {
//            List<Long> l3Uids = l3ClientUserList.stream().map(ClientUserModel::getUid).toList();
//            UserFamilyMemberListQuery userFamilyMemberListQueryL1 = new UserFamilyMemberListQuery()
//                    .setUids(l3Uids)
//                    .setUserLevelList(List.of(LevelEnums.L1.getLevel()));
//            List<UserFamilyMemberModel> memberModelListL1 = queryUserFamilyMemberList(userFamilyMemberListQueryL1);
//            Set<Long> l3MemberUids;
//            if (!CollectionUtils.isEmpty(memberModelListL1)) {
//                l3MemberUids = memberModelListL1.stream().map(UserFamilyMemberModel::getUid).collect(Collectors.toSet());
//            } else {
//                l3MemberUids = new HashSet<>();
//            }
//            l3MemberList = l3ClientUserList.stream().filter(clientUserModel1 -> !l3MemberUids.contains(clientUserModel1.getUid())).toList();
//            l3MemberList.forEach(clientUserModel1 -> {
//                if (disableUids.contains(clientUserModel1.getInviteUid())) {
//                    disableUidsL3.add(clientUserModel1.getUid());
//                }
//            });
//        }
//
//        //判断当前用户是不是在某个家族，需要退出
//        List<ClientUserModel> clientUserModels = new ArrayList<>();
//        clientUserModels.add(clientUserModel);
//        clientUserModels.addAll(l2MemberUserList);
//        clientUserModels.addAll(l3MemberList);
//        deleteUserFamilyMember(clientUserModels.stream().map(ClientUserModel::getUid).toList());
//        // 上级成员子成员数量-1
//        if (userFamilyMemberModel != null) {
//            UserFamilyMemberModel p = getUserFamilyMember(userFamilyMemberModel.getPid(), UserStatusEnum.ENABLED.getStatus());
//            if (p != null) {
//                UserFamilyMemberModel pUpdate = new UserFamilyMemberModel()
//                        .setId(p.getId())
//                        .setSubMemberNum(p.getSubMemberNum() - 1)
//                        .setUpdatedTime(new Date());
//                userFamilyMemberMapper.updateById(pUpdate);
//                if (LevelEnums.L1.getLevel().equals(p.getUserLevel())){
//                    // 解冻用户
//                    QueryWrapper<UserFamilyMemberModel> queryWrapper = new QueryWrapper<>();
//                    queryWrapper.eq("pid", p.getUid());
//                    queryWrapper.eq("status", UserStatusEnum.DISABLED.getStatus());
//                    queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
//                    queryWrapper.orderByDesc("created_time");
//                    queryWrapper.last("limit 1");
//                    UserFamilyMemberModel l2 = userFamilyMemberMapper.selectOne(queryWrapper);
//                    if (l2 != null){
//                        UserFamilyMemberModel update = new UserFamilyMemberModel()
//                                .setId(l2.getId())
//                                .setStatus(UserStatusEnum.ENABLED.getStatus())
//                                .setCreatedTime(new Date())
//                                .setUpdatedTime(new Date());
//                        userFamilyMemberMapper.updateById(update);
//                    }
//
//                }
//            }
//        }
//
//        // 创建新家族
//        UserFamilyGroupModel userFamilyGroupModel = createUserFamilyGroup(clientUserModel, propsModel);
//
//        // 创建家族成员
//        createUserFamilyMember(clientUserModel, l2MemberUserList, l3MemberList, userFamilyGroupModel.getId(), disableUids, disableUidsL3);
//        //支付两笔
//
//        if (PropsTypeEnums.FAMILY_CARD.equals(propsTypeEnums)) {
//            List<UserTransferAccountRequest> requests = new ArrayList<>();
//
//            BigDecimal price = propsModel.getPrice().multiply(MediaUserConstant.KING_DIViDE);
//            if (userFamilyMemberModel != null) {
//                QueryWrapper<UserFamilyMemberModel> queryWrapper = new QueryWrapper<>();
//                queryWrapper.eq("family_id", userFamilyMemberModel.getFamilyId());
//                queryWrapper.eq("user_level", LevelEnums.L1.getLevel());
//                queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
//                UserFamilyMemberModel king = userFamilyMemberMapper.selectOne(queryWrapper);
//                UserTransferAccountRequest request = new UserTransferAccountRequest()
//                        .setUid(uid)
//                        .setAmount(price)
//                        .setCcy(propsModel.getCcy())
//                        .setEventId(PointEventEnums.EXIT_THE_FAMILY.getEventId())
//                        .setToUid(king.getUid());
//                requests.add(request);
//            }
//            UserTransferAccountRequest request = new UserTransferAccountRequest()
//                    .setUid(uid)
//                    .setAmount(price)
//                    .setCcy(propsModel.getCcy())
//                    .setEventId(PointEventEnums.FAMILY_ORDER.getEventId())
//                    .setToUid(UidEnums.PROP_SALES.getUid());
//            requests.add(request);
//            ApiResponse response = userAccountBalanceClient.transferBatch(requests);
//            if (!response.getSuccess()) {
//                throw new ApiException(response.getCode());
//            }
//        } else {
//            UserTransferAccountRequest request = new UserTransferAccountRequest()
//                    .setUid(uid)
//                    .setAmount(propsModel.getPrice())
//                    .setEventId(PointEventEnums.CREATION_FAMILY_ORDER.getEventId());
//            ApiResponse<Long> response = userAccountBalanceClient.transferAccount(request);
//            if (!response.getSuccess()) {
//                throw new ApiException(response.getCode());
//            }
//        }
//
//        List<String> userKey = new ArrayList<>();
//        userKey.add(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + clientUserModel.getUid());
//        if (!CollectionUtils.isEmpty(l2MemberUserList)) {
//            List<String> l2UserKey = clientUserModelList.stream().map(member -> MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + member.getUid()).toList();
//            userKey.addAll(l2UserKey);
//        }
//        if (!CollectionUtils.isEmpty(l3MemberList)) {
//            List<String> l3UserKey = l3ClientUserList.stream().map(member -> MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + member.getUid()).toList();
//            userKey.addAll(l3UserKey);
//        }
//        stringRedisTemplate.delete(userKey);
    }

    @Transactional(rollbackFor = Exception.class)
    public UserFamilyGroupModel createUserFamilyGroup(ClientUserModel clientUserModel, PropsModel propsModel) {
        UserFamilyGroupModel userFamilyGroupModel = new UserFamilyGroupModel()
                .setId(redisIdGenerator.generate())
                .setName(clientUserModel.getNickName())
                .setAvatarUrl(clientUserModel.getAvatarUrl())
                .setCreatedBy(clientUserModel.getUid())
                .setL2MaxNum(propsModel.getCapacity())
                .setCreatedTime(new Date());
        userFamilyGroupMapper.insert(userFamilyGroupModel);
        return userFamilyGroupModel;
    }

    @Transactional(rollbackFor = Exception.class)
    public void createUserFamilyMember(ClientUserModel l1, List<ClientUserModel> l2, List<ClientUserModel> l3, Long familyId, Set<Long> disableUidsL2, Set<Long> disableUidsL3) {
        Date now = new Date();
        List<UserFamilyMemberModel> userFamilyMemberModelList = new ArrayList<>();
        // 创建一级
        UserFamilyMemberModel userFamilyMemberModel = new UserFamilyMemberModel()
                .setId(redisIdGenerator.generate())
                .setFamilyId(familyId)
                .setUid(l1.getUid())
                .setUserLevel(LevelEnums.L1.getLevel())
                .setPid(MediaUserConstant.rootId)
                .setPointRate(LevelEnums.L1.getPointRate())
                .setSubMemberNum(l2.size())
                .setStatus(UserStatusEnum.ENABLED.getStatus())
                .setPointTotal(BigDecimal.ZERO)
                .setTokenTotal(BigDecimal.ZERO)
                .setCreatedTime(now)
                .setDeleted(DeletedEnum.UNDELETED.getStatus());
        userFamilyMemberModelList.add(userFamilyMemberModel);
        // 创建二级
        Map<Long, List<ClientUserModel>> l3Map;
        if (CollectionUtils.isEmpty(l3)) {
            l3Map = new HashMap<>();
        } else {
            l3Map = l3.stream().collect(Collectors.groupingBy(ClientUserModel::getInviteUid));
        }


        List<UserFamilyMemberModel> l2Member = l2.stream().map((clientUserModel) -> {
            UserFamilyMemberModel userFamilyMemberModel2 = new UserFamilyMemberModel()
                    .setId(redisIdGenerator.generate())
                    .setFamilyId(familyId)
                    .setUserLevel(LevelEnums.L2.getLevel())
                    .setPid(l1.getUid())
                    .setUid(clientUserModel.getUid())
                    .setCreatedTime(now)
                    .setPointRate(LevelEnums.L2.getPointRate())
                    .setPointTotal(BigDecimal.ZERO)
                    .setTokenTotal(BigDecimal.ZERO)
                    .setDeleted(DeletedEnum.UNDELETED.getStatus())
                    .setUid(clientUserModel.getUid());
            if (disableUidsL2.contains(clientUserModel.getUid())) {
                userFamilyMemberModel2.setStatus(UserStatusEnum.DISABLED.getStatus());
            } else {
                userFamilyMemberModel2.setStatus(UserStatusEnum.ENABLED.getStatus());
            }
            List<ClientUserModel> l3ClientUserModelList = l3Map.get(clientUserModel.getUid());
            if (CollectionUtils.isEmpty(l3ClientUserModelList)) {
                userFamilyMemberModel2.setSubMemberNum(MediaUserConstant.ZERO);
            } else {
                userFamilyMemberModel2.setSubMemberNum(l3ClientUserModelList.size());
            }
            return userFamilyMemberModel2;
        }).toList();
        userFamilyMemberModelList.addAll(l2Member);
        if (!CollectionUtils.isEmpty(l3)) {
            // 创建三级
            Map<Long, ClientUserModel> l2UserMap = l2.stream().collect(Collectors.toMap(ClientUserModel::getUid, Function.identity()));
            List<ClientUserModel> clientUserModelList = clientUserService.queryUserListByInviteIds(l2.stream().map(ClientUserModel::getUid).toList());
            Map<Long, List<ClientUserModel>> l4Map;
            if (CollectionUtils.isEmpty(clientUserModelList)) {
                l4Map = new HashMap<>();
            } else {
                l4Map = clientUserModelList.stream().collect(Collectors.groupingBy(ClientUserModel::getUid));
            }

            List<UserFamilyMemberModel> l3Member = l3.stream().map((clientUserModel) -> {
                ClientUserModel p = l2UserMap.get(clientUserModel.getInviteUid());
                UserFamilyMemberModel userFamilyMemberModel3 = new UserFamilyMemberModel()
                        .setId(redisIdGenerator.generate())
                        .setFamilyId(familyId)
                        .setPid(p.getUid())
                        .setUid(clientUserModel.getUid())
                        .setUserLevel(LevelEnums.L3.getLevel())
                        .setPointRate(LevelEnums.L3.getPointRate())
                        .setPointTotal(BigDecimal.ZERO)
                        .setTokenTotal(BigDecimal.ZERO)
                        .setCreatedTime(now)
                        .setDeleted(DeletedEnum.UNDELETED.getStatus());
                if (disableUidsL3.contains(clientUserModel.getUid())) {
                    userFamilyMemberModel3.setStatus(UserStatusEnum.DISABLED.getStatus());
                } else {
                    userFamilyMemberModel3.setStatus(UserStatusEnum.ENABLED.getStatus());
                }
                List<ClientUserModel> l4ClientUserModelList = l4Map.get(clientUserModel.getUid());
                if (CollectionUtils.isEmpty(l4ClientUserModelList)) {
                    userFamilyMemberModel3.setSubMemberNum(MediaUserConstant.ZERO);
                } else {
                    userFamilyMemberModel3.setSubMemberNum(l4ClientUserModelList.size());
                }
                return userFamilyMemberModel3;
            }).toList();

            userFamilyMemberModelList.addAll(l3Member);

        }

        userFamilyMemberMapper.insertBatch(userFamilyMemberModelList);

    }

    public List<UserFamilyMemberModel> queryUserFamilyMemberList(UserFamilyMemberListQuery query) {
        QueryWrapper<UserFamilyMemberModel> queryWrapper = new QueryWrapper<UserFamilyMemberModel>();
        queryWrapper.in("uid", query.getUids());
        if (query.getStatus() != null) {
            queryWrapper.eq("status", query.getStatus());
        }
        queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
        if (!CollectionUtils.isEmpty(query.getUserLevelList())) {
            queryWrapper.in("user_level", query.getUserLevelList());
        }

        return userFamilyMemberMapper.selectList(queryWrapper);
    }

    public void deleteUserFamilyMember(List<Long> uids) {
        QueryWrapper<UserFamilyMemberModel> queryWrapper = new QueryWrapper<UserFamilyMemberModel>();
        queryWrapper.in("uid", uids);
        queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());

        UserFamilyMemberModel updatedUserFamilyMemberModel = new UserFamilyMemberModel()
                .setDeleted(DeletedEnum.DELETED.getStatus())
                .setUpdatedTime(new Date());
        userFamilyMemberMapper.update(updatedUserFamilyMemberModel, queryWrapper);

    }

    /**
     * 扩容
     *
     * @param uid
     */
    @Transactional(rollbackFor = Exception.class)
    public void expandCapacityUserFamilyGroup(Long uid) {
        //校验有没有扩容权限
        UserFamilyMemberModel userFamilyMemberModel = getUserFamilyMember(uid, UserStatusEnum.ENABLED.getStatus());
        if (userFamilyMemberModel == null) {
            throw new ApiException(MediaUserExceptionCodeApi.MEMBER_NOT_EXIST);
        }
        if (!LevelEnums.L1.getLevel().equals(userFamilyMemberModel.getUserLevel())) {
            throw new ApiException(MediaUserExceptionCodeApi.MEMBER_LEVEL_NOT_SUPPORT_EXPAND);
        }
        UserFamilyGroupModel userFamilyGroupModel = getUserFamilyGroup(userFamilyMemberModel.getFamilyId());
//        if (userFamilyGroupModel.getL2MaxNum() > userFamilyMemberModel.getSubMemberNum()){
//            throw new ApiException(MediaUserExceptionCodeApi.SUB_MEMBER_NUM_NOT_SUPPORT_EXPAND);
//        }
        //查询道具
        PropsModel propsModel = propsService.getPropsModelByType(PropsTypeEnums.EXPAND_CARD.getCode());
        UserFamilyGroupModel updateGroup = new UserFamilyGroupModel()
                .setId(userFamilyGroupModel.getId())
                .setL2MaxNum(userFamilyGroupModel.getL2MaxNum() + propsModel.getCapacity())
                .setUpdatedTime(new Date());
        userFamilyGroupMapper.updateById(updateGroup);
        // 解冻
        List<Long> uids = unfreezeUserFamilyMember(userFamilyGroupModel.getId(), propsModel.getCapacity());
        //支付接口
        UserTransferAccountRequest request = new UserTransferAccountRequest()
                .setFromUid(uid)
                .setAmount(propsModel.getPrice())
                .setEventId(PointEventEnums.EXPANSION_CARD.getEventId())
                .setCcy(propsModel.getCcy());
        ApiResponse<Long> response = userAccountBalanceClient.transferAccount(request);
        if (!response.getSuccess()) {
            throw new ApiException(response.getCode());
        }
        if (!CollectionUtils.isEmpty(uids)) {
            List<String> userKey = uids.stream().map(id -> MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + id).toList();
            stringRedisTemplate.delete(userKey);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public List<Long> unfreezeUserFamilyMember(Long familyId, Integer subMemberNum) {
        QueryWrapper<UserFamilyMemberModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("family_id", familyId);
        queryWrapper.eq("status", UserStatusEnum.DISABLED.getStatus());
        queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
        queryWrapper.orderByAsc("id");
        queryWrapper.last("limit " + subMemberNum);
        List<UserFamilyMemberModel> userFamilyMemberModelList = userFamilyMemberMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(userFamilyMemberModelList)) {
            List<Long> pids = userFamilyMemberModelList.stream().map(UserFamilyMemberModel::getUid).toList();
            QueryWrapper<UserFamilyMemberModel> subQuery = new QueryWrapper<>();
            subQuery.eq("family_id", familyId);
            subQuery.in("pid", pids);
            subQuery.eq("status", UserStatusEnum.DISABLED.getStatus());
            subQuery.eq("deleted", DeletedEnum.UNDELETED.getStatus());
            subQuery.orderByAsc("id");
            List<UserFamilyMemberModel> subMemberList = userFamilyMemberMapper.selectList(subQuery);
            List<Long> ids = userFamilyMemberModelList.stream().map(UserFamilyMemberModel::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(subMemberList)) {
                ids.addAll(subMemberList.stream().map(UserFamilyMemberModel::getId).toList());
            }
            userFamilyMemberMapper.updateUserFamilyMemberBatch(ids, new Date());
            return ids;
        }
        return new ArrayList<>();
    }


    public UserFamilyMemberModel getUserFamilyMember(Long uid, Byte status) {
        QueryWrapper<UserFamilyMemberModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        return userFamilyMemberMapper.selectOne(queryWrapper);
    }

    public UserFamilyGroupModel getUserFamilyGroup(Long familyId) {
        return userFamilyGroupMapper.selectById(familyId);
    }

    public UserFamilyInfoResponse getUserFamilyInfo(Long uid) {
        UserFamilyMemberModel userFamilyMemberModel = getUserFamilyMember(uid, UserStatusEnum.ENABLED.getStatus());
        if (userFamilyMemberModel == null) {
            return null;
        }
        UserFamilyGroupModel userFamilyGroupModel = getUserFamilyGroup(userFamilyMemberModel.getFamilyId());

        UserFamilyInfoResponse response = new UserFamilyInfoResponse()
                .setId(userFamilyGroupModel.getId())
                .setFamilyName(userFamilyGroupModel.getName())
                .setFamilyAvatarUrl(userFamilyGroupModel.getAvatarUrl())
                .setPointRate(userFamilyMemberModel.getPointRate())
                .setL2MaxNum(userFamilyGroupModel.getL2MaxNum())
                .setPointTotal(userFamilyMemberModel.getPointTotal().setScale(2, BigDecimal.ROUND_DOWN))
                .setTokenTotal(userFamilyMemberModel.getTokenTotal().setScale(6, BigDecimal.ROUND_DOWN))
                .setMySubMemberTotal(userFamilyMemberModel.getSubMemberNum());
        if (LevelEnums.L1.getLevel().equals(userFamilyMemberModel.getUserLevel())) {
            response.setSubMemberTotal(userFamilyGroupModel.getL2MaxNum());
            UserPinInfoResponse userPinInfoResponse = userDevicePushService.getUserPinInfo(uid);
            response.setMaxTimes(userPinInfoResponse.getMaxTimes())
                    .setTimes(userPinInfoResponse.getTimes());
        } else if (LevelEnums.L2.getLevel().equals(userFamilyMemberModel.getUserLevel())) {
            response.setSubMemberTotal(MediaUserConstant.L2_MIN_NUM);
        } else {
            UserInviteInfoResponse userInviteInfoResponse = clientUserService.getUserInviteInfo(uid);
            response.setSubMemberTotal(MediaUserConstant.L2_MIN_NUM);
            response.setMySubMemberTotal(userInviteInfoResponse.getInviteCount().intValue());

        }
        String dateStr = DateFormatUtils.format(new Date(), MediaUserConstant.dateFormat);

        UserFamilyDayAssetsModel memberDayAssetsModel = getUserFamilyDayAssets(userFamilyMemberModel.getUid(), userFamilyMemberModel.getFamilyId(), null);
        if (memberDayAssetsModel == null) {
            response.setTodayPointBonusTotal(BigDecimal.ZERO);
        } else {
            response.setTodayPointBonusTotal(memberDayAssetsModel.getPointsBonus());
        }
        UserFamilyDayAssetsSumQuery query = new UserFamilyDayAssetsSumQuery()
                .setPid(userFamilyMemberModel.getUid())
                .setFamilyId(userFamilyMemberModel.getFamilyId())
                .setCreatedDate(dateStr)
                .setUserLevel(userFamilyMemberModel.getUserLevel());
        UserFamilyDayAssetsModel userFamilyDayAssetsModel = userFamilyDayAssetsMapper.selectUserFamilyDayAssetsSum(query);
        if (userFamilyDayAssetsModel == null) {
            response.setTodayMemberPointTotal(BigDecimal.ZERO);
        } else {
            response.setTodayMemberPointTotal(userFamilyDayAssetsModel.getMemberPointTotal().setScale(2, BigDecimal.ROUND_DOWN));
        }

        return response;
    }

    public UserFamilyDayAssetsModel getUserFamilyDayAssets(Long uid, Long familyId, String dateStr) {

        QueryWrapper<UserFamilyDayAssetsModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("family_id", familyId);
        queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
        if (StringUtils.isBlank(dateStr)) {
            dateStr = DateFormatUtils.format(new Date(), MediaUserConstant.dateFormat);
        }
        queryWrapper.eq("created_date", dateStr);
        return userFamilyDayAssetsMapper.selectOne(queryWrapper);
    }

    public PageResponse<UserFamilySubMemberResponse> getUserFamilySubMemberPage(UserFamilySubMemberQuery query) {
        UserFamilyMemberModel userFamilyMemberModel = getUserFamilyMember(query.getUid(), UserStatusEnum.ENABLED.getStatus());
        if (userFamilyMemberModel == null || !userFamilyMemberModel.getUserLevel().equals(LevelEnums.L2.getLevel())) {
            throw new ApiException(MediaUserExceptionCodeApi.MEMBER_NOT_EXIST);
        }

        IPage<UserFamilyMemberModel> userFamilyMemberModelPage = getUserFamilySubMemberModelPage(query);
        if (CollectionUtils.isEmpty(userFamilyMemberModelPage.getRecords())) {
            return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
        }
        List<Long> uids = userFamilyMemberModelPage.getRecords().stream().map(UserFamilyMemberModel::getUid).collect(Collectors.toList());
        Map<Long, ClientUserResponse> map = clientUserService.selectUserByIds(uids);
        List<UserFamilySubMemberResponse> list = new ArrayList<>();
        for (UserFamilyMemberModel model : userFamilyMemberModelPage.getRecords()) {

            ClientUserResponse clientUserResponse = map.get(model.getUid());
            UserFamilySubMemberResponse userFamilySubMemberResponse = new UserFamilySubMemberResponse();
            userFamilySubMemberResponse.setUid(model.getUid())
                    .setNickName(clientUserResponse.getNickName())
                    .setPointTotal(model.getPointBaseTotal().multiply(MediaUserConstant.DIRECT_DIViDE).setScale(2, BigDecimal.ROUND_DOWN));
            list.add(userFamilySubMemberResponse);
        }

        return new PageResponse<>(query.getPage(), query.getSize(), userFamilyMemberModelPage.getTotal(), list);
    }

    public IPage<UserFamilyMemberModel> getUserFamilySubMemberModelPage(UserFamilySubMemberQuery query) {
        QueryWrapper<UserFamilyMemberModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pid", query.getUid());
        queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
        queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
        queryWrapper.orderByAsc("point_base_total");

        IPage<UserFamilyMemberModel> page = new Page<>(query.getPage(), query.getSize());
        return userFamilyMemberMapper.selectPage(page, queryWrapper);
    }

    public PageResponse<UserFamilyMemberResponse> getUserFamilyMemberPage(UserFamilyMemberQuery query) {
        UserFamilyMemberModel userFamilyMemberModel = getUserFamilyMember(query.getUid(), UserStatusEnum.ENABLED.getStatus());
        if (userFamilyMemberModel == null || !userFamilyMemberModel.getUserLevel().equals(LevelEnums.L1.getLevel())) {
            throw new ApiException(MediaUserExceptionCodeApi.MEMBER_NOT_EXIST);
        }
        query.setFamilyId(userFamilyMemberModel.getFamilyId());
        IPage<UserFamilyMemberModel> userFamilyMemberModelPage = getUserFamilyMemberModelPage(query);

        if (CollectionUtils.isEmpty(userFamilyMemberModelPage.getRecords())) {
            return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
        }

        List<Long> uids = userFamilyMemberModelPage.getRecords().stream().map(UserFamilyMemberModel::getUid).collect(Collectors.toList());
        List<Long> pids = userFamilyMemberModelPage.getRecords().stream().map(UserFamilyMemberModel::getPid).collect(Collectors.toList());
        uids.addAll(pids);
        Map<Long, ClientUserResponse> map = clientUserService.selectUserByIds(uids.stream().distinct().toList());
        List<UserFamilyMemberResponse> list = new ArrayList<>();
        for (UserFamilyMemberModel model : userFamilyMemberModelPage.getRecords()) {
            ClientUserResponse clientUserResponse = map.get(model.getUid());
            ClientUserResponse pClientUserResponse = map.get(model.getPid());

            UserFamilyMemberResponse userFamilyMemberResponse = new UserFamilyMemberResponse()
                    .setUid(model.getUid())
                    .setLevel(model.getUserLevel())
                    .setInviteCount(model.getSubMemberNum())
                    .setNickName(clientUserResponse.getNickName())
                    .setLeaderNickName(pClientUserResponse.getNickName())
                    .setLeaderAvatarUrl(pClientUserResponse.getAvatarUrl());
            list.add(userFamilyMemberResponse);
        }
        return new PageResponse<>(query.getPage(), query.getSize(), userFamilyMemberModelPage.getTotal(), list);
    }

    public IPage<UserFamilyMemberModel> getUserFamilyMemberModelPage(UserFamilyMemberQuery query) {
        QueryWrapper<UserFamilyMemberModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("family_id", query.getFamilyId());
        queryWrapper.in("user_level", List.of(LevelEnums.L2.getLevel(), LevelEnums.L3.getLevel()));
        queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
        queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
        queryWrapper.orderByDesc("sub_member_num")
                .orderByAsc("user_level");
        Page<UserFamilyMemberModel> page = new Page<>(query.getPage(), query.getSize());
        return userFamilyMemberMapper.selectPage(page, queryWrapper);
    }

    public UserFamilyMemberInfoResponse selectUserMemberInfo(Long uid) {
        QueryWrapper<UserFamilyMemberModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
        queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
        UserFamilyMemberModel userFamilyMemberModel = userFamilyMemberMapper.selectOne(queryWrapper);
        if (userFamilyMemberModel == null) {
            return null;
        }
        QueryWrapper<UserFamilyMemberModel> queryWrapper2 = new QueryWrapper<>();
        queryWrapper2.eq("family_id", userFamilyMemberModel.getFamilyId());
        queryWrapper2.eq("deleted", DeletedEnum.UNDELETED.getStatus());
        queryWrapper2.eq("status", UserStatusEnum.ENABLED.getStatus());
        queryWrapper2.eq("user_level", LevelEnums.L1.getLevel());
        UserFamilyMemberModel familyOwner = userFamilyMemberMapper.selectOne(queryWrapper2);
        return new UserFamilyMemberInfoResponse()
                .setUid(userFamilyMemberModel.getUid())
                .setPid(userFamilyMemberModel.getPid())
                .setUserLevel(userFamilyMemberModel.getUserLevel())
                .setFamilyOwnerId(familyOwner.getUid())
                .setCreatedTime(userFamilyMemberModel.getCreatedTime())
                .setUpdatedTime(userFamilyMemberModel.getUpdatedTime());
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateFamilyAssets(PointDetailsResponse response) {
        UserFamilyMemberModel userFamilyMemberModel = getUserFamilyMember(response.getUid(), UserStatusEnum.ENABLED.getStatus());
        if (userFamilyMemberModel == null) {
            return;
        }
        log.info("UserFamilyService.updateFamilyAssets response:{}", response);
        if (response.getCreatedTime().getTime() < userFamilyMemberModel.getCreatedTime().getTime()) {
            return;
        }

        if (PointEventEnums.POINT_BONUS.equals(PointEventEnums.getEnumsByEventId(response.getEventId()))) {
            UserFamilyMemberModel update = new UserFamilyMemberModel()
                    .setId(userFamilyMemberModel.getId())
                    .setPointTotal(userFamilyMemberModel.getPointTotal().add(response.getPointChangeCount()))
                    .setUpdatedTime(new Date());
            userFamilyMemberMapper.updateById(update);
        } else {
            UserFamilyMemberModel update = new UserFamilyMemberModel()
                    .setId(userFamilyMemberModel.getId())
                    .setPointBaseTotal(userFamilyMemberModel.getPointBaseTotal().add(response.getPointChangeCount()))
                    .setUpdatedTime(new Date());
            userFamilyMemberMapper.updateById(update);
        }
        QueryWrapper<UserFamilyDayAssetsModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("family_id", userFamilyMemberModel.getFamilyId());
        queryWrapper.eq("uid", userFamilyMemberModel.getUid());
        String dateStr = DateFormatUtils.format(new Date(), MediaUserConstant.dateFormat);
        queryWrapper.eq("created_date", dateStr);
        queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
        UserFamilyDayAssetsModel userFamilyDayAssetsModel = userFamilyDayAssetsMapper.selectOne(queryWrapper);
        if (userFamilyDayAssetsModel == null) {
            UserFamilyDayAssetsModel insertUserFamilyDayAssetsModel = new UserFamilyDayAssetsModel()
                    .setId(redisIdGenerator.generate())
                    .setCreatedDate(dateStr)
                    .setFamilyId(userFamilyMemberModel.getFamilyId())
                    .setUid(userFamilyMemberModel.getUid())
                    .setDeleted(DeletedEnum.UNDELETED.getStatus())
                    .setCreatedTime(new Date());
            if (PointEventEnums.POINT_BONUS.equals(PointEventEnums.getEnumsByEventId(response.getEventId()))) {
                insertUserFamilyDayAssetsModel.setPointsBonus(response.getPointChangeCount())
                        .setMemberPointTotal(BigDecimal.ZERO);
            } else {
                insertUserFamilyDayAssetsModel
                        .setMemberPointTotal(response.getPointChangeCount())
                        .setPointsBonus(BigDecimal.ZERO);
            }
            userFamilyDayAssetsMapper.insert(insertUserFamilyDayAssetsModel);
        } else {
            UserFamilyDayAssetsModel updateUserFamilyDayAssetsModel = new UserFamilyDayAssetsModel()
                    .setId(userFamilyDayAssetsModel.getId())
                    .setUpdatedTime(new Date());
            if (PointEventEnums.POINT_BONUS.equals(PointEventEnums.getEnumsByEventId(response.getEventId()))) {
                updateUserFamilyDayAssetsModel.setPointsBonus(userFamilyDayAssetsModel.getPointsBonus().add(response.getPointChangeCount()));
            } else {
                updateUserFamilyDayAssetsModel.setMemberPointTotal(userFamilyDayAssetsModel.getMemberPointTotal().add(response.getPointChangeCount()));
            }
            userFamilyDayAssetsMapper.updateById(updateUserFamilyDayAssetsModel);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateFamilyAssetsToken(FamilyAssetsTokenResponse response) {

        UserFamilyMemberModel userFamilyMemberModel = getUserFamilyMember(response.getUid(), UserStatusEnum.ENABLED.getStatus());

        if (userFamilyMemberModel == null) {
            return;
        }

        UserFamilyDayAssetsModel userFamilyDayAssetsModel = getUserFamilyDayAssets(userFamilyMemberModel.getUid(), userFamilyMemberModel.getFamilyId(), response.getCreatedDate());

        if (userFamilyDayAssetsModel == null) {
            return;
        }
        Date now = new Date();
        UserFamilyMemberModel update = new UserFamilyMemberModel()
                .setId(userFamilyMemberModel.getId())
                .setUpdatedTime(now);
        BigDecimal token = response.getAmount().multiply(userFamilyDayAssetsModel.getPointsBonus()).divide(response.getPointTotal(), 2, BigDecimal.ROUND_DOWN);
        update.setTokenTotal(userFamilyMemberModel.getTokenTotal().add(token));

        userFamilyMemberMapper.updateById(update);
    }

    public List<UserFamilyDayAssetsModel> queryUserFamilyDayAssetsList(UserFamilyDayAssetsQuery query) {
        QueryWrapper<UserFamilyDayAssetsModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("uid", query.getUids());
        queryWrapper.eq("created_date", query.getCreatedDate());
        queryWrapper.eq("deleted", DeletedEnum.UNDELETED.getStatus());
        return userFamilyDayAssetsMapper.selectList(queryWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void joinFamily(Long pid, Long uid) {
        UserFamilyMemberModel userFamilyMemberModel = getUserFamilyMember(pid, null);
        if (userFamilyMemberModel == null || LevelEnums.L3.getLevel().equals(userFamilyMemberModel.getUserLevel())) {
            return;
        }
        Date now = new Date();
        UserFamilyMemberModel insert = new UserFamilyMemberModel()
                .setFamilyId(userFamilyMemberModel.getFamilyId())
                .setId(redisIdGenerator.generate())
                .setPid(pid)
                .setUid(uid)
                .setDeleted(DeletedEnum.UNDELETED.getStatus())
                .setStatus(userFamilyMemberModel.getStatus())
                .setCreatedTime(now)
                .setPointTotal(BigDecimal.ZERO)
                .setSubMemberNum(0)
                .setTokenTotal(BigDecimal.ZERO);
        LevelEnums levelEnums = LevelEnums.L3;
        if (LevelEnums.L1.getLevel().equals(userFamilyMemberModel.getUserLevel())) {
            levelEnums = LevelEnums.L2;
        }
        insert.setPointRate(levelEnums.getPointRate())
                .setUserLevel(levelEnums.getLevel());
        if (LevelEnums.L1.getLevel().equals(userFamilyMemberModel.getUserLevel())) {
            UserFamilyGroupModel familyGroupModel = getUserFamilyGroup(userFamilyMemberModel.getFamilyId());
            if (userFamilyMemberModel.getSubMemberNum() + 1 > familyGroupModel.getL2MaxNum()) {
                insert.setStatus(UserStatusEnum.DISABLED.getStatus());
            }
        }

        UserFamilyMemberModel update = new UserFamilyMemberModel()
                .setId(userFamilyMemberModel.getId())
                .setSubMemberNum(userFamilyMemberModel.getSubMemberNum() + 1)
                .setUpdatedTime(now);
        userFamilyMemberMapper.insert(insert);
        userFamilyMemberMapper.updateById(update);
    }

    public UserFamilyConfigModel queryUserFamilyConfig() {
        QueryWrapper<UserFamilyConfigModel> queryWrapper = new QueryWrapper();
        return userFamilyConfigMapper.selectOne(queryWrapper);
    }

}

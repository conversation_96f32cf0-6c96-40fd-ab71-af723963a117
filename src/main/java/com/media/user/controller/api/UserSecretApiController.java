package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.user.dto.request.*;
import com.media.user.service.*;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户安全中心接口
 *
 * 1.绑定手机号接口
 * 2.绑定邮箱接口
 * 3.手机号验证接口
 * 4.邮箱验证接口
 */
@Slf4j
@RestController
@RequestMapping("/user/api/user")
public class UserSecretApiController {

    @Autowired
    UserSecretService userSecretService;

    /**
     * 绑定手机号接口
     * 手机号，验证码
     */
    @PostMapping("/bind/phone")
    @RequireAuth
    public ApiResponse<Void> bindPhone(@Validated @RequestBody BindPhoneRequest request) {
        Integer epValue = userSecretService.bindPhone(CloudSession.getUid(), request);
        return ApiResponse.success(null, epValue, epValue);
    }

    /**
     * 绑定邮箱接口
     * 邮箱，验证码
     */
    @PostMapping("/bind/email")
    @RequireAuth
    public ApiResponse<Void> bindEmail(@Validated @RequestBody BindEmailRequest request) {
        Integer epValue = userSecretService.bindEmail(CloudSession.getUid(), request);
        return ApiResponse.success(null, epValue, epValue);
    }

    /**
     * 手机号验证接口
     * 验证码
     */
    @PostMapping("/verify/phone")
    @RequireAuth
    public ApiResponse<Void> verifyPhone(@Validated @RequestBody VerifyPhoneRequest request) {
        Integer epValue = userSecretService.verifyPhone(CloudSession.getUid(), request.getCode());
        return ApiResponse.success(null, epValue, epValue);
    }

    /**
     * 邮箱验证接口
     * 验证码
     */
    @PostMapping("/verify/email")
    @RequireAuth
    public ApiResponse<Void> verifyEmail(@Validated @RequestBody VerifyPhoneRequest request) {
        Integer epValue = userSecretService.verifyEmail(CloudSession.getUid(), request.getCode());
        return ApiResponse.success(null, epValue, epValue);
    }

}

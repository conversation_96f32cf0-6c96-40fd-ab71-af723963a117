-- 用户邀请奖励汇总表
CREATE TABLE `user_invite_reward_summary` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` bigint NOT NULL COMMENT '用户ID',
  `currency` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货币类型',
  `total_amount` bigint NOT NULL DEFAULT '0' COMMENT '总奖励数量',
  `total_count` int NOT NULL DEFAULT '0' COMMENT '总奖励次数',
  `last_updated_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_uid_currency` (`uid`, `currency`) USING BTREE,
  KEY `idx_uid` (`uid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户邀请奖励汇总表'; 
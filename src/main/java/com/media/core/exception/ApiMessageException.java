package com.media.core.exception;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;

/**
 * <AUTHOR>  2022/11/15 13:56
 * @version 1.0
 */
@Getter
public class ApiMessageException extends ApiException {
    private static final long serialVersionUID = 112312342L;

    private Object[] args = null;

    public ApiMessageException() {
    }

    public ApiMessageException(Integer code, Object...args) {
        super(code);
        this.args = args;
    }

    public Object[] getArgs() {
        return args;
    }

    public void setArgs(Object[] args) {
        this.args = args;
    }

    @Override
    public String getMessage() {
        return "code=" + getCode() + ", args=" + JSONObject.toJSONString(args);
    }
}

package com.media.user.utils;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * DES加密工具类
 */
@Slf4j
public class DesUtils {

    private static final String ALGORITHM = "DES";

    /**
     * DES加密
     *
     * @param plainText 明文
     * @param key 密钥
     * @return 加密后的字符串
     */
    public static String encrypt(String plainText, String key) {
        try {
            // 创建密钥规范
            DESKeySpec desKeySpec = new DESKeySpec(key.getBytes(StandardCharsets.UTF_8));

            // 创建密钥工厂
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
            SecretKey secretKey = keyFactory.generateSecret(desKeySpec);

            // 创建Cipher对象
            Cipher cipher = Cipher.getInstance(ALGORITHM);

            // 初始化Cipher对象，设置为加密模式
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);

            // 加密
            byte[] encryptBytes = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));

            // Base64编码
            return Base64.getEncoder().encodeToString(encryptBytes);
        } catch (Exception e) {
            log.error("DES加密失败，plainText: {}, key: {}", plainText, key, e);
        }
        return StringUtils.EMPTY;
    }

    /**
     * DES解密
     *
     * @param encryptText 密文
     * @param key 密钥
     * @return 解密后的字符串
     */
    public static String decrypt(String encryptText, String key) {
        try {
            // 创建密钥规范
            DESKeySpec desKeySpec = new DESKeySpec(key.getBytes(StandardCharsets.UTF_8));

            // 创建密钥工厂
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance(ALGORITHM);
            SecretKey secretKey = keyFactory.generateSecret(desKeySpec);

            // 创建Cipher对象
            Cipher cipher = Cipher.getInstance(ALGORITHM);

            // 初始化Cipher对象，设置为解密模式
            cipher.init(Cipher.DECRYPT_MODE, secretKey);

            // Base64解码
            byte[] encryptBytes = Base64.getDecoder().decode(encryptText);

            // 解密
            byte[] decryptBytes = cipher.doFinal(encryptBytes);

            return new String(decryptBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("DES解密失败，encryptText: {}, key: {}", encryptText, key, e);
        }
        return StringUtils.EMPTY;
    }

    public static void main(String[] args) {
        Map<String, Object> map = new HashMap<>();
        map.put("uid", 18126592304696L);
        map.put("taskCode", "10011");
        String encrypt = encrypt(JSON.toJSONString(map), "T77qfP&xC8d04s7bb#8zNBVA~C6kX#2+OX)Lbop5(lO~B^C!!Y!7oXRW$!L*aX4PwX2g9&qPjNm!pd3SG3hBW*C34Vf5DFN*HfQ!FibT(mpBhTX%@6C7r0WAM+)1A_TH");
        System.out.println(encrypt);
        String decrypt = decrypt(encrypt, "T77qfP&xC8d04s7bb#8zNBVA~C6kX#2+OX)Lbop5(lO~B^C!!Y!7oXRW$!L*aX4PwX2g9&qPjNm!pd3SG3hBW*C34Vf5DFN*HfQ!FibT(mpBhTX%@6C7r0WAM+)1A_TH");
        System.out.println(decrypt);

    }

}

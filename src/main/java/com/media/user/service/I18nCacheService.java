package com.media.user.service;

import com.alibaba.fastjson2.JSON;
import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.model.ConfigChange;
import com.media.user.client.I18nServiceClient;
import com.media.user.dto.i18n.I18nServiceConfig;
import com.media.user.dto.i18n.I18nTranslationResponse;
import com.media.user.enums.LanguageEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ctrip.framework.apollo.model.ConfigChangeEvent;
import com.ctrip.framework.apollo.spring.annotation.ApolloConfigChangeListener;

import jakarta.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;


/**
 * i18n翻译缓存服务
 */
@Slf4j
@Service
public class I18nCacheService {

    @Autowired
    private I18nServiceClient i18nServiceClient;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * i18n服务配置，从Apollo获取
     */
    @Value("${i18n.service.config:{}}")
    private String i18nServiceConfigJson;



    /**
     * Redis缓存过期时间（小时）
     */
    @Value("${i18n.cache.expire.hours:24}")
    private int cacheExpireHours;

    /**
     * Redis缓存键前缀 - 按namespace和版本存储
     * 格式: i18n:namespace:{namespace}:{version}
     */
    private static final String NAMESPACE_PREFIX = "i18n:namespace:";

    /**
     * 分布式锁键前缀
     */
    private static final String LOCK_PREFIX = "i18n:lock:";

    /**
     * 锁过期时间（秒）
     */
    private static final int LOCK_EXPIRE_SECONDS = 300;

    private final Config config = com.ctrip.framework.apollo.ConfigService.getAppConfig();


    /**
     * 本地内存缓存（按namespace存储完整翻译数据）
     * key: namespace, value: Map<translationKey.language, translation>
     */
    private final Map<String, Map<String, String>> namespaceCache = new ConcurrentHashMap<>();

    /**
     * 缓存加载状态标记
     * key: namespace, value: 是否已加载
     */
    private final Map<String, Boolean> cacheLoadStatus = new ConcurrentHashMap<>();

    /**
     * 异步任务执行器（用于Apollo配置变更处理）
     */
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    @PostConstruct
    public void init() {
        // 启动时加载翻译数据
        reloadTranslations();

        config.addChangeListener(changeEvent -> {
            if (changeEvent.isChanged("i18n.cache.reload.trigger")) {
                ConfigChange change = changeEvent.getChange("i18n.cache.reload.trigger");
                try {
                    // 异步重新加载缓存，避免阻塞配置更新
                    scheduler.execute(() -> {
                        try {
                            log.info("Starting i18n cache reload due to Apollo config change...");
                            // 配置变更时强制从远程服务加载最新数据，避免Redis脏读
                            reloadTranslations(true);
                            log.info("I18n cache reload completed successfully from remote service");
                        } catch (Exception e) {
                            log.error("Failed to reload i18n cache after Apollo config change", e);
                        }
                    });

                } catch (Exception e) {
                    log.error("Failed to schedule i18n cache reload after Apollo config change", e);
                }
            }
        });

        log.info("I18n cache service initialized. Cache will be updated via Apollo config changes.");
    }

    /**
     * 获取翻译内容 - 纯本地内存查询
     * 1. 确保所有namespace已加载到本地内存
     * 2. 直接从本地内存查询，无需Redis查询
     *
     * @param key 翻译键
     * @param language 语言
     * @return 翻译内容，如果未找到返回原键值
     */
    public String getTranslation(String key, LanguageEnums language) {
        if (!StringUtils.hasText(key)) {
            return "";
        }

        if (language == null) {
            language = LanguageEnums.en_US;
        }

        try {
            // 确保所有namespace已加载
            ensureAllNamespacesLoaded();

            // 1. 在所有namespace中查找翻译（支持多种语言代码格式）
            String translation = findTranslationInCache(key, language);
            if (translation != null) {
                return translation;
            }

            // 2. 如果当前语言未找到，尝试使用英语
            if (language != LanguageEnums.en_US) {
                String englishTranslation = findTranslationInCache(key, LanguageEnums.en_US);
                if (englishTranslation != null) {
                    return englishTranslation;
                }
            }

            // 3. 如果都未找到，返回原键值
            log.warn("Translation not found for key: {}, language: {}", key, language.getValue());
            return key;

        } catch (Exception e) {
            log.error("Failed to get translation for key: {}, language: {}", key, language.getValue(), e);
            return key;
        }
    }

    /**
     * 在缓存中查找翻译，支持下划线格式
     * 将横线格式转换为下划线格式，如 zh-CN -> zh_CN
     */
    private String findTranslationInCache(String key, LanguageEnums language) {
        String languageCode = language.getValue();

        // 转换为下划线格式（远程服务使用的格式）
        String underscoreFormat = languageCode.replace("-", "_");
        String cacheKey = key + "." + underscoreFormat;

        // 在所有namespace中查找
        for (Map<String, String> namespaceTranslations : namespaceCache.values()) {
            String translation = namespaceTranslations.get(cacheKey);
            if (translation != null) {
                return translation;
            }
        }

        return null;
    }

    /**
     * 确保所有namespace已加载到本地内存
     */
    private void ensureAllNamespacesLoaded() {
        I18nServiceConfig config = parseConfig();
        if (config == null || config.getNamespaces() == null) {
            return;
        }

        for (I18nServiceConfig.NamespaceConfig namespaceConfig : config.getNamespaces()) {
            String namespace = namespaceConfig.getNamespace();

            // 检查该namespace是否已加载
            if (!cacheLoadStatus.getOrDefault(namespace, false)) {
                loadSingleNamespace(namespaceConfig);
            }
        }
    }

    /**
     * 加载单个namespace的翻译数据到本地内存
     * 使用分布式锁确保多机器环境下的数据一致性
     */
    private void loadSingleNamespace(I18nServiceConfig.NamespaceConfig namespaceConfig) {
        loadSingleNamespace(namespaceConfig, false);
    }

    /**
     * 加载单个namespace的翻译数据到本地内存
     * 使用分布式锁确保多机器环境下的数据一致性
     *
     * @param namespaceConfig namespace配置
     * @param forceRemote 是否强制从远程服务加载，跳过Redis缓存
     */
    private void loadSingleNamespace(I18nServiceConfig.NamespaceConfig namespaceConfig, boolean forceRemote) {
        String namespace = namespaceConfig.getNamespace();

        try {
            Map<String, String> translations = new ConcurrentHashMap<>();

            // 如果强制从远程加载，或者Redis中没有数据，使用分布式锁从远程服务加载
            if (forceRemote) {
                // 强制从远程服务加载最新数据
                log.info("Force loading from remote service for namespace: {}, version: {}",
                    namespace, namespaceConfig.getVersion());
            } else {
                // 先尝试从Redis加载
                translations = loadNamespaceFromRedis(namespace, namespaceConfig.getVersion());
            }

            if (forceRemote || translations.isEmpty()) {
                if (acquireLock(namespace)) {
                    try {
                        if (forceRemote) {
                            // 强制从远程服务加载最新数据
                            translations = loadNamespaceFromRemoteService(namespaceConfig);
                            log.info("Force loaded {} translations from remote service for namespace: {}, version: {} (with lock)",
                                translations.size(), namespace, namespaceConfig.getVersion());
                        } else {
                            // 获得锁后再次检查Redis，防止其他机器已经加载了数据
                            translations = loadNamespaceFromRedis(namespace, namespaceConfig.getVersion());

                            if (translations.isEmpty()) {
                                // 从远程服务加载数据
                                translations = loadNamespaceFromRemoteService(namespaceConfig);
                                log.info("Loaded {} translations from remote service for namespace: {}, version: {} (with lock)",
                                    translations.size(), namespace, namespaceConfig.getVersion());
                            } else {
                                log.info("Found {} translations in Redis after acquiring lock for namespace: {}, version: {}",
                                    translations.size(), namespace, namespaceConfig.getVersion());
                            }
                        }
                    } finally {
                        releaseLock(namespace);
                    }
                } else {
                    if (forceRemote) {
                        // 强制远程加载时，如果未获得锁，等待后重试获取锁
                        log.info("Failed to acquire lock for force remote loading, waiting and retrying...");
                        Thread.sleep(1000);
                        // 递归重试
                        loadSingleNamespace(namespaceConfig, forceRemote);
                        return;
                    } else {
                        // 未获得锁，等待一段时间后重试从Redis加载
                        log.info("Failed to acquire lock for namespace: {}, waiting and retrying...", namespace);
                        Thread.sleep(1000); // 等待1秒
                        translations = loadNamespaceFromRedis(namespace, namespaceConfig.getVersion());
                    }
                }
            }

            // 存储到本地内存
            if (!translations.isEmpty()) {
                namespaceCache.put(namespace, new ConcurrentHashMap<>(translations));
                cacheLoadStatus.put(namespace, true);
                log.info("Loaded {} translations to local cache for namespace: {}", translations.size(), namespace);
            }

        } catch (Exception e) {
            log.error("Failed to load namespace: {}", namespace, e);
        }
    }

    /**
     * 获取分布式锁
     */
    private boolean acquireLock(String namespace) {
        String lockKey = LOCK_PREFIX + namespace;
        String lockValue = String.valueOf(System.currentTimeMillis());

        try {
            Boolean success = stringRedisTemplate.opsForValue()
                .setIfAbsent(lockKey, lockValue, LOCK_EXPIRE_SECONDS, TimeUnit.SECONDS);
            return Boolean.TRUE.equals(success);
        } catch (Exception e) {
            log.error("Failed to acquire lock for namespace: {}", namespace, e);
            return false;
        }
    }

    /**
     * 释放分布式锁
     */
    private void releaseLock(String namespace) {
        String lockKey = LOCK_PREFIX + namespace;
        try {
            stringRedisTemplate.delete(lockKey);
        } catch (Exception e) {
            log.error("Failed to release lock for namespace: {}", namespace, e);
        }
    }

    /**
     * 从Redis加载namespace的翻译数据
     * 使用Hash结构按namespace和版本存储，避免keys()操作
     */
    private Map<String, String> loadNamespaceFromRedis(String namespace, String version) {
        Map<String, String> translations = new ConcurrentHashMap<>();
        try {
            String namespaceKey = NAMESPACE_PREFIX + namespace + ":" + version;

            // 使用Hash结构获取整个namespace的翻译数据
            Map<Object, Object> hashEntries = stringRedisTemplate.opsForHash().entries(namespaceKey);

            if (hashEntries != null && !hashEntries.isEmpty()) {
                for (Map.Entry<Object, Object> entry : hashEntries.entrySet()) {
                    if (entry.getKey() != null && entry.getValue() != null) {
                        translations.put(entry.getKey().toString(), entry.getValue().toString());
                    }
                }
            }

            log.debug("Loaded {} translations from Redis for namespace: {}, version: {}",
                translations.size(), namespace, version);

        } catch (Exception e) {
            log.error("Failed to load translations from Redis for namespace: {}, version: {}",
                namespace, version, e);
        }

        return translations;
    }

    /**
     * 从远程服务加载namespace的翻译数据
     * 使用Hash结构按namespace存储到Redis
     */
    private Map<String, String> loadNamespaceFromRemoteService(I18nServiceConfig.NamespaceConfig namespaceConfig) {
        Map<String, String> translations = new ConcurrentHashMap<>();

        try {
            I18nTranslationResponse response = i18nServiceClient.getTranslations(
                namespaceConfig.getNamespace(),
                namespaceConfig.getVersion()
            );

            if (response != null && response.getResult() != null && response.getResult().getTranslations() != null) {
                translations.putAll(response.getResult().getTranslations());

                // 使用Hash结构存储到Redis，按namespace和版本组织
                String namespaceKey = NAMESPACE_PREFIX + namespaceConfig.getNamespace() + ":" + namespaceConfig.getVersion();

                if (!translations.isEmpty()) {
                    // 批量存储到Redis Hash
                    stringRedisTemplate.opsForHash().putAll(namespaceKey, translations);

                    // 设置过期时间
                    stringRedisTemplate.expire(namespaceKey, cacheExpireHours, TimeUnit.HOURS);

                    log.debug("Stored {} translations to Redis Hash for namespace: {}",
                        translations.size(), namespaceConfig.getNamespace());
                }

                log.debug("Loaded {} translations from remote service for namespace: {}",
                    translations.size(), namespaceConfig.getNamespace());
            }

        } catch (Exception e) {
            log.error("Failed to load translations from remote service for namespace: {}, version: {}",
                namespaceConfig.getNamespace(), namespaceConfig.getVersion(), e);
        }

        return translations;
    }


    /**
     * 解析配置
     */
    private I18nServiceConfig parseConfig() {
        if (!StringUtils.hasText(i18nServiceConfigJson)) {
            return null;
        }

        try {
            return JSON.parseObject(i18nServiceConfigJson, I18nServiceConfig.class);
        } catch (Exception e) {
            log.error("Failed to parse i18n service config: {}", i18nServiceConfigJson, e);
            return null;
        }
    }

    /**
     * 清空缓存
     */
    public void clearCache() {
        try {
            // 清空本地namespace缓存
            namespaceCache.clear();
            cacheLoadStatus.clear();

            // Redis缓存不需要主动清理，重新加载时会自动覆盖
            // 避免使用keys()操作，对Redis性能影响较大

            log.info("Local cache cleared successfully");

        } catch (Exception e) {
            log.error("Failed to clear cache", e);
        }
    }

    /**
     * 重新加载翻译数据
     */
    public void reloadTranslations() {
        reloadTranslations(true);
    }

    /**
     * 重新加载翻译数据
     *
     * @param forceRemote 是否强制从远程服务加载，跳过Redis缓存
     */
    public void reloadTranslations(boolean forceRemote) {
        try {
            // 清空本地缓存
            namespaceCache.clear();
            cacheLoadStatus.clear();

            // 重新加载所有namespace
            I18nServiceConfig config = parseConfig();
            if (config != null && config.getNamespaces() != null) {
                for (I18nServiceConfig.NamespaceConfig namespaceConfig : config.getNamespaces()) {
                    loadSingleNamespace(namespaceConfig, forceRemote);
                }
            }

            String loadType = forceRemote ? "from remote service" : "from cache/remote";
            log.info("Translations reloaded successfully {}", loadType);

        } catch (Exception e) {
            log.error("Failed to reload translations", e);
        }
    }

    /**
     * 手动重新加载翻译数据
     */
    public void manualReloadTranslations() {
        log.info("Manually reloading i18n translations...");
        reloadTranslations();
    }

    /**
     * 获取缓存大小
     */
    public int getCacheSize() {
        return namespaceCache.values().stream()
            .mapToInt(Map::size)
            .sum();
    }
}

package com.media.user.es;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.media.core.constant.PageResponse;
import com.media.user.dto.query.UserSearchQuery;
import com.media.user.dto.response.internal.SearchUserResponse;
import com.media.user.dto.response.internal.UserMeResponse;
import com.media.user.enums.UserFollowStateEnum;
import com.media.user.es.dto.ClientUserDto;
import com.media.user.es.dto.PageClientUserDto;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.service.UserFollowService;
import com.media.user.service.cache.ClientUserCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.*;
import org.elasticsearch.common.unit.Fuzziness;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.media.user.utils.LivenessStatusUtils.livenessStatusVerify;

@Service
@Slf4j
public class EsClientUserService extends EsService {

    // 文档索引名称
    public static final String USER_INDEX_NAME = "client_user";

    @Autowired
    RestHighLevelClient restHighLevelClient;

    @Autowired
    UserFollowService userFollowService;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    public PageResponse<SearchUserResponse> searchUser(UserSearchQuery query) {
        log.info("uid:{} search:{}", query.getUid(), query.getKey());
        //关键字为空 则返回 空数据
        if (StringUtils.isBlank(query.getKey())) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
        if(query.getPage() == null || query.getPage() < 0) {
            query.setPage(0);
        }
        if(query.getSize() == null || query.getSize() < 1) {
            query.setSize(10);
        }

        PageClientUserDto pageData = this.searchEsClientUser(query.getPage(), query.getSize(), query.getKey());
        if (pageData.getTotal() == 0) {
            return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
        }

        List<SearchUserResponse> list = new ArrayList<>();
        SearchUserResponse searchUserResponse = null;
        for (ClientUserDto clientUserDto : pageData.getData()) {
            searchUserResponse = new SearchUserResponse();
            Long uid = Long.parseLong(clientUserDto.getUid());
            UserMeResponse userMeResponse = clientUserCacheService.userInfo(uid);
            if(userMeResponse == null) {
                continue;
            }
            BeanUtils.copyProperties(userMeResponse, searchUserResponse);
            searchUserResponse.setFollowersNumber(userFollowService.countFollowers(uid));
            searchUserResponse.setFansNumber(userFollowService.countFans(uid));
            searchUserResponse.setFollowState(UserFollowStateEnum.FOLLOW_STATE_0.getState());
            searchUserResponse.setGenesisBadge(userMeResponse.getGenesisBadge() == null ? 0 : userMeResponse.getGenesisBadge());
            if(query.getUid() != null && query.getUid() > 0){
                boolean followRation = userFollowService.isFollowing(query.getUid(), uid);
                if(followRation){
                    searchUserResponse.setFollowState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
                }
            }
            if (!livenessStatusVerify(userMeResponse.getFaceLivenessStatus(), userMeResponse.getPhoneVerify(), userMeResponse.getEmailVerify())) {
                searchUserResponse.setInviteCode("");
            }
            list.add(searchUserResponse);
        }
        return new PageResponse<>(query.getPage(), query.getSize(), pageData.getTotal(), list);
    }

    public PageClientUserDto searchEsClientUser(int page, int size, String nickName) {
        if(page > 0){
            page = page - 1;
        }
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(super.createIndexName(USER_INDEX_NAME));

        // 构建查询条件
        SearchSourceBuilder sourceBuilder = null;
        if(isChineseChar(nickName)){
            log.info("chinese");
            // 构建查询条件
            sourceBuilder = new SearchSourceBuilder()
                    .query(QueryBuilders.matchQuery("nick_name", nickName.toLowerCase()).fuzziness(Fuzziness.AUTO)) // 模糊查询
                    .from(page * size) // 分页起点
                    .size(size) // 每页条数
            ; // 按价格升序排序
        }else{
            if (nickName.contains(" ")) {
                log.info("other space");
                // 构建查询条件
                sourceBuilder = new SearchSourceBuilder()
                        .query(QueryBuilders.matchQuery("nick_name", nickName.toLowerCase()).fuzziness(Fuzziness.AUTO)) // 模糊查询
//                .query(QueryBuilders.wildcardQuery("nick_name", "*" + nickName.toLowerCase() + "*")) // 模糊查询
                        .from(page * size) // 分页起点
                        .size(size) // 每页条数
                ; // 按价格升序排序
            } else {
                log.info("other no space");
                // 构建查询条件
                sourceBuilder = new SearchSourceBuilder()
//                        .query(QueryBuilders.matchQuery("nick_name", nickName.toLowerCase()).fuzziness(Fuzziness.AUTO)) // 模糊查询
                        .query(QueryBuilders.wildcardQuery("nick_name", "*" + nickName.toLowerCase() + "*")) // 模糊查询
                        .from(page * size) // 分页起点
                        .size(size) // 每页条数
                ; // 按价格升序排序
            }
        }

        // 设置查询条件
        searchRequest.source(sourceBuilder);

        // 执行查询
        try {
            SearchResponse response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            if(response == null || response.getHits() == null || response.getHits().getHits().length == 0) {
                return new PageClientUserDto().setTotal(0L).setData(null);
            }

            // 解析结果
            List<ClientUserDto> result = new ArrayList<>();
            ClientUserDto clientUserDto = null;
            for(SearchHit searchHit : response.getHits()){
                JSONObject jsonObject = JSON.parseObject(searchHit.getSourceAsString());
                clientUserDto = new ClientUserDto();
                clientUserDto.setUid(jsonObject.getString("uid"));
                clientUserDto.setNickName(jsonObject.getString("nick_name"));
                result.add(clientUserDto);
            }
            return new PageClientUserDto().setTotal(response.getHits().getTotalHits().value).setData(result);
        }catch (Exception e){
            log.error("query es error: {}", e.getMessage());
        }
        return new PageClientUserDto().setTotal(0L).setData(null);
    }

    /**
     * 插入一条
     * @param uid
     * @param nickName
     */
    @Async("threadPoolTaskExecutor")
    public void saveOne(Long uid, String nickName) {
        log.info("send user to es {} - {}", uid, nickName);
        List<Map<String, String>> dataList = new ArrayList<>();
        Map<String, String> data = new HashMap<>();
        data.put("uid", "" + uid);
        data.put("nick_name", nickName);
        dataList.add(data);
        this.bulkUpsert(USER_INDEX_NAME, "uid", dataList);
    }



    /**
     * 批量插入用户信息
     *
     * @param indexName 索引名称
     * @param priKey    主键名称
     * @param dataList  数据列表（每个 map 表示一条文档）
     */
    public void bulkUpsert(String indexName, String priKey, List<Map<String, String>> dataList) {
        try {
            // 创建 BulkRequest
            BulkRequest bulkRequest = new BulkRequest();

            // 遍历数据列表，添加到批量请求
            for (Map<String, String> data : dataList) {
                String id = data.get(priKey); // 假设每条数据都有唯一的 id
                bulkRequest.add(
                        new org.elasticsearch.action.index.IndexRequest(createIndexName(indexName)) // 指定索引
                                .id(id) // 设置文档 ID
                                .source(data, XContentType.JSON) // 设置文档内容
                );
            }

            // 执行批量请求
            BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);

            // 检查响应状态
            if (bulkResponse.hasFailures()) {
                log.error("Bulk operation had failures: {}", bulkResponse.buildFailureMessage());
            } else {
                log.info("Bulk operation completed successfully.");
            }
        }catch (Exception e){
            log.error("Bulk operation failed." + e.getMessage());
        }
    }


    /**
     * client_user 在 es 中的数据格式
     * @return
     */
    public String createUserBodyJson() {
        return  "{\n" +
                "  \"mappings\": {\n" +
                "    \"properties\": {\n" +
                "      \"uid\": {\n" +
                "        \"type\": \"text\"\n" +
                "      },\n" +
                "      \"nick_name\": {\n" +
                "        \"type\": \"text\"\n" +
                "      }\n" +
                "    }\n" +
                "  },\n" +
                "  \"settings\": {\n" +
                "    \"index\": {\n" +
                "      \"number_of_shards\": \"2\",\n" +
                "      \"number_of_replicas\": \"2\"\n" +
                "    }\n" +
                "  }\n" +
                "}";
    }


    private boolean isChineseChar(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        // 中文字符的Unicode范围通常是：\u4e00-\u9fa5
        return str.matches(".*[\u4e00-\u9fa5].*");
    }
}

package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.core.request.ClientInfoContext;
import com.media.user.dto.response.LotteryShareResponse;
import com.media.user.service.LotteryShareService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 彩票分享控制器
 */
@RestController
@RequestMapping("/user/api/lottery")
public class LotteryShareController {

    @Autowired
    private LotteryShareService lotteryShareService;

    /**
     * 获取彩票分享数据
     */
    @PostMapping("/share/data")
    @RequireAuth
    public LotteryShareResponse getLotteryShareData() {
        return lotteryShareService.getLotteryShareData(CloudSession.getUid(), ClientInfoContext.getLanguage());
    }
}

package com.media.user.service.filter.impl;

import com.media.user.dto.im.RongCloudMessageData;
import com.media.user.dto.rongCloud.RongCloudCallbackDTO;
import com.media.user.enums.ImExceptionEnums;
import com.media.user.enums.ImMsgTypeEnum;
import com.media.user.service.filter.MessageFilter;
import com.media.user.service.filter.annotation.ChannelType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.media.user.enums.ImExceptionEnums.SUCCESS;

@Slf4j
@Component
@Order(2)
@ChannelType(value = {"PERSON"})
public class FollowMessageTypeFilter implements MessageFilter {

    @Override
    public ImExceptionEnums doFilter(RongCloudMessageData data) {
        log.info("<FollowMessageTypeFilter> start data:{}", data);
        if (Objects.isNull(data) || Objects.isNull(data.getMessageDTO())) {
            return SUCCESS;
        }
        RongCloudCallbackDTO messageDTO = data.getMessageDTO();
        String msgType = messageDTO.getMsgType();
        if (msgType.equals(ImMsgTypeEnum.APP_FOLLOW.getType())) {
            log.info("关注消息，不进行处理");
            return ImExceptionEnums.SPECIAL_SCENARIO_BLOCKED;
        }
        return SUCCESS;
    }
}

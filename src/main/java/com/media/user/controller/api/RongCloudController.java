package com.media.user.controller.api;

import com.media.core.auth.RequireAuth;
import com.media.user.service.RongCloudService;
import com.media.user.service.UserLoginLogService;
import com.xme.xme_base_depends.models.ApiResponse;
import io.rong.models.response.TokenResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;

@Slf4j
@RestController
@RequestMapping("/user/api/rongCloud")
public class RongCloudController {

    @Autowired
    private RongCloudService rongCloudService;

    @PostMapping("/getToken")
    @RequireAuth
    public ApiResponse<TokenResult> getToken() {
        TokenResult token = rongCloudService.getToken();
        if (token == null) {
            return ApiResponse.fail(500, "融云Token生成失败");
        }
        return new ApiResponse<>(token);
    }

}
package com.media.user.dto.request;

import com.media.user.dto.group.ForgetPasswordGroup;
import com.media.user.dto.group.RegisterGroup;
import com.media.user.enums.BusinessTypeEnum;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SendEmailCodeRequest {

    private Long uid;

    @NotBlank(groups = {ForgetPasswordGroup.class, RegisterGroup.class})
    @Email(groups = {ForgetPasswordGroup.class, RegisterGroup.class})
    private String email;

    @NotNull(groups = {ForgetPasswordGroup.class, RegisterGroup.class, ForgetPasswordRequest.class})
    private Integer businessType;

    private BusinessTypeEnum businessTypeEnum;

    private String code;
}

package com.media.core.utils.aws;

import com.alibaba.fastjson.JSON;
import com.media.core.exception.ApiException;
import com.media.core.exception.BaseException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.AwsSessionCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.core.client.config.ClientOverrideConfiguration;
import software.amazon.awssdk.core.exception.SdkClientException;
import software.amazon.awssdk.core.exception.SdkServiceException;
import software.amazon.awssdk.core.retry.RetryPolicy;
import software.amazon.awssdk.core.retry.backoff.BackoffStrategy;
import software.amazon.awssdk.core.retry.backoff.EqualJitterBackoffStrategy;
import software.amazon.awssdk.core.retry.conditions.RetryCondition;
import software.amazon.awssdk.http.SdkHttpClient;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.rekognition.RekognitionClient;
import software.amazon.awssdk.services.rekognition.model.*;
import software.amazon.awssdk.services.sts.StsClient;
import software.amazon.awssdk.services.sts.model.AssumeRoleRequest;
import software.amazon.awssdk.services.sts.model.AssumeRoleResponse;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;

/**
 * AWS工具类，提供与AWS Rekognition服务交互的各种方法
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class AwsUtils {

    /**
     * AWS认证相关配置
     */
    @Value("${aws.faceLive.accessKey:********************}")
    String accessKey;

    @Value("${aws.faceLive.secretKey:zfc8DgMcXBBIsZhVhzhhP2g/MSYDzwxbyi5GsDy6}")
    String secretKey;

    @Value("${aws.faceLive.roleArn:arn:aws:iam::145023116201:role/Rekognition}")
    String roleArn;

    @Value("${aws.faceLive.roleSessionName:Rekognition}")
    String roleSessionName;

    @Value("${aws.faceLive.collectionId:XME_FACE_USERS}")
    String collectionId;

    /**
     * 网络和重试策略配置
     */
    @Value("${aws.client.timeout.seconds:10}")
    private int clientTimeoutSeconds;

    @Value("${aws.client.attempt.timeout.seconds:2000}")
    private int clientAttemptTimeoutMillis;

    @Value("${aws.client.retries:2}")
    private int clientRetries;

    @Value("${aws.client.retry.base.delay:200}")
    private long retryBaseDelayMs;

    @Value("${aws.client.retry.max.delay:1000}")
    private long retryMaxDelayMs;

    /**
     * 会话凭证配置
     */
    @Getter
    @Value("${liveness.threshold:90}")
    private int livenessThreshold;

    @Value("${face.match.threshold:90}")
    private int faceMatchThreshold;

    /**
     * 人脸识别配置
     */
    @Value("${aws.credentials.duration.seconds:3600}")
    private int credentialsDurationSeconds;

    @Value("${face.image.sharpness.threshold:70}")
    private float sharpnessThreshold;

    @Value("${aws.auto.create.collection:false}")
    private boolean autoCreateCollection;

    /**
     * 活体数据临时缓存
     */
    private static final String FACE_LIVENESS_RESULT_CACHE_KEY = "aws:face:liveness:result:";

    /**
     * 共享重试策略
     */
    private final AtomicReference<RetryPolicy> retryPolicyRef = new AtomicReference<>();

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    // 共享的HTTP客户端实例，用于所有AWS服务客户端
    private final SdkHttpClient sharedHttpClient;

    private final ScheduledExecutorService scheduler;

    /**
     * 初始化共享HTTP客户端
     * 这将减少线程泄漏问题，因为AWS SDK的HTTP客户端会创建线程池
     */
    public AwsUtils() {
        // 创建一个共享的HTTP客户端，使用合理的配置
        this.scheduler = Executors.newScheduledThreadPool(50);
        this.sharedHttpClient = ApacheHttpClient.builder()
                .connectionTimeout(Duration.ofSeconds(10))
                .socketTimeout(Duration.ofSeconds(30))
                .connectionMaxIdleTime(Duration.ofSeconds(60))
                .maxConnections(120)
                .build();
        log.info("已初始化AWS共享HTTP客户端");
    }

    /**
     * 初始化组件
     */
    @PostConstruct
    public void init() {
        log.info("初始化AWS工具类...");
        validateConfiguration();
        initRetryPolicy();

        if (autoCreateCollection) {
            ensureCollectionExists();
        }
    }

    /**
     * 验证配置的有效性
     */
    private void validateConfiguration() {
        if (accessKey == null || accessKey.trim().isEmpty()) {
            log.error("AWS访问密钥未配置");
            throw new IllegalStateException("AWS访问密钥未配置");
        }

        if (secretKey == null || secretKey.trim().isEmpty()) {
            log.error("AWS密钥未配置");
            throw new IllegalStateException("AWS密钥未配置");
        }

        if (roleArn == null || roleArn.trim().isEmpty()) {
            log.error("AWS角色ARN未配置");
            throw new IllegalStateException("AWS角色ARN未配置");
        }

        log.info("AWS配置验证通过");
    }

    /**
     * 统一客户端超时重试配置
     */
    private ClientOverrideConfiguration createClientConfig() {
        return ClientOverrideConfiguration.builder()
                .retryPolicy(retryPolicyRef.get())
                .apiCallTimeout(Duration.ofSeconds(clientTimeoutSeconds))
                .apiCallAttemptTimeout(Duration.ofMillis(clientAttemptTimeoutMillis))
                .scheduledExecutorService(scheduler)
                .build();
    }

    /**
     * 初始化重试策略
     */
    private void initRetryPolicy() {
        BackoffStrategy backoffStrategy = EqualJitterBackoffStrategy.builder()
                .baseDelay(Duration.ofMillis(retryBaseDelayMs))
                .maxBackoffTime(Duration.ofMillis(retryMaxDelayMs))
                .build();

        // 自定义重试条件
        RetryCondition retryCondition = (context) -> {
            // 默认重试条件
            boolean shouldRetry = RetryCondition.defaultRetryCondition().shouldRetry(context);

            // 添加对连接超时等特定异常的重试支持
            if (!shouldRetry && context.exception() != null) {
                Throwable ex = context.exception();
                return ex instanceof java.net.SocketTimeoutException ||
                        ex instanceof java.net.ConnectException ||
                        ex.getMessage() != null && ex.getMessage().contains("timeout");
            }

            return shouldRetry;
        };

        RetryPolicy retryPolicy = RetryPolicy.builder()
                .numRetries(clientRetries)
                .retryCondition(retryCondition)
                .backoffStrategy(backoffStrategy)
                .throttlingBackoffStrategy(backoffStrategy)
                .build();

        retryPolicyRef.set(retryPolicy);
        log.info("AWS重试策略已初始化: 最大重试次数={}, 基础延迟={}ms, 最大延迟={}ms",
                clientRetries, retryBaseDelayMs, retryMaxDelayMs);
    }

    /**
     * 创建AWS认证会话
     *
     * @param region 区域
     * @return 会话响应对象
     * @throws BaseException 当会话创建失败时
     */
    public AwsCreateSessionResponse createSession(Region region) {
        if (region == null) {
            throw new BaseException("AWS区域不能为空");
        }

        try {
            AwsSessionCredentials credentials = getTemporaryCredentials(region);
            String sessionId = createFaceLiveSession(credentials, region);

            if (sessionId == null) {
                throw new BaseException("无法创建人脸识别会话");
            }

            return new AwsCreateSessionResponse()
                    .setAccessKeyId(credentials.accessKeyId())
                    .setAccessSecret(credentials.secretAccessKey())
                    .setSessionToken(credentials.sessionToken())
                    .setRegion(region.toString())
                    .setSessionId(sessionId);
        } catch (BaseException e) {
            throw e;
        } catch (SdkServiceException e) {
            log.error("创建AWS会话时发生服务异常: {}, 状态码: {}", e.getMessage(), e.statusCode(), e);
            throw new BaseException("AWS服务错误");
        } catch (SdkClientException e) {
            log.error("创建AWS会话时发生客户端异常: {}", e.getMessage(), e);
            throw new BaseException("AWS客户端错误");
        } catch (Exception e) {
            log.error("创建AWS会话失败: {}", e.getMessage(), e);
            throw new BaseException("创建AWS会话失败");
        }
    }

    /**
     * 获取Rekognition客户端
     *
     * @param credentialsResponse 认证响应信息
     * @return Rekognition客户端
     * @throws BaseException 当创建客户端失败时
     */
    public RekognitionClient getRekognitionClient(AwsCreateSessionResponse credentialsResponse) {
        if (credentialsResponse == null) {
            throw new BaseException("认证响应为空");
        }

        validateSessionCredentials(credentialsResponse);

        try {
            AwsSessionCredentials credentials = AwsSessionCredentials.create(
                    credentialsResponse.getAccessKeyId(),
                    credentialsResponse.getAccessSecret(),
                    credentialsResponse.getSessionToken()
            );

            return RekognitionClient.builder()
                    .region(Region.of(credentialsResponse.getRegion()))
                    .credentialsProvider(StaticCredentialsProvider.create(credentials))
                    .httpClient(sharedHttpClient) // 使用共享HTTP客户端
                    .overrideConfiguration(createClientConfig())
                    .build();
        } catch (Exception e) {
            log.error("创建Rekognition客户端失败: {}", e.getMessage(), e);
            throw new BaseException("创建Rekognition客户端失败");
        }
    }

    /**
     * 验证会话凭证有效性
     *
     * @param credentialsResponse 会话凭证
     * @throws BaseException 当凭证无效时
     */
    private void validateSessionCredentials(AwsCreateSessionResponse credentialsResponse) {
        if (credentialsResponse.getAccessKeyId() == null || credentialsResponse.getAccessKeyId().isEmpty()) {
            throw new BaseException("AWS访问密钥ID不能为空");
        }

        if (credentialsResponse.getAccessSecret() == null || credentialsResponse.getAccessSecret().isEmpty()) {
            throw new BaseException("AWS访问密钥不能为空");
        }

        if (credentialsResponse.getSessionToken() == null || credentialsResponse.getSessionToken().isEmpty()) {
            throw new BaseException("AWS会话令牌不能为空");
        }

        if (credentialsResponse.getRegion() == null || credentialsResponse.getRegion().isEmpty()) {
            throw new BaseException("AWS区域不能为空");
        }
    }

    /**
     * 获取人脸活体检测会话结果
     *
     * @param credentials 会话凭证
     * @return 活体检测会话结果
     * @throws BaseException 当活体检测失败时
     */
    public GetFaceLivenessSessionResultsResponse getFaceLiveSession(AwsCreateSessionResponse credentials, RekognitionClient rekognitionClient) {
        if (credentials == null) {
            throw new BaseException("认证响应不能为空");
        }

        String sessionId = credentials.getSessionId();
        if (sessionId == null || sessionId.isEmpty()) {
            throw new BaseException("无效的会话ID");
        }

        // 尝试从缓存获取
        String cacheKey = FACE_LIVENESS_RESULT_CACHE_KEY + sessionId;
        String cachedResult = stringRedisTemplate.opsForValue().get(cacheKey);
        if (cachedResult != null && !cachedResult.isEmpty()) {
            log.info("从缓存获取活体检测结果，会话ID: {}", sessionId);
            try {
                GetFaceLivenessSessionResultsResponse response = JSON.parseObject(cachedResult, GetFaceLivenessSessionResultsResponse.class);
                if (response == null || response.referenceImage() == null) {
                    log.error("缓存的活体检测结果不完整,{}", cachedResult);
                }
                return response;
            } catch (Exception e) {
                log.warn("反序列化缓存的活体检测结果失败: {}", e.getMessage());
            }
        }
        return executeAwsOperation("获取人脸活体会话结果", () -> {
            // 使用传入的客户端或创建新客户端
            RekognitionClient client = rekognitionClient != null ?
                    rekognitionClient : getRekognitionClient(credentials);
            GetFaceLivenessSessionResultsRequest request = GetFaceLivenessSessionResultsRequest.builder()
                    .sessionId(sessionId)
                    .build();

            GetFaceLivenessSessionResultsResponse response = client.getFaceLivenessSessionResults(request);

            if (!LivenessSessionStatus.SUCCEEDED.equals(response.status())) {
                log.info("活体检测失败: {}", response);
                throw new ApiException(MediaUserExceptionCodeApi.AWS_LIVENESS_CHECK_FAILED);
            }

            log.info("活体检测置信度得分: {}", response.confidence());

            try {
                stringRedisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(response), 5, TimeUnit.MINUTES);
                log.info("活体检测结果已缓存，会话ID: {}, 置信度: {}", sessionId, response.confidence());
            } catch (Exception e) {
                log.warn("缓存活体检测结果失败: {}", e.getMessage());
            }

            return response;
        });

    }

    /**
     * 验证活体检测响应
     *
     * @param response 活体检测响应
     * @throws BaseException 当活体检测失败时
     */
    public void validateLivenessResponse(GetFaceLivenessSessionResultsResponse response) {
        if (response.confidence() < livenessThreshold) {
            log.info("活体检测置信度过低（得分: {}, 阈值:{}", response.confidence(), livenessThreshold);
            throw new ApiException(MediaUserExceptionCodeApi.AWS_LIVENESS_CHECK_FAILED);
        }
    }

    /**
     * 获取临时凭证
     *
     * @param region 区域
     * @return AWS会话凭证
     * @throws BaseException 当获取凭证失败时
     */
    private AwsSessionCredentials getTemporaryCredentials(Region region) {
        StsClient stsClient = null;
        try {
            stsClient = createStsClient(region);
            AssumeRoleRequest request = AssumeRoleRequest.builder()
                    .roleArn(roleArn)
                    .roleSessionName(roleSessionName)
                    .durationSeconds(credentialsDurationSeconds)
                    .build();

            AssumeRoleResponse response = stsClient.assumeRole(request);
            if (response.credentials() == null) {
                throw new BaseException("未能获取有效的临时凭证");
            }

            return AwsSessionCredentials.create(
                    response.credentials().accessKeyId(),
                    response.credentials().secretAccessKey(),
                    response.credentials().sessionToken()
            );
        } catch (BaseException e) {
            throw e;
        } catch (SdkServiceException e) {
            log.error("获取临时凭证时发生服务异常: {}, 状态码: {}", e.getMessage(), e.statusCode(), e);
            throw new BaseException("获取AWS临时凭证失败(服务错误)");
        } catch (SdkClientException e) {
            log.error("获取临时凭证时发生客户端异常: {}", e.getMessage(), e);
            throw new BaseException("获取AWS临时凭证失败(客户端错误)");
        } catch (Exception e) {
            log.error("获取临时凭证失败: {}", e.getMessage(), e);
            throw new BaseException("获取AWS临时凭证失败:");
        }
    }

    /**
     * 创建STS客户端
     *
     * @param region 区域
     * @return STS客户端
     * @throws BaseException 当创建客户端失败时
     */
    private StsClient createStsClient(Region region) {
        if (region == null) {
            throw new BaseException("AWS区域不能为空");
        }

        try {
            AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(accessKey, secretKey);
            return StsClient.builder()
                    .region(region)
                    .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                    .httpClient(sharedHttpClient) // 使用共享HTTP客户端
                    .overrideConfiguration(createClientConfig())
                    .build();
        } catch (Exception e) {
            log.error("创建STS客户端失败: {}", e.getMessage(), e);
            throw new BaseException("创建STS客户端失败");
        }
    }

    /**
     * 创建人脸活体会话
     *
     * @param credentials AWS会话凭证
     * @param region      区域
     * @return 会话ID
     * @throws BaseException 当创建会话失败时
     */
    private String createFaceLiveSession(AwsSessionCredentials credentials, Region region) {
        if (credentials == null) {
            throw new BaseException("AWS会话凭证不能为空");
        }

        if (region == null) {
            throw new BaseException("AWS区域不能为空");
        }

        RekognitionClient rekognitionClient = null;
        try {
            rekognitionClient = RekognitionClient.builder()
                    .region(region)
                    .credentialsProvider(StaticCredentialsProvider.create(credentials))
                    .httpClient(sharedHttpClient) // 使用共享HTTP客户端以防止线程泄漏
                    .overrideConfiguration(b -> b
                            .retryPolicy(retryPolicyRef.get())
                            .apiCallTimeout(Duration.ofSeconds(clientTimeoutSeconds))
                            .apiCallAttemptTimeout(Duration.ofMillis(clientAttemptTimeoutMillis))
                            .scheduledExecutorService(scheduler)
                    )
                    .build();

            CreateFaceLivenessSessionRequest request = CreateFaceLivenessSessionRequest.builder().build();
            CreateFaceLivenessSessionResponse response = rekognitionClient.createFaceLivenessSession(request);

            if (response.sessionId() == null || response.sessionId().isEmpty()) {
                throw new BaseException("创建人脸活体会话返回的会话ID无效");
            }

            return response.sessionId();
        } catch (BaseException e) {
            throw e;
        } catch (SdkServiceException e) {
            log.error("创建人脸活体会话时发生服务异常: {}, 状态码: {}", e.getMessage(), e.statusCode(), e);
            throw new BaseException("创建人脸活体会话失败(服务错误)");
        } catch (SdkClientException e) {
            log.error("创建人脸活体会话时发生客户端异常: {}", e.getMessage(), e);
            throw new BaseException("创建人脸活体会话失败(客户端错误)");
        } catch (Exception e) {
            log.error("创建人脸活体会话失败: {}", e.getMessage(), e);
            throw new BaseException("创建人脸活体会话失败");
        } finally {
            if (rekognitionClient != null) {
                try {
                    rekognitionClient.close();
                } catch (Exception e) {
                    log.warn("关闭Rekognition客户端时出错: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 提取人脸特征向量FaceId
     *
     * @param imageBytes  图像字节
     * @param credentials 会话凭证
     * @param uid         用户ID
     * @return 人脸ID
     * @throws BaseException 当提取失败时
     */
    public String extractFaceVector(SdkBytes imageBytes, AwsCreateSessionResponse credentials, Long uid, RekognitionClient rekognitionClient) {
        validateImageAndCredentials(imageBytes, credentials);

        if (uid == null) {
            throw new BaseException("用户ID不能为空");
        }

        return executeAwsOperation("人脸特征录入", () -> {
            // 使用传入的客户端或创建新客户端
            RekognitionClient client = rekognitionClient != null ?
                    rekognitionClient : getRekognitionClient(credentials);
            IndexFacesRequest request = buildIndexFacesRequest(imageBytes, uid);
            IndexFacesResponse response = client.indexFaces(request);

            if (response.faceRecords().isEmpty()) {
                throw new ApiException(MediaUserExceptionCodeApi.AWS_LIVENESS_CHECK_FAILED);
            }

            return response.faceRecords().get(0).face().faceId();
        });
    }

    private IndexFacesRequest buildIndexFacesRequest(SdkBytes imageBytes, Long uid) {
        return IndexFacesRequest.builder()
                .collectionId(collectionId)
                .image(Image.builder().bytes(imageBytes).build())
                .externalImageId("xme_face_" + uid)
                .maxFaces(1)
                .qualityFilter(QualityFilter.AUTO)
                .build();
    }
    
    /**
     * 检查是否存在重复人脸，返回包含重复信息的Map
     *
     * @param imageBytes  图像字节
     * @param credentials 会话凭证
     * @param rekognitionClient Rekognition客户端
     * @return 包含检查结果和匹配详情的Map: {"isDuplicate": boolean, "similarity": float, "faceId": String}
     * @throws BaseException 当检查失败时
     */
    public Map<String, Object> isDuplicateFace(SdkBytes imageBytes, AwsCreateSessionResponse credentials, RekognitionClient rekognitionClient) {
        validateImageAndCredentials(imageBytes, credentials);

        return executeAwsOperation("校验重复人脸", () -> {
            Map<String, Object> result = new HashMap<>();
            result.put("isDuplicate", false);
            
            // 使用传入的客户端或创建新客户端
            RekognitionClient client = rekognitionClient != null ?
                    rekognitionClient : getRekognitionClient(credentials);
            Image image = Image.builder().bytes(imageBytes).build();
            
            // 增加maxFaces参数，获取更多潜在匹配项用于分析
            SearchFacesByImageRequest request = SearchFacesByImageRequest.builder()
                    .collectionId(collectionId)
                    .image(image)
                    .faceMatchThreshold((float) faceMatchThreshold)
                    .maxFaces(5) // 获取多个潜在匹配项以便分析
                    .build();

            SearchFacesByImageResponse response = client.searchFacesByImage(request);
            
            // 如果没有匹配项，直接返回结果
            if (response.faceMatches().isEmpty()) {
                return result;
            }
            
            // 获取匹配度最高的项
            float highestSimilarity = 0;
            String matchedFaceId = null;
            
            // 定义更严格的匹配阈值，用于精确匹配判断
            float strictMatchThreshold = faceMatchThreshold; // 更高的阈值，减少误识别
            
            for (int i = 0; i < response.faceMatches().size(); i++) {
                float similarity = response.faceMatches().get(i).similarity();
                String faceId = response.faceMatches().get(i).face().faceId();
                
                // 记录所有匹配项，便于分析问题
                log.info("人脸匹配项 #{}: 匹配度: {}%, 人脸ID: {}", i+1, similarity, faceId);
                
                // 更新最高匹配度
                if (similarity > highestSimilarity) {
                    highestSimilarity = similarity;
                    matchedFaceId = faceId;
                }
                
                // 如果任何匹配超过严格阈值，则视为重复
                if (similarity >= strictMatchThreshold) {
                    log.error("检测到重复人脸，匹配度: {}%, 人脸ID: {}", similarity, faceId);
                    result.put("isDuplicate", true);
                    result.put("similarity", similarity);
                    result.put("faceId", faceId);
                    return result;
                }
            }
            
            // 检查最高匹配度是否超过配置的阈值
            boolean hasDuplicate = highestSimilarity >= faceMatchThreshold;
            
            if (hasDuplicate) {
                log.error("检测到重复人脸，最高匹配度: {}%, 人脸ID: {}", highestSimilarity, matchedFaceId);
                result.put("isDuplicate", true);
                result.put("similarity", highestSimilarity);
                result.put("faceId", matchedFaceId);
            }
            
            return result;
        });
    }

    public List<String> getFaces(SdkBytes imageBytes, AwsCreateSessionResponse credentials, RekognitionClient rekognitionClient) {
        validateImageAndCredentials(imageBytes, credentials);
        List<String> faces = new ArrayList<>();

        return executeAwsOperation("获取重复人脸", () -> {
            // 使用传入的客户端或创建新客户端
            RekognitionClient client = rekognitionClient != null ?
                    rekognitionClient : getRekognitionClient(credentials);
            Image image = Image.builder().bytes(imageBytes).build();
            SearchFacesByImageRequest request = SearchFacesByImageRequest.builder()
                    .collectionId(collectionId)
                    .image(image)
                    .faceMatchThreshold((float) livenessThreshold)
                    .maxFaces(1) // 只需要检查是否有匹配的，不需要所有结果
                    .build();

            SearchFacesByImageResponse response = client.searchFacesByImage(request);
            if(!response.faceMatches().isEmpty()){
                List<FaceMatch> faceImageMatches = response.faceMatches();
                for (FaceMatch item: faceImageMatches) {
                    faces.add(item.face().faceId());
                }
            }
            return faces;
        });
    }

    /**
     * 检测图像中的人脸并验证清晰度，暂时用不到
     *
     * @param imageBytes          图像字节
     * @param credentialsResponse 会话凭证
     * @throws BaseException 当人脸检测失败或图像不清晰时
     */
    public void detectFaces(SdkBytes imageBytes, AwsCreateSessionResponse credentialsResponse) {
        validateImageAndCredentials(imageBytes, credentialsResponse);

        RekognitionClient rekognitionClient = null;
        try {
            rekognitionClient = getRekognitionClient(credentialsResponse);
            DetectFacesRequest request = DetectFacesRequest.builder()
                    .image(Image.builder().bytes(imageBytes).build())
                    .attributes(Attribute.ALL)
                    .build();

            DetectFacesResponse detectResult = rekognitionClient.detectFaces(request);

            if (detectResult == null || detectResult.faceDetails().isEmpty()) {
                throw new BaseException("未检测到人脸");
            }

            // 检查人脸质量
            FaceDetail faceDetail = detectResult.faceDetails().get(0);
            Float sharpness = Optional.ofNullable(faceDetail.quality())
                    .map(ImageQuality::sharpness)
                    .orElse(0.0f);

            log.info("人脸清晰度: {}, 阈值: {}", sharpness, sharpnessThreshold);
            if (sharpness < sharpnessThreshold) {
                throw new BaseException("图片模糊，请重新拍摄");
            }

            // 检查人脸姿态
            int poseThreshold = 20;
            if (faceDetail.pose() != null) {
                Float pitch = faceDetail.pose().pitch();
                Float roll = faceDetail.pose().roll();
                Float yaw = faceDetail.pose().yaw();

                if (Math.abs(pitch) > poseThreshold || Math.abs(roll) > poseThreshold || Math.abs(yaw) > poseThreshold) {
                    log.warn("人脸姿态不理想: pitch={}, roll={}, yaw={}", pitch, roll, yaw);
                }
            }

            // 检查人脸是否被遮挡
            int marks = 5;
            if (faceDetail.landmarks() != null && faceDetail.landmarks().size() < marks) {
                log.warn("人脸特征点较少，可能被遮挡: {}", faceDetail.landmarks().size());
            }
        } catch (BaseException e) {
            throw e;
        } catch (SdkServiceException e) {
            log.error("人脸检测时发生服务异常: {}, 状态码: {}", e.getMessage(), e.statusCode(), e);
            throw new BaseException("人脸检测失败(服务错误)");
        } catch (SdkClientException e) {
            log.error("人脸检测时发生客户端异常: {}", e.getMessage(), e);
            throw new BaseException("人脸检测失败(客户端错误)");
        } catch (Exception e) {
            log.error("人脸检测失败: {}", e.getMessage(), e);
            throw new BaseException("人脸检测失败");
        } finally {
            // 确保客户端被关闭
            if (rekognitionClient != null) {
                try {
                    rekognitionClient.close();
                    log.debug("已关闭RekognitionClient");
                } catch (Exception e) {
                    log.warn("关闭RekognitionClient时发生异常: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 验证图像和凭证参数
     *
     * @param imageBytes          图像字节
     * @param credentialsResponse 会话凭证
     * @throws BaseException 当参数无效时
     */
    private void validateImageAndCredentials(SdkBytes imageBytes, AwsCreateSessionResponse credentialsResponse) {
        if (imageBytes == null || imageBytes.asByteArray().length == 0) {
            throw new BaseException("图像数据不能为空");
        }

        if (credentialsResponse == null) {
            throw new BaseException("认证响应不能为空");
        }

        validateSessionCredentials(credentialsResponse);
    }

    /**
     * 创建人脸集合,只用一次初始化
     *
     * @param rekognitionClient Rekognition客户端
     * @throws BaseException 当创建失败时
     */
    public void createCollection(RekognitionClient rekognitionClient) {
        if (rekognitionClient == null) {
            throw new BaseException("Rekognition客户端不能为空");
        }

        try {
            CreateCollectionRequest request = CreateCollectionRequest.builder()
                    .collectionId(collectionId)
                    .build();

            CreateCollectionResponse response = rekognitionClient.createCollection(request);
            log.info("人脸集合创建成功, CollectionArn: {}, 状态码: {}",
                    response.collectionArn(), response.statusCode());
        } catch (ResourceAlreadyExistsException e) {
            log.info("人脸集合 {} 已存在", collectionId);
        } catch (SdkServiceException e) {
            log.error("创建人脸集合时发生服务异常: {}, 状态码: {}", e.getMessage(), e.statusCode(), e);
            throw new BaseException("创建人脸集合失败(服务错误)");
        } catch (SdkClientException e) {
            log.error("创建人脸集合时发生客户端异常: {}", e.getMessage(), e);
            throw new BaseException("创建人脸集合失败(客户端错误)");
        } catch (Exception e) {
            log.error("创建人脸集合失败: {}", e.getMessage(), e);
            throw new BaseException("创建人脸集合失败");
        }
    }

    /**
     * 确保人脸集合存在
     */
    private void ensureCollectionExists() {
        try {
            Region defaultRegion = Region.US_EAST_1;
            AwsBasicCredentials basicCredentials = AwsBasicCredentials.create(accessKey, secretKey);

            try (RekognitionClient rekognitionClient = RekognitionClient.builder()
                    .region(defaultRegion)
                    .credentialsProvider(StaticCredentialsProvider.create(basicCredentials))
                    .httpClient(sharedHttpClient) // 使用共享HTTP客户端以防止线程泄漏
                    .overrideConfiguration(b -> b.retryPolicy(retryPolicyRef.get()).scheduledExecutorService(scheduler))
                    .build()) {

                // 检查集合是否已存在
                boolean collectionExists = false;
                try {
                    DescribeCollectionRequest describeRequest = DescribeCollectionRequest.builder()
                            .collectionId(collectionId)
                            .build();
                    rekognitionClient.describeCollection(describeRequest);
                    collectionExists = true;
                } catch (ResourceNotFoundException e) {
                    log.info("人脸集合 {} 不存在，将创建新集合", collectionId);
                }

                // 如果不存在，创建集合
                if (!collectionExists) {
                    createCollection(rekognitionClient);
                }
            }
        } catch (Exception e) {
            log.error("确保人脸集合存在时出错: {}", e.getMessage(), e);
        }
    }

    /**
     * 通用异常处理方法
     */
    private <T> T executeAwsOperation(String operationName, Supplier<T> operation) {
        try {
            return operation.get();
        } catch (ApiException e) {
            throw e;
        } catch (SdkServiceException e) {
            log.error("{}时发生AWS服务异常: {}, 状态码: {}", operationName, e.getMessage(), e.statusCode(), e);
            throw new ApiException(MediaUserExceptionCodeApi.AWS_SERVICE_ERROR);
        } catch (SdkClientException e) {
            log.error("{}时发生AWS客户端异常: {}", operationName, e.getMessage(), e);
            throw new ApiException(MediaUserExceptionCodeApi.AWS_CLIENT_ERROR);
        } catch (Exception e) {
            log.error("{}失败: {}", operationName, e.getMessage(), e);
            throw new ApiException(MediaUserExceptionCodeApi.AWS_OPERATION_ERROR);
        }
    }


    /**
     * 从集合中删除人脸
     */
    public void delFace(RekognitionClient rekognitionClient, AwsCreateSessionResponse credentialsResponse, List<String> faces){

        try {
            RekognitionClient client = rekognitionClient == null ? getRekognitionClient(credentialsResponse) : rekognitionClient;
            DeleteFacesRequest request = DeleteFacesRequest.builder()
                    .collectionId(collectionId)
                    .faceIds(faces)
                    .build();
            DeleteFacesResponse deleteFacesResult = client.deleteFaces(request);
            List < String > faceRecords = deleteFacesResult.deletedFaces();
            System.out.println(faceRecords.size() + " face(s) deleted:");
            for (String face: faceRecords) {
                System.out.println("FaceID: " + face);
            }
        }catch (Exception e){
            throw e;
        }
    }

    /**
     * 删除人脸集合
     *
     * @param rekognitionClient Rekognition客户端
     * @throws BaseException 当创建失败时
     */
    public void delCollection(RekognitionClient rekognitionClient) {
        if (rekognitionClient == null) {
            throw new BaseException("Rekognition客户端不能为空");
        }

        DeleteCollectionRequest request = DeleteCollectionRequest.builder()
                .collectionId(collectionId)
                .build();

        rekognitionClient.deleteCollection(request);
    }

    public String getCollectionId(){
        return collectionId;
    }
}
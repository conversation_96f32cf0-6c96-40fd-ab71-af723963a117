# 联盟信息集成功能说明

## 概述

为 `inviteShare` 接口添加了联盟信息查询的下游调用，根据用户的联盟状态返回不同的邀请分享文案。

## 功能实现

### 1. 新增DTO类

#### UnionInfoRequest (联盟信息查询请求)
```java
@Data
@Accessors(chain = true)
public class UnionInfoRequest implements Serializable {
    private Long unionId;      // 联盟ID（可选）
    private Long uid;          // 用户id（可选）注意可以不是创建人
    private String inviteCode; // 邀请码（可选）
    private Byte status;       // 联盟状态过滤（可选）
}
```

#### UnionInfoResponse (联盟信息响应)
```java
@Data
@Accessors(chain = true)
public class UnionInfoResponse implements Serializable {
    private Long id;           // 联盟ID，唯一主键
    private String name;       // 联盟名称
    private String slogan;     // 联盟口号
    private String inviteCode; // 联盟邀请码
    private Long creatorId;    // 创建人用户ID
    private Integer userNum;   // 联盟用户数
    private Byte status;       // 任务状态，-2：自动解散，-1：审核不通过，0：审核中，1：人数不足，2：组建成功
    private String auditFeedback;     // 审核反馈结果
    private Date createTime;          // 记录创建时间
    private Date updateTime;          // 记录更新时间
    private UnionUserData createUser; // 创建用户
    private List<UnionUserData> elderUser; // 初始用户，不包括创建者5人中其他人
}
```

### 2. 新增Feign客户端

#### UnionInfoClient
```java
@FeignClient(url = "${union.service.url:http://envtest-api.x.me}", 
             value = "unionInfoClient", 
             configuration = UnionInfoClient.UnionInfoClientConfiguration.class)
public interface UnionInfoClient {
    
    @PostMapping("/union/info")
    ApiResponse<UnionInfoResponse> getUnionInfo(@RequestBody @Valid UnionInfoRequest request);
}
```

### 3. 修改 inviteShare 接口

在 `ShareContentService.getInviteShareContent()` 方法中添加了联盟信息查询逻辑：

#### 核心流程：
1. **查询用户基本信息** - 验证用户存在性
2. **调用联盟信息下游接口** - 通过用户ID查询联盟状态
3. **根据联盟状态返回不同文案**：
   - **联盟组建成功** (status = 2)：返回特殊庆祝文案
   - **其他状态**：返回默认邀请文案

#### 代码实现：
```java
// 调用下游联盟信息查询接口
UnionInfoResponse unionInfo = null;
try {
    UnionInfoRequest unionRequest = new UnionInfoRequest();
    unionRequest.setUid(uid);
    
    ApiResponse<UnionInfoResponse> unionResponse = unionInfoClient.getUnionInfo(unionRequest);
    if (unionResponse != null && unionResponse.getSuccess() && unionResponse.getResult() != null) {
        unionInfo = unionResponse.getResult();
        log.info("User {} union info: status={}, unionId={}", uid, unionInfo.getStatus(), unionInfo.getId());
    }
} catch (Exception e) {
    log.warn("Failed to query union info for user {}: {}", uid, e.getMessage());
    // 查询失败不影响主流程，继续使用默认文案
}

// 根据联盟状态设置不同文案
if (unionInfo != null && unionInfo.getStatus() != null && unionInfo.getStatus() == 2) {
    // 联盟组建成功，返回特殊文案
    setUnionSuccessContent(response, languageEnums, unionInfo);
} else {
    // 默认文案（未加入联盟或联盟未成功）
    setDefaultInviteContent(response, languageEnums);
}
```

## 文案配置

### 联盟组建成功时的特殊文案

#### 中文文案：
- **标题**: "恭喜！您的联盟\"{联盟名称}\"已组建成功！"
- **描述**: "联盟成员享受挖矿加速福利，邀请更多好友加入联盟，共同获得更多奖励！"
- **高亮标题**: "挖矿加速"
- **高亮描述**: "更多奖励"

#### 英文文案：
- **标题**: "Congratulations! Your union \"{联盟名称}\" has been successfully formed!"
- **描述**: "Union members enjoy mining acceleration benefits. Invite more friends to join and earn more rewards together!"
- **高亮标题**: "Mining Acceleration"
- **高亮描述**: "More Rewards"

### 默认邀请文案（保持原有逻辑不变）

#### 中文文案：
- **标题**: "邀请用户加入联盟，成团后即奖励联盟内"
- **描述**: "新用户下载X.me应用程序，填写以下邀请码并完成注册。注册成功后新用户还可以获得12XME奖励。"
- **高亮文本**: "12XME"

#### 英文文案：
- **标题**: "Invite users to join. Get a 3% mining speed boost once the team is formed."
- **描述**: "New users need to download X.me App and fill in the following invitation code to complete the registration. New users will also get 6 XME."
- **高亮文本**: "3%"

## 联盟状态说明

| 状态值 | 状态描述 | 返回文案类型 |
|--------|----------|--------------|
| -2 | 自动解散 | 默认文案 |
| -1 | 审核不通过 | 默认文案 |
| 0 | 审核中 | 默认文案 |
| 1 | 人数不足 | 默认文案 |
| **2** | **组建成功** | **特殊庆祝文案** |

## 异常处理

1. **下游接口调用失败**: 使用 try-catch 包裹，失败时记录警告日志，但不影响主流程
2. **联盟信息为空**: 当查询结果为null时，使用默认文案
3. **状态判断**: 只有当联盟状态明确为 `2`（组建成功）时才使用特殊文案

## 配置项

在 `application.yml` 中需要配置联盟服务地址：
```yaml
union:
  service:
    url: http://envtest-api.x.me  # 联盟服务地址
```

## 接口响应示例

### 联盟组建成功时的响应：
```json
{
    "invitationCode": "ABC123",
    "title": "恭喜！您的联盟\"超级挖矿联盟\"已组建成功！",
    "description": "联盟成员享受挖矿加速福利，邀请更多好友加入联盟，共同获得更多奖励！",
    "highlightTitleText": "挖矿加速",
    "highlightDescriptionText": "更多奖励",
    "shareUrl": "https://short.x.me/abc123"
}
```

### 默认状态的响应：
```json
{
    "invitationCode": "ABC123",
    "title": "邀请用户加入联盟，成团后即奖励联盟内",
    "description": "新用户下载X.me应用程序，填写以下邀请码并完成注册。注册成功后新用户还可以获得12XME奖励。",
    "highlightTitleText": "12XME",
    "shareUrl": "https://short.x.me/abc123"
}
```

## 日志记录

- **成功查询**: `log.info("User {} union info: status={}, unionId={}", uid, unionInfo.getStatus(), unionInfo.getId())`
- **查询失败**: `log.warn("Failed to query union info for user {}: {}", uid, e.getMessage())`

## 注意事项

1. 联盟信息查询失败不会影响 `inviteShare` 接口的正常响应
2. 只有联盟状态为 `2`（组建成功）时才显示特殊文案
3. 特殊文案中动态包含联盟名称，增强个性化体验
4. 保持向后兼容，原有的默认文案逻辑完全保留 
package com.media.user.service.strategy.impl;

import com.google.common.collect.Lists;
import com.media.user.dto.im.RongCloudMessageData;
import com.media.user.dto.rongCloud.RongCloudCallbackResponse;
import com.media.user.enums.ImExceptionEnums;
import com.media.user.enums.LanguageEnums;
import com.media.user.service.filter.MessageFilter;
import com.media.user.service.filter.annotation.ChannelType;
import com.media.user.service.strategy.MessageHandlerStrategy;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import static com.media.user.enums.ImExceptionEnums.SUCCESS;

@Slf4j
public abstract class AbstractMessageHandlerStrategy implements MessageHandlerStrategy {

    private final List<MessageFilter> filters = Lists.newArrayList();

    @Autowired
    private List<MessageFilter> allFilters;

    @PostConstruct
    public void initFilters() {
        String channelType = getChannelType();
        log.info("初始化频道策略: {}", channelType);
        List<MessageFilter> applicableFilters = allFilters.stream().filter(filter -> isFilterApplicable(filter, channelType)).sorted(Comparator.comparing(filter -> {
            Order order = filter.getClass().getAnnotation(Order.class);
            return order != null ? order.value() : Integer.MAX_VALUE;
        })).toList();
        filters.addAll(applicableFilters);
        log.info("频道 {} 策略初始化完成，共添加 {} 个 Filter:", channelType, applicableFilters.size());
    }

    private boolean isFilterApplicable(MessageFilter filter, String channelType) {
        ChannelType channelTypeAnnotation = filter.getClass().getAnnotation(ChannelType.class);
        if (channelTypeAnnotation == null) {
            log.warn("Filter {} 没有 @ChannelType 注解，默认适用于所有频道", filter.getClass().getSimpleName());
            return true;
        }
        if (!channelTypeAnnotation.enabled()) {
            log.info("Filter {} 已禁用", filter.getClass().getSimpleName());
            return false;
        }
        String[] supportedChannels = channelTypeAnnotation.value();
        for (String supportedChannel : supportedChannels) {
            if (supportedChannel.equals(channelType)) {
                return true;
            }
        }
        return false;
    }

    public RongCloudCallbackResponse handleMessage(RongCloudMessageData messageData) {
        log.info("处理 {} 频道消息，messageData: {}", getChannelType(), messageData);
        return buildFilterChain().apply(messageData);
    }

    private Function<RongCloudMessageData, RongCloudCallbackResponse> buildFilterChain() {
        Function<RongCloudMessageData, RongCloudCallbackResponse> chain = this::createSuccessResponse;
        for (int i = filters.size() - 1; i >= 0; i--) {
            MessageFilter filter = filters.get(i);
            Function<RongCloudMessageData, RongCloudCallbackResponse> next = chain;
            chain = messageData -> {
                ImExceptionEnums result = filter.doFilter(messageData);
                if (!Objects.equals(SUCCESS, result)) {
                    log.warn("Filter {} 拦截消息，结果: {}", filter.getClass().getSimpleName(), result);
                    String language = LanguageEnums.en_US.getValue();
                    if (Objects.nonNull(messageData.getUserInfo())) {
                        language = messageData.getUserInfo().getLanguage();
                    }
                    return RongCloudCallbackResponse.error(result.getCode(), language);
                }
                return next.apply(messageData);
            };
        }
        return chain;
    }

    private RongCloudCallbackResponse createSuccessResponse(RongCloudMessageData messageData) {
        RongCloudCallbackResponse response = RongCloudCallbackResponse.success();
        response.setReplaceContent(replaceContent(messageData));
        return response;
    }
}

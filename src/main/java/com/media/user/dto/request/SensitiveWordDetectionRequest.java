package com.media.user.dto.request;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 敏感词检测请求参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SensitiveWordDetectionRequest {
    
    /**
     * 待检测文本
     */
    private String text;
    
    /**
     * 是否检查敏感词
     */
    private Boolean checkSensitive;
    
    /**
     * 是否检测语言
     */
    private Boolean detectLanguage;
    
    /**
     * 语言候选数量
     */
    private Integer languageCandidates;
    
    /**
     * 是否使用多语言
     */
    private Boolean useMultilingual;
} 
package com.media.core.i18n;

import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.parser.AcceptLanguage;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.StringReader;
import java.util.List;
import java.util.Locale;

/**
 * 国际化资源加载类
 */
@Slf4j
public class LocalMessageResource {

    private static ReloadableResourceBundleMessageSource MESSAGE_SOURCE = new ReloadableResourceBundleMessageSource();


    public static String getMessage(String code, Locale locale) {
        if (locale == null) {
            locale = Locale.US;
        }
        if (code == null) {
            return null;
        }
        if ("Hant".equalsIgnoreCase(locale.getScript())) {
            locale = Locale.TAIWAN;
        }
        if ("Hans".equalsIgnoreCase(locale.getScript())) {
            locale = Locale.TAIWAN;
        }
        if ("en".equalsIgnoreCase(locale.getLanguage())) {
            locale = Locale.US;
        }

        return MESSAGE_SOURCE.getMessage(code, null, locale);
    }

    public static String getMessage(String code) {
        return getMessage(code, getLocale());
    }

    public static Locale getLocale() {
        Locale locale = LocaleContextHolder.getLocale();
        if ("Hant".equalsIgnoreCase(locale.getScript())) {
            locale = Locale.TAIWAN;
        }
        if ("Hans".equalsIgnoreCase(locale.getScript())) {
            locale = Locale.CHINA;
        }
        if ("en".equalsIgnoreCase(locale.getLanguage())) {
            locale = Locale.US;
        }
        return locale;
    }

    public static Locale parseLocale(String language) {
        try (StringReader reader = new StringReader(language)) {
            try {
                List<AcceptLanguage> languages = AcceptLanguage.parse(reader);
                if (!languages.isEmpty()) {
                    return languages.get(0).getLocale();
                }
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            }
        }
        return Locale.US;
    }

    public static String getLocaleString() {
        Locale locale = getLocale();
        StringBuilder sb = new StringBuilder(locale.getLanguage());
        if (StringUtils.hasLength(locale.getCountry())) {
            sb.append("-").append(locale.getCountry());
        }
        if (StringUtils.hasLength(locale.getScript())) {
            sb.append("-").append(locale.getScript());
        }
        return sb.toString();
    }

    public static void setMessageSource(ReloadableResourceBundleMessageSource messageSource) {
        MESSAGE_SOURCE = messageSource;
    }
}

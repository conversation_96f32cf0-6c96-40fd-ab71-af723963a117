package com.media.user.controller.api;

import com.media.user.dto.request.ChannelAppReleaseRequest;
import com.media.user.dto.response.AppReleaseResponse;
import com.media.core.request.ClientInfoContext;
import com.media.user.service.AppReleaseService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/user/api/appRelease")
public class AppReleaseApiController {

    @Autowired
    private AppReleaseService appReleaseService;

    /**
     * 获取app最新版本
     */
    @PostMapping("/latest")
//    @RequireAuth
    public AppReleaseResponse getAppReleaseLatest(){
        return appReleaseService.getAppReleaseLatest(ClientInfoContext.getLanguage());
    }

    /**
     * 下载列表
     */
    @PostMapping("/list")
    public List<AppReleaseResponse> listAppReleaseLatest(){
        return appReleaseService.listAppReleaseLatest(ClientInfoContext.getLanguage());
    }

    /**
     * 根据渠道获取应用版本列表
     */
    @PostMapping("/list/channel")
    public List<AppReleaseResponse> listAppReleaseByChannel(@RequestBody(required = false) ChannelAppReleaseRequest request){
        String channelCode = null;
        String inviteCode = null;
        
        if (request != null) {
            if (StringUtils.isNotBlank(request.getChannelCode())) {
                channelCode = request.getChannelCode();
            }
            if (StringUtils.isNotBlank(request.getInviteCode())) {
                inviteCode = request.getInviteCode();
            }
        }
        
        // 如果有邀请码，优先使用邀请码获取应用版本列表
        if (StringUtils.isNotBlank(inviteCode)) {
            return appReleaseService.listAppReleaseByInviteCode(ClientInfoContext.getLanguage(), inviteCode);
        }
        
        // 否则使用渠道代码获取应用版本列表
        return appReleaseService.listAppReleaseByChannel(ClientInfoContext.getLanguage(), channelCode);
    }

}

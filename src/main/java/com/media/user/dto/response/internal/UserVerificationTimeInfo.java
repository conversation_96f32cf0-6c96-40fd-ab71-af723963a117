package com.media.user.dto.response.internal;

import com.alibaba.fastjson.annotation.JSONField;
import com.media.user.domain.ClientUserModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class UserVerificationTimeInfo {
    private long userid;

    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @com.alibaba.fastjson2.annotation.JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date faceTime;

    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @com.alibaba.fastjson2.annotation.JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date phoneTime;

    @com.fasterxml.jackson.annotation.JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @com.alibaba.fastjson2.annotation.JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private  Date emailTime;


    public static UserVerificationTimeInfo convertToUserVerificationTimeInfo(ClientUserModel clientUserModel) {
        if (clientUserModel == null) {
            return null;
        }
        UserVerificationTimeInfo verificationTimeInfo = new UserVerificationTimeInfo();
        verificationTimeInfo.setUserid(clientUserModel.getUid())
                .setFaceTime(clientUserModel.getFaceLivenessAt())
                .setPhoneTime(clientUserModel.getPhoneVerifyTime())
                .setEmailTime(clientUserModel.getEmailVerifyTime());

        return verificationTimeInfo;
    }

}

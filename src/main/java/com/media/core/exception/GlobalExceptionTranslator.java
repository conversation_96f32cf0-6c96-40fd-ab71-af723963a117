package com.media.core.exception;


import com.media.core.constant.VoidResponse;
import com.media.core.i18n.LocalMessageResource;
import com.media.core.log.RequestLog;
import com.media.core.log.RequestLogTrace;
import com.xme.xme_base_depends.models.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingRequestCookieException;
import org.springframework.web.bind.MissingRequestHeaderException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;
import java.text.MessageFormat;
import java.util.List;
import java.util.Locale;

@RestControllerAdvice
@Slf4j
public class GlobalExceptionTranslator {

    @ExceptionHandler({ApiWithParametersException.class})
    public Object handleException(ApiWithParametersException e, HttpServletRequest r) {
        String i18nMessage = getI18nMessage(e.getCode(), r.getLocale());
        log.error("Api with parameters exception,code={},message={}", e.getCode(), i18nMessage);
        ApiResponse<Object> response = new ApiResponse<>(e.getCode(), i18nMessage, e.getParams());
        if (i18nMessage.startsWith("result.code") && e.getMessage() != null) {
            i18nMessage = e.getMessage();
        }
        error();
        if (ExceptionResponse.isInternal()) {
            ExceptionCode exceptionCode = new ExceptionCode(e.getStatus(), i18nMessage, e.getCode());
            exceptionCode.setParam(e.getParams());
            return ExceptionResponse.create(exceptionCode, e);
        }
        return ResponseEntity.ok(response);
    }

    @ExceptionHandler({ApiException.class})
    public Object handleException(ApiException e, HttpServletRequest r) {
        String i18nMessage = getI18nMessage(e.getCode(), r.getLocale());
        log.error("Api exception,code={},message={}", e.getCode(), i18nMessage);
        ApiResponse<Object> response = new ApiResponse<>(e.getCode(), i18nMessage);
        if (i18nMessage.startsWith("result.code") && e.getMessage() != null) {
            i18nMessage = e.getMessage();
        }
        error();
        if (ExceptionResponse.isInternal()) {
            return ExceptionResponse.create(new ExceptionCode(e.getStatus(), i18nMessage, e.getCode()), e);
        }
        return ResponseEntity.ok(response);
    }

    @ExceptionHandler({CustomizeApiException.class})
    public Object handleException(CustomizeApiException e, HttpServletRequest r) {
        String i18nMessage = e.getMessage();
        if(StringUtils.isEmpty(i18nMessage)){
            i18nMessage = getI18nMessage(e.getCode(), r.getLocale());
            log.error("Customize exception,code={},message={}", e.getCode(), i18nMessage);
        }
        ApiResponse<Object> response = new ApiResponse<>(e.getCode(), i18nMessage);
        if (i18nMessage.startsWith("result.code") && e.getMessage() != null) {
            i18nMessage = e.getMessage();
        }
        error();
        if (ExceptionResponse.isInternal()) {
            return ExceptionResponse.create(new ExceptionCode(e.getStatus(), i18nMessage, e.getCode()), e);
        }
        return ResponseEntity.ok(response);
    }

    @ExceptionHandler({ApiMessageException.class})
    public Object handleException(ApiMessageException e, HttpServletRequest r) {
        String i18nMessage = getI18nMessage(e.getCode(), r.getLocale());
        if (e.getArgs() != null && e.getArgs().length > 0) {
            try {
                i18nMessage = MessageFormat.format(i18nMessage, e.getArgs());
            } catch (Exception ex) {
                log.error("Error Message Format Error, message={}", i18nMessage, ex);
            }
        }
        log.error("Api exception,code={},message={}", e.getCode(), i18nMessage);
        ApiResponse<Object> response = new ApiResponse<>(e.getCode(), i18nMessage);
        if (i18nMessage.startsWith("result.code") && e.getMessage() != null) {
            i18nMessage = e.getMessage();
        }
        error();
        if (ExceptionResponse.isInternal()) {
            ExceptionCode exceptionCode = new ExceptionCode(e.getStatus(), i18nMessage, e.getCode());
            exceptionCode.setArgs(e.getArgs());
            return ExceptionResponse.create(exceptionCode, e);
        }
        return ResponseEntity.ok(response);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ResponseEntity<ApiResponse<VoidResponse>> validationBodyException(MethodArgumentNotValidException exception, HttpServletRequest r) {
        BindingResult result = exception.getBindingResult();
        StringBuilder sb = new StringBuilder();
        if (result.hasErrors()) {
            List<ObjectError> errors = result.getAllErrors();
            if (!CollectionUtils.isEmpty(errors)) {
                for (ObjectError p : errors) {
                    FieldError fieldError = (FieldError) p;
                    log.error("Data check failure : object{" + fieldError.getObjectName() + "},field{" + fieldError.getField() +
                            "},errorMessage{" + fieldError.getDefaultMessage() + "}");
                    String defaultMessage = fieldError.getDefaultMessage();
                    // 使用占位符的,直接展示固定消息
                    if (defaultMessage != null && defaultMessage.startsWith("{") && defaultMessage.endsWith("}")) {
                        defaultMessage = defaultMessage.replace("{", "")
                                .replace("}", "");
                        String message = LocalMessageResource.getMessage(defaultMessage, r.getLocale());
                        sb = new StringBuilder(message);
                        break;
                    } else {
                        sb.append(fieldError.getField())
                                .append(" ")
                                .append(fieldError.getDefaultMessage())
                                .append(" ");
                    }
                }
            }
        }
        error();
        ApiResponse<VoidResponse> response = new ApiResponse<>(HttpStatus.BAD_REQUEST.value(), sb.toString());
        if (ExceptionResponse.isInternal()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(response);
        }
        return ResponseEntity.ok(response);
    }

    public String getI18nMessage(Integer code, Locale locale) {
        // 如果locale只有语言代码，没有国家代码，需要映射到完整的locale
        if (locale != null && StringUtils.isEmpty(locale.getCountry())) {
            String language = locale.getLanguage();
            // 使用LanguageEnums的映射逻辑
            switch (language.toLowerCase()) {
                case "de":
                    locale = Locale.forLanguageTag("de-DE");
                    break;
                case "fr":
                    locale = Locale.forLanguageTag("fr-FR");
                    break;
                case "es":
                    locale = Locale.forLanguageTag("es-ES");
                    break;
                case "ja":
                    locale = Locale.forLanguageTag("ja-JP");
                    break;
                case "ko":
                    locale = Locale.forLanguageTag("ko-KR");
                    break;
                case "zh":
                    locale = Locale.forLanguageTag("zh-CN");
                    break;
                case "ar":
                    locale = Locale.forLanguageTag("ar-SA");
                    break;
                case "hi":
                    locale = Locale.forLanguageTag("hi-IN");
                    break;
                case "th":
                    locale = Locale.forLanguageTag("th-TH");
                    break;
                case "vi":
                    locale = Locale.forLanguageTag("vi-VN");
                    break;
                case "tr":
                    locale = Locale.forLanguageTag("tr-TR");
                    break;
                case "ru":
                    locale = Locale.forLanguageTag("ru-RU");
                    break;
                case "pt":
                    locale = Locale.forLanguageTag("pt-PT");
                    break;
                case "id":
                    locale = Locale.forLanguageTag("id-ID");
                    break;
                case "bn":
                    locale = Locale.forLanguageTag("bn-BD");
                    break;
                case "fa":
                    locale = Locale.forLanguageTag("fa-IR");
                    break;
                case "ur":
                    locale = Locale.forLanguageTag("ur-PK");
                    break;
                case "en":
                default:
                    locale = Locale.forLanguageTag("en-US");
                    break;
            }
        }
        
        return LocalMessageResource.getMessage("result.code." + code, locale);
    }

    public void error() {
        RequestLog requestLog = RequestLogTrace.getRequestLog();
        if (requestLog != null) {
            requestLog.setError(true);
        }
    }

    @ExceptionHandler(ServiceException.class)
    public ExceptionResponse handleError(ServiceException e) {
        if (e.isPrintLog()) {
            log.error("Service Exception", e);
        }
        error();
        return ExceptionResponse.create(new ExceptionCode(e.getStatus(), e.getMessage(), e.getCode()), e);
    }

    @ExceptionHandler({
            MissingServletRequestPartException.class,
    })
    public ExceptionResponse handleError(MissingServletRequestPartException e) {
        log.error(e.getMessage(), e);
        error();
        return ExceptionResponse.create(new ExceptionCode(HttpStatus.BAD_REQUEST.value(), e.getMessage()), e);
    }

    @ExceptionHandler({
            MissingRequestHeaderException.class,
    })
    public ExceptionResponse handleError(MissingRequestHeaderException e) {
        log.error(e.getMessage(), e);
        error();
        return ExceptionResponse.create(new ExceptionCode(HttpStatus.BAD_REQUEST.value(), e.getMessage()), e);
    }

    @ExceptionHandler({
            MissingRequestCookieException.class,
    })
    public ExceptionResponse handleError(MissingRequestCookieException e) {
        log.error(e.getMessage(), e);
        error();
        return ExceptionResponse.create(new ExceptionCode(HttpStatus.BAD_REQUEST.value(), e.getMessage()), e);
    }

    @ExceptionHandler({
            MultipartException.class
    })
    public ExceptionResponse handleError(MultipartException e) {
        log.error(e.getMessage(), e);
        error();
        return ExceptionResponse.create(new ExceptionCode(HttpStatus.BAD_REQUEST.value(), e.getMessage()), e);
    }


    @ExceptionHandler(ForbiddenException.class)
    public ExceptionResponse handleError(ForbiddenException e) {
        if (e.isPrintLog()) {
            log.error("Permission Denied", e);
        }
        error();
        return ExceptionResponse.create(new ExceptionCode(e.getStatus(), e.getMessage(), e.getCode()), e);
    }


    @ExceptionHandler(NotFoundException.class)
    public ExceptionResponse handleError(NotFoundException e) {
        if (e.isPrintLog()) {
            log.error("Not Found", e);
        }
        error();
        return ExceptionResponse.create(new ExceptionCode(e.getStatus(), e.getMessage(), e.getCode()), e);
    }

    @ExceptionHandler(BadRequestException.class)
    public ExceptionResponse handleError(BadRequestException e) {
        if (e.isPrintLog()) {
            log.error("Bad Request", e);
        }
        error();
        return ExceptionResponse.create(new ExceptionCode(e.getStatus(), e.getMessage(), e.getCode()), e);
    }

    @ExceptionHandler(IllegalArgumentException.class)
    public ExceptionResponse handleError(IllegalArgumentException e) {
        log.error("IllegalArgumentException:", e);
        error();
        return ExceptionResponse.create(new ExceptionCode(HttpStatus.BAD_REQUEST.value()
                , e.getMessage(), HttpStatus.BAD_REQUEST.value()), e);
    }


    @ExceptionHandler(TooManyRequestsException.class)
    public ExceptionResponse handleError(TooManyRequestsException e) {
        if (e.isPrintLog()) {
            log.error("Too many requests", e);
        }
        error();
        return ExceptionResponse.create(new ExceptionCode(e.getStatus(), e.getMessage(), e.getCode()), e);
    }


    @ExceptionHandler(UnAuthorizedException.class)
    public ExceptionResponse handleError(UnAuthorizedException e) {
        if (e.isPrintLog()) {
            log.error("UnAuthorized", e);
        }
        error();
        return ExceptionResponse.create(new ExceptionCode(e.getStatus(), e.getMessage(), e.getCode()), e);
    }


    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ExceptionResponse handleError(MissingServletRequestParameterException e) {
        log.warn("Missing Request Parameter", e);
        String message = String.format("Missing Request Parameter: %s", e.getParameterName());
        error();
        return ExceptionResponse.create(new ExceptionCode(HttpStatus.BAD_REQUEST.value(), message), e);
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ExceptionResponse handleError(MethodArgumentTypeMismatchException e) {
        log.warn("Method Argument Type Mismatch", e);
        String message = String.format("Method Argument Type Mismatch: %s", e.getName());
        error();
        return ExceptionResponse.create(new ExceptionCode(HttpStatus.BAD_REQUEST.value(), message), e);
    }


    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<VoidResponse>> handleError(BindException e, HttpServletRequest r) {
        BindingResult result = e.getBindingResult();
        StringBuilder sb = new StringBuilder();
        if (result.hasErrors()) {
            List<ObjectError> errors = result.getAllErrors();
            if (!CollectionUtils.isEmpty(errors)) {
                for (ObjectError p : errors) {
                    FieldError fieldError = (FieldError) p;
                    log.error("Data check failure : object{" + fieldError.getObjectName() + "},field{" + fieldError.getField() +
                            "},errorMessage{" + fieldError.getDefaultMessage() + "}");
                    String defaultMessage = fieldError.getDefaultMessage();
                    // 使用占位符的,直接展示固定消息
                    if (defaultMessage != null && defaultMessage.startsWith("{") && defaultMessage.endsWith("}")) {
                        defaultMessage = defaultMessage.replace("{", "")
                                .replace("}", "");
                        String message = LocalMessageResource.getMessage(defaultMessage, r.getLocale());
                        sb = new StringBuilder(message);
                        break;
                    } else {
                        sb.append(fieldError.getField())
                                .append(" ")
                                .append(fieldError.getDefaultMessage())
                                .append(" ");
                    }
                }
            }
        }
        error();
        ApiResponse<VoidResponse> response = new ApiResponse<>(HttpStatus.BAD_REQUEST.value(), sb.toString());
        if (ExceptionResponse.isInternal()) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(response);
        }
        return ResponseEntity.ok(response);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    public ExceptionResponse handleError(ConstraintViolationException e) {
        log.warn("Constraint Violation", e);
        StringBuilder sb = new StringBuilder();
        for (ConstraintViolation<?> constraintViolation : e.getConstraintViolations()) {
            String message = constraintViolation.getMessage();
            sb.append(message);
            sb.append(",");
        }
        error();
        return ExceptionResponse.create(new ExceptionCode(HttpStatus.BAD_REQUEST.value(), sb.toString()), e);
    }

    @ExceptionHandler(NoHandlerFoundException.class)
    public ExceptionResponse handleError(NoHandlerFoundException e) {
        log.error("404 Not Found", e);
        error();
        return ExceptionResponse.create(ExceptionCode.notFound(), e);
    }

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public ExceptionResponse handleError(HttpMessageNotReadableException e) {
        log.error("Message Not Readable", e);
        error();
        return ExceptionResponse.create(ExceptionCode.messageNotReadable(), e);
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<ExceptionCode> handleError(HttpRequestMethodNotSupportedException e) {
        log.error("Request Method Not Supported", e);
        error();
        return ExceptionResponse.create(ExceptionCode.methodNotSupported(), e);

    }

    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public ExceptionResponse handleError(HttpMediaTypeNotSupportedException e) {
        log.error("Media Type Not Supported", e);
        error();
        return ExceptionResponse.create(ExceptionCode.mediaTypeNotSupported(), e);
    }

    @ExceptionHandler(Throwable.class)
    public ExceptionResponse handleError(Throwable e) {
        log.error("Internal Server Error", e);
        error();
        return ExceptionResponse.create(ExceptionCode.internalServerError(), e);
    }
//
//    @ExceptionHandler(FeignException.class)
//    public void handleError(FeignException e, HttpServletResponse response) throws IOException {
//        log.error("FeignException ", e);
//        String content = e.contentUTF8();
//        int status = e.status();
//        response.setStatus(status);
//        try (OutputStream out = response.getOutputStream()) {
//            out.write(content.getBytes(StandardCharsets.UTF_8));
//        }
//    }

}

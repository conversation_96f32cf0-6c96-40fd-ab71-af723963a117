package com.media.user.feign.client;

import com.media.user.dto.request.SensitiveWordDetectionRequest;
import com.media.user.dto.response.SensitiveWordDetectionResponse;
import com.xme.xme_base_depends.models.ApiResponse;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.concurrent.TimeUnit;

@FeignClient(url = "${sensitive.word.url:http://envtest-api.x.me}", value = "sensitiveWordClient", configuration = SensitiveWordClient.SensitiveWordClientConfiguration.class)
public interface SensitiveWordClient {
    @Configuration
    class SensitiveWordClientConfiguration {
        @Bean("sensitiveWordClientOptions")
        public Request.Options options() {
            return new Request.Options(100, TimeUnit.MILLISECONDS, 1000, TimeUnit.MILLISECONDS, true);
        }
    }

    @PostMapping(value = "/textshield/api/v1/sensitive/multilingual/detect")
    ApiResponse<SensitiveWordDetectionResponse> sensitiveWordDetection(@RequestBody SensitiveWordDetectionRequest request);

}

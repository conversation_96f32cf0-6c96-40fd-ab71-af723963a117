package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("props")
@Data
@Accessors(chain = true)
public class PropsModel extends Model<PropsModel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("zh_name")
    private String zhName;

    @TableField("en_name")
    private String enName;

    @TableField("ccy")
    private String ccy;

    @TableField("price")
    private BigDecimal price;

    @TableField("type")
    private Integer type;

    @TableField("capacity")
    private Integer capacity;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;

    @TableField("created_by")
    private Long createdBy;

    @TableField("updated_by")
    private Long updatedBy;

}
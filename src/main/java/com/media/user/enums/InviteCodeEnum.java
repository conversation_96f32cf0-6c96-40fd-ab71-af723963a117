package com.media.user.enums;

import lombok.Getter;

/**
 * 早鸟截止日期文案
 */
@Getter
public enum InviteCodeEnum {

    EARLY_BIRD_CONTENT_EN_US("en-US", "Invitation code:"),
    EARLY_BIRD_CONTENT_ZH_TW("zh-TW", "邀请码："),
    EARLY_BIRD_CONTENT_ZH_CN("zh-CN", "邀请码："),
    EARLY_BIRD_CONTENT_FR_FR("fr-FR", "Code d'invitation :"),
    EARLY_BIRD_CONTENT_DE_DE("de-DE", "Einladungscode:"),
    EARLY_BIRD_CONTENT_RU_RU("ru-RU", "Код приглашения:"),
    EARLY_BIRD_CONTENT_ES_ES("es-ES", "Código de invitación:"),
    EARLY_BIRD_CONTENT_JA_JP("ja-JP", "招待コード："),
    EARLY_BIRD_CONTENT_TH_TH("th-TH", "รหัสคำเชิญ:"),
    EARLY_BIRD_CONTENT_VI_VN("vi-VN", "Mã mời:"),
    EARLY_BIRD_CONTENT_KO_KR("ko-KR", "초대 코드:"),
    EARLY_BIRD_CONTENT_ID_ID("id-ID", "Kode undangan:"),
    EARLY_BIRD_CONTENT_HI_IN("hi-IN", "निमंत्रण कोड:"),
    EARLY_BIRD_CONTENT_UR_PK("ur-PK", "دعوتی کوڈ:"),
    EARLY_BIRD_CONTENT_TR_TR("tr-TR", "Davet kodu:"),
    EARLY_BIRD_CONTENT_AR_SA("ar-SA", "رمز الدعوة:"),
    EARLY_BIRD_CONTENT_FA_IR("fa-IR", "کد دعوت:"),
    EARLY_BIRD_CONTENT_PT_PT("pt-PT", "Código de convite:"),
    EARLY_BIRD_CONTENT_BN_BD("bn-BD", "আমন্ত্রণ কোড:"),
    ;

    private final String language;

    private final String content;

    InviteCodeEnum(String language, String content) {
        this.language = language;
        this.content = content;
    }

    public static String getContent(String language) {
        for (InviteCodeEnum contentEarlyBirdEnum : InviteCodeEnum.values()) {
            if (contentEarlyBirdEnum.getLanguage().equals(language)) {
                return contentEarlyBirdEnum.getContent();
            }
        }
        return "";
    }
}

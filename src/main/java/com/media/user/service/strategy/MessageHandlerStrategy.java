package com.media.user.service.strategy;

import com.media.user.dto.im.RongCloudMessageData;
import com.media.user.dto.rongCloud.RongCloudCallbackResponse;

import java.util.Map;

public interface MessageHandlerStrategy {

    default RongCloudCallbackResponse handleMessage(RongCloudMessageData messageData) {
        return RongCloudCallbackResponse.success();
    }

    default Map<String, Object> replaceContent(RongCloudMessageData messageData) {
        return null;
    }

    String getChannelType();
} 
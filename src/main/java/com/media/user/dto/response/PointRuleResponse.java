package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class PointRuleResponse  {

    private Long id; // eventId

    private String zhEventName;

    private String enEventName;

    private BigDecimal pointChangeCount;

    private Byte changeType;

    private Long maxTimes;

    private Long sortNum;

    // 0-未领取 1-已完成 2-已领取
    private Byte status;
}
package com.media.user.scheduler;

import com.media.user.dto.request.internal.ActivityStatusRequest;
import com.media.user.dto.request.internal.UnionInfoRequest;
import com.media.user.dto.response.internal.ActivityStatusResponse;
import com.media.user.dto.response.internal.UnionInfoResponse;
import com.media.user.feign.client.GameClient;
import com.media.user.service.ActivityReservationService;
import com.media.user.service.UserLoginLogService;
import com.xme.xme_base_depends.models.ApiResponse;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class LoginLogVersionScheduler {
    @Autowired
    UserLoginLogService userLoginLogService;

    @Autowired
    private GameClient gameClient;

    private static Long actId = 5L;

    /**
     * 强更奖励
     */
    @XxlJob("loginLogVersionReward")
    public void loginLogVersionReward() {
        ActivityStatusRequest req = new ActivityStatusRequest();
        req.setActivityId(actId);
        ApiResponse<ActivityStatusResponse> resp = gameClient.getActivityStatus(req);
        if (resp.getResult().isOpen()) {
            //校验当前活动日期，判断是否在活动期间内，并且传输当前活动过期时间
            userLoginLogService.checkNewLoginLogs(resp.getResult().getEndTime());
        }
    }
}

package com.media.user.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 给用户批量添加道具响应结构
 * {"code":200,"message":"success","result":["EP_800","EP_1000"],"success":true}
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddUserPropResponse {
    /**
     * 响应码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 结果，成功添加的道具代码列表
     */
    private List<String> result;
    
    /**
     * 是否成功
     */
    private Boolean success;
}

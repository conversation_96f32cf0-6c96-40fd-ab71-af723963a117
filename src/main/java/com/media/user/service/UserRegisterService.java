package com.media.user.service;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.eventbus.EventBus;
import com.media.core.config.SystemConfig;
import com.media.core.constant.BeanConstant;
import com.media.core.exception.ApiException;
import com.media.core.id.InviteCodeGenerator;
import com.media.core.id.RedisIdGenerator;
import com.media.core.request.ClientInfoContext;
import com.media.core.utils.IpUtil;
import com.media.core.utils.PasswordUtils;
import com.media.core.utils.UserAgentUtil;
import com.media.core.utils.PlatformTypeUtil;
import com.media.core.utils.BoxIdUtil;
import com.media.user.constant.MediaUserConstant;
import com.media.user.convert.mapper.ClientUserConvert;
import com.media.user.domain.ChannelConfigModel;
import com.media.user.domain.ClientUserModel;
import com.media.user.domain.UserFollowRelationModel;
import com.media.user.domain.UserInviteRelationModel;
import com.media.user.dto.request.ClientUserRequest;
import com.media.user.dto.request.MarketingRiskRequest;
import com.media.user.dto.request.RegisterRiskRequest;
import com.media.user.dto.request.internal.UnionInfoRequest;
import com.media.user.dto.request.internal.UnionInviteInfoRequest;
import com.media.user.dto.response.AuthTokenBaseResponse;
import com.media.user.dto.response.AuthTokenResponse;
import com.media.user.dto.response.internal.InviteCodeParseResult;
import com.media.user.dto.response.internal.UnionInfoResponse;
import com.media.user.enums.*;
import com.media.user.es.EsClientUserService;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.GameClient;
import com.media.user.feign.client.PointClient;
import com.media.user.feign.client.RecommendClient;
import com.media.user.feign.client.RiskClient;
import com.media.user.mapper.ChannelConfigMapper;
import com.media.user.mapper.ClientUserMapper;
import com.media.user.mapper.UserFollowRelationMapper;
import com.media.user.model.internal.VerifyInfo;
import com.media.user.mq.provider.UserSendMessageProvider;
import com.media.user.tools.PhoneUtils;
import com.media.user.tools.UserImgUtils;
import com.media.user.tools.UserInviteCodeUtils;
import com.xme.xme_base_depends.models.ApiResponse;
import com.xme.xme_base_depends.mq.message.UserRegisterMqDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import lombok.Data;
import lombok.AllArgsConstructor;
import com.media.user.context.VerificationContext;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.media.user.constant.MediaUserConstant.TOKEN_ID_PREFIX;
import static com.media.user.utils.LivenessStatusUtils.livenessStatusVerify;

@Service
@Slf4j
public class UserRegisterService {

    // 移除ThreadLocal，使用VerificationContext
    // private static final ThreadLocal<VerifyInfo> verifyInfoThreadLocal = ...;

    @Autowired
    RecommendClient recommendClient;
    @Autowired
    UserFollowRelationMapper userFollowRelationMapper;
    @Autowired
    ConfigSwitchService configSwitchService;
    @Autowired
    UserLoginLogService userLoginLogService;
    @Autowired
    EsClientUserService esClientUserService;
    @Autowired
    IpUtil ipUtil;
    @Autowired
    SystemConfig systemConfig;
    @Autowired
    UserInviteRelationService userInviteRelationService;
    @Autowired
    PhoneUtils phoneUtils;
    @Autowired
    UserImgUtils userImgUtils;
    @Autowired
    InviteCodeGenerator inviteCodeGenerator;
    @Autowired
    UserInviteCodeUtils userInviteCodeUtils;
    @Autowired
    ActivityInviteRelationService activityInviteRelationService;
    @Autowired
    DeviceService deviceService;
    @Autowired
    private ClientUserMapper clientUserMapper;
    @Autowired
    private SmsService smsService;
    @Autowired
    private PhoneService phoneService;
    @Autowired
    private RedisIdGenerator redisIdGenerator;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private PasswordUtils passwordUtils;
    @Autowired
    private PointClient pointClient;
    @Autowired
    private UserSendMessageProvider userSendMessageProvider;
    @Autowired
    private ChannelConfigMapper channelConfigMapper;
    @Autowired
    private GameClient unionClient;
    @Autowired
    @Qualifier(value = BeanConstant.USER_EVENT_BUS_BEAN)
    private EventBus userEventBus;
    @Autowired
    private GameClient gameClient;
    @Autowired
    private UserSecretService userSecretService;

    @Autowired
    private UserInviteFaceRecordService userInviteFaceRecordService;

    @Autowired
    private RiskClient riskClient;

    @Autowired
    private UserAgentUtil userAgentUtil;

    @Autowired
    private PlatformTypeUtil platformTypeUtil;

    @Autowired
    private BoxIdUtil boxIdUtil;

    @Data
    @AllArgsConstructor
    public static class RegisterResult {
        private AuthTokenResponse authTokenResponse;
        private Integer epValue;
    }

    public Map<String, Integer> registerConfig() {
        Integer amountXme = systemConfig.getInvitedXmeAmount();
        Map<String, Integer> map = new HashMap<>();
        map.put("inviteXmeAmount", amountXme);
        return map;
    }

    /**
     * 完整的注册流程，包含验证码校验和任务奖励
     */
    public RegisterResult registerWithVerification(ClientUserRequest request) {
        return VerificationContext.execute(() -> {
            // 验证手机号码
            boolean hasPhoneVerify = false;
            if (StringUtils.isNotBlank(request.getPhone())) {
                if (StringUtils.isBlank(request.getPhoneCode())) {
                    throw new ApiException(MediaUserExceptionCodeApi.PHONE_CODE_ERROR);
                }
                phoneService.verifyPhoneCode(null, request.getCountryCode(), request.getPhonePrefix(),
                        request.getPhone(), BusinessTypeEnum.SIGNUP.getValue(), request.getPhoneCode());
                VerificationContext.setPhoneVerified(true);
                hasPhoneVerify = true;
            }

            // 验证邮箱
            boolean hasEmailVerify = false;
            if (StringUtils.isNotBlank(request.getEmail())) {
                if (StringUtils.isBlank(request.getEmailCode())) {
                    throw new ApiException(MediaUserExceptionCodeApi.EMAIL_CODE_ERROR);
                }
                smsService.verifyEmailCode(null, request.getEmail(), BusinessTypeEnum.SIGNUP, request.getEmailCode());
                VerificationContext.setEmailVerified(true);
                hasEmailVerify = true;
            }

            // 执行注册
            AuthTokenResponse authTokenResponse = register(request);

            userInviteFaceRecordService.handleFirstVerification(authTokenResponse.getUserId());

            // 处理任务奖励
            Integer epValue = null;
            if (hasPhoneVerify) {
                epValue = userSecretService.sendMediaTask(authTokenResponse.getUserId(),
                        MediaUserConstant.TASK_CODE_PHONE_VERIFY, "/user/api/user/bind/phone");
                // 营销风险检测
                sendMarketingRiskAsync(authTokenResponse.getUserId(), ipUtil.getIpAddr(), userAgentUtil.getUserAgent(),
                        ClientInfoContext.get().getVersion(), platformTypeUtil.getPlatformType(), boxIdUtil.getBoxId(),
                        "task", MediaUserConstant.TASK_CODE_PHONE_VERIFY);
            }
            if (hasEmailVerify) {
                epValue = userSecretService.sendMediaTask(authTokenResponse.getUserId(),
                        MediaUserConstant.TASK_CODE_EMAIL_VERIFY, "/user/api/user/bind/email");
                // 营销风险检测
                sendMarketingRiskAsync(authTokenResponse.getUserId(), ipUtil.getIpAddr(), userAgentUtil.getUserAgent(),
                        ClientInfoContext.get().getVersion(), platformTypeUtil.getPlatformType(), boxIdUtil.getBoxId(),
                        "task", MediaUserConstant.TASK_CODE_EMAIL_VERIFY);
            }

            sendRegisterRiskAsync(authTokenResponse.getUserId(), ipUtil.getIpAddr(), userAgentUtil.getUserAgent(),
                    ClientInfoContext.get().getVersion(), platformTypeUtil.getPlatformType(), boxIdUtil.getBoxId(),
                    "userPassword");

            return new RegisterResult(authTokenResponse, epValue);
        });
    }

    /**
     * 基础注册方法（不包含验证码校验）
     */
    public AuthTokenResponse register(ClientUserRequest request) {
        String code = request.getInviteCode();
        RegisterTxResp registerTxResp = registerTx(request);
        UserRegisterMqDTO mqDTO = buildUserRegisterMqDTO(registerTxResp.clientUserModel, code);
        userEventBus.post(mqDTO);
        return registerTxResp.authTokenResponse;
    }

    @Transactional(rollbackFor = Exception.class)
    public RegisterTxResp registerTx(ClientUserRequest request) {
        log.info("=== REGISTER START === uid generation and validation, request:{}", request);
        Long uid = redisIdGenerator.generate();
        log.info("=== REGISTER DEBUG === Generated uid: {}", uid);
        String originInviteCode = request.getInviteCode();
        Long inviterUid = null;
        InviteCodeParseResult gameInviteResult = null; // 保存游戏邀请码解析结果
        boolean isGameInviteCode = false; // 标记是否为游戏邀请码

        if (!Strings.isNullOrEmpty(originInviteCode) && originInviteCode.length() == 9) {
            gameInviteResult = gameClient.parseInviteCode(originInviteCode).getResult();
            if (!Objects.isNull(gameInviteResult)) {
                inviterUid = gameInviteResult.getUserID();
                isGameInviteCode = true;
                log.info("=== GAME INVITE CODE === Parsed game invite code: {}, inviterUid: {}, activityId: {}",
                        originInviteCode, inviterUid, gameInviteResult.getMainActivityId());
            }
        }

        if (StringUtils.isBlank(request.getUsername())) {
            request.setUsername("user_" + uid);
            log.info("=== REGISTER DEBUG === Set default username: {}", request.getUsername());
        }

        if (StringUtils.isBlank(request.getEmail()) && StringUtils.isBlank(request.getPhone())) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
        this.checkUserName(request.getUsername());
        // 设置邀请码： 如果用户未填写邀请码，则去 activity_invite_relation 中查询是否存在
        if (StringUtils.isBlank(request.getInviteCode())) {
            if (StringUtils.isNotBlank(request.getEmail())) {
                request.setInviteCode(activityInviteRelationService.getInviteCodeByEmail(request.getEmail()));
                log.info("email register activity_invite_relation 中的 inviteCode:{}", request.getInviteCode());
            }
            if (StringUtils.isNotBlank(request.getPhone())) {
                request.setInviteCode(activityInviteRelationService.getInviteCodeByPhone(request.getCountryCode(),
                        request.getPhonePrefix(), request.getPhone()));
                log.info("phone register activity_invite_relation 中的 inviteCode:{}", request.getInviteCode());
            }
        }

        // 邮箱注册
        ClientUserModel clientUserModel = null;
        log.info("=== REGISTER DEBUG === Starting user registration, uid: {}", uid);
        if (StringUtils.isNotBlank(request.getEmail())) {
            log.info("=== REGISTER DEBUG === Registering by email: {}, uid: {}", request.getEmail(), uid);
            try {
                clientUserModel = this.registerByEmail(uid, request, inviterUid);
                log.info("=== REGISTER DEBUG === Email registration SUCCESS, uid: {}, returned model uid: {}", uid,
                        clientUserModel != null ? clientUserModel.getUid() : "NULL");
            } catch (Exception e) {
                log.error("=== REGISTER ERROR === Email registration FAILED, uid: {}, error: {}", uid, e.getMessage(),
                        e);
                throw e;
            }
        }
        if (StringUtils.isNotBlank(request.getPhone())) {
            log.info("=== REGISTER DEBUG === Registering by phone: {}-{}-{}, uid: {}", request.getCountryCode(),
                    request.getPhonePrefix(), request.getPhone(), uid);
            // 校验手机号格式
            phoneUtils.checkPhoneRegex(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());
            try {
                clientUserModel = this.registerByPhone(uid, request, inviterUid);
                log.info("=== REGISTER DEBUG === Phone registration SUCCESS, uid: {}, returned model uid: {}", uid,
                        clientUserModel != null ? clientUserModel.getUid() : "NULL");
            } catch (Exception e) {
                log.error("=== REGISTER ERROR === Phone registration FAILED, uid: {}, error: {}", uid, e.getMessage(),
                        e);
                throw e;
            }
        }

        log.info("=== REGISTER DEBUG === User registration completed, creating wallet account, uid: {}", uid);
        // 生成用户钱包账户
        try {
            pointClient.pointCreatAccountBalance(uid);
            log.info("=== REGISTER DEBUG === Wallet account created SUCCESS, uid: {}", uid);
        } catch (Exception e) {
            log.error("=== REGISTER ERROR === Wallet account creation FAILED, uid: {}, error: {}", uid, e.getMessage(),
                    e);
            throw e;
        }

        // todo caoshuo 邀请关系存储

        // 邀请码不为空
        if (StringUtils.isNotBlank(request.getInviteCode())) {
            // 创世大使期间不发送邀请代币, 只保存邀请关系
            Integer amountStatus = InviteAmountStatusEnum.INVITE_AMOUNT_STATUS_1.getStatus();
            String idempotent = DigestUtils
                    .md5Hex(uid + "-" + clientUserModel.getInviteUid() + "-" + PointEventEnums.INVITE_XME.getEventId());
            Integer inviteXmeAmount = systemConfig.getInviteXmeAmount();

            // 保存邀请关系 - 根据邀请码类型选择不同的处理方式
            UserInviteRelationModel relationModel;
            if (isGameInviteCode && gameInviteResult != null && gameInviteResult.isOpen()) {
                // 游戏活动邀请 - 保存活动ID
                Long activityId = gameInviteResult.getMainActivityId();
                relationModel = userInviteRelationService.saveInviteRelation(
                        clientUserModel.getInviteUid(),
                        uid,
                        InviteTypeEnum.INVITE_TYPE_3.getType(), // 游戏活动邀请
                        activityId, // 游戏活动ID
                        inviteXmeAmount,
                        CcyEnums.XME.name(),
                        amountStatus,
                        idempotent);
                log.info(
                        "=== GAME INVITE RELATION === Saved game invite relation: inviterUid={}, invitedUid={}, activityId={}",
                        clientUserModel.getInviteUid(), uid, activityId);
            } else {
                // 普通邀请关系 - 保持原有逻辑
                relationModel = userInviteRelationService.saveInviteRelation(
                        clientUserModel.getInviteUid(),
                        uid,
                        InviteTypeEnum.INVITE_TYPE_1.getType(), // 普通邀请
                        null, // 普通邀请无活动ID
                        inviteXmeAmount,
                        CcyEnums.XME.name(),
                        amountStatus,
                        idempotent);
                log.info("=== NORMAL INVITE RELATION === Saved normal invite relation: inviterUid={}, invitedUid={}",
                        clientUserModel.getInviteUid(), uid);
            }

            // 发送邀请注册成功的消息
            userSendMessageProvider.sendFollowMessage(clientUserModel.getUid(), clientUserModel.getNickName(),
                    clientUserModel.getAvatarUrl(), clientUserModel.getInviteUid());
            // 邀请默认 follow 关系
            this.saveFollowRelation(clientUserModel, clientUserModel.getInviteUid());
        }

        // 推送给推荐平台用户 - 异步推送
        log.info("=== REGISTER DEBUG === Sending user to recommendation system, uid: {}", uid);
        try {
            recommendClient.recommendSendUserId("" + uid);
            log.info("=== REGISTER DEBUG === Recommendation system notification SUCCESS, uid: {}", uid);
        } catch (Exception e) {
            log.error("=== REGISTER ERROR === Recommendation system notification FAILED, uid: {}, error: {}", uid,
                    e.getMessage(), e);
            // 这里不抛异常，因为是异步推送，不应该影响主流程
        }

        // 推送用户至 ES
        log.info("=== REGISTER DEBUG === Saving user to ES, uid: {}, nickname: {}", uid, clientUserModel.getNickName());
        try {
            esClientUserService.saveOne(uid, clientUserModel.getNickName());
            log.info("=== REGISTER DEBUG === ES save SUCCESS, uid: {}", uid);
        } catch (Exception e) {
            log.error("=== REGISTER ERROR === ES save FAILED, uid: {}, error: {}", uid, e.getMessage(), e);
            // 这里不抛异常，因为ES失败不应该影响用户注册
        }

        // 异步记录用户登录日志信息
        log.info("=== REGISTER DEBUG === Logging user login, uid: {}", clientUserModel.getUid());
        try {
            userLoginLogService.loginLog(clientUserModel.getUid(), "platform-register",
                    PlatformEnums.get(ClientInfoContext.get().getPlatformType()).getCode());
            log.info("=== REGISTER DEBUG === Login log SUCCESS, uid: {}", clientUserModel.getUid());
        } catch (Exception e) {
            log.error("=== REGISTER ERROR === Login log FAILED, uid: {}, error: {}", clientUserModel.getUid(),
                    e.getMessage(), e);
            // 这里不抛异常，因为登录日志失败不应该影响用户注册
        }

        // 用户注册
        log.info("=== REGISTER DEBUG === Binding device, uid: {}, deviceId: {}", clientUserModel.getUid(),
                ClientInfoContext.get().getDeviceId());
        deviceService.AsyncUserBindDevice(ClientInfoContext.get().getDeviceId(), clientUserModel.getUid());
        // 生成token
        log.info("=== REGISTER DEBUG === Generating token for uid: {}", clientUserModel.getUid());
        AuthTokenResponse tokenResponse = generateToken(clientUserModel.getUid(), MediaUserConstant.timeout,
                MediaUserConstant.timeUnit);
        RegisterTxResp resp = new RegisterTxResp();
        resp.authTokenResponse = tokenResponse;
        resp.clientUserModel = clientUserModel;
        return resp;
    }

    private Triple<Boolean, String, String> parseUnionInviteCode(String inviteCode) {
        if (StringUtils.isNotBlank(inviteCode) && inviteCode.length() == 10) {
            String unionInviteCode = inviteCode.substring(0, 4);
            String useCode = inviteCode.substring(4);
            return Triple.of(true, useCode, unionInviteCode);
        }
        return Triple.of(false, inviteCode, null);
    }

    private Pair<Boolean, UnionInfoResponse> verifyUnionInviteCode(String unionInviteCode) {
        ApiResponse<UnionInfoResponse> resp = unionClient
                .QueryUnionInfo(new UnionInfoRequest().setInviteCode(unionInviteCode));
        log.info("QueryUnionInfo:{}", JSON.toJSONString(resp));
        if (resp != null && resp.getSuccess() && resp.getResult() != null) {
            return Pair.of(UnionStatusEnums.isEffective(resp.getResult().getStatus()), resp.getResult());
        }
        return Pair.of(false, null);
    }

    /**
     * 按照邮箱注册
     */
    private ClientUserModel registerByEmail(Long uid, ClientUserRequest request, Long inviterUid) {
        // 校验邮箱是否已注册
        checkEmail(request.getEmail());

        // 邮箱验证
        // smsService.verifyEmailCode(request.getEmail(), BusinessTypeEnum.SIGNUP,
        // request.getEmailCode());

        ClientUserModel clientUserModel = ClientUserConvert.INSTANCE.req2Model(request)
                .setType(UserTypeEnums.USER.getCode());

        clientUserModel.setUid(uid);
        Date now = new Date();
        // clientUserModel.setUsername(UsernameGenerator.generateUniqueUsername());
        clientUserModel.setFirstRegistration(FirstRegistrationEnum.EMAIL.getCode());
        clientUserModel.setIpAddress(ipUtil.getIpAddr());
        clientUserModel.setCreatedTime(now);
        clientUserModel.setRegistrationTime(now);
        clientUserModel.setChannelId(this.resolveChannelId(request));
        String md5Password = passwordUtils.bcryptHash(clientUserModel.getPassword(),
                PasswordVersionEnum.PASSWORD_VERSION_1.getState());
        clientUserModel.setPassword(md5Password);
        clientUserModel.setLanguage(ClientInfoContext.getLanguage().getValue());
        clientUserModel.setStatus(UserStatusEnum.ENABLED.getStatus());
        clientUserModel.setInviteCode(userInviteCodeUtils.makeUserInviteCode());
        if (!StringUtils.isBlank(request.getNickName())) {
            clientUserModel.setNickName(request.getNickName());
        } else {
            clientUserModel.setNickName(request.getUsername());
        }

        VerificationContext.applyToClientUserModel(now, clientUserModel);

        if (StringUtils.isBlank(clientUserModel.getAvatarUrl())) {
            clientUserModel.setAvatarUrl(userImgUtils.getUserAvatarUrl());
        }
        clientUserModel.setBackground(userImgUtils.getUserBackground());
        List<ClientUserModel> clientUserModelList;
        // TODO 曹朔 统一邀请码逻辑
        if (!StringUtils.isBlank(request.getInviteCode()) && request.getInviteCode().length() != 9) {
            Map<String, Object> query = Map.of("invite_code", request.getInviteCode(), "status", 1);
            clientUserModelList = clientUserMapper.selectByMap(query);
            if (CollectionUtils.isEmpty(clientUserModelList)) {
                throw new ApiException(MediaUserExceptionCodeApi.INVITE_CODE_INVALID);
            }
            // 检查邀请人是否已经进行了 人脸识别
            ClientUserModel inviteUser = clientUserModelList.get(0);
            if (!livenessStatusVerify(inviteUser.getFaceLivenessStatus(), inviteUser.getPhoneVerify(),
                    inviteUser.getEmailVerify())) {
                throw new ApiException(MediaUserExceptionCodeApi.INVITE_USER_NOT_FACE_LIVENESS);
            }
            clientUserModel.setInviteUid(inviteUser.getUid());
        } else {
            clientUserModel.setInviteUid(inviterUid);
        }
        log.info("=== REGISTER DB === About to insert client_user, uid: {}, email: {}, username: {}",
                clientUserModel.getUid(), clientUserModel.getEmail(), clientUserModel.getUsername());
        try {
            clientUserMapper.insert(clientUserModel);
            log.info("=== REGISTER DB SUCCESS === client_user inserted successfully, uid: {}, email: {}",
                    clientUserModel.getUid(), clientUserModel.getEmail());

            // 验证插入是否成功 - 立即查询
            ClientUserModel verifyUser = clientUserMapper.selectByUserId(clientUserModel.getUid());
            if (verifyUser != null) {
                log.info("=== REGISTER DB VERIFY === User found after insert, uid: {}, email: {}",
                        verifyUser.getUid(), verifyUser.getEmail());
            } else {
                log.error("=== REGISTER DB ERROR === User NOT FOUND after insert! uid: {}", clientUserModel.getUid());
            }

            return clientUserModel;
        } catch (Exception e) {
            log.error("=== REGISTER DB ERROR === Database insert failed, uid: {}, email: {}, error: {}",
                    clientUserModel.getUid(), clientUserModel.getEmail(), e.getMessage(), e);
            if (e.getMessage() != null && e.getMessage().contains("unique_index_email")) {
                throw new ApiException(MediaUserExceptionCodeApi.EMAIL_REGISTERED_ERROR);
            } else {
                throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
            }
        }

    }

    /**
     * 手机号注册
     */
    private ClientUserModel registerByPhone(Long uid, ClientUserRequest request, Long inviterUid) {
        // 校验手机号格式
        phoneUtils.checkPhoneRegex(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());

        // 校验手机号是否已注册
        checkPhone(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());

        // 验证手机号注册验证码
        // phoneService.verifyPhoneCode(null, request.getPhone(),
        // PhoneVerifyCodeTypeEnum.SIGNUP.getValue(), request.getPhone());

        ClientUserModel clientUserModel = ClientUserConvert.INSTANCE.req2Model(request)
                .setType(UserTypeEnums.USER.getCode());

        clientUserModel.setUid(uid);
        Date now = new Date();
        // clientUserModel.setUsername(UsernameGenerator.generateUniqueUsername());
        clientUserModel.setFirstRegistration(FirstRegistrationEnum.PHONE.getCode());
        clientUserModel.setIpAddress(ipUtil.getIpAddr());
        clientUserModel.setCreatedTime(now);
        clientUserModel.setRegistrationTime(now);
        clientUserModel.setChannelId(this.resolveChannelId(request));
        String md5Password = passwordUtils.bcryptHash(clientUserModel.getPassword(),
                PasswordVersionEnum.PASSWORD_VERSION_1.getState());
        clientUserModel.setPassword(md5Password);
        clientUserModel.setLanguage(ClientInfoContext.getLanguage().getValue());
        clientUserModel.setStatus(UserStatusEnum.ENABLED.getStatus());
        clientUserModel.setInviteCode(userInviteCodeUtils.makeUserInviteCode());
        clientUserModel.setNickName(request.getUsername());
        if (StringUtils.isBlank(clientUserModel.getAvatarUrl())) {
            clientUserModel.setAvatarUrl(userImgUtils.getUserAvatarUrl());
        }
        clientUserModel.setBackground(userImgUtils.getUserBackground());

        VerificationContext.applyToClientUserModel(now, clientUserModel);
        List<ClientUserModel> clientUserModelList;
        if (!StringUtils.isBlank(request.getInviteCode()) && request.getInviteCode().length() != 9) {
            Map<String, Object> query = Map.of("invite_code", request.getInviteCode(), "status", 1);
            clientUserModelList = clientUserMapper.selectByMap(query);
            if (CollectionUtils.isEmpty(clientUserModelList)) {
                throw new ApiException(MediaUserExceptionCodeApi.INVITE_CODE_INVALID);
            }
            // 检查邀请人是否已经进行了 人脸识别
            ClientUserModel inviteUser = clientUserModelList.get(0);
            if (!livenessStatusVerify(inviteUser.getFaceLivenessStatus(), inviteUser.getPhoneVerify(),
                    inviteUser.getEmailVerify())) {
                throw new ApiException(MediaUserExceptionCodeApi.INVITE_USER_NOT_FACE_LIVENESS);
            }
            clientUserModel.setInviteUid(clientUserModelList.get(0).getUid());
        } else {
            clientUserModel.setInviteUid(inviterUid);
        }
        log.info("=== REGISTER DB === About to insert client_user by phone, uid: {}, phone: {}-{}-{}, username: {}",
                clientUserModel.getUid(), clientUserModel.getCountryCode(), clientUserModel.getPhonePrefix(),
                clientUserModel.getPhone(), clientUserModel.getUsername());
        try {
            clientUserMapper.insert(clientUserModel);
            log.info("=== REGISTER DB SUCCESS === client_user inserted successfully by phone, uid: {}, phone: {}-{}-{}",
                    clientUserModel.getUid(), clientUserModel.getCountryCode(), clientUserModel.getPhonePrefix(),
                    clientUserModel.getPhone());

            // 验证插入是否成功 - 立即查询
            ClientUserModel verifyUser = clientUserMapper.selectByUserId(clientUserModel.getUid());
            if (verifyUser != null) {
                log.info("=== REGISTER DB VERIFY === User found after insert by phone, uid: {}, phone: {}-{}-{}",
                        verifyUser.getUid(), verifyUser.getCountryCode(), verifyUser.getPhonePrefix(),
                        verifyUser.getPhone());
            } else {
                log.error("=== REGISTER DB ERROR === User NOT FOUND after insert by phone! uid: {}",
                        clientUserModel.getUid());
            }

            return clientUserModel;
        } catch (Exception e) {
            log.error("=== REGISTER DB ERROR === Database insert failed by phone, uid: {}, phone: {}-{}-{}, error: {}",
                    clientUserModel.getUid(), clientUserModel.getCountryCode(), clientUserModel.getPhonePrefix(),
                    clientUserModel.getPhone(), e.getMessage(), e);
            if (e.getMessage() != null && e.getMessage().contains("unique_idx_phone")) {
                throw new ApiException(MediaUserExceptionCodeApi.PHONE_ALREADY_REGISTERED);
            } else {
                throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
            }
        }
    }

    public ClientUserModel robotRegister() {
        String nickName = inviteCodeGenerator.generateInviteCode(MediaUserConstant.NICK_NAME_LENGTH);
        Date now = new Date();
        ClientUserModel clientUserModel = new ClientUserModel()
                .setUid(redisIdGenerator.generate())
                .setType(UserTypeEnums.ROBOT.getCode())
                .setSourceType(PlatformEnums.ANDROID.getCode())
                .setEmail(nickName)
                .setNickName(nickName)
                .setCreatedTime(now)
                .setLanguage(LanguageEnums.en_US.getValue())
                .setStatus(UserStatusEnum.ENABLED.getStatus());
        clientUserModel.setAvatarUrl(userImgUtils.getUserAvatarUrl());
        clientUserModel.setInviteCode(userInviteCodeUtils.makeUserInviteCode());
        String password = inviteCodeGenerator.generateInviteCode(MediaUserConstant.PASSWORD_LENGTH);
        clientUserModel.setPassword(password)
                .setRegistrationTime(now);
        clientUserMapper.insert(clientUserModel);
        // 生成用户钱包账户
        pointClient.pointCreatAccountBalance(clientUserModel.getUid());
        // 积分
        // userPointClient.signupEvent(new
        // PointEventRequest().setUid(clientUserModel.getUid()));

        // 推送用户至 ES
        esClientUserService.saveOne(clientUserModel.getUid(), clientUserModel.getNickName());

        return clientUserModel;
    }

    private Long getExpire(long timeout, TimeUnit timeUnit) {
        return System.currentTimeMillis() + TimeUnit.MILLISECONDS.convert(timeout, timeUnit);
    }

    public AuthTokenResponse generateToken(Long uid, Long timeout, TimeUnit timeUnit) {
        String token = UUID.randomUUID().toString().replace("-", "");
        Long expire = getExpire(timeout, timeUnit);
        AuthTokenBaseResponse authToken = new AuthTokenBaseResponse()
                .setUserId(uid)
                .setTimeout(timeout)
                .setTimeUnit(timeUnit)
                .setExpire(expire);
        log.info("generateToken ok! authToken = {}", authToken);
        return saveTokenToRedis(uid, expire, token, authToken);
    }

    private AuthTokenResponse saveTokenToRedis(Long userId, Long expire, String token,
            AuthTokenBaseResponse authToken) {
        stringRedisTemplate.opsForValue().set(TOKEN_ID_PREFIX + token, JSON.toJSONString(authToken),
                authToken.getTimeout(), authToken.getTimeUnit());
        AuthTokenResponse tokenResult = new AuthTokenResponse();
        tokenResult.setExpire(expire);
        tokenResult.setAccessToken(token);
        tokenResult.setUserId(userId);
        return tokenResult;
    }

    /**
     * 校验昵称名是否已存在
     *
     * @param nickName
     */
    public void checkNickName(String nickName) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("nick_name", nickName);
        queryWrapper.eq("virtual_type", 0);
        List<ClientUserModel> list = clientUserMapper.selectList(queryWrapper);
        if (list != null && !list.isEmpty()) {
            throw new ApiException(MediaUserExceptionCodeApi.NICK_NAME_EXIST_ERROR);
        }
    }

    /**
     * 校验昵称名是否已存在
     *
     * @param username
     */
    public void checkUserName(String username) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", username);
        queryWrapper.eq("virtual_type", 0);
        List<ClientUserModel> list = clientUserMapper.selectList(queryWrapper);
        if (list != null && !list.isEmpty()) {
            throw new ApiException(MediaUserExceptionCodeApi.USERNAME_EXIST_ERROR);
        }
    }

    public void checkEmail(String email) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email);
        ClientUserModel clientUserModel = clientUserMapper.selectOne(queryWrapper);
        if (clientUserModel != null) {
            throw new ApiException(MediaUserExceptionCodeApi.EMAIL_REGISTERED_ERROR);
        }
    }

    public void checkPhone(String countryCode, String phonePrefix, String phone) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("country_code", countryCode);
        queryWrapper.eq("phone_prefix", phonePrefix);
        queryWrapper.eq("phone", phone);
        ClientUserModel clientUserModel = clientUserMapper.selectOne(queryWrapper);
        if (clientUserModel != null) {
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_ALREADY_REGISTERED);
        }
    }

    private void saveFollowRelation(ClientUserModel user, Long followedUid) {
        try {
            UserFollowRelationModel followRelationModel = new UserFollowRelationModel();
            followRelationModel.setRelationId(redisIdGenerator.generate());
            followRelationModel.setFollowUid(user.getUid());
            followRelationModel.setFollowedNewUid(followedUid);
            followRelationModel.setFollowState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
            followRelationModel.setFollowTime(new Date());
            followRelationModel.setCreatedTime(new Date());
            userFollowRelationMapper.insert(followRelationModel);

            // 添加到我关注列表
            stringRedisTemplate.opsForSet().add(MediaUserConstant.USER_FOLLOWING_PREFIX + user.getUid(),
                    String.valueOf(followedUid));
            // 添加到对方的粉丝列表
            stringRedisTemplate.opsForSet().add(MediaUserConstant.USER_FANS_PREFIX + followedUid,
                    String.valueOf(user.getUid()));

            // 发送关注关系
            userSendMessageProvider.sendFollowMessage(user.getUid(), user.getNickName(), user.getAvatarUrl(),
                    followedUid);
        } catch (Exception e) {
            log.error("saveFollowRelation error:{}", e.getMessage());
        }
    }

    /**
     * 根据渠道编码获取渠道ID
     */
    private Long getChannelIdByCode(String channelCode) {
        if (StringUtils.isBlank(channelCode)) {
            return null;
        }
        channelCode = Strings.replace(channelCode, "ANDROID-", "");

        ChannelConfigModel config = channelConfigMapper.selectOne(
                new QueryWrapper<ChannelConfigModel>()
                        .eq("channel_code", channelCode.toLowerCase())
                        .eq("status", 1));
        return config != null ? config.getChannelId() : null;
    }

    /**
     * 根据渠道编码获取渠道ID
     */
    private Long getChannelIdByInviteCode(String inviteCode) {
        if (StringUtils.isBlank(inviteCode)) {
            return null;
        }
        ChannelConfigModel config = channelConfigMapper.selectOne(
                new QueryWrapper<ChannelConfigModel>()
                        .eq("invite_code", inviteCode)
                        .eq("status", 1));
        return config != null ? config.getChannelId() : null;
    }

    /**
     * 解析渠道ID
     */
    private Long resolveChannelId(ClientUserRequest request) {
        if (StringUtils.isNotBlank(request.getChannelInviteCode())) {
            Long channelId = getChannelIdByInviteCode(request.getChannelInviteCode());
            if (channelId != null) {
                return channelId;
            }
        }
        return 0L;
    }

    private int compareVersions(String version1, String version2) {
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");

        int maxLength = Math.max(v1Parts.length, v2Parts.length);

        for (int i = 0; i < maxLength; i++) {
            int v1 = (i < v1Parts.length) ? Integer.parseInt(v1Parts[i]) : 0;
            int v2 = (i < v2Parts.length) ? Integer.parseInt(v2Parts[i]) : 0;

            if (v1 > v2) {
                return 1;
            } else if (v1 < v2) {
                return -1;
            }
        }

        return 0; // Versions are equal
    }

    /**
     * 根据邀请码获取渠道代码（公共方法）
     */
    public String getChannelCodeByInviteCode(String inviteCode) {
        if (StringUtils.isBlank(inviteCode)) {
            return null;
        }

        // 通过邀请码查询渠道配置
        ChannelConfigModel config = channelConfigMapper.selectOne(
                new QueryWrapper<ChannelConfigModel>()
                        .eq("invite_code", inviteCode)
                        .eq("status", 1));

        if (config == null || StringUtils.isBlank(config.getChannelCode())) {
            log.debug("邀请码 {} 未找到对应渠道", inviteCode);
            return null;
        }

        log.info("邀请码 {} 对应渠道代码: {}", inviteCode, config.getChannelCode());
        return config.getChannelCode();
    }

    /**
     * 构建UserRegisterMqDTO
     */
    private UserRegisterMqDTO buildUserRegisterMqDTO(ClientUserModel model, String originInviteCode) {
        return new UserRegisterMqDTO()
                .setUid(model.getUid())
                .setUsername(model.getUsername())
                .setEmail(model.getEmail())
                .setCountryCode(model.getCountryCode())
                .setPhonePrefix(model.getPhonePrefix())
                .setPhone(model.getPhone())
                .setAvatarUrl(model.getAvatarUrl())
                .setInviteCode(originInviteCode)
                .setNickName(model.getNickName())
                .setPersonIntroduce(model.getPersonIntroduce())
                .setSourceType(model.getSourceType())
                .setLanguage(model.getLanguage())
                .setStatus(model.getStatus())
                .setInviteUid(model.getInviteUid())
                .setType(model.getType())
                .setChannelId(model.getChannelId())
                .setIpAddress(model.getIpAddress())
                .setRegistrationTime(model.getRegistrationTime())
                .setCreatedTime(model.getCreatedTime())
                .setEmailVerify(model.getEmailVerify())
                .setPhoneVerify(model.getPhoneVerify())
                .setFaceLivenessStatus(model.getFaceLivenessStatus());
    }

    class RegisterTxResp {
        AuthTokenResponse authTokenResponse;
        ClientUserModel clientUserModel;
    }

    /**
     * 异步执行注册风险检测
     *
     * @param userId       用户ID
     * @param ip           IP地址
     * @param userAgent    用户代理
     * @param appVersion   应用版本
     * @param os           操作系统
     * @param boxId        设备盒子ID
     * @param registerType 注册类型
     */
    @Async("threadPoolTaskExecutor")
    public void sendRegisterRiskAsync(Long userId, String ip, String userAgent, String appVersion, String os,
            String boxId,
            String registerType) {
        if (boxId == null) {
            log.warn("注册风险检测异步调用失败: userId={}, boxId is null", userId);
            return;
        }
        try {
            RegisterRiskRequest registerRiskRequest = new RegisterRiskRequest();
            registerRiskRequest.setUserId(userId);
            registerRiskRequest.setIp(ip);
            registerRiskRequest.setUserAgent(userAgent);
            registerRiskRequest.setAppVersion(appVersion);
            registerRiskRequest.setOs(os);
            registerRiskRequest.setBoxId(boxId);
            registerRiskRequest.setRegisterType(registerType);

            riskClient.registerRisk(registerRiskRequest);
            log.info("注册风险检测异步调用成功: userId={}", userId);
        } catch (Exception e) {
            log.warn("注册风险检测异步调用失败: userId={}, error={}", userId, e.getMessage());
        }
    }

    /**
     * 异步执行营销风险检测
     *
     * @param userId        用户ID
     * @param ip            IP地址
     * @param userAgent     用户代理
     * @param appVersion    应用版本
     * @param os            操作系统
     * @param boxId         设备盒子ID
     * @param marketingType 营销类型
     * @param taskId        任务ID
     */
    @Async("threadPoolTaskExecutor")
    public void sendMarketingRiskAsync(Long userId, String ip, String userAgent, String appVersion, String os,
            String boxId, String marketingType, String taskId) {
        if (boxId == null) {
            log.warn("营销风险检测异步调用失败: userId={}, boxId is null", userId);
            return;
        }
        try {
            MarketingRiskRequest marketingRiskRequest = new MarketingRiskRequest();
            marketingRiskRequest.setUserId(userId);
            marketingRiskRequest.setIp(ip);
            marketingRiskRequest.setUserAgent(userAgent);
            marketingRiskRequest.setAppVersion(appVersion);
            marketingRiskRequest.setOs(os);
            marketingRiskRequest.setBoxId(boxId);
            marketingRiskRequest.setMarketingType(marketingType);
            marketingRiskRequest.setTaskId(taskId);

            riskClient.marketingRisk(marketingRiskRequest);
            log.info("营销风险检测异步调用成功: userId={}, marketingType={}, taskId={}", userId, marketingType, taskId);
        } catch (Exception e) {
            log.warn("营销风险检测异步调用失败: userId={}, marketingType={}, taskId={}, error={}",
                    userId, marketingType, taskId, e.getMessage());
        }
    }
}

package com.media.core.config;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.media.core.request.ClientInfoContext;
import com.media.user.enums.RewardRuleEnum;
import com.media.user.utils.DesUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/1/28
 */
@Slf4j
@Data
@Configuration
public class SystemConfig {

    // bee登录按钮：0:不显示 1: 显示
    @Value("${system.config.beeLoginShow}")
    private Integer beeLoginShow;

    //审核版本号 Android
    @Value("${system.config.checkApp.versionAndroid}")
    private String checkVersionAndroid;

    //审核版本号 Android-Gp
    @Value("${system.config.checkApp.versionAndroidGP}")
    private String checkVersionAndroidGp;

    //审核版本号 Ios
    @Value("${system.config.checkApp.versionIos}")
    private String checkVersionIos;

    //早鸟活动状态：0:不开启 1: 开启
    @Value("${system.config.earlyBird}")
    private Integer earlyBird;

    //任务中心显示状态：0:隐藏 1: 显示
    @Value("${system.config.taskShow}")
    private Integer taskShow;

    //礼物 icon 显示隐藏：0:隐藏 1: 显示
    @Value("${system.config.giftIconShow}")
    private Integer giftIconShow;

    // 邀请奖励(普通)
    @Value("${system.config.inviteXmeAmount}")
    private Integer inviteXmeAmount;

    // 被邀请奖励(普通)
    @Value("${system.config.invitedXmeAmount}")
    private Integer invitedXmeAmount;

    // 活体认证奖励(普通)
    @Value("${system.config.faceXmeAmount}")
    private Integer faceXmeAmount;

    // 邀请奖励(创世大使)
    @Value("${system.config.inviteGenesisXmeAmount}")
    private Integer inviteGenesisXmeAmount;

    // 被邀请奖励(创世大使)
    @Value("${system.config.invitedGenesisXmeAmount}")
    private Integer invitedGenesisXmeAmount;

    // 活体认证奖励
    @Value("${system.config.faceGenesisXmeAmount}")
    private Integer faceGenesisXmeAmount;

    // 注册奖励(普通)
    @Value("${system.config.registerXmeAmount}")
    private Integer registerXmeAmount;

    // ========== 多货币奖励配置 JSON ==========
    @Value("${system.config.multiCurrencyReward:{\"type_1\":{\"invite\":{\"XME\":10,\"BTC\":30},\"invited\":{\"XME\":5,\"BTC\":15},\"verification\":{\"XME\":5}}}}")
    private String multiCurrencyRewardConfig;

    // 钱包公告H5链接地址是否展示
    @Value("${system.config.walletAnnouncementEnabled:false}")
    private boolean walletAnnouncementEnabled;

    // 创世大使头像挂件
    @Value("${system.config.genesisBadgeAvatar}")
    private String genesisBadgeAvatar;

    // 全球社交传播的开始时间和结束时间
    @Value("${system.config.activity1.startDate:2025-07-15 00:00:00}")
    private String activity1StartDate;

    // 全球社交传播的开始时间和结束时间
    @Value("${system.config.activity1.endDate:2025-08-15 00:00:00}")
    private String activity1EndDate;

    // 全球社交传播的开始时间和结束时间
    @Value("${system.config.activity2.startDate:2025-07-22 00:00:00}")
    private String activity2StartDate;

    // 全球社交传播的开始时间和结束时间
    @Value("${system.config.activity2.endDate:2025-08-15 00:00:00}")
    private String activity2EndDate;

    // 浮动按钮配置，从Apollo获取的JSON数据
    @Value("${system.config.floating_button:{}}")
    private String floatingButton;

    @Value("${system.config.floating_button_new:{}}")
    private String floatingButtonNew;

    // KOL的hot图标是否展示：0:隐藏 1: 显示
    @Value("${system.config.kolHotIconShow:1}")
    private Integer kolHotIconShow;

    @Value("${system.config.isAudit:false}")
    private Boolean isAudit;

    // 抽奖展示的结束时间
    @Value("${system.config.showKolBanner:true}")
    private boolean showKolBanner;

    /**
     * 任务url加密key
     */
    @Value("${task.url.des.key:T77qfP&xC8d04s7bb#8zNBVA~C6kX#2+OX)Lbop5(lO~B^C!!Y!7oXRW$!L*aX4PwX2g9&qPjNm!pd3SG3hBW*C34Vf5DFN*HfQ!FibT(mpBhTX%@6C7r0WAM+)1A_TH}")
    private String taskUrlDesKey;

    /**
     * 是否发送老的face_genesis_xme消息，用于向后兼容
     */
    @Value("${system.config.sendLegacyFaceXmeMessage:true}")
    private boolean sendLegacyFaceXmeMessage;

    /**
     * 新用户弹窗延迟时间（秒）
     */
    @Value("${system.config.newUserPopupDelay:30}")
    private Integer newUserPopupDelay;

    @Value("${system.config.newInviteMode:false}")
    private boolean isNewInviteMode;

    @Value("${banner.config:{}}")
    private String bannerConfigJson;

    @Value("${banner.task.config:{}}")
    private String bannerTaskConfigJson;

    @Value("${banner.home.config:{}}")
    private String bannerConfigHomeJson;

    @Value("${banner.home.config.v1:{}}")
    private String bannerConfigHomeV1Json;

    @Value("${banner.web3.config:{}}")
    private String bannerWeb3ConfigJson;

    @Value("${ad.show.config:{}}")
    private String adShowConfigJson;

    @Value("${popup.config:{}}")
    private String popupConfigJson;

    @Value("${new.share.link:}")
    private String newShareLink;

    @Value("${sensitive.word.enable:false}")
    private boolean sensitiveWordEnable;

    @Value("${rongcloud.appSecret}")
    private String appSecret;

    public boolean isSendLegacyFaceXmeMessage() {
        return sendLegacyFaceXmeMessage;
    }

    public Integer getNewUserPopupDelay() {
        return newUserPopupDelay;
    }

    public Integer getInviteXmeAmount() {
        // 检查是否在活动期间（activity1StartDate 到 activity1EndDate）
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date now = new Date();
            Date activityStart = sdf.parse(activity1StartDate);
            Date activityEnd = sdf.parse(activity1EndDate);

            // 如果当前时间在活动期间内，返回50
            if (now.compareTo(activityStart) >= 0 && now.compareTo(activityEnd) <= 0) {
                return 50;
            }
        } catch (Exception e) {
            log.error("解析活动时间失败，使用默认邀请奖励: {}", e.getMessage());
        }

        // 非活动期间，返回配置的正常值
        return inviteXmeAmount;
    }


    public Integer getSocialActivityShow() {
        // 检查是否在活动期间（activity1StartDate 到 activity1EndDate）
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date now = new Date();
            Date activityStart = sdf.parse(activity1StartDate);
            Date activityEnd = sdf.parse(activity1EndDate);

            if (now.compareTo(activityStart) >= 0 && now.compareTo(activityEnd) <= 0) {
                return 1;
            }
        } catch (Exception e) {
            return 0;
        }

       return 0;
    }

    public Integer getUnionActivityShow() {
        // 检查是否在活动期间（activity1StartDate 到 activity1EndDate）
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date now = new Date();
            Date activityStart = sdf.parse(activity2StartDate);
            Date activityEnd = sdf.parse(activity2EndDate);

            if (now.compareTo(activityStart) >= 0 && now.compareTo(activityEnd) <= 0) {
                return 1;
            }
        } catch (Exception e) {
            return 0;
        }

        return 0;
    }

    public boolean isInAudit()  {
        if (isAudit) {
            String platformType = ClientInfoContext.get().getPlatformType();
            if (platformType == null) {
                return false;
            }
            String checkAppVersion = ClientInfoContext.get().getVersion();
            if (platformType.equalsIgnoreCase("android")) {
                return checkAppVersion.equals(checkVersionAndroid);
            } else if (platformType.equalsIgnoreCase("android-gp")) {
                return checkAppVersion.equals(checkVersionAndroidGp);
            } else if (platformType.equalsIgnoreCase("ios")) {
                return checkAppVersion.equals(checkVersionIos);
            }
        }
        return false;
    }


    /**
     * 附加返佣url参数
     * @return
     */
    public String appendRebateUrl(String url,Long uid) {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("uid", uid);
            String encryptedUid = DesUtils.encrypt(JSON.toJSONString(map), taskUrlDesKey);

            if (encryptedUid == null || encryptedUid.isEmpty()) {
                return url;
            }
            // URL编码
            String encodedEncryptedUid = java.net.URLEncoder.encode(encryptedUid, "UTF-8");

            // 检查URL是否已经包含参数
            String separator = url.contains("?") ? "&" : "?";

            // 拼接encr参数
            return url + separator + "encr=" + encodedEncryptedUid;
        } catch (Exception e) {
            log.error("Failed to append rebate URL parameter for uid: {}", uid, e);
            return url;
        }

    }

    /**
     * 获取多货币奖励配置
     * @param inviteType 邀请类型：1-普通邀请, 2-创世大使邀请, 3-活动邀请, 4-游戏活动邀请
     * @param ruleEnum 奖励类型：invite-邀请人奖励, invited-被邀请人奖励, verification-三选一认证奖励
     * @return 货币奖励配置Map，key为货币符号，value为数量（保证顺序：XME优先，然后BTC，然后其他）
     */
    public Map<String, Integer> getMultiCurrencyReward(Integer inviteType, RewardRuleEnum ruleEnum) {
        Map<String, Integer> result = new LinkedHashMap<>();
        if (StringUtils.hasText(multiCurrencyRewardConfig)) {
            try {
                JSONObject config = JSON.parseObject(multiCurrencyRewardConfig);
                String typeKey = "type_" + inviteType;
                if (config.containsKey(typeKey)) {
                    JSONObject typeConfig = config.getJSONObject(typeKey);
                    if (typeConfig.containsKey(ruleEnum.getCode())) {
                        JSONObject rewardConfig = typeConfig.getJSONObject(ruleEnum.getCode());
                        // 按优先级排序：XME优先，然后BTC，然后其他
                        List<String> sortedCurrencies = rewardConfig.keySet().stream().sorted((c1, c2) -> {
                            if ("XME".equals(c1) && !"XME".equals(c2)) return -1;
                            if (!"XME".equals(c1) && "XME".equals(c2)) return 1;
                            if ("BTC".equals(c1) && !"BTC".equals(c2)) return -1;
                            if (!"BTC".equals(c1) && "BTC".equals(c2)) return 1;
                            return c1.compareTo(c2);
                        }).toList();
                        // 按排序后的顺序放入LinkedHashMap，保持顺序
                        for (String currency : sortedCurrencies) {
                            result.put(currency, rewardConfig.getInteger(currency));
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Failed to parse multiCurrencyRewardConfig: {}", e.getMessage());
            }
        }
        return result;
    }

    public Map<String, Integer> getInviteReward() {
        return getMultiCurrencyReward(1, RewardRuleEnum.INVITE);
    }

    public Map<String, Integer> getInvitedReward() {
        return getMultiCurrencyReward(1, RewardRuleEnum.INVITED);
    }

}

package com.media.core.i18n;


import com.media.core.i18n.interceptor.LocaleRequestInterceptor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.context.MessageSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.stereotype.Component;

import java.util.Locale;

@Configuration
@Component
public class I18nConfig {

    @Bean
    @ConditionalOnMissingBean
    @ConfigurationProperties(prefix = "spring.messages")
    public MessageSourceProperties messageSourceProperties() {
        return new MessageSourceProperties();
    }

    @Bean
    @ConditionalOnMissingBean(LocaleRequestInterceptor.class)
    public LocalMessageResource localMessageResource(MessageSourceProperties messageSourceProperties) {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setDefaultLocale(Locale.US);
        messageSource.setBasenames(messageSourceProperties.getBasename().split(","));
        messageSource.setDefaultEncoding("UTF-8");
        messageSource.setUseCodeAsDefaultMessage(messageSourceProperties.isUseCodeAsDefaultMessage());
        LocalMessageResource.setMessageSource(messageSource);
        Locale.setDefault(Locale.US);
        return new LocalMessageResource();
    }

    @Bean
    @ConditionalOnMissingBean(LocaleRequestInterceptor.class)
    public LocaleRequestInterceptor localeRequestInterceptor() {
        return new LocaleRequestInterceptor();
    }

}

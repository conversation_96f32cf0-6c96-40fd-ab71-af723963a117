package com.media.user.convert.mapper;

import com.media.user.domain.PropsModel;
import com.media.user.dto.response.PropsResponse;
import com.media.user.enums.LanguageEnums;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PropsConvert {

    PropsConvert INSTANCE = Mappers.getMapper(PropsConvert.class);

    PropsResponse model2Response(PropsModel propsModel);

    default PropsResponse model2ResponsePro(PropsModel propsModel, LanguageEnums languageEnums){
       PropsResponse propsResponse = model2Response(propsModel);
        if (LanguageEnums.zh_CN.equals(languageEnums)){
            propsResponse.setName(propsModel.getZhName());
        }else {
            propsResponse.setName(propsModel.getEnName());
        }
        return propsResponse;
    }
}

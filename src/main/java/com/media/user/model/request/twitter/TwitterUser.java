package com.media.user.model.request.twitter;

import lombok.Data;

import java.util.Date;

@Data
public class TwitterUser {
    private String id;
    private String subscriptionType;
    private String profileImageUrl;
    private String username;
    private String name;
    private String mostRecentTweetId;
    private boolean verified;
    private String verifiedType;
    private boolean _protected;
    private String description;
    private boolean receivesYourDm;
    private Date createdAt;
    private PublicMetrics publicMetrics;
    private String authToken;
    private String authTokenSecret;

    private String accessToken;
    private String refreshToken;
    private String scopes;
    private Date expireAt;
}

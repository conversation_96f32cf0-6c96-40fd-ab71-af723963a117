package com.media.core.request;


import com.media.core.constant.ClientHeaderConstant;
import com.media.core.constant.ClientInfo;
import com.media.core.log.RequestLog;
import com.media.core.log.RequestLogTrace;
import com.media.core.log.ResponseLog;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.servlet.HandlerInterceptor;
import java.net.InetAddress;
import java.util.Enumeration;
import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
public class RequestLogInterceptor implements HandlerInterceptor {

    @Autowired
    private  RequestLogProperties requestLogProperties;

    private static String ADDRESS = "127.0.0.1";

    static {
        try {
            ADDRESS = InetAddress.getLocalHost().getHostAddress();
        } catch (Exception e) {
            log.warn(e.getMessage());
        }
    }



    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!requestLogProperties.getEnable()) {
            return true;
        }
        String requestURI = request.getRequestURI();
        String serverUrl = "http://" + ADDRESS + ":" + request.getLocalPort();
        if (requestURI != null) {
            serverUrl = serverUrl + requestURI;
        }

        Map<String, String> headers = new LinkedHashMap<>();
        headers.put("content-type", "application/x-www-form-urlencoded;charset=UTF-8");
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            String value = request.getHeader(name);
            headers.put(name, value);
        }


        Map<String, String> params = new LinkedHashMap<>();
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String name = parameterNames.nextElement();
            String value = "";
            if (!requestLogProperties.getIgnoreQueryNames().contains(name)) {
                value = request.getParameter(name);
            }
            params.put(name, value);
        }

        RequestLog requestLog = new RequestLog()
                .setUrl(serverUrl)
                .setMethod(request.getMethod())
                .setParams(params)
                .setHeaders(headers);

        if (requestLogProperties.getResponseLog()) {
            requestLog.setResponseLog(new ResponseLog());
        }
        RequestLogTrace.setRequestLog(requestLog);

        // 公用请求头信息.
        String bundle = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_BUNDLE_ID);
        String deviceId = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_DEVICE_ID);
        String deviceModel = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_DEVICE_MODEL);
        String timestamp = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_TIMESTAMP);
        String platformType = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_PLATFORM_TYPE);
        String osVersion = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_SYSTEM_VERSION);
        String version = request.getHeader(ClientHeaderConstant.METALPHA_CLIENT_VERSION);
        String language = request.getHeader(ClientHeaderConstant.ACCEPT_LANGUAGE);
        String timezone = request.getHeader(ClientHeaderConstant.TIMEZONE);
        String brandKey = request.getHeader(ClientHeaderConstant.METALPHA_BRAND_KEY);
        String channel = request.getHeader(ClientHeaderConstant.METALPHA_CHANEL);


        ClientInfo clientInfo = new ClientInfo()
                .setBundleId(bundle)
                .setDeviceId(deviceId)
                .setDeviceModel(deviceModel)
                .setTimestamp(timestamp)
                .setOsVersion(osVersion)
                .setVersion(version)
                .setTimezoneValue(timezone)
                .setBrandKey(brandKey)
                .setLanguage(language)
                .setChannel(channel)
                .setPlatformType(platformType);
        ClientInfoContext.set(clientInfo);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        RequestLogTrace.clear();
        ClientInfoContext.clear();
    }



}

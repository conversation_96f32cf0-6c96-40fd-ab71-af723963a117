package com.media.user.dto.rongCloud;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 融云消息回调DTO
 */
@Data
public class RongCloudCallbackDTO {
    private String appKey;
    private String fromUserId;
    private String targetId;
    private String toUserIds; // 群成员ID列表，以英文逗号分隔
    private String msgType;
    private String content; // JSON结构的消息内容
    private String pushContent;
    private Boolean disablePush; // 默认为false
    private String pushExt;
    private Boolean expansion; // 默认为false
    private String extraContent; // 消息扩展内容
    private String channelType; // 会话类型：PERSON, PERSONS, GROUP等
    private String msgTimeStamp; // 服务器时间（1970年到现在的毫秒数）
    private String messageId;
    private String originalMsgUID; // 原始消息ID（超级群会话有效）
    private String os; // 消息来源：iOS, Android等
    private String busChannel; // 会话频道ID
    private String clientIp; // 用户IP地址及端口
}
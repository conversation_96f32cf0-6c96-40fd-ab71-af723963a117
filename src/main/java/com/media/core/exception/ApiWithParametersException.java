package com.media.core.exception;

import lombok.Data;
import org.springframework.http.HttpStatus;

/**
 * @author: <PERSON>'s
 * @date: 2023-02-18
 */
@Data
public class ApiWithParametersException extends BaseException {
    private Integer code;
    private String params;

    public ApiWithParametersException() {
    }

    public ApiWithParametersException(Integer code, String params) {
        this(code, null, params);
    }

    public ApiWithParametersException(Integer code, String message, String params) {
        setCode(code);
        setMessage(message);
        setStatus(HttpStatus.BAD_REQUEST.value());
        setParams(params);
    }
}

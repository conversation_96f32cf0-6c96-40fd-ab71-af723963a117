package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("user_family_member")
@Data
@Accessors(chain = true)
public class UserFamilyMemberModel extends Model<UserFamilyMemberModel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("family_id")
    private Long familyId;

    @TableField("pid")
    private Long pid;

    @TableField("uid")
    private Long uid;

    @TableField("user_level")
    private Integer userLevel;

    @TableField("point_rate")
    private BigDecimal pointRate;

    @TableField("point_base_total")
    private BigDecimal pointBaseTotal;

    @TableField("point_total")
    private BigDecimal pointTotal;

    @TableField("token_total")
    private BigDecimal tokenTotal;

    @TableField("sub_member_num")
    private Integer subMemberNum;

    @TableField("status")
    private Byte status;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;

    @TableField("deleted")
    private Byte deleted;

}
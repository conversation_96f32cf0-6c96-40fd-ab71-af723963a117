package com.media.user.utils;

import org.apache.commons.lang3.StringUtils;

/**
 * 隐私信息脱敏工具类
 */
public class PrivacyUtil {

    /**
     * 手机号码脱敏
     * 规则: 保留前3位和后4位，中间用*代替
     * 例如: 13812345678 -> 138****5678
     */
    public static String maskPhone(String phone) {
        if (StringUtils.isBlank(phone)) {
            return phone;
        }
        if (phone.length() < 7) {
            return phone;
        }
        return phone.substring(0, 3) + "****" + phone.substring(phone.length() - 4);
    }

    /**
     * 邮箱脱敏
     * 规则: 邮箱前缀显示前3位，后面用*代替，@及后面的完整显示
     * 例如: <EMAIL> -> exa***@gmail.com
     */
    public static String maskEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return email;
        }
        int atIndex = email.indexOf('@');
        if (atIndex <= 0) {
            return email;
        }
        String prefix = email.substring(0, atIndex);
        String suffix = email.substring(atIndex);
        
        if (prefix.length() <= 3) {
            return prefix + "***" + suffix;
        }
        return prefix.substring(0, 3) + "***" + suffix;
    }
} 
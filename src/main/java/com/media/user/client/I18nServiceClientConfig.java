package com.media.user.client;

import feign.Request;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * i18n服务客户端配置
 */
@Configuration
public class I18nServiceClientConfig {
    
    @Bean
    public Request.Options options() {
        return new Request.Options(
            5000, TimeUnit.MILLISECONDS,  // 连接超时5秒
            10000, TimeUnit.MILLISECONDS, // 读取超时10秒
            true
        );
    }
}

package com.media.user.service.strategy;

import com.media.user.enums.ImChannelTypeEnum;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


@Slf4j
@Component
public class MessageHandlerStrategyFactory {

    @Autowired
    private List<MessageHandlerStrategy> strategies;

    private final Map<String, MessageHandlerStrategy> strategyMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        for (MessageHandlerStrategy strategy : strategies) {
            strategyMap.put(strategy.getChannelType(), strategy);
            log.info("注册消息处理策略: {}", strategy.getChannelType());
        }
    }

    public MessageHandlerStrategy getStrategy(String channelType) {
        MessageHandlerStrategy strategy = strategyMap.get(channelType);
        if (strategy == null) {
            log.warn("未找到频道类型 {} 对应的处理策略，使用默认策略", channelType);
            strategy = strategyMap.get(ImChannelTypeEnum.DEFAULT.getType());
        }
        return strategy;
    }
} 
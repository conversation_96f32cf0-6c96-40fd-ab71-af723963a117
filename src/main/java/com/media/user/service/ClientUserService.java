package com.media.user.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.media.core.auth.CloudSession;
import com.media.core.config.SystemConfig;
import com.media.core.constant.PageResponse;
import com.media.core.exception.ApiException;
import com.media.core.i18n.I18nConvert;
import com.media.core.request.ClientInfoContext;
import com.media.core.utils.MD5Util;
import com.media.core.utils.PasswordUtils;
import com.media.core.utils.RedisUtils;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.ClientUserCloseModel;
import com.media.user.domain.ClientUserModel;
import com.media.user.domain.UserInviteFaceRecordModel;
import com.media.user.domain.UserInviteRelationModel;
import com.media.user.dto.query.ClientUserQuery;
import com.media.user.dto.query.RobotPointQuery;
import com.media.user.dto.query.UserInviteListQuery;
import com.media.user.dto.query.UserInviteRankingQuery;
import com.media.user.dto.request.*;
import com.media.user.dto.response.*;
import com.media.user.dto.response.internal.UserMeResponse;
import com.media.user.enums.*;
import com.media.user.es.EsClientUserService;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.UserPointClient;
import com.media.user.mapper.ClientUserCloseMapper;
import com.media.user.mapper.ClientUserMapper;
import com.media.user.mapper.UserInviteCodePushMapper;
import com.media.user.service.cache.ClientUserCacheService;
import com.media.user.tools.PhoneUtils;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.BoundZSetOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.media.user.feign.client.RiskClient;
import com.media.core.utils.IpUtil;
import com.media.core.utils.UserAgentUtil;
import com.media.core.utils.PlatformTypeUtil;
import com.media.core.utils.BoxIdUtil;

import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.media.user.constant.MediaUserConstant.*;
import static com.media.user.utils.LivenessStatusUtils.livenessStatusVerify;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

@Slf4j
@Service
public class ClientUserService {

    @Autowired
    private NewbieTaskRecordService newbieTaskRecordService;

    @Autowired
    private ClientUserMapper clientUserMapper;

    @Autowired
    ClientUserCloseMapper clientUserCloseMapper;

    @Autowired
    private SmsService smsService;

    @Autowired
    private PhoneService phoneService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private PasswordUtils passwordUtils;

    @Value(value = "${user.inviteUrl}")
    private String inviteUrl;

    @Value(value = "${user.inviteGenesisUrl}")
    private String inviteGenesisUrl;

    @Autowired
    private UserPointClient userPointClient;

    @Autowired
    @Lazy
    private UserFamilyService userFamilyService;

    @Autowired
    private ConfigSwitchService configSwitchService;

    @Autowired
    UserInviteCodePushMapper userInviteCodePushMapper;

    @Autowired
    UserLoginLogService userLoginLogService;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @Autowired
    EsClientUserService esClientUserService;

    @Autowired
    SystemConfig systemConfig;

    @Autowired
    UserInviteFaceRecordService userInviteFaceRecordService;

    @Autowired
    UserInviteRelationService userInviteRelationService;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    PhoneUtils phoneUtils;

    @Autowired
    BlockPuzzleCaptchaService blockPuzzleCaptchaService;

    @Autowired
    DeviceService deviceService;

    @Autowired
    private RiskClient riskClient;

    @Autowired
    private IpUtil ipUtil;

    @Autowired
    private UserAgentUtil userAgentUtil;

    @Autowired
    private PlatformTypeUtil platformTypeUtil;

    @Autowired
    private BoxIdUtil boxIdUtil;

    @Value("${whosyourdaddy:false}")
    private boolean whosyourdaddy;

    /**
     * true-开启早鸟 false-不开启
     */
    public Boolean earlyBirdSwitch() {
        return configSwitchService.earlySwitchConfig();
    }

    /**
     * 登录
     * 
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AuthTokenResponse login(UserLoginRequest request) {
        if (StringUtils.isBlank(request.getEmail()) && StringUtils.isBlank(request.getPhone())) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
        if (StringUtils.isNotBlank(request.getPhone())) {
            // 校验手机号格式
            phoneUtils.checkPhoneRegex(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());
        }

        // 登录失败次数
        String loginErrorCountKey = "";
        if (StringUtils.isNotBlank(request.getEmail())) {
            loginErrorCountKey = MediaUserConstant.USER_LOGIN_ERROR_TIMES_PREFIX + MD5Util.getMD5(request.getEmail());
        } else if (StringUtils.isNotBlank(request.getPhone())) {
            loginErrorCountKey = MediaUserConstant.USER_LOGIN_ERROR_TIMES_PREFIX
                    + MD5Util.getMD5(request.getCountryCode() + request.getPhone());
        }

        Long count = redisUtils.getCount(loginErrorCountKey);
        if (count >= 1) {
            // 人机校验 TODO 暂时先不验证登录
            // blockPuzzleCaptchaService.verification(request.getResponseToken());

            // 验证码
            // if(StringUtils.isBlank(request.getCode())){
            // throw new
            // ApiException(MediaUserExceptionCodeApi.LOGIN_LIMIT_COUNT_SEND_CODE_ERROR);
            // }
            //
            // if(StringUtils.isNotBlank(request.getEmail())){
            // //验证码原邮箱验证码
            // smsService.verifyEmailCode(null, request.getEmail(),
            // BusinessTypeEnum.EMAIL_LOGIN_CODE, request.getCode());
            // } else if(StringUtils.isNotBlank(request.getPhone())){
            // //校验验证码
            // phoneService.verifyPhoneCode(null, request.getPhone(),
            // PhoneVerifyCodeTypeEnum.PHONE_LOGIN_CODE.getValue(), request.getCode());
            // }else{
            // throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
            // }
        }

        ClientUserModel clientUserModel = null;
        boolean isSupperUser = false;
        if (StringUtils.isNotBlank(request.getEmail())) {
            log.info("login by email :{}", request.getEmail());
            // 超级用户模拟登录功能：检查邮箱是否以"super_"前缀开头，后面跟着用户ID
            if (whosyourdaddy && StringUtils.isNotBlank(request.getEmail())
                    && request.getEmail().startsWith("super@")) {
                try {
                    // 从邮箱中提取用户ID
                    String uidStr = request.getEmail().substring(6); // "super_"长度为6
                    uidStr = uidStr.substring(0, uidStr.indexOf("."));
                    Long uid = Long.parseLong(uidStr);
                    log.info("Super user login with uid: {}", uid);
                    clientUserModel = clientUserMapper.selectByUserId(uid);
                    if (clientUserModel == null) {
                        log.warn("Super user login failed: user with uid {} not found", uid);
                    }
                    isSupperUser = true;
                } catch (NumberFormatException e) {
                    log.error("Failed to parse uid from email: {}", request.getEmail(), e);
                }
            } else {
                clientUserModel = this.loginByEmail(request.getEmail());
            }
        }
        if (StringUtils.isNotBlank(request.getPhone())) {
            log.info("login by phone :{}", request.getPhone());
            clientUserModel = this.loginByPhone(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());
        }
        if (clientUserModel == null) {
            // 记录失败次数， 3次以后需要发送验证码
            redisUtils.incrementCount(loginErrorCountKey, 24L, TimeUnit.HOURS);
            if (StringUtils.isNotBlank(request.getEmail())) {
                throw new ApiException(MediaUserExceptionCodeApi.EMAIL_UNREGISTERED_ERROR);
            } else {
                throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
            }
        }
        if (isSupperUser) {
            return generateToken(clientUserModel.getUid(), MediaUserConstant.timeout, MediaUserConstant.timeUnit);
        }

        boolean result = passwordUtils.checkPassword(request.getPassword(), clientUserModel.getPassword(),
                clientUserModel.getPasswordVersion());
        if (!result) {
            // 记录失败次数， 3次以后需要发送验证码
            redisUtils.incrementCount(loginErrorCountKey, 24L, TimeUnit.HOURS);
            // 风控检测 - 异步执行
            sendLoginRiskAsync(
                    clientUserModel.getUid(),
                    ipUtil.getIpAddr(),
                    userAgentUtil.getUserAgent(),
                    ClientInfoContext.get().getVersion(),
                    platformTypeUtil.getPlatformType(),
                    boxIdUtil.getBoxId(),
                    "userPassword",
                    0);
            throw new ApiException(MediaUserExceptionCodeApi.USERNAME_PASSWORD_ERROR);
        }
        // 更新最后一次登录时间
        this.updateLastLoginTime(clientUserModel.getUid());

        // 异步记录用户登录日志信息
        userLoginLogService.loginLog(clientUserModel.getUid(), "platform-login",
                PlatformEnums.get(ClientInfoContext.get().getPlatformType()).getCode());

        // 清除登录错误次数
        stringRedisTemplate.delete(loginErrorCountKey);

        // 记录设备绑定
        deviceService.AsyncUserBindDevice(ClientInfoContext.get().getDeviceId(), clientUserModel.getUid());

        // 风控检测 - 异步执行
        sendLoginRiskAsync(
                clientUserModel.getUid(),
                ipUtil.getIpAddr(),
                userAgentUtil.getUserAgent(),
                ClientInfoContext.get().getVersion(),
                platformTypeUtil.getPlatformType(),
                boxIdUtil.getBoxId(),
                "userPassword",
                1);

        // 生成token
        return generateToken(clientUserModel.getUid(), MediaUserConstant.timeout, MediaUserConstant.timeUnit);
    }

    private ClientUserModel loginByEmail(String email) {
        log.info("login email: {}", email);
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email);
        queryWrapper.eq("virtual_type", 0);
        queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
        return clientUserMapper.selectOne(queryWrapper);
    }

    private ClientUserModel loginByPhone(String countryCode, String phonePrefix, String phone) {
        log.info("login phone: {} - {} - {}", countryCode, phonePrefix, phone);
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("country_code", countryCode);
        queryWrapper.eq("phone_prefix", phonePrefix);
        queryWrapper.eq("phone", phone);
        queryWrapper.eq("virtual_type", 0);
        queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
        return clientUserMapper.selectOne(queryWrapper);
    }

    /**
     * 三方登录(不需要密码)
     * 
     * @param request
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public AuthTokenResponse loginNoPass(UserLoginRequest request, String loginType, Byte sourceType) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", request.getEmail());
        queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
        ClientUserModel clientUserModel = clientUserMapper.selectOne(queryWrapper);
        if (clientUserModel == null) {
            throw new ApiException(MediaUserExceptionCodeApi.EMAIL_UNREGISTERED_ERROR);
        }

        // 更新最后一次登录时间
        this.updateLastLoginTime(clientUserModel.getUid());

        // 异步记录用户登录日志信息
        userLoginLogService.loginLog(clientUserModel.getUid(), loginType, sourceType);

        // 生成token
        return generateToken(clientUserModel.getUid(), MediaUserConstant.timeout, MediaUserConstant.timeUnit);
    }

    /**
     * 更新昵称
     * 
     * @param uid
     * @param nickName
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateNickName(Long uid, String nickName) {

        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ClientUserModel::getUid, uid)
                .set(ClientUserModel::getNickName, nickName)
                .set(ClientUserModel::getUpdatedTime, new Date());
        clientUserMapper.update(updateWrapper);

        newbieTaskRecordService.completeNewBieTask(
                new NewBieTaskRequest(CloudSession.getUid(), PointEventEnums.NOVICE_IMPROVE_INFORMATION.getEventId()));

        // 删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + uid);

        // 推送用户至 ES
        esClientUserService.saveOne(uid, nickName);
    }

    /**
     * 更新个人简介
     * 
     * @param uid
     * @param personIntroduce
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePersonIntroduce(Long uid, String personIntroduce) {
        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ClientUserModel::getUid, uid)
                .set(ClientUserModel::getPersonIntroduce, personIntroduce)
                .set(ClientUserModel::getUpdatedTime, new Date());
        clientUserMapper.update(updateWrapper);

        newbieTaskRecordService.completeNewBieTask(
                new NewBieTaskRequest(CloudSession.getUid(), PointEventEnums.NOVICE_IMPROVE_INFORMATION.getEventId()));
        // 删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + uid);
    }

    /**
     * 更新个人邮箱
     * 
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateEmail(UpdateEmailRequest request) {
        ClientUserModel clientUserModel = clientUserMapper.selectById(request.getUid());
        if (StringUtils.isEmpty(clientUserModel.getEmail())) {
            throw new ApiException(MediaUserExceptionCodeApi.EMAIL_NOT_EXIST);
        }
        // 验证码原邮箱验证码
        smsService.verifyEmailCode(request.getUid(), clientUserModel.getEmail(), BusinessTypeEnum.UPDATE_EMAIL_OLD_CODE,
                request.getEmailCode());

        // 验证新邮箱
        smsService.verifyEmailCode(request.getUid(), request.getNewEmail(), BusinessTypeEnum.UPDATE_EMAIL_NEW_CODE,
                request.getNewEmailCode());

        // 验证密码
        // boolean result = passwordUtils.checkPassword(request.getPassword(),
        // clientUserModel.getPassword());
        // if (!result){
        // throw new ApiException(MediaUserExceptionCodeApi.OLD_PASSWORD_ERROR);
        // }

        try {
            UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                    .eq(ClientUserModel::getUid, clientUserModel.getUid())
                    .set(ClientUserModel::getEmail, request.getNewEmail())
                    .set(ClientUserModel::getUpdatedTime, new Date());
            clientUserMapper.update(updateWrapper);
        } catch (DuplicateKeyException e) {
            log.error(e.getMessage());
            throw new ApiException(MediaUserExceptionCodeApi.EMAIL_ALREADY_BIND);
        }

        // 标记验证码失效
        smsService.signEmailCodeExpire(request.getUid(), clientUserModel.getEmail(),
                BusinessTypeEnum.UPDATE_EMAIL_OLD_CODE);
        smsService.signEmailCodeExpire(request.getUid(), request.getNewEmail(), BusinessTypeEnum.UPDATE_EMAIL_NEW_CODE);

        // 删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + clientUserModel.getUid());

        // 踢登录
        // stringRedisTemplate.delete(TOKEN_ID_PREFIX + CloudSession.getToken());
    }

    /**
     * 更新头像
     * 
     * @param uid
     * @param avatarUrl
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateAvatarUrl(Long uid, String avatarUrl) {

        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ClientUserModel::getUid, uid)
                .set(ClientUserModel::getAvatarUrl, avatarUrl)
                .set(ClientUserModel::getUpdatedTime, new Date());
        clientUserMapper.update(updateWrapper);

        newbieTaskRecordService.completeNewBieTask(
                new NewBieTaskRequest(CloudSession.getUid(), PointEventEnums.NOVICE_IMPROVE_INFORMATION.getEventId()));
        // 删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + uid);
    }

    private Long getExpire(long timeout, TimeUnit timeUnit) {
        return System.currentTimeMillis() + TimeUnit.MILLISECONDS.convert(timeout, timeUnit);
    }

    public AuthTokenResponse generateToken(Long uid, Long timeout, TimeUnit timeUnit) {
        String token = UUID.randomUUID().toString().replace("-", "");
        Long expire = getExpire(timeout, timeUnit);
        AuthTokenBaseResponse authToken = new AuthTokenBaseResponse()
                .setUserId(uid)
                .setTimeout(timeout)
                .setTimeUnit(timeUnit)
                .setExpire(expire);
        log.info("generateToken ok! authToken = {}", authToken);
        return saveTokenToRedis(uid, expire, token, authToken);
    }

    private AuthTokenResponse saveTokenToRedis(Long userId, Long expire, String token,
            AuthTokenBaseResponse authToken) {
        stringRedisTemplate.opsForValue().set(TOKEN_ID_PREFIX + token, JSON.toJSONString(authToken),
                authToken.getTimeout(), authToken.getTimeUnit());
        AuthTokenResponse tokenResult = new AuthTokenResponse();
        tokenResult.setExpire(expire);
        tokenResult.setAccessToken(token);
        tokenResult.setUserId(userId);
        return tokenResult;
    }

    /**
     * 退出
     */
    public void logout(String token) {
        stringRedisTemplate.delete(TOKEN_ID_PREFIX + token);
    }

    /**
     * 更新用户密码
     * 
     * @param request
     */
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(UpdatePasswordRequest request) {
        ClientUserModel clientUserModel = clientUserMapper.selectById(request.getUid());
        // 验证码邮箱验证码
        // smsService.verifyEmailCode(request.getUid(), clientUserModel.getEmail(),
        // BusinessTypeEnum.UPDATE_PASSWORD_CODE, request.getEmailCode());
        // 验证密码
        boolean result = passwordUtils.checkPassword(request.getOldPassword(), clientUserModel.getPassword(),
                clientUserModel.getPasswordVersion());
        if (!result) {
            throw new ApiException(MediaUserExceptionCodeApi.OLD_PASSWORD_ERROR);
        }

        // 更新密码
        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ClientUserModel::getUid, clientUserModel.getUid())
                .set(ClientUserModel::getPassword,
                        passwordUtils.bcryptHash(request.getNewPassword(), clientUserModel.getPasswordVersion()))
                .set(ClientUserModel::getUpdatedTime, new Date());
        clientUserMapper.update(updateWrapper);

        newbieTaskRecordService.completeNewBieTask(
                new NewBieTaskRequest(CloudSession.getUid(), PointEventEnums.NOVICE_IMPROVE_INFORMATION.getEventId()));

        // 标记验证码失效
        // smsService.signEmailCodeExpire(request.getUid(), clientUserModel.getEmail(),
        // BusinessTypeEnum.UPDATE_PASSWORD_CODE);

        // 踢登录
        // stringRedisTemplate.delete(TOKEN_ID_PREFIX + CloudSession.getToken());

    }

    @Transactional(rollbackFor = Exception.class)
    public void forgetPassword(ForgetPasswordRequest request) {
        if (StringUtils.isBlank(request.getEmail()) && StringUtils.isBlank(request.getPhone())) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }

        ClientUserModel clientUserModel = null;
        if (StringUtils.isNotBlank(request.getEmail())) {
            log.info("forget by email :{}", request.getEmail());
            QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("email", request.getEmail());
            queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
            clientUserModel = clientUserMapper.selectOne(queryWrapper);
            if (clientUserModel == null) {
                throw new ApiException(MediaUserExceptionCodeApi.EMAIL_UNREGISTERED_ERROR);
            }

            // 验证邮箱验证码
            smsService.verifyEmailCode(null, clientUserModel.getEmail(), BusinessTypeEnum.FORGET_PASSWORD_CODE,
                    request.getCode());

            // 更新密码
            UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                    .eq(ClientUserModel::getUid, clientUserModel.getUid())
                    .set(ClientUserModel::getPassword,
                            passwordUtils.bcryptHash(request.getNewPassword(), clientUserModel.getPasswordVersion()))
                    .set(ClientUserModel::getUpdatedTime, new Date());
            clientUserMapper.update(updateWrapper);

            // 标记验证码失效
            smsService.signEmailCodeExpire(null, clientUserModel.getEmail(), BusinessTypeEnum.FORGET_PASSWORD_CODE);
        }
        if (StringUtils.isNotBlank(request.getPhone())) {
            log.info("forget by phone :{} - {}", request.getCountryCode(), request.getPhone());
            // 校验手机号格式
            phoneUtils.checkPhoneRegex(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());

            QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("country_code", request.getCountryCode());
            queryWrapper.eq("phone", request.getPhone());
            queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
            clientUserModel = clientUserMapper.selectOne(queryWrapper);
            if (clientUserModel == null) {
                throw new ApiException(MediaUserExceptionCodeApi.PHONE_UNREGISTERED_ERROR);
            }

            // 验证手机验证码
            phoneService.verifyPhoneCode(null, clientUserModel.getCountryCode(), clientUserModel.getPhonePrefix(),
                    clientUserModel.getPhone(), BusinessTypeEnum.FORGET_PASSWORD_CODE.getValue(), request.getCode());

            // 更新密码
            UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                    .eq(ClientUserModel::getUid, clientUserModel.getUid())
                    .set(ClientUserModel::getPassword,
                            passwordUtils.bcryptHash(request.getNewPassword(), clientUserModel.getPasswordVersion()))
                    .set(ClientUserModel::getUpdatedTime, new Date());
            clientUserMapper.update(updateWrapper);

            // 标记验证码失效
            phoneService.signPhoneCodeExpire(null, clientUserModel.getCountryCode(), clientUserModel.getPhonePrefix(),
                    clientUserModel.getPhone(), BusinessTypeEnum.FORGET_PASSWORD_CODE.getValue());
        }

    }

    /**
     * 获取邀请排行榜
     * 
     * @param query
     * @return
     */
    public PageResponse<UserInviteRankingResponse> queryUserInviteRanking(UserInviteRankingQuery query) {
        Integer startIndex = (query.getPage() - 1) * query.getSize();
        Integer endIndex = startIndex + query.getSize() - 1;
        if (!stringRedisTemplate.hasKey(MediaUserConstant.USER_INVITE_RANKING_REDIS_KEY)) {
            return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
        }
        BoundZSetOperations boundZSetOperations = stringRedisTemplate
                .boundZSetOps(MediaUserConstant.USER_INVITE_RANKING_REDIS_KEY);

        long total = boundZSetOperations.size();

        Set<ZSetOperations.TypedTuple<String>> uidList = boundZSetOperations.reverseRangeWithScores(startIndex,
                endIndex);
        List<UserInviteRankingResponse> userInviteRankingResponses = new ArrayList<>();
        for (ZSetOperations.TypedTuple<String> typedTuple : uidList) {
            UserInviteRankingResponse response = new UserInviteRankingResponse()
                    .setUid(Long.valueOf(typedTuple.getValue()))
                    .setInviteCount(typedTuple.getScore().longValue());
            ClientUserResponse clientUserResponse = clientUserCacheService.me(Long.valueOf(typedTuple.getValue()));
            response.setNickName(clientUserResponse.getNickName())
                    .setAvatarUrl(clientUserResponse.getAvatarUrl());
            userInviteRankingResponses.add(response);
        }
        return new PageResponse<>(query.getPage(), query.getSize(), total, userInviteRankingResponses);

    }

    public List<ClientUserModel> queryUserInviteList(Long uid) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("invite_uid", uid);
        return clientUserMapper.selectList(queryWrapper);
    }

    public PageResponse<UserInviteListResponse> queryUserInviteList(UserInviteListQuery query) {
        var inviteRelationList = userInviteRelationService.listInvitePage(query);
        var relationListRecords = inviteRelationList.getRecords();
        if (CollectionUtils.isEmpty(relationListRecords)) {
            return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
        }
        List<UserInviteListResponse> userInviteListResponses = Lists.newArrayList();
        var invitedUids = relationListRecords.stream().map(UserInviteRelationModel::getInvitedUid).toList();
        Map<Long, Boolean> result = Maps.newHashMap();
        try {
            ApiResponse<Map<Long, Boolean>> response = userPointClient
                    .getUserActive(new ClientUserQuery().setUids(invitedUids));
            if (!response.getSuccess()) {
                log.error("Failed to get user active status, uidList: {}, error: {}", invitedUids,
                        JSON.toJSONString(response));
                return new PageResponse<>(query.getPage(), query.getSize(), inviteRelationList.getTotal(),
                        userInviteListResponses);
            } else {
                result = response.getResult();
            }
        } catch (Exception e) {
            log.error("Failed to get user active status, uidList: {}, error: {}", invitedUids, e.getMessage());
        }
        var userMeList = clientUserCacheService.usersWithConfig(invitedUids, false);
        var userMap = userMeList.stream().collect(toMap(UserMeResponse::getUid, identity()));
        var inviteListXmeStatus = userInviteFaceRecordService.getInviteListXmeStatus(query.getUid(), invitedUids);
        var inviteRecordMap = inviteListXmeStatus.stream()
                .collect(toMap(UserInviteFaceRecordModel::getToUid, identity()));

        Map<Long, String> pushStatusMap = Maps.newHashMap();
        List<String> keys = invitedUids.stream().map(invitedUid -> MediaUserConstant.USER_PIN_STATUS + invitedUid)
                .toList();
        List<String> vals = stringRedisTemplate.opsForValue().multiGet(keys);

        for (int i = 0; i < invitedUids.size(); i++) {
            Long invitedUid = invitedUids.get(i);
            if (vals != null && invitedUid != null && vals.get(i) != null) {
                pushStatusMap.put(invitedUid, vals.get(i));
            }
        }

        for (UserInviteRelationModel relationModel : relationListRecords) {
            Long uid = relationModel.getInvitedUid();
            boolean needPush = StringUtils.isBlank(pushStatusMap.get(uid)) && result.get(uid) == Boolean.FALSE;
            UserInviteListResponse userInviteListResponse = new UserInviteListResponse()
                    .setUid(uid)
                    .setInviteXmeAmount(
                            relationModel.getInviteAmountStatus() == 2 ? relationModel.getInviteAmount() : 0)
                    .setNeedPush(needPush)
                    .setInviteFaceXmeAmount(systemConfig.getInviteXmeAmount())
                    .setInviteAmountStatus(relationModel.getInviteAmountStatus())
                    .setActive(result.get(uid) == Boolean.TRUE)
                    .setInviteTime(relationModel.getCreatedTime().getTime());
            UserMeResponse userMeResponse = userMap.get(uid);
            if (userMeResponse != null) {
                userInviteListResponse.setNickName(userMeResponse.getNickName())
                        .setFaceLivenessStatus(userMeResponse.getFaceLivenessStatus())
                        .setGenesisBadge(
                                userMeResponse.getGenesisBadge() == null ? 0 : userMeResponse.getGenesisBadge())
                        .setAvatarUrl(userMeResponse.getAvatarUrl());
            }
            UserInviteFaceRecordModel userInviteFaceRecord = inviteRecordMap.get(uid);
            if (userInviteFaceRecord != null) {
                userInviteListResponse.setInviteAmountStatus(userInviteFaceRecord.getAmountStatus());
                userInviteListResponse.setInviteXmeAmount(userInviteFaceRecord.getFromAmount());
            }
            userInviteListResponses.add(userInviteListResponse);
        }
        return new PageResponse<>(query.getPage(), query.getSize(), inviteRelationList.getTotal(),
                userInviteListResponses);
    }

    /**
     * 获取个人邀请信息
     * 
     * @param uid
     * @return
     */
    public UserInviteInfoResponse getUserInviteInfo(Long uid) {
        final Pair<String, String> coins = getInviteCoins();
        final String inviteTitle = getInviteTitle(coins.getLeft());
        final String inviteSubTitle = getInviteSubTitle(coins.getRight());
        if (uid == null) {
            return new UserInviteInfoResponse()
                    .setInviteXmeAmount(systemConfig.getInviteXmeAmount())
                    .setInvitedXmeAmount(systemConfig.getInvitedXmeAmount() + systemConfig.getFaceXmeAmount())
                    .setInviteGenesisXmeAmount(systemConfig.getInviteGenesisXmeAmount())
                    .setInvitedGenesisXmeAmount(
                            systemConfig.getInvitedGenesisXmeAmount() + systemConfig.getFaceGenesisXmeAmount())
                    .setInviteCode("")
                    .setInviteUrl("")
                    .setInviteCount(0L)
                    .setLevel2MinNum(0)
                    .setInviteTitle(inviteTitle)
                    .setInviteSubTitle(inviteSubTitle);
        }
        ClientUserResponse response = clientUserCacheService.me(uid);
        Long size = userInviteRelationService.count(uid);
        String inviteShareUrl = String.format(inviteUrl, response.getInviteCode(),
                URLEncoder.encode(response.getNickName()));
        UserInviteInfoResponse userInviteInfoResponse = new UserInviteInfoResponse()
                .setInviteXmeAmount(systemConfig.getInviteXmeAmount())
                .setInvitedXmeAmount(systemConfig.getInvitedXmeAmount() + systemConfig.getFaceXmeAmount())
                .setInviteGenesisXmeAmount(systemConfig.getInviteGenesisXmeAmount())
                .setInvitedGenesisXmeAmount(
                        systemConfig.getInvitedGenesisXmeAmount() + systemConfig.getFaceGenesisXmeAmount())
                .setInviteUrl(inviteShareUrl)
                .setInviteCount(size == null ? 0 : size)
                .setInviteTitle(inviteTitle)
                .setInviteSubTitle(inviteSubTitle);
        if (livenessStatusVerify(response.getFaceLivenessStatus(), response.getPhoneVerify(),
                response.getEmailVerify())) {
            userInviteInfoResponse.setInviteCode(response.getInviteCode());
        } else {
            userInviteInfoResponse.setInviteCode("");
        }
        if (systemConfig.isNewInviteMode()) {
            userInviteInfoResponse.setInviteTitle(inviteTitle);
            userInviteInfoResponse.setInviteSubTitle(inviteSubTitle);
        }
        return userInviteInfoResponse;
    }

    private Pair<String, String> getInviteCoins() {
        String inviteRewardString = formatRewardString(systemConfig.getInviteReward());
        String invitedRewardString = formatRewardString(systemConfig.getInvitedReward());
        return Pair.of(inviteRewardString, invitedRewardString);
    }

    private String formatRewardString(Map<String, Integer> rewardMap) {
        if (rewardMap == null || rewardMap.isEmpty()) {
            return "";
        }

        List<String> rewardStrings = new ArrayList<>();
        int count = 0;
        for (Map.Entry<String, Integer> entry : rewardMap.entrySet()) {
            if (count >= 2)
                break; // 最多取前两个币种

            CcyEnums ccyEnums = CcyEnums.form(entry.getKey());
            String rewardString = entry.getValue() + ccyEnums.getUnit();
            rewardStrings.add(rewardString);
            count++;
        }

        return String.join(" + ", rewardStrings);
    }

    private String getInviteTitle(String inviteCoins) {
        return StringUtils.replace(I18nConvert.getI18nMessage(INVITE_TITLE_I18N_KEY, ClientInfoContext.getLanguage()),
                "{coins}", inviteCoins);
    }

    private String getInviteSubTitle(String inviteCoins) {
        return StringUtils.replace(
                I18nConvert.getI18nMessage(INVITE_SUBTITLE_I18N_KEY, ClientInfoContext.getLanguage()), "{coins}",
                inviteCoins);
    }

    /**
     * 校验昵称名是否已存在
     * 
     * @param nickName
     */
    public void checkNickName(String nickName) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("nick_name", nickName);
        queryWrapper.eq("virtual_type", 0);
        List<ClientUserModel> list = clientUserMapper.selectList(queryWrapper);
        if (list != null && !list.isEmpty()) {
            throw new ApiException(MediaUserExceptionCodeApi.NICK_NAME_EXIST_ERROR);
        }
    }

    public void checkEmail(String email) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email);
        ClientUserModel clientUserModel = clientUserMapper.selectOne(queryWrapper);
        if (clientUserModel != null) {
            throw new ApiException(MediaUserExceptionCodeApi.EMAIL_REGISTERED_ERROR);
        }
    }

    public Map<Long, ClientUserResponse> selectUserByIds(List<Long> uids) {
        Map<Long, ClientUserResponse> map = new HashMap<>();
        for (Long uid : uids) {
            ClientUserResponse response = clientUserCacheService.me(uid);
            map.put(uid, response);
        }
        return map;
    }

    public List<ClientUserResponse> selectListUserByIds(List<Long> uids) {
        List<ClientUserResponse> list = new ArrayList<>();
        for (Long uid : uids) {
            ClientUserResponse response = clientUserCacheService.me(uid);
            if (response != null) {
                list.add(response);
            }
        }
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateUserLanguage(Long uid, String language) {
        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ClientUserModel::getUid, uid)
                .set(ClientUserModel::getLanguage, language)
                .set(ClientUserModel::getUpdatedTime, new Date());
        clientUserMapper.update(updateWrapper);

        // 删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + uid);
    }

    public void inviteCodeExist(String inviteCode) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("invite_code", inviteCode);
        queryWrapper.eq("status", UserStatusEnum.DISABLED.getStatus());
        boolean exist = clientUserMapper.exists(queryWrapper);
        if (!exist) {
            throw new ApiException(MediaUserExceptionCodeApi.INVITE_CODE_INVALID);
        }
    }

    public List<ClientUserModel> queryUserListByInviteId(Long inviteId) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("invite_uid", inviteId);
        queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
        queryWrapper.eq("type", UserTypeEnums.USER.getCode());
        return clientUserMapper.selectList(queryWrapper);
    }

    public List<ClientUserModel> queryUserListByInviteIds(List<Long> inviteIds) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("invite_uid", inviteIds);
        queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
        queryWrapper.eq("type", UserTypeEnums.USER.getCode());
        return clientUserMapper.selectList(queryWrapper);
    }

    public ClientUserModel selectUserById(Long uid) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
        queryWrapper.eq("type", UserTypeEnums.USER.getCode());
        return clientUserMapper.selectOne(queryWrapper);
    }

    public Long robotPointsGeneration(RobotPointQuery query) {
        List<Long> uids = clientUserMapper.selectClientUserList(query);
        log.info("ClientUserService.robotPointsGeneration uids:{}", JSON.toJSONString(uids));

        if (CollectionUtils.isEmpty(uids)) {
            return MediaUserConstant.longZero;
        }

        ApiResponse response = userPointClient.robotPointsGeneration(new RobotPointsGenerationRequest().setUids(uids));
        if (!response.getSuccess()) {
            log.error("robotPointsGeneration failed response:{}", response);
        }
        return uids.get(uids.size() - 1);
    }

    @Transactional(rollbackFor = Exception.class)
    public void signOldMan(Long uid) {

        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ClientUserModel::getUid, uid)
                .set(ClientUserModel::getIsNewer, 0)
                .set(ClientUserModel::getUpdatedTime, new Date());
        clientUserMapper.update(updateWrapper);

        // 删除个人信息缓存
        stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + uid);
    }

    @Transactional(rollbackFor = Exception.class)
    public void closeUser(Long uid, String token) {
        log.info("uid:{}, close user account.", uid);
        ClientUserModel clientUserModel = clientUserMapper.selectById(uid);
        if (clientUserModel != null) {
            ClientUserCloseModel clientUserCloseModel = new ClientUserCloseModel();
            BeanUtils.copyProperties(clientUserModel, clientUserCloseModel);
            log.info("bak delete user[{}] to close table.", uid);
            clientUserCloseModel.setUpdatedTime(new Date());
            clientUserCloseMapper.insert(clientUserCloseModel);
            log.info("delete user[{}].", uid);
            clientUserMapper.deleteById(uid);
            log.info("clear login accessToken[{}].", uid);
            this.logout(token);
        }
    }

    /*
     * 随机选一个邀请码 TODO 对应的邀请码是否需要手动人脸识别
     * 
     * @return
     */
    public Map<String, String> pushInviteCode() {
        Map<String, String> rtn = new HashMap<>();
        // QueryWrapper queryWrapper = new QueryWrapper<>();
        // queryWrapper.last("limit 1000");
        // List<UserInviteCodePushModel> list =
        // userInviteCodePushMapper.selectList(queryWrapper);
        // if(list == null || list.isEmpty()){
        // rtn.put("inviteCode", "");
        // return rtn;
        // }
        // int index = new Random().nextInt(list.size());
        // UserInviteCodePushModel userInviteCodePushModel = list.get(index);
        // rtn.put("inviteCode", userInviteCodePushModel.getInviteCode());
        rtn.put("inviteCode", ""); // 接口不可用了
        return rtn;
    }

    /**
     * 更新最后一次登录时间
     * 
     * @param uid
     */
    private void updateLastLoginTime(Long uid) {
        UpdateWrapper<ClientUserModel> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda()
                .eq(ClientUserModel::getUid, uid)
                .set(ClientUserModel::getLastLoginTime, new Date())
                .set(ClientUserModel::getUpdatedTime, new Date());
        clientUserMapper.update(updateWrapper);
    }

    /**
     * 异步执行登录风险检测
     *
     * @param userId     用户ID
     * @param ip         IP地址
     * @param userAgent  用户代理
     * @param appVersion 应用版本
     * @param os         操作系统
     * @param boxId      设备盒子ID
     * @param loginType  登录类型
     */
    @Async("threadPoolTaskExecutor")
    public void sendLoginRiskAsync(Long userId, String ip, String userAgent, String appVersion, String os,
            String boxId, String loginType, Integer loginValid) {
        if (boxId == null) {
            log.warn("登录风险检测异步调用失败: userId={}, boxId is null", userId);
            return;
        }
        try {
            LoginRiskRequest loginRiskRequest = new LoginRiskRequest();
            loginRiskRequest.setUserId(userId);
            loginRiskRequest.setIp(ip);
            loginRiskRequest.setUserAgent(userAgent);
            loginRiskRequest.setAppVersion(appVersion);
            loginRiskRequest.setOs(os);
            loginRiskRequest.setBoxId(boxId);
            loginRiskRequest.setLoginType(loginType);
            loginRiskRequest.setLoginValid(loginValid);

            riskClient.loginRisk(loginRiskRequest);
            log.info("登录风险检测异步调用成功: userId={}", userId);
        } catch (Exception e) {
            log.warn("登录风险检测异步调用失败: userId={}, error={}", userId, e.getMessage());
        }
    }

}

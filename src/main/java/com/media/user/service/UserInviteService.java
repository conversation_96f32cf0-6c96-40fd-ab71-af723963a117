package com.media.user.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.media.common.utils.JsonUtils;
import com.media.common.utils.StringUtils;
import com.media.core.auth.CloudSession;
import com.media.core.config.SystemConfig;
import com.media.core.constant.PageResponse;
import com.media.core.i18n.I18nConvert;
import com.media.core.request.ClientInfoContext;
import com.media.user.cache.UserInviteCache;
import com.media.user.domain.UserInviteFaceRecordModel;
import com.media.user.domain.UserInviteRewardSummaryModel;
import com.media.user.dto.popup.PopupUserInviteInfo;
import com.media.user.dto.query.UserInviteListQuery;
import com.media.user.dto.response.*;
import com.media.user.dto.response.internal.UserMeResponse;
import com.media.user.enums.CcyEnums;
import com.media.user.enums.LanguageEnums;
import com.media.user.enums.RewardRuleEnum;
import com.media.user.service.cache.ClientUserCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.google.common.cache.CacheLoader.asyncReloading;
import static com.google.common.cache.CacheLoader.from;
import static com.media.core.constant.UserRedisKey.RECENT_USER_REWARD_LIST;
import static com.media.user.constant.MediaUserConstant.*;
import static com.media.user.utils.LivenessStatusUtils.livenessStatusVerify;
import static java.util.function.Function.identity;

@Slf4j
@Service
public class UserInviteService {

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @Autowired
    UserInviteCache userInviteCache;

    @Autowired
    private UserInviteFaceRecordService userInviteFaceRecordService;

    @Autowired
    SystemConfig systemConfig;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private UserInviteRewardSummaryService userInviteRewardSummaryService;

    private final ExecutorService cacheRefreshExecutor = Executors.newFixedThreadPool(2, r -> {
        Thread thread = new Thread(r, "notice-cache-refresh");
        thread.setDaemon(true);
        return thread;
    });

    private static final int NOTICE_LIMITE_COUNT = 20;

    private final LoadingCache<String, List<InviteAwardNoticeResponse>> noticeCache = CacheBuilder.newBuilder().refreshAfterWrite(3, TimeUnit.MINUTES).maximumSize(100).build(asyncReloading(from(this::loadNotices), cacheRefreshExecutor));

    public UserInviteDashboardResponse getInviteInfo(Long uid) {
        log.info("getInviteInfo uid:{}", uid);
        UserInviteDashboardResponse response = new UserInviteDashboardResponse();
        // 设置邀请码
        ClientUserResponse userResponse = clientUserCacheService.me(uid);
        response.setInviteCode(livenessStatusVerify(userResponse.getFaceLivenessStatus(), userResponse.getPhoneVerify(), userResponse.getEmailVerify()) ? userResponse.getInviteCode() : "");
        // 设置奖励信息
        response.setInviteReward(convertInviteReward(systemConfig.getInviteReward()));
        response.setInvitedReward(convertInviteReward(systemConfig.getInvitedReward()));
        // 设置币种信息
        setCurrencyInfo(response);
        // 设置总奖励和通知
        response.setTotalRewards(getTotalInviteRewards(uid));
        userInviteCache.clickPopup(uid);
        log.info("getInviteInfo uid:{} resp:{}", uid, JsonUtils.toJsonString(response));
        return response;
    }

    public PageResponse<UserInviteDashboardListResponse> getInviteList(UserInviteListQuery query) {
        log.info("getInviteList query:{}", JsonUtils.toJsonString(query));
        Preconditions.checkArgument(query != null && query.getUid() != null);
        int page = query.getPage() != null ? query.getPage() : 1;
        int size = query.getSize() != null ? query.getSize() : 10;
        Long total = userInviteFaceRecordService.countInviteRecordTotal(query.getUid());
        if (total == 0) {
            return new PageResponse<>(page, size, 0L, Collections.emptyList());
        }
        // 获取邀请记录
        List<UserInviteFaceRecordModel> inviteRecords = userInviteFaceRecordService.getInviteRecordList(query.getUid(), page, size);
        if (CollUtil.isEmpty(inviteRecords)) {
            return new PageResponse<>(page, size, 0L, Collections.emptyList());
        }
        // 获取用户信息
        List<Long> invitedUids = inviteRecords.stream().map(UserInviteFaceRecordModel::getToUid).filter(Objects::nonNull).toList();
        if (CollUtil.isEmpty(invitedUids)) {
            return new PageResponse<>(page, size, 0L, Collections.emptyList());
        }
        Map<Long, UserMeResponse> userMap = clientUserCacheService.usersWithConfig(invitedUids).stream().collect(Collectors.toMap(UserMeResponse::getUid, identity()));
        // 构建响应列表
        List<UserInviteDashboardListResponse> responseList = inviteRecords.stream().map(record -> buildInviteListResponse(record, userMap)).filter(Objects::nonNull).toList();
        log.info("getInviteList page:{} size:{} total:{} resp:{}", page, size, total, JsonUtils.toJsonString(responseList));
        return new PageResponse<>(page, size, total, responseList);
    }

    public List<InviteAwardNoticeResponse> getRewardNotices() {
        try {
            List<InviteAwardNoticeResponse> notices = noticeCache.get(ClientInfoContext.getLanguage().getValue());
            if (CollectionUtils.isEmpty(notices)) {
                log.warn("缓存返回空数据，notices: {}", notices);
                return Collections.emptyList();
            }
            List<InviteAwardNoticeResponse> shuffledNotices = new ArrayList<>(notices);
            Collections.shuffle(shuffledNotices);
            log.info("成功获取奖励通知，count: {}", shuffledNotices.size());
            return shuffledNotices;
        } catch (Exception e) {
            log.error("成功获取奖励通知失败", e);
            return Collections.emptyList();
        }
    }

    private List<InviteAwardNoticeResponse> loadNotices(String language) {
        log.info("loadNotices language:{}", language);
        List<Long> fromUids = getLatestRewardUserIds(NOTICE_LIMITE_COUNT);
        if (CollectionUtils.isEmpty(fromUids)) {
            return Collections.emptyList();
        }
        List<InviteAwardNoticeResponse> res = Lists.newArrayList();
        Map<Long, UserMeResponse> userMap = clientUserCacheService.usersWithConfig(fromUids).stream().collect(Collectors.toMap(UserMeResponse::getUid, identity()));
        Map<Long, List<UserInviteRewardSummaryModel>> userRewardSummary = userInviteRewardSummaryService.batchGetUserRewardSummary(fromUids);
        for (Map.Entry<Long, List<UserInviteRewardSummaryModel>> entry : userRewardSummary.entrySet()) {
            Long userId = entry.getKey();
            List<UserInviteRewardSummaryModel> rewardList = entry.getValue();
            UserMeResponse userInfo = userMap.get(userId);
            if (userInfo == null) {
                log.warn("UserMeResponse is null uid:{}", userId);
                continue;
            }
            String rewardString = formatRewardString(rewardList);
            String maskedUsername = maskUsername(userInfo.getUsername());
            String noticeContent = buildNoticeContent(maskedUsername, rewardString, language);
            InviteAwardNoticeResponse notice = new InviteAwardNoticeResponse();
            notice.setText(noticeContent);
            res.add(notice);
        }
        return res;
    }

    private String maskUsername(String username) {
        if (StringUtils.isBlank(username)) {
            return username;
        }
        int length = username.length();
        if (length <= 2) {
            return username;
        } else if (length <= 4) {
            return username.charAt(0) + "*" + username.charAt(length - 1);
        } else {
            int maskLength = Math.max(1, length / 3);
            int start = (length - maskLength) / 2;
            String mask = "*".repeat(maskLength);
            return username.substring(0, start) + mask + username.substring(start + maskLength);
        }
    }

    private String formatRewardString(List<UserInviteRewardSummaryModel> rewardList) {
        if (CollectionUtils.isEmpty(rewardList)) {
            return "";
        }
        List<String> rewardStrings = new ArrayList<>();
        int count = 0;
        for (UserInviteRewardSummaryModel reward : rewardList) {
            if (count >= 2) break; // 最多显示两种奖励
            String currency = reward.getCurrency();
            CcyEnums ccyEnums = CcyEnums.form(currency);
            String rewardString = reward.getTotalAmount() + " " + ccyEnums.getUnit();
            rewardStrings.add(rewardString);
            count++;
        }
        return String.join(" + ", rewardStrings);
    }

    private String buildNoticeContent(String username, String rewardString, String language) {
        String noticeContent = I18nConvert.getI18nMessage(REWARD_RECEIVED_MESSAGE_I18N_KEY, LanguageEnums.get(language));
        noticeContent = StringUtils.replace(noticeContent, "{name}", username);
        noticeContent = StringUtils.replace(noticeContent, "{coins}", rewardString);
        return noticeContent;
    }

    public List<Long> getLatestRewardUserIds(Integer limit) {
        try {
            String key = RECENT_USER_REWARD_LIST.of();
            Set<String> userUidStrings = stringRedisTemplate.opsForZSet().reverseRange(key, 0, limit - 1);
            if (userUidStrings == null || userUidStrings.isEmpty()) {
                log.info("最近奖励用户ZSET为空");
                return new ArrayList<>();
            }
            List<Long> userIds = userUidStrings.stream().map(Long::valueOf).toList();
            log.info("获取最新{}个获奖用户UID列表成功，数量: {}", limit, userIds.size());
            return userIds;
        } catch (Exception e) {
            log.error("获取最新{}个获奖用户UID列表失败: {}", limit, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    private void setCurrencyInfo(UserInviteDashboardResponse response) {
        Map<String, Integer> inviteReward = systemConfig.getInviteReward();
        Iterator<Map.Entry<String, Integer>> entryIterator = inviteReward.entrySet().iterator();
        if (entryIterator.hasNext()) {
            Map.Entry<String, Integer> ccyOne = entryIterator.next();
            response.setFirstCcy(ccyOne.getKey());
            if (entryIterator.hasNext()) {
                Map.Entry<String, Integer> ccyTwo = entryIterator.next();
                if (ccyTwo.getKey().equals(CcyEnums.BTC.getCcy())) {
                    response.setSecondCcy("Bitcoin");
                } else {
                    response.setSecondCcy(ccyTwo.getKey());
                }
            }
        }
    }

    private List<InviteRewardItemResponse> getTotalInviteRewards(Long uid) {
        List<UserInviteRewardSummaryModel> userRewardSummary = userInviteRewardSummaryService.getUserRewardSummary(uid);
        Map<String, UserInviteRewardSummaryModel> hasReward = CollectionUtils.isEmpty(userRewardSummary) ? Collections.emptyMap() : userRewardSummary.stream().collect(Collectors.toMap(UserInviteRewardSummaryModel::getCurrency, identity()));
        Map<String, Integer> inviteReward = systemConfig.getInviteReward();
        return inviteReward.keySet().stream().map(mapRewardItem(hasReward)).toList();
    }

    private Function<String, InviteRewardItemResponse> mapRewardItem(Map<String, UserInviteRewardSummaryModel> hasReward) {
        return currency -> hasReward.containsKey(currency) ? buildRewardResponse(hasReward.get(currency)) : createZeroReward(currency);
    }

    private InviteRewardItemResponse buildRewardResponse(UserInviteRewardSummaryModel userInviteRewardSummaryModel) {
        if (userInviteRewardSummaryModel == null) {
            log.warn("buildRewardResponse: userInviteRewardSummaryModel is null");
            return null;
        }
        InviteRewardItemResponse response = new InviteRewardItemResponse();
        response.setAmount(BigDecimal.valueOf(userInviteRewardSummaryModel.getTotalAmount()));
        String ccy = userInviteRewardSummaryModel.getCurrency();
        CcyEnums ccyEnums = CcyEnums.form(ccy);
        response.setUnit(ccyEnums.getUnit());
        response.setCcy(ccyEnums.getCcy());
        return response;
    }

    private InviteRewardItemResponse createZeroReward(String currency) {
        InviteRewardItemResponse zeroReward = new InviteRewardItemResponse();
        zeroReward.setCcy(currency);
        zeroReward.setAmount(BigDecimal.ZERO);
        CcyEnums ccyEnum = CcyEnums.form(currency);
        zeroReward.setUnit(ccyEnum.getUnit());
        return zeroReward;
    }

    private List<InviteRewardItemResponse> convertInviteReward(Map<String, Integer> inviteRewardList) {
        List<InviteRewardItemResponse> res = Lists.newArrayListWithCapacity(inviteRewardList.size());
        for (Map.Entry<String, Integer> entry : inviteRewardList.entrySet()) {
            InviteRewardItemResponse inviteRewardItemResponse = new InviteRewardItemResponse();
            CcyEnums ccyEnum = CcyEnums.form(entry.getKey());
            inviteRewardItemResponse.setCcy(entry.getKey());
            inviteRewardItemResponse.setUnit(ccyEnum.getUnit());
            inviteRewardItemResponse.setAmount(BigDecimal.valueOf(entry.getValue()));
            res.add(inviteRewardItemResponse);
        }
        return res;
    }

    private UserInviteDashboardListResponse buildInviteListResponse(UserInviteFaceRecordModel record, Map<Long, UserMeResponse> userMap) {
        UserMeResponse userInfo = userMap.get(record.getToUid());
        if (userInfo == null) {
            return null;
        }
        UserInviteDashboardListResponse response = new UserInviteDashboardListResponse();
        response.setUid(userInfo.getUid());
        response.setAvatarUrl(userInfo.getAvatarUrl());
        response.setNickName(userInfo.getNickName());
        response.setInviteTime(record.getCreatedTime().getTime());
        List<InviteRewardItemResponse> inviteReward = buildRewardList(record);
        response.setInviteReward(inviteReward);
        return response;
    }

    private List<InviteRewardItemResponse> buildRewardList(UserInviteFaceRecordModel record) {
        List<InviteRewardItemResponse> inviteReward = Lists.newArrayList();
        if (record.getFromAmount() != null && record.getFromAmount() > 0 && record.getFromAmountSymbol() != null) {
            inviteReward.add(createRewardItem(record.getFromAmountSymbol(), record.getFromAmount()));
        }
        if (record.getFromAmount2() != null && record.getFromAmount2() > 0 && record.getFromAmountSymbol2() != null) {
            inviteReward.add(createRewardItem(record.getFromAmountSymbol2(), record.getFromAmount2()));
        }
        return inviteReward;
    }

    private InviteRewardItemResponse createRewardItem(String symbol, Integer amount) {
        InviteRewardItemResponse rewardItem = new InviteRewardItemResponse();
        rewardItem.setCcy(symbol);
        rewardItem.setAmount(BigDecimal.valueOf(amount));
        CcyEnums ccyEnum = CcyEnums.form(symbol);
        rewardItem.setUnit(ccyEnum.getUnit());
        return rewardItem;
    }

    public Map<String, Object> inviteNoticeMsg() {

        List<PopupUserInviteInfo> userInviteInfoList = userInviteCache.getInviteUserCache(CloudSession.getUid());

        log.info("用户:{}查询邀请人计算奖励,redis返回:{}", CloudSession.getUid(), JSON.toJSON(userInviteInfoList));
        if (CollUtil.isEmpty(userInviteInfoList)) {
            return null;
        }
        Map<String, Object> result = Maps.newHashMap();

        List<PopupUserInviteInfo.RewardInfo> rewardInfos = userInviteInfoList.stream().flatMap(t -> t.getRewardInfoList().stream()).toList();
        Map<String, BigDecimal> collect = rewardInfos.stream().collect(Collectors.groupingBy(PopupUserInviteInfo.RewardInfo::getCcy, Collectors.reducing(BigDecimal.ZERO, PopupUserInviteInfo.RewardInfo::getPrice, BigDecimal::add)));
        StringJoiner titleReplace = new StringJoiner(" + ");
        collect.forEach((k, v) -> {
            titleReplace.add(v.toString().concat(" ").concat(Objects.equals(CcyEnums.BTC.getCcy(), k) ? CcyEnums.form(k).getUnit() : k));
        });
//        Map<String, Integer> inviteReward = systemConfig.getMultiCurrencyReward(1, RewardRuleEnum.INVITE);
//        StringJoiner contentReplace = new StringJoiner(" + ");
//        inviteReward.forEach((k, v) -> {
//            contentReplace.add(v.toString().concat(" ").concat(Objects.equals(CcyEnums.BTC.getCcy(), k) ? CcyEnums.form(k).getUnit() : k));
//        });

        String title = I18nConvert.getI18nMessage(POPUP_BITCOIN_INVITE_USER_NOTICE_TITLE_I18N_KEY, ClientInfoContext.getLanguage());
        String content = I18nConvert.getI18nMessage(POPUP_BITCOIN_INVITE_USER_NOTICE_CONTENT_I18N_KEY, ClientInfoContext.getLanguage());


        result.put("title", String.format(title, titleReplace));
        result.put("content", content);
        result.put("url", "https://s3.x.me/images/2025-08-10/adb5a8e0-d69b-42b5-8e42-b57c4c601c97.png");

        return result;

    }
}


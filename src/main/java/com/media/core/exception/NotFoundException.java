package com.media.core.exception;


import org.springframework.http.HttpStatus;

public class NotFoundException extends BaseException {

    public NotFoundException() {
        this(HttpStatus.NOT_FOUND.getReasonPhrase());
    }

    public NotFoundException(String message) {
        this(HttpStatus.NOT_FOUND.value(), message, null);
    }

    public NotFoundException(String message, Integer code) {
        this(HttpStatus.NOT_FOUND.value(), message, code);
    }

    public NotFoundException(Integer status, String message) {
        this(status, message, null);
    }

    public NotFoundException(Integer status, String message, Integer code) {
        super(status, message, code);
    }

    public NotFoundException(Throwable cause) {
        super(cause);
        setStatus(HttpStatus.NOT_FOUND.value());
    }

}

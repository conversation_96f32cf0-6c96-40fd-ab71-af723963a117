package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.user.dto.request.UserFeedbackRequest;
import com.media.user.service.UserFeedbackService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/user/api/feedback")
public class UserFeedbackApiController{

    @Autowired
    private UserFeedbackService userFeedbackService;

    /**
     * Submit user feedback
     *
     * @param request Feedback request containing:
     *                - content: Feedback content
     *                - type: Feedback type (optional, default: 1)
     *                  1 - General feedback
     *                  2 - Appeal
     *                - contract: Optional contact information
     *                - imageList: Optional images related to feedback
     */
    @PostMapping("/submitFeedback")
    public void submitFeedback(@Validated  @RequestBody UserFeedbackRequest request){
        if (CloudSession.isLogin()){
            request.setUid(CloudSession.getUid());
        }
        userFeedbackService.submitFeedback(request);
    }
}

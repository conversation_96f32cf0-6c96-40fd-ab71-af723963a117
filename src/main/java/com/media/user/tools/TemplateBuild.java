package com.media.user.tools;

import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.util.Map;

@Slf4j
@Component
public class TemplateBuild {

    public String buildContent(String template, Map<String, Object> data) {
        try {
            Configuration configuration = new Configuration(Configuration.VERSION_2_3_23);
            // 渲染标题
            StringTemplateLoader templateLoader = new StringTemplateLoader();

            templateLoader.putTemplate("subject", template); // template = 虚拟名称, 用来当作获取静态文件的key
            configuration.setTemplateLoader(templateLoader);
            Template subjectTemplate = configuration.getTemplate("subject", "utf-8");
            return FreeMarkerTemplateUtils.processTemplateIntoString(subjectTemplate, data);
        } catch (Exception e) {
            log.error("TemplateBuild.buildSubject err::{}", e.getMessage());
        }
        return null;
    }
}

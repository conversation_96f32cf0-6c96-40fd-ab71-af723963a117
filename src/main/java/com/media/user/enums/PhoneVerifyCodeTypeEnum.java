package com.media.user.enums;

import lombok.Getter;

/**
 * 手机验证码类别
 */
@Getter
public enum PhoneVerifyCodeTypeEnum {
    /**
     * 忘记密码发送验证码
     */
    FORGET_PASSWORD_CODE(1),

    /**
     * 注册验证码
     */
    SIGNUP(2),
    /**
     * 更新密码
     */
    UPDATE_PASSWORD_CODE(3),

    /**
     * 绑定手机号
     */
    PHONE_BIND_CODE(4),

    /**
     * 验证手机号
     */
    PHONE_VERIFY_CODE(5),
    /**
     * 登录验证码
     */
    PHONE_LOGIN_CODE(6),
    ;

    private Integer value;


    PhoneVerifyCodeTypeEnum(int value) {
        this.value = value;
    }


    public static PhoneVerifyCodeTypeEnum getEnumFromCode(Integer code) {
        for (PhoneVerifyCodeTypeEnum typeEnum : PhoneVerifyCodeTypeEnum.values()) {
            if (typeEnum.getValue().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}

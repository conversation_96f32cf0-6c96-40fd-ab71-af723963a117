package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class UserFamilyMemberResponse {

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 积分总数
     */
    private BigDecimal pointTotal;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 子成员人数
     */
    private Integer inviteCount;

    /**
     * 上级昵称
     */
    private String leaderNickName;

    /**
     * 上级头像
     */
    private String leaderAvatarUrl;

}
package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;


@Data
@Accessors(chain = true)
public class UserInviteDashboardListResponse {

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 用户头像
     */
    private String avatarUrl;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 邀请时间
     */
    private Long inviteTime;

    /**
     * 邀请奖励
     */
    private List<InviteRewardItemResponse> inviteReward;

}

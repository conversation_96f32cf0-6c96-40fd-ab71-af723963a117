package com.media.user.service.strategy.impl;

import com.media.user.dto.im.RongCloudMessageData;
import com.media.user.enums.ImChannelTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

@Slf4j
@Component
public class GroupMessageHandlerStrategy extends AbstractMessageHandlerStrategy {

    @Override
    public Map<String, Object> replaceContent(RongCloudMessageData messageData) {
        log.info("处理群聊消息内容替换");
        return null;
    }

    @Override
    public String getChannelType() {
        return ImChannelTypeEnum.GROUP.getType();
    }
} 
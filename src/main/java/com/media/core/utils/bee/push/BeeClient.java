package com.media.core.utils.bee.push;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.media.core.utils.bee.push.vo.param.OriginalPushParam;
import com.media.core.utils.bee.push.vo.rtn.AdminMessageTemplate;
import com.media.core.utils.bee.push.vo.rtn.BeeResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class BeeClient {

    @Autowired
    BeeConfiguration beeConfiguration;


    /**
     * 业务推送，和模板推送的区别是文案里的参数由业务方定义
     * @param pushParam
     */
    @Async("threadPoolTaskExecutor")
    public void originalPush(OriginalPushParam pushParam){
        Map<String, Object> param = new HashMap<>();
//        params.put("platform", "google");
        param.put("isBroadcastAll", false);
        Map<String,Object> audience = new HashMap<>();
        audience.put("userId", pushParam.getUserId());
        param.put("audience", audience);
        Map<String,Object> notification = new HashMap<>();
        Map<String,Object> alert = new HashMap<>();
        alert.put("title", pushParam.getTitle());
        alert.put("message", pushParam.getMessage());
        alert.put("payload", "");
        notification.put("alert", alert);
        param.put("notification", notification);
        log.info("bee originalPush param:{}", JSONObject.toJSONString(param));
        doPost("/push-server/server/originalPush", JSONObject.toJSONString(param));
    }

    /**
     * 获取所有消息模板
     */
    public List<AdminMessageTemplate> adminMessageTemplateList(){
        BeeResult beeResult = doGet("/push-server/admin/messageTemplate/list");
        return JSONArray.parseArray(JSONObject.toJSONString(beeResult.getData()), AdminMessageTemplate.class);
    }


    // http method
    public BeeResult doGet(String url){
        HttpRequest httpRequest = new HttpRequest(UrlBuilder.of(beeConfiguration.getUri() + url));
        Map<String, String> headerMap = headerMap();
        for(String key : headerMap.keySet()){
            httpRequest.header(key, headerMap.get(key));
        }
        httpRequest.method(Method.GET);
        try (HttpResponse httpResponse = httpRequest.execute()) {
            if (httpResponse.isOk()) {
                return JSONObject.parseObject(httpResponse.body(), BeeResult.class);
            }
        }
        return null;
    }

    public void doPost(String url, String body){
        HttpRequest httpRequest = new HttpRequest(UrlBuilder.of(beeConfiguration.getUri() + url));
        Map<String, String> headerMap = this.headerMap();
        for(String key : headerMap.keySet()){
            httpRequest.header(key, headerMap.get(key));
        }
        httpRequest.method(Method.POST);
        httpRequest.body(body);
        try (HttpResponse httpResponse = httpRequest.execute()) {
            if (httpResponse.isOk()) {
                String responseBoy = httpResponse.body();
                log.info("bee push responseBoy:{}", responseBoy);
            }else{
                log.error("bee push error: {}", body);
            }
        }
    }

    public Map<String, String> headerMap(){
        String timestamp = String.valueOf(System.currentTimeMillis());
        Map<String, String> map = new HashMap<>();
        map.put("user-agent", "Dart/3.5 (dart:io)");
        map.put("host", beeConfiguration.getUri().replace("https://", ""));
        map.put("content-type", "application/json");
        map.put("appid", beeConfiguration.getAppid());
        map.put("timestamp", timestamp);
        map.put("sign", sign(timestamp));
        return map;
    }

    public String sign(String timestamp){
        String text = "timestamp=" + timestamp;
        String sign = RSAUtils.encryptedDataOnJavaByPrivateKey(text, beeConfiguration.getPrivateKey());
        return sign;
    }



}

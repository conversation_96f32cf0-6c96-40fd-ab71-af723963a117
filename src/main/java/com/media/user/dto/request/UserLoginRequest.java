package com.media.user.dto.request;

import com.media.core.constant.ValidatorRegexpConstant;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors
public class UserLoginRequest {

    private String countryCode;

    private String phonePrefix;

    private String phone;

    @Email
    private String email;


    @Pattern(regexp = ValidatorRegexpConstant.PASSWORD_REGEX, message = "{result.code.10020}")
    @NotBlank
    private String password;

    private String code;

    /**
     * 谷歌人机校验 token
     */
    String responseToken;
}

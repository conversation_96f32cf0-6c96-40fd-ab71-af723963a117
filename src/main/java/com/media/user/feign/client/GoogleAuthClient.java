package com.media.user.feign.client;

import com.alibaba.fastjson.JSONObject;
import com.media.user.dto.response.GoogleAuthResponse;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.concurrent.TimeUnit;

@FeignClient(url = "${google.auth.uri}", value = "googleAuthClient", configuration = GoogleAuthClient.GoogleAuthClientConfiguration.class)
public interface GoogleAuthClient {
    
    @Configuration
    class GoogleAuthClientConfiguration {
        @Bean("googleAuthClientOptions")
        public Request.Options options() {
            // Set connect timeout to 300ms and read timeout to 2000ms
            return new Request.Options(300, TimeUnit.MILLISECONDS, 2000, TimeUnit.MILLISECONDS, true);
        }
    }


    @PostMapping(value = "/token", consumes = "application/x-www-form-urlencoded")
    GoogleAuthResponse checkToken(@RequestParam("code") String code,
                                  @RequestParam("client_id") String client_id,
                                  @RequestParam("client_secret") String client_secret,
                                  @RequestParam("redirect_uri") String redirect_uri,
                                  @RequestParam("grant_type") String grant_type);


    @GetMapping(value = "/tokeninfo", consumes = "application/x-www-form-urlencoded")
    JSONObject tokenInfo(@RequestParam("accessToken") String accessToken);


    @GetMapping(value = "/tokeninfo", consumes = "application/x-www-form-urlencoded")
    JSONObject tokenInfoByIdToken(@RequestParam("id_token") String idToken);

}

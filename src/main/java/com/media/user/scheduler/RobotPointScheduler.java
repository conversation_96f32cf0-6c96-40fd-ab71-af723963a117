package com.media.user.scheduler;


import com.media.user.service.ClientUserService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Deprecated
@Slf4j
@Component
public class RobotPointScheduler {

    @Autowired
    private ClientUserService clientUserService;

    /**
     * 生成机器人积分
     */
    @XxlJob("robotPointsGenerationBatch")
    public void robotPointsGenerationBatch() {
//        int index = XxlJobHelper.getShardIndex();
//        int total = XxlJobHelper.getShardTotal();
//        Long id = -1L;
//        while (!id.equals(MediaUserConstant.longZero)){
//            RobotPointQuery query = new RobotPointQuery()
//                    .setId(id)
//                    .setIndex(index)
//                    .setTotal(total)
//                    .setPageSize(MediaUserConstant.pageSize);
//            id  = clientUserService.robotPointsGeneration(query);
//        }
    }


}

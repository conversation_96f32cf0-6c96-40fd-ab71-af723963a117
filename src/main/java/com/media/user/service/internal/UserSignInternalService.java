package com.media.user.service.internal;

import cn.hutool.core.date.DateUtil;
import com.media.user.mapper.UserSignInMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class UserSignInternalService {

    @Autowired
    UserSignInMapper userSignInMapper;

    /**
     * 查询当天是否签到
     * @param uid
     */
    public Map<String, Integer> isSignToday(Long uid){
        Map<String, Integer> rtnMap = new HashMap<>();
        rtnMap.put("signFlag", 0);
        // 检查当天是否已经签到
        if(uid != null && uid > 0) {
            String today = DateUtil.today();
            Long signToday = userSignInMapper.countSignToday(uid, today);
            log.info("sign date: {} - {} - {}", uid, today, signToday);
            if (signToday != null && signToday > 0) {
                rtnMap.put("signFlag", 1);
            }
        }
        return rtnMap;
    }


}

package com.media.user.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.core.auth.CloudSession;
import com.media.core.utils.VersionUtil;
import com.media.user.convert.mapper.AppReleaseConvert;
import com.media.user.domain.AppReleaseModel;
import com.media.user.domain.ChannelConfigModel;
import com.media.user.dto.response.AppReleaseResponse;
import com.media.user.enums.*;
import com.media.user.mapper.AppReleaseMapper;
import com.media.user.mapper.ChannelConfigMapper;
import com.media.core.request.ClientInfoContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
@Slf4j
@Service
public class AppReleaseService {

    @Autowired
    private AppReleaseMapper appReleaseMapper;

    @Autowired
    private ChannelConfigMapper channelConfigMapper;

    @Value("${app.force.update:false}")
    private boolean forceUpdate;

    @Value("${app.force.update.version:1.0.50}")
    private String forceVersion;

    @Value("${app.force.update.version_ios:1.0.50}")
    private String forceVersionIos;


    @Value("${app.force.update.config:{}}")
    private String forceUpdateConfig;

    @Value("${app.update.language.config:{}}")
    private String updateLanguageConfig;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${app.gray.release.config:{}}")
    private String grayReleaseConfig;

    /**
     * 渠道下载配置 - Apollo配置
     * 配置格式示例：
     * {
     *   "suke": {
     *     "androidUrl": "https://suke.example.com/app.apk",
     *     "androidQrCode": "https://suke.example.com/qrcode-android.png",
     *     "googlePlayUrl": "https://play.google.com/store/apps/details?id=com.example.suke",
     *     "googlePlayQrCode": "https://suke.example.com/qrcode-gp.png",
     *     "iosUrl": "https://apps.apple.com/app/suke/id123456789",
     *     "iosQrCode": "https://suke.example.com/qrcode-ios.png"
     *   },
     *   "wxkd": {
     *     "androidUrl": "https://wxkd.example.com/app.apk",
     *     "androidQrCode": "https://wxkd.example.com/qrcode-android.png",
     *     "googlePlayUrl": "https://play.google.com/store/apps/details?id=com.example.wxkd",
     *     "iosUrl": "https://apps.apple.com/app/wxkd/id987654321"
     *   },
     *   "yiki": {
     *     "androidUrl": "https://yiki.example.com/app.apk",
     *     "androidQrCode": "https://yiki.example.com/qrcode.png"
     *   }
     * }
     */
    @Value("${app.channel.download.config:{}}")
    private String channelDownloadConfig;

    /**
     * 获取app最新版本
     */
    public AppReleaseResponse getAppReleaseLatest(LanguageEnums languageEnums){
        QueryWrapper<AppReleaseModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("platform_type", AppReleasePlatformEnum.getByCode(ClientInfoContext.get().getPlatformType()));
        queryWrapper.eq("enable", AppReleaseEnableEnum.ENABLED.getStatus());
        queryWrapper.orderByDesc("id");
        queryWrapper.last("limit 1");
        AppReleaseModel appReleaseModel = appReleaseMapper.selectOne(queryWrapper);
        if (appReleaseModel == null){
            return null;
        }

        // 灰度机制处理
        boolean hitGrayRelease = false;
        if (CloudSession.isLogin()) {
            AppReleaseModel grayAppRelease = checkGrayRelease(appReleaseModel);
            if (grayAppRelease != null) {
                appReleaseModel = grayAppRelease;
                hitGrayRelease = true;
            }
        }

        AppReleaseResponse response =  AppReleaseConvert.INSTANCE.model2ResponsePro(appReleaseModel, languageEnums);
        //没有命中灰度走正常逻辑
        if (!hitGrayRelease) {
            // Check Apollo configuration for force update
            boolean forceUpdate = false;

            String platformType = ClientInfoContext.get().getPlatformType();
            String currentVersion = appReleaseModel.getVersion();

            // Parse the JSON configuration
            JSONObject config = parseForceUpdateConfig();
            String clientVersion = ClientInfoContext.get().getVersion();
            if (config != null && StringUtils.isNotBlank(clientVersion) && VersionUtil.CompareVersions(clientVersion, currentVersion) <= 0) {
                // Check if force update is globally enabled
                boolean globalEnabled = config.containsKey("enabled") && config.getBooleanValue("enabled");

                // Platform-specific settings
                JSONObject platformConfig = null;

                // Get platform-specific configuration

                if (platformType.equalsIgnoreCase("android")) {
                    platformConfig = config.getJSONObject("android");
                } else if (platformType.equalsIgnoreCase("android-gp")) {
                    platformConfig = config.getJSONObject("androidgp");
                } else if (platformType.equalsIgnoreCase("ios")) {
                    platformConfig = config.getJSONObject("ios");
                }


                // Check platform-specific settings first
                if (platformConfig != null && platformConfig.containsKey("enabled") && platformConfig.getBooleanValue("enabled")) {
                    // Get the list of versions that need force update
                    JSONArray versions = platformConfig.getJSONArray("versions");
                    if (versions != null && !versions.isEmpty()) {
                        forceUpdate = isVersionInForceUpdateList(clientVersion, versions);
                    }
                } else if (globalEnabled) {
                    // Fall back to global settings
                    JSONArray globalVersions = config.getJSONArray("versions");
                    if (globalVersions != null && !globalVersions.isEmpty()) {
                        forceUpdate = isVersionInForceUpdateList(clientVersion, globalVersions);
                    }
                }
            }
            response.setForceUpdate(forceUpdate);
        }

        // Get language-specific update content from Apollo configuration
        try {
            // Parse the language configuration from Apollo
            String languageKey = languageEnums.getValue(); // Use getValue() to get the format with hyphens (e.g., "en-US")
            JSONObject languageConfig = JSON.parseObject(updateLanguageConfig);

            if (languageConfig != null) {
                // Get the language-specific content directly using the language key
                JSONObject languageContent = languageConfig.getJSONObject(languageKey);

                // If not found, fall back to English for non-English/non-Chinese languages
                if (languageContent == null && !"en-US".equals(languageKey)) {
                    languageContent = languageConfig.getJSONObject("en-US");
                }

                // Apply language-specific content if found
                if (languageContent != null) {
                    String updateContent = null;

                    // Choose appropriate update content based on force update status
                    if (forceUpdate && languageContent.containsKey("forceUpdateContent")) {
                        updateContent = languageContent.getString("forceUpdateContent");
                    } else if (languageContent.containsKey("updateContent")) {
                        updateContent = languageContent.getString("updateContent");
                    }

                    // Set title based on update type (force update or regular update)
                    if (forceUpdate) {
                        // For force updates, use forceUpdateTitle if available
                        if (languageContent.containsKey("forceUpdateTitle")) {
                            response.setTitle(languageContent.getString("forceUpdateTitle"));
                        } else if (languageContent.containsKey("title")) {
                            // Fall back to regular title if force update title not available
                            response.setTitle(languageContent.getString("title"));
                        } else {
                            // Default force update title
                            response.setTitle("A New Update is Ready");
                        }
                    } else {
                        // For regular updates
                        if (languageContent.containsKey("title")) {
                            response.setTitle(languageContent.getString("title"));
                        } else {
                            // Default regular update title
                            response.setTitle("A New Update is Ready");
                        }
                    }

                    if (updateContent != null) {
                        response.setUpdateContent(updateContent);
                    }
                }
            }
        } catch (Exception e) {
            log.error("Failed to parse language configuration: {}", e.getMessage());
        }
        return response;
    }

    /**
     * Parse the force update configuration JSON string from Apollo
     *
     * @return JSONObject containing force update configuration, or null if invalid
     */
    private JSONObject parseForceUpdateConfig() {
        try {
            if (forceUpdateConfig == null || forceUpdateConfig.isEmpty() || "{}".equals(forceUpdateConfig)) {
                return null;
            }
            return JSON.parseObject(forceUpdateConfig);
        } catch (Exception e) {
            log.error("Failed to parse force update config: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Check if the current version requires force update based on a list of versions in the config
     *
     * @param currentVersion Current app version
     * @param versionArray JSONArray of version strings or version objects
     * @return true if force update is required, false otherwise
     */
    private boolean isVersionInForceUpdateList(String currentVersion, JSONArray versionArray) {
        if (currentVersion == null || versionArray == null || versionArray.isEmpty()) {
            return false;
        }

        for (int i = 0; i < versionArray.size(); i++) {
            Object item = versionArray.get(i);
            String versionToCheck;

            if (item instanceof String) {
                versionToCheck = (String) item;
            } else if (item instanceof JSONObject) {
                JSONObject versionObj = (JSONObject) item;
                versionToCheck = versionObj.getString("value");
            } else {
                continue;
            }

            if (versionToCheck != null && !versionToCheck.isEmpty()) {
                // Check if it's a version range (contains '-')
                if (versionToCheck.contains("-")) {
                    // Process version range
                    String[] range = versionToCheck.split("-");
                    if (range.length == 2) {
                        String minVersion = range[0].trim();
                        String maxVersion = range[1].trim();

                        // Check if current version is within the range (inclusive)
                        if (!minVersion.isEmpty() && !maxVersion.isEmpty()) {
                            int compareToMin = VersionUtil.CompareVersions(currentVersion, minVersion);
                            int compareToMax = VersionUtil.CompareVersions(currentVersion, maxVersion);

                            // Version is within range if >= min and <= max
                            if (compareToMin >= 0 && compareToMax <= 0) {
                                return true;
                            }
                        }
                    }
                } else {
                    // Simple version comparison (for backward compatibility)
                    if (VersionUtil.CompareVersions(currentVersion, versionToCheck) <= 0) {
                        return true;
                    }
                }
            }
        }

        return false;
    }


    public List<AppReleaseResponse> listAppReleaseLatest(LanguageEnums languageEnums){
        List<AppReleaseModel> list = appReleaseMapper.selectListRelease();
        if (list == null){
            return null;
        }
        List<AppReleaseResponse> rtnList = new ArrayList<>();
        for(AppReleaseModel appReleaseModel : list ){
            rtnList.add(AppReleaseConvert.INSTANCE.model2ResponsePro(appReleaseModel, languageEnums));
        }
        return rtnList;
    }

    /**
     * 获取app最新版本
     */
    public AppReleaseResponse getAppReleaseLatestByPlatformType(LanguageEnums languageEnums, String platformType){
        QueryWrapper<AppReleaseModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("platform_type", platformType);
        queryWrapper.eq("enable", AppReleaseEnableEnum.ENABLED.getStatus());
        queryWrapper.orderByDesc("id");
        queryWrapper.last("limit 1");
        AppReleaseModel appReleaseModel = appReleaseMapper.selectOne(queryWrapper);
        if (appReleaseModel == null){
            return null;
        }
        return AppReleaseConvert.INSTANCE.model2ResponsePro(appReleaseModel, languageEnums);
    }

    public boolean isVersionValid() {
        if (forceUpdate) {
            //按照版本号来支持登录
            String platformType = ClientInfoContext.get().getPlatformType();
            //增加版本判断，1.0.50版本以下的接口都提示下载新版本
            if(!PlatformEnums.WEB.name().equalsIgnoreCase(platformType)) {  //web 不是 web 才校验版本号
                if(StringUtils.isBlank(ClientInfoContext.get().getVersion())){
                    return false;
                }
                if (PlatformEnums.ANDROID.name().equalsIgnoreCase(ClientInfoContext.get().getPlatformType())) {
                    int result = VersionUtil.CompareVersions(ClientInfoContext.get().getVersion(), forceVersion);
                    if(result < 0){
                        return false;
                    }
                }
                if (PlatformEnums.ANDROID_GP.name().equalsIgnoreCase(ClientInfoContext.get().getPlatformType())) {
                    int result = VersionUtil.CompareVersions(ClientInfoContext.get().getVersion(), forceVersion);
                    if(result < 0){
                        return false;
                    }
                }
                if (PlatformEnums.IOS.name().equalsIgnoreCase(ClientInfoContext.get().getPlatformType())) {
                    int result =  VersionUtil.CompareVersions(ClientInfoContext.get().getVersion(), forceVersionIos);
                    if(result < 0){
                        return false;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 检查灰度发布
     */
    private AppReleaseModel checkGrayRelease(AppReleaseModel currentRelease) {
        try {
            String platformType = ClientInfoContext.get().getPlatformType();

            // 只对Android和Google Play进行灰度
            if (!platformType.equalsIgnoreCase("android") && !platformType.equalsIgnoreCase("android-gp")) {
                return null;
            }

            // 获取当前用户ID
            Long userId = getCurrentUserId();
            if (userId == null) {
                log.warn("无法获取用户ID，跳过灰度检查");
                return null;
            }

            // 解析灰度配置
            JSONObject grayConfig = parseGrayReleaseConfig();
            if (grayConfig == null || !grayConfig.getBooleanValue("enabled")) {
                return null;
            }

            // 获取平台特定的灰度配置
            String configKey = platformType.equalsIgnoreCase("android-gp") ? "googleplay" : "android";
            JSONObject platformGrayConfig = grayConfig.getJSONObject(configKey);

            if (platformGrayConfig == null || !platformGrayConfig.getBooleanValue("enabled")) {
                return null;
            }

            // 检查灰度策略
            if (isUserInGrayRelease(userId, platformGrayConfig)) {
                // 获取灰度版本
                AppReleaseModel grayRelease = getGrayReleaseVersion(platformType, platformGrayConfig);
                if (grayRelease != null) {
                    log.info("用户 {} 命中灰度发布，平台: {}, 灰度版本: {}",
                        userId, platformType, grayRelease.getVersion());
                    return grayRelease;
                }
            }

        } catch (Exception e) {
            log.error("灰度发布检查失败", e);
        }

        return null;
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        try {
            return CloudSession.getUid();
        } catch (Exception e) {
            log.error("获取用户ID失败", e);
            return null;
        }
    }

    /**
     * 解析灰度发布配置
     */
    private JSONObject parseGrayReleaseConfig() {
        try {
            // 从配置获取灰度配置
            return JSON.parseObject(grayReleaseConfig);
        } catch (Exception e) {
            log.error("解析灰度配置失败", e);
            return null;
        }
    }

    /**
     * 解析渠道下载配置
     */
    private JSONObject parseChannelDownloadConfig() {
        try {
            if (StringUtils.isBlank(channelDownloadConfig) || "{}".equals(channelDownloadConfig)) {
                return null;
            }
            return JSON.parseObject(channelDownloadConfig);
        } catch (Exception e) {
            log.error("解析渠道下载配置失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 根据渠道代码获取URL(下载地址或二维码地址)
     */
    private String getChannelURL(String channelCode, String originalUrl, boolean isQrCode) {
        if (StringUtils.isBlank(channelCode)) {
            return originalUrl;
        }

        try {
            ChannelConfigModel config = channelConfigMapper.selectOne(
                new QueryWrapper<ChannelConfigModel>()
                    .eq("channel_code", channelCode)
                    .eq("status", 1)
            );

            if (config == null) {
                return originalUrl;
            }

            String url = isQrCode ? config.getQrCode() : config.getDownloadUrl();
            if (StringUtils.isNotBlank(url)) {
                return url;
            }

            return originalUrl;

        } catch (Exception e) {
            return originalUrl;
        }
    }


    /**
     * 根据渠道代码获取URL(下载地址或二维码地址)
     */
    private String getChannelURLByInviteCode(String inviteCode, String originalUrl, boolean isQrCode) {
        if (StringUtils.isBlank(inviteCode)) {
            return originalUrl;
        }

        try {
            ChannelConfigModel config = channelConfigMapper.selectOne(
                    new QueryWrapper<ChannelConfigModel>()
                            .eq("invite_code", inviteCode)
                            .eq("status", 1)
            );

            if (config == null) {
                return originalUrl;
            }

            String url = isQrCode ? config.getQrCode() : config.getDownloadUrl();
            if (StringUtils.isNotBlank(url)) {
                return url;
            }

            return originalUrl;

        } catch (Exception e) {
            return originalUrl;
        }
    }

    /**
     * 判断用户是否在灰度范围内
     */
    private boolean isUserInGrayRelease(Long userId, JSONObject platformGrayConfig) {
        try {

            // 策略1: 从Redis获取灰度用户列表
            if (isUserInRedisGrayList(userId, platformGrayConfig)) {
                return true;
            }

            // 策略2: 百分比灰度
            /**
            Integer percentage = platformGrayConfig.getInteger("percentage");
            if (percentage != null && percentage > 0) {
                // 使用用户ID的哈希值进行一致性哈希
                int hash = Math.abs(userId.hashCode() % 100);
                if (hash < percentage) {
                    return true;
                }
            }*/

            // 策略4: 配置中的白名单用户
            JSONArray whitelistUsers = platformGrayConfig.getJSONArray("whitelistUsers");
            if (whitelistUsers != null && !whitelistUsers.isEmpty()) {
                for (int i = 0; i < whitelistUsers.size(); i++) {
                    Long whitelistUserId = whitelistUsers.getLong(i);
                    if (userId.equals(whitelistUserId)) {
                        return true;
                    }
                }
            }

            return false;
        } catch (Exception e) {
            log.error("判断灰度用户失败", e);
            return false;
        }
    }

    /**
     * 检查用户是否在Redis灰度列表中
     */
    private boolean isUserInRedisGrayList(Long userId, JSONObject platformGrayConfig) {
        try {
            String platformType = ClientInfoContext.get().getPlatformType();
            String configKey = platformType.equalsIgnoreCase("android-gp") ? "googleplay" : "android";

            // 从配置中获取Redis key
            String redisKey = platformGrayConfig.getString("redisKey");
            if (StringUtils.isBlank(redisKey)) {
                // 如果没有配置redisKey，使用默认格式
                redisKey = "app:gray:release:" + configKey + ":users";
            }

            log.debug("检查灰度用户，userId: {}, redisKey: {}", userId, redisKey);

            Boolean isMember = redisTemplate.opsForSet().isMember(redisKey, userId.toString());
            if (isMember != null && isMember) {
                log.info("用户 {} 在Redis灰度集合中 {}", userId, redisKey);
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("检查Redis灰度用户失败，userId: {}", userId, e);
            return false;
        }
    }

    /**
     * 添加用户到灰度列表（管理接口用）
     */
    public boolean addUserToGrayRelease(String platformType, Long userId) {
        try {
            String configKey = platformType.equalsIgnoreCase("android-gp") ? "googleplay" : "android";
            String redisKey = "app:gray:release:" + configKey + ":users";

            redisTemplate.opsForSet().add(redisKey, userId.toString());
            // 设置过期时间，比如30天
            redisTemplate.expire(redisKey, 30 * 24 * 60 * 60, java.util.concurrent.TimeUnit.SECONDS);

            log.info("添加用户 {} 到灰度列表 {}", userId, redisKey);
            return true;
        } catch (Exception e) {
            log.error("添加用户到灰度列表失败，userId: {}, platformType: {}", userId, platformType, e);
            return false;
        }
    }

    /**
     * 从灰度列表移除用户（管理接口用）
     */
    public boolean removeUserFromGrayRelease(String platformType, Long userId) {
        try {
            String configKey = platformType.equalsIgnoreCase("android-gp") ? "googleplay" : "android";
            String redisKey = "app:gray:release:" + configKey + ":users";

            redisTemplate.opsForSet().remove(redisKey, userId.toString());

            log.info("从灰度列表移除用户 {} {}", userId, redisKey);
            return true;
        } catch (Exception e) {
            log.error("从灰度列表移除用户失败，userId: {}, platformType: {}", userId, platformType, e);
            return false;
        }
    }

    /**
     * 批量添加用户到灰度列表
     */
    public boolean batchAddUsersToGrayRelease(String platformType, List<Long> userIds) {
        try {
            String configKey = platformType.equalsIgnoreCase("android-gp") ? "googleplay" : "android";
            String redisKey = "app:gray:release:" + configKey + ":users";

            String[] userIdStrings = userIds.stream()
                    .map(String::valueOf)
                    .toArray(String[]::new);

            redisTemplate.opsForSet().add(redisKey, userIdStrings);
            // 设置过期时间，比如30天
            redisTemplate.expire(redisKey, 30 * 24 * 60 * 60, java.util.concurrent.TimeUnit.SECONDS);

            log.info("批量添加 {} 个用户到灰度列表 {}", userIds.size(), redisKey);
            return true;
        } catch (Exception e) {
            log.error("批量添加用户到灰度列表失败，platformType: {}, userCount: {}",
                    platformType, userIds.size(), e);
            return false;
        }
    }

    /**
     * 获取灰度用户数量
     */
    public Long getGrayUserCount(String platformType) {
        try {
            String configKey = platformType.equalsIgnoreCase("android-gp") ? "googleplay" : "android";
            String redisKey = "app:gray:release:" + configKey + ":users";

            return redisTemplate.opsForSet().size(redisKey);
        } catch (Exception e) {
            log.error("获取灰度用户数量失败，platformType: {}", platformType, e);
            return 0L;
        }
    }

    /**
     * 获取灰度版本
     */
    private AppReleaseModel getGrayReleaseVersion(String platformType, JSONObject platformGrayConfig) {
        try {
            // 从配置中直接构建灰度版本信息
            String grayVersion = platformGrayConfig.getString("version");
            if (StringUtils.isBlank(grayVersion)) {
                return null;
            }

            // 创建灰度版本对象
            AppReleaseModel grayRelease = new AppReleaseModel();
            grayRelease.setVersion(grayVersion);
            grayRelease.setMinVersion("1.0.0");


            grayRelease.setPlatformType(AppReleasePlatformEnum.getByCode(platformType));
            grayRelease.setEnable(AppReleaseEnableEnum.ENABLED.getStatus());

            // 从配置中获取其他必要信息
            String downloadUrl = platformGrayConfig.getString("downloadUrl");

            if (StringUtils.isNotBlank(downloadUrl)) {
                grayRelease.setUrl(downloadUrl);

            }
            log.info("构建灰度版本信息成功，平台: {}, 版本: {}", platformType, grayVersion);
            return grayRelease;

        } catch (Exception e) {
            log.error("构建灰度版本失败，平台: {}", platformType, e);
            return null;
        }
    }


    /**
     * 根据渠道获取app版本列表
     * @param languageEnums 语言枚举
     * @param channelCode 渠道代码
     * @return 应用版本列表
     */
    public List<AppReleaseResponse> listAppReleaseByChannel(LanguageEnums languageEnums, String channelCode){
        List<AppReleaseModel> list = appReleaseMapper.selectListRelease();
        if (list == null){
            return null;
        }

        List<AppReleaseResponse> rtnList = new ArrayList<>();
        for(AppReleaseModel appReleaseModel : list ){

            AppReleaseResponse appResponse = AppReleaseConvert.INSTANCE.model2ResponsePro(appReleaseModel, languageEnums);

            // 根据渠道代码获取配置的下载地址和二维码地址
            if (StringUtils.isNotBlank(channelCode) &&  AppReleasePlatformEnum.ANDROID.getCode().equalsIgnoreCase(appReleaseModel.getPlatformType()))  {
                String originalUrl = appResponse.getUrl();
                String originalQrCode = appResponse.getQrCode();

                String channelUrl = getChannelURL(channelCode, originalUrl, false);
                String channelQrCode = getChannelURL(channelCode, originalQrCode, true);

                appResponse.setUrl(channelUrl);
                appResponse.setQrCode(channelQrCode);

            }

            rtnList.add(appResponse);
        }

        return rtnList;
    }

    /**
     * 根据邀请码获取app版本列表
     * @param languageEnums 语言枚举
     * @param inviteCode 邀请码
     * @return 应用版本列表
     */
    public List<AppReleaseResponse> listAppReleaseByInviteCode(LanguageEnums languageEnums, String inviteCode){
        List<AppReleaseModel> list = appReleaseMapper.selectListRelease();
        if (list == null){
            return null;
        }

        List<AppReleaseResponse> rtnList = new ArrayList<>();
        for(AppReleaseModel appReleaseModel : list ){

            AppReleaseResponse appResponse = AppReleaseConvert.INSTANCE.model2ResponsePro(appReleaseModel, languageEnums);

            // 根据渠道代码获取配置的下载地址和二维码地址
            if (StringUtils.isNotBlank(inviteCode) &&  AppReleasePlatformEnum.ANDROID.getCode().equalsIgnoreCase(appReleaseModel.getPlatformType()))  {
                String originalUrl = appResponse.getUrl();
                String originalQrCode = appResponse.getQrCode();

                String channelUrl = getChannelURLByInviteCode(inviteCode,  originalUrl, false);
                String channelQrCode = getChannelURLByInviteCode(inviteCode,  originalQrCode, true);

                appResponse.setUrl(channelUrl);
                appResponse.setQrCode(channelQrCode);
            }

            rtnList.add(appResponse);
        }
        return rtnList;
    }

}

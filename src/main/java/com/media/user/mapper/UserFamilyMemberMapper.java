package com.media.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.UserFamilyMemberModel;

import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.List;

@Mapper
public interface UserFamilyMemberMapper extends BaseMapper<UserFamilyMemberModel> {

    @Insert("<script> " +
            "INSERT INTO user_family_member(id, uid, pid, family_id, user_level, point_rate,created_time,status,deleted, sub_member_num,point_total,token_total) VALUES " +
            "<foreach collection='list' item='item' separator=','> " +
            "(#{item.id}, #{item.uid}, #{item.pid}, #{item.familyId}, #{item.userLevel}, #{item.pointRate}, #{item.createdTime}, #{item.status},#{item.deleted},#{item.subMemberNum},#{item.pointTotal},#{item.tokenTotal}) " +
            "</foreach> " +
            "</script>")
    long insertBatch(@Param("list") List<UserFamilyMemberModel> list);

    @Update("<script> " +
            "UPDATE user_family_member SET status = 1, created_time = #{updatedTime}, updated_time = #{updatedTime} where id IN" +
            "<foreach collection='ids' item='id' separator=',' open='(' close=')'> " +
            "#{id} " +
            "</foreach> " +
            "</script>")
    long updateUserFamilyMemberBatch(@Param("ids") List<Long> ids, @Param("updatedTime") Date updatedTime);

    @Update("<script> " +
            "<foreach collection='list' item='item' separator=';' open='' close=''> " +
            "UPDATE user_family_member SET token_total=#{item.tokenTotal}, updated_time=#{item.updatedTime} where id = #{item.id} " +
            "</foreach> " +
            "</script>")
    long updateBatch(@Param("list") List<UserFamilyMemberModel> list);

}
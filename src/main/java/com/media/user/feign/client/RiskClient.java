package com.media.user.feign.client;

import com.media.user.dto.request.LoginRiskRequest;
import com.media.user.dto.request.MarketingRiskRequest;
import com.media.user.dto.request.RegisterRiskRequest;
import com.media.user.dto.request.TaskLogRequest;
import com.media.user.dto.response.LoginRiskResponse;
import com.media.user.dto.response.MarketingRiskResponse;
import com.media.user.dto.response.RegisterRiskResponse;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.concurrent.TimeUnit;

/**
 * 风控服务客户端
 */
@FeignClient(url = "${risk.host:http://envtest-api.x.me}", value = "riskClient", configuration = RiskClient.RiskClientConfiguration.class)
public interface RiskClient {

    @Configuration
    class RiskClientConfiguration {
        @Bean("riskClientOptions")
        public Request.Options options() {
            // 设置连接超时为 200ms，读取超时为 2000ms
            return new Request.Options(200, TimeUnit.MILLISECONDS, 2000, TimeUnit.MILLISECONDS, true);
        }
    }

    /**
     * 创建任务日志
     * 
     * @param request 任务日志请求
     */
    @PostMapping(value = "/risk/user/task/log")
    void createTaskLog(@RequestBody TaskLogRequest request);

    /**
     * 登录风险检测
     * 
     * @param request 登录风险请求
     * @return 登录风险响应结果
     */
    @PostMapping(value = "/risk/login")
    LoginRiskResponse loginRisk(@RequestBody LoginRiskRequest request);

    /**
     * 注册风险检测
     * 
     * @param request 注册风险请求
     * @return 注册风险响应结果
     */
    @PostMapping(value = "/risk/register")
    RegisterRiskResponse registerRisk(@RequestBody RegisterRiskRequest request);

    /**
     * 营销风险检测
     * 
     * @param request 营销风险请求
     * @return 营销风险响应结果
     */
    @PostMapping(value = "/risk/marketing")
    MarketingRiskResponse marketingRisk(@RequestBody MarketingRiskRequest request);
}
package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.user.dto.request.NewBieTaskRequest;
import com.media.user.dto.response.PointRuleResponse;
import com.media.user.service.NewbieTaskRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/user/api/newbie")
public class NewbieTaskController {

    @Autowired
    private NewbieTaskRecordService newbieTaskRecordService;


    @PostMapping("/list")
//    @RequireAuth
    public List<PointRuleResponse> list(){
        Long uid = null;
        if(CloudSession.isLogin()){
            uid = CloudSession.getUid();
        }
        return newbieTaskRecordService.newBieTaskList(uid);
    }

    @PostMapping("/received")
    @RequireAuth
    public BigDecimal received(@Validated @RequestBody NewBieTaskRequest request){
        request.setUid(CloudSession.getUid());
         return newbieTaskRecordService.receiveNewBieTask(request);
    }


    @PostMapping("/update")
    @RequireAuth
    public void update(@Validated @RequestBody NewBieTaskRequest request){
        request.setUid(CloudSession.getUid());
        newbieTaskRecordService.completeNewBieTask(request);
    }

    @PostMapping("/activeInfo")
    @RequireAuth
    public Map<String, Long> activeInfo(){
        return newbieTaskRecordService.activeUserNum(CloudSession.getUid());
    }
}

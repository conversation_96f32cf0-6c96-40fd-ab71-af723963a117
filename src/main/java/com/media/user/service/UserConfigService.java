package com.media.user.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.core.config.SystemConfig;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.ClientUserModel;
import com.media.user.domain.UserConfigModel;
import com.media.user.dto.response.UserConfigResponse;
import com.media.user.enums.LanguageEnums;
import com.media.user.mapper.ClientUserMapper;
import com.media.user.mapper.UserConfigMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
public class UserConfigService {

    @Autowired
    private UserConfigMapper userConfigMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ClientUserMapper clientUserMapper;

    @Autowired
    SystemConfig systemConfig;

    @Autowired
    ConfigSwitchService configSwitchService;

    public UserConfigResponse getUserConfig(LanguageEnums languageEnums){
        String cacheKey = MediaUserConstant.USER_CONFIG + ":" + languageEnums.getValue();
        if (!Boolean.TRUE.equals(stringRedisTemplate.hasKey(cacheKey))){
            UserConfigModel userConfigModel = this.getConfigByLanguage(languageEnums.getValue());
            if(userConfigModel == null){
                userConfigModel = this.getConfigByLanguage(LanguageEnums.en_US.getValue());
            }
            stringRedisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(userConfigModel));
        }
        String userConfigModelStr = stringRedisTemplate.opsForValue().get(cacheKey);
        UserConfigModel userConfigModel = JSON.parseObject(userConfigModelStr, UserConfigModel.class);
        UserConfigResponse userConfigResponse = new UserConfigResponse();
        if(userConfigModel != null){
            BeanUtils.copyProperties(userConfigModel, userConfigResponse);
        }
        return userConfigResponse;
    }

    public Map<String, Integer> calcFaceXme(Long uid){
        Map<String, Integer> map = new HashMap<>();
        map.put("invitedGenesisXmeAmount", 0);
        map.put("faceGenesisXmeAmount", systemConfig.getFaceXmeAmount());
        ClientUserModel clientUserModel = clientUserMapper.selectById(uid);
        if(clientUserModel.getInviteUid() != null && clientUserModel.getInviteUid() > 0){
            map.put("invitedGenesisXmeAmount", systemConfig.getInvitedXmeAmount());
        }

        if(clientUserModel.getChannelId() != null && clientUserModel.getChannelId() > 0){
            map.put("invitedGenesisXmeAmount", systemConfig.getInvitedXmeAmount());
        }
        return map;
    }

    private UserConfigModel getConfigByLanguage(String language){
        QueryWrapper<UserConfigModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("language", language);
        return userConfigMapper.selectOne(queryWrapper);
    }
}

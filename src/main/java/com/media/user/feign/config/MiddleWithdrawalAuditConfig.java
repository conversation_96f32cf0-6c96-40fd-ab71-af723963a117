//package com.media.user.feign.config;
//
//import com.antalpha.prime.invest.properties.MiddleWithdrawalAuditProperties;
//import feign.RequestInterceptor;
//import feign.codec.ErrorDecoder;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Bean;
//import org.tomitribe.auth.signatures.Signature;
//import org.tomitribe.auth.signatures.Signer;
//
//import javax.crypto.Mac;
//import javax.crypto.spec.SecretKeySpec;
//import java.io.IOException;
//import java.nio.charset.StandardCharsets;
//import java.security.InvalidKeyException;
//import java.security.Key;
//import java.security.NoSuchAlgorithmException;
//import java.text.SimpleDateFormat;
//import java.util.*;
//
///**
// * <AUTHOR>  2022/7/27 15:45
// * @version 1.0
// */
//@Slf4j
//public class MiddleWithdrawalAuditConfig {
//    ErrorDecoder.Default defaultDecoder = new ErrorDecoder.Default();
//
//    @Bean
//    public RequestInterceptor testInterceptor(MiddleWithdrawalAuditProperties middleWithdrawalAuditProperties) {
//        return template -> {
//            String path = template.path();
//            log.info("path:{}", template.path());
//            Map<String, String> headers = new HashMap();
//            headers.put("date", getServerTime());
//            headers.put("(request-target)", path);
//            String signature = "";
//            try {
//                signature = getSignature(middleWithdrawalAuditProperties.getKey(), middleWithdrawalAuditProperties.getSecret(), "post",path, headers);
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//            template.header("date", headers.get("date"));
//            template.header("Authorization", signature);
//        };
//    }
//
////    @Bean
////    public ErrorDecoder errorDecoder() {
////        return (methodKey, response) -> {
////            String s = response.toString();
////            log.info("res => {}", s);
////            String body = null;
////            try {
////                body = IOUtils.toString(response.body().asReader(CharsetUtil.CHARSET_UTF_8));
////            } catch (Exception e) {
////                e.printStackTrace();
////            }
////            if (StringUtils.isNotBlank(body)) {
////                log.warn("error body: {}", body);
////                Object code = JSONPath.extract(body, "$.errors[0].code");
////                if (code == null || !StringUtils.isNumeric(code.toString())) {
////                    return new RuntimeException(body);
////                }
////                Object title = JSONPath.extract(body, "$.errors[0].title");
////                ApiException err = new ApiException(Integer.valueOf(code.toString()));
////                if (title != null) {
////                    err.setMessage(title.toString());
////                }
////                return err;
////            }
////            return defaultDecoder.decode(methodKey, response);
////        };
////    }
//
//    private String getHmac(String method, String query, String payload, MiddleWithdrawalAuditProperties middleWithdrawalAuditProperties) {
//        try {
//            String nonce = String.valueOf(System.currentTimeMillis());
//
//            String data = method + "\n" + query + "\n" + nonce;
//
//            if (payload != null) {
//                data += "\n" + payload;
//            }
//            SecretKeySpec signingKey = new SecretKeySpec(middleWithdrawalAuditProperties.getSecret().getBytes(StandardCharsets.UTF_8), "HmacSHA256");
//            Mac mac = Mac.getInstance("HmacSHA256");
//            mac.init(signingKey);
//            return middleWithdrawalAuditProperties.getKey() + ":" + toHexString(mac.doFinal(data.getBytes(StandardCharsets.UTF_8))) + ":" + nonce;
//        } catch (NoSuchAlgorithmException e) {
//            throw new RuntimeException(e);
//        } catch (InvalidKeyException e) {
//            throw new RuntimeException(e);
//        }
//
//    }
//
//    private String toHexString(byte[] bytes) {
//        Formatter formatter = new Formatter();
//        for (byte b : bytes) {
//            formatter.format("%02x", b);
//        }
//        return formatter.toString();
//    }
//
//    private String getSignature (String keyId, String secret, String method, String uri, Map<String, String> headers) throws IOException {
//        final Signature signature = new Signature(keyId, "hmac-sha256", null, headers.keySet().toArray(new String[0]));
//        final Key key = new SecretKeySpec(secret.getBytes(), "HmacSHA256");
//        Signer signer = new Signer(key, signature);
//        Signature sign = signer.sign(method, uri, headers);
//        return sign.toString();
//    }
//
//    private String getServerTime() {
//        Calendar calendar = Calendar.getInstance();
//        SimpleDateFormat dateFormat = new SimpleDateFormat(
//                "EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
//        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
//        return dateFormat.format(calendar.getTime());
//    }
//}

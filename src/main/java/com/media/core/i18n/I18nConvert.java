package com.media.core.i18n;


import com.media.user.enums.LanguageEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.NoSuchMessageException;

import java.util.Locale;

@Slf4j
public class I18nConvert {

    public static String getI18nMessage(String key, LanguageEnums language) {
        if (StringUtils.isBlank(key)) {
            return "";
        }
        if (language == null) {
            language = LanguageEnums.en_US;
        }

        return LocalMessageResource.getMessage(key, Locale.forLanguageTag(language.getValue()));
    }

    public static String getI18nMessageWithDefaultVal(String key, LanguageEnums language, String defaultVal) {
        if (StringUtils.isBlank(key)) {
            return "";
        }
        if (language == null) {
            language = LanguageEnums.en_US;
        }

        String message = "";
        try {
            // 尝试获取指定语言的消息
            message = LocalMessageResource.getMessage(key, Locale.forLanguageTag(language.getValue()));
        } catch (NoSuchMessageException e) {
            log.error("I18nConvert.getI18nMessage error: {} key :{}", e.getMessage(),key);
        }

        // 如果当前语言不是英语，且消息为空，尝试获取英语消息
        if (language != LanguageEnums.en_US && "".equals(message)) {
            try {
                message = LocalMessageResource.getMessage(key, Locale.forLanguageTag(LanguageEnums.en_US.getValue()));
            } catch (NoSuchMessageException e) {
                log.error("I18nConvert.getI18nMessage error: {} key: {}", e.getMessage(),key);
                // 英语消息也不存在，使用默认值
                return defaultVal;
            }
        }

        if (StringUtils.isBlank(message)) {
            return defaultVal;
        }
        return message;
    }
}

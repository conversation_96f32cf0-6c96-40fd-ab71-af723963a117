package com.media.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.user.domain.*;
import com.media.user.dto.response.*;
import com.media.user.dto.response.internal.UserMeResponse;
import com.media.user.enums.UserFollowStateEnum;
import com.media.user.service.internal.UserInternalService;
import org.apache.ibatis.ognl.security.UserMethod;
import org.springframework.data.redis.core.StringRedisTemplate;
import com.media.user.mapper.TwitterUserMapper;
import com.media.user.service.cache.ClientUserCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class UserRecommendService {

    @Autowired
    UserFollowService userFollowService;

    @Autowired
    TwitterUserMapper twitterUserMapper;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @Autowired
    UserInternalService userInternalService;

    @Value(value = "${user.recommendCount}")
    private Integer recommendCount;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Value("${redis.user.recommend.cn.key:user:recommend:cn}")
    private String redisKeyRecommend;

    //审核期间显示
    public List<UserFollowingMemberResponse> recommendCheck(Long uid) {
        List<UserFollowingMemberResponse> list = new ArrayList<>();
        List<Long> recommendUids = new ArrayList<>();
        recommendUids.add(17108098624728L);  //AlexShin
        recommendUids.add(18107972288196L);  //mantian0329
        recommendUids.add(13108056987093L);  //jinrui9061
        recommendUids.add(12108017260264L);  //zzzzzeroooo
        recommendUids.add(13107969293202L);  //Moon1ightSt
        recommendUids.add(11107965142720L);  //Crypto_He
        for(Long tmpId : recommendUids) {
            list.add(this.convertCheck(uid, tmpId));
        }
        return list;
    }


    /**
     * 组装数据
     * @param uid
     * @return
     */
    private UserFollowingMemberResponse convertCheck(Long uid, Long recommendUid) {
        UserMeResponse userMeResponse = clientUserCacheService.userInfo(recommendUid);
        UserFollowingMemberResponse modelFollowing = new UserFollowingMemberResponse();
        modelFollowing.setUid(recommendUid)
                .setNickName(userMeResponse.getNickName())
                .setUserName(userMeResponse.getEmail())
                .setFollowState(UserFollowStateEnum.FOLLOW_STATE_0.getState())
                .setFansCount(userMeResponse.getFansNumber())
                .setAvatarUrl(userMeResponse.getAvatarUrl());

        boolean isFollowing = userFollowService.isFollowing(uid, recommendUid);
        if(isFollowing) {
            modelFollowing.setFollowState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
        }
        return modelFollowing;
    }

    //未关注的用户
    //第一条记录 xme 第二条记录 musk
    //随机获取用户表中 100 条记录
    //从结果中随机抽取 7 条数据
    public List<UserFollowingMemberResponse> recommend(Long uid) {
        List<UserFollowingMemberResponse> list = new ArrayList<>();

        //获取 xme 和 musk 的用户信息，并看当前用户是否已经关联
        TwitterUserModel xmeTwitter = this.getTwitterByUsername("Xme_Anything");
        if(xmeTwitter != null) {
            UserFollowingMemberResponse xmeResponse = this.convert(uid, xmeTwitter);
            if(xmeResponse == null || Objects.equals(xmeResponse.getFollowState(), UserFollowStateEnum.FOLLOW_STATE_0.getState())) {
                list.add(xmeResponse);
            }
        }

        TwitterUserModel muskTwitter = this.getTwitterByUsername("elonmusk");
        if(muskTwitter != null) {
            UserFollowingMemberResponse muskResponse = this.convert(uid, muskTwitter);
            if(muskResponse == null || Objects.equals(muskResponse.getFollowState(), UserFollowStateEnum.FOLLOW_STATE_0.getState())) {
                list.add(muskResponse);
            }
        }

        int count = recommendCount;  //总共返回 9 条数据
        if(count < 2) {
            count = 2;
        }
        int fixedCount = list.size();  //固定用户的数量
        int need = count - fixedCount;
        if(need > 0){
            if(uid == null) {
                uid = 0L;
            }

            List<TwitterUserModel> twitterUser100 =  twitterUserMapper.getTwitterUser100(uid);
            int size = twitterUser100.size() > 100 ? 100: twitterUser100.size();

            if(need > size){
                need = size;
            }

            Integer[] l = this.random(size, need);
            for(Integer index : l){
                list.add(this.convert(uid, twitterUser100.get(index - 1)));
            }
        }
        return list;
    }


    //获取中国区的用户
    //从结果中随机抽取最大 showCnt 条数据
    //默认取未关注的，未关注的太少用关注的补足
    public List<UserFollowingMemberResponse> recommendCN(Long uid) {
        List<UserFollowingMemberResponse> list = new ArrayList<>();

        // 获取100个随机用户
        Set<String> randomUsers = getRandomUsersFromRedis(redisKeyRecommend, 20);
        List<Long> randomUserIDs = new ArrayList<Long>();
        for (String randomUser : randomUsers) {
            randomUserIDs.add(Long.parseLong(randomUser));
        }

        if (!randomUserIDs.isEmpty()) {
            Map<Long, UserMeResponse> recommendUsers = userInternalService.batchUserMes(
                    uid,
                    randomUserIDs,
                    true, false, true
            );

            // 先添加未关注的用户
            List<UserFollowingMemberResponse> unfollowedUsers = new ArrayList<>();
            List<UserFollowingMemberResponse> followedUsers = new ArrayList<>();

            // 分类用户为已关注和未关注
            for (Map.Entry<Long, UserMeResponse> entry : recommendUsers.entrySet()) {
                Long recommendUid = entry.getKey();
                UserMeResponse userInfo = entry.getValue();

                UserFollowingMemberResponse modelFollowing = new UserFollowingMemberResponse();
                modelFollowing.setFollowState(UserFollowStateEnum.FOLLOW_STATE_0.getState());
                modelFollowing.setUid(recommendUid)
                        .setNickName(userInfo.getNickName())
                        .setUserName(userInfo.getEmail())
                        .setFollowState(userInfo.getFollowState())
                        .setFansCount(userInfo.getFansNumber())
                        .setAvatarUrl(userInfo.getAvatarUrl());

                // 根据关注状态分类
                if (userInfo.getFollowState() != null && userInfo.getFollowState() == UserFollowStateEnum.FOLLOW_STATE_1.getState()) {
                    followedUsers.add(modelFollowing);
                } else {
                    modelFollowing.setFollowState(UserFollowStateEnum.FOLLOW_STATE_0.getState());
                    unfollowedUsers.add(modelFollowing);
                }
            }

            // 先添加未关注的用户
            for (UserFollowingMemberResponse user : unfollowedUsers) {
                list.add(user);
                if (list.size() >= recommendCount) {
                    break;
                }
            }

            // 如果未关注的用户不足showCnt，则用已关注的用户补足
            if (list.size() < recommendCount) {
                for (UserFollowingMemberResponse user : followedUsers) {
                    list.add(user);
                    if (list.size() >= recommendCount) {
                        break;
                    }
                }
            }
        } else {
            return recommend(uid);
        }
        return list;
    }

    /**
     * 从Redis中随机获取指定数量的用户
     */
    private Set<String> getRandomUsersFromRedis(String key, int count) {
        Set<String> randomUsers = new HashSet<>();
        try {
            Long setSize = stringRedisTemplate.opsForSet().size(key);
            if (setSize == null || setSize == 0) {
                return randomUsers;
            }

            // 如果集合大小小于需要的数量，直接返回所有成员
            if (setSize <= count) {
                Set<String> allMembers = stringRedisTemplate.opsForSet().members(key);
                return allMembers != null ? allMembers : randomUsers;
            }

            // 随机抽取指定数量的用户
            randomUsers = stringRedisTemplate.opsForSet().distinctRandomMembers(key, count);
        } catch (Exception e) {
            log.error("Failed to get random users from redis, key: {}", key, e);
        }
        return randomUsers != null ? randomUsers : new HashSet<>();
    }

    /**
     * 根据 twitter 名称获取信息
     * @return
     */
    private TwitterUserModel getTwitterByUsername(String userName){
        QueryWrapper<TwitterUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", userName);
        return twitterUserMapper.selectOne(queryWrapper);
    }

    /**
     * 组装数据
     * @param uid
     * @param twitterUserModel
     * @return
     */
    private UserFollowingMemberResponse convert(Long uid, TwitterUserModel twitterUserModel){
        Long tmpUid = twitterUserModel.getOldUid();
        String nickName = twitterUserModel.getName();
        String avatarUrl = twitterUserModel.getProfileImageUrl();
        Long fansCount = 0L;
        if(twitterUserModel.getNewUid() != null) {
            tmpUid = twitterUserModel.getNewUid();
        }
        UserMeResponse userMeResponse = clientUserCacheService.userInfo(tmpUid);
        if(userMeResponse != null){
            nickName = userMeResponse.getNickName();
            avatarUrl = userMeResponse.getAvatarUrl();
            fansCount = userMeResponse.getFansNumber();
        }
        UserFollowingMemberResponse modelFollowing = new UserFollowingMemberResponse();
        modelFollowing.setUid(tmpUid)
                .setNickName(nickName)
                .setUserName(twitterUserModel.getUsername())
                .setFollowState(UserFollowStateEnum.FOLLOW_STATE_0.getState())
                .setFansCount(fansCount)
                .setAvatarUrl(avatarUrl);

        boolean isFollowing = userFollowService.isFollowing(uid, tmpUid);
        if(isFollowing) {
            modelFollowing.setFollowState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
        }
        return modelFollowing;
    }

    /**
     * 随机获取对应的数据
     * @param size
     * @param need
     * @return
     */
    private Integer[] random(int size, int need){
        Random random = new Random();
        Set<Integer> numbers = new HashSet<>();

        while (numbers.size() < need) {
            int number = random.nextInt(size) + 1;
            numbers.add(number);
        }

        // 转换为数组并排序
        return numbers.toArray(new Integer[0]);
    }

}

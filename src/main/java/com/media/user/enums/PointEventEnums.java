package com.media.user.enums;

import lombok.Getter;

import java.util.List;
import java.util.Set;

@Getter
public enum PointEventEnums {

    LOGIN(1L),

    READ(2L),

    LIKE(3L),

    COLLECT(4L),

    SHARE(5L),

    INVITE(6L),

    SIGNUP(7L),

    EXCHANGE_POINT_FOR_TOKENS(8L),
    /**
     * 游戏抽奖
     */
    GAME_PRIZE(9L),

    /**
     * 扩容卡
     */
    EXPANSION_CARD(10L),

    /**
     * 家族令
     */
    FAMILY_ORDER(11L),

    /**
     * 积分加成
     */
    POINT_BONUS(12L),

    /**
     * 创世家族令
     */
    CREATION_FAMILY_ORDER(13L),

    /**
     * Fomo Doge
     */
    FOMO_DOGE(14L),

    /**
     * 退出家族
     */
    EXIT_THE_FAMILY(15L),
    /**
     * 内容激励
     */
    CONTENT_INCENTIVES(16L),


    /**
     * 账户合并
     */
    ACCOUNT_CONSOLIDATION(17L),

    /**
     * 新手任务关注用户
     */
    NOVICE_FOLLOW_USERS(18L),

    /**
     * 完善信息
     */
    NOVICE_IMPROVE_INFORMATION(20L),
    /**
     * 加入官方社群
     */
    NOVICE_JOIN_OFFICIAL_GROUP(21L),
    /**
     * 签到任务第一天
     */
    SIGN_IN_ONE(22L),
    /**
     * 签到任务第二天
     */
    SIGN_IN_TWO(23L),
    /**
     * 签到第三天
     */
    SIGN_IN_THREE(24L),
    /**
     * 签到第四天
     */
    SIGN_IN_FOUR(25L),

    /**
     * 签到第五天
     */
    SIGN_IN_FIVE(26L),
    /**
     * 签到第六天
     */
    SIGN_IN_SIX(27L),
    /**
     * 签到第七天
     */
    SIGN_IN_SEVEN(28L),

    /**
     * 邀请获得XME
     */
    INVITE_XME(29L),

    /**
     * 人脸认证 xme event
     */
    LIVE_AUTH_XME(30L),
    ;



    private final Long eventId;

    private PointEventEnums(Long eventId){
        this.eventId = eventId;
    }

    public static List<Long> getUserJobEventIds(){
        return List.of(LOGIN.eventId,READ.eventId, LIKE.eventId, COLLECT.eventId, SHARE.eventId, INVITE.eventId);
    }

    public static Set<Long> getPointBonusEventIds(){
        return Set.of(LOGIN.eventId,READ.eventId, LIKE.eventId, COLLECT.eventId, SHARE.eventId, INVITE.eventId);
    }

    public static Set<Long> getWriteEventIds(){
        return Set.of(LOGIN.eventId,READ.eventId, LIKE.eventId, COLLECT.eventId, SHARE.eventId, INVITE.eventId, POINT_BONUS.eventId, SIGNUP.eventId);
    }

    public static Set<Long> getNewbieTaskEventIds(){
        return Set.of(NOVICE_FOLLOW_USERS.eventId, NOVICE_IMPROVE_INFORMATION.eventId, NOVICE_JOIN_OFFICIAL_GROUP.eventId);
    }

    public static PointEventEnums getEnumsByEventId(Long eventId){
        for (PointEventEnums pointEventEnums : PointEventEnums.values()){
            if (pointEventEnums.getEventId().equals(eventId)){
                return pointEventEnums;
            }
        }
        return null;
    }
}

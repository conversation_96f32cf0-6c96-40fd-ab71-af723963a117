package com.media.user.dto.response;

import lombok.Data;

import java.util.List;

@Data
public class BannerResponse {
    private List<BannerItem> list;

    @Data
    public static class BannerItem {
        private String id;          // 活动ID
        private String title;       // 活动标题
        private String description;  // 描述标题
        private String icon;        // 图标
        private String jumpType;        // 跳转类型: native/h5
        private String jumpUrl;         // 跳转链接
        private boolean clickable;  // 是否可点击
        private String backgroundImage; // 背景图片地址
        private Integer status;     // 活动状态
        private Integer order;      // 排序
        private boolean needLogin;  // 是否需要登录
        private boolean needFace;  // 是否需要人脸
        private boolean needVerify; //是否需要三合一认证
        private String textColor;  // 文本颜色
    }
}

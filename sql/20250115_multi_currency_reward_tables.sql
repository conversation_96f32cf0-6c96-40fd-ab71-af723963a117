-- 多货币奖励系统数据库表结构
-- 执行日期: 2025-01-15
-- 说明: 支持多货币奖励的邀请系统表结构

-- 1. 修改 user_invite_face_record 表，添加多货币支持字段和奖励类型
ALTER TABLE `user_invite_face_record` 
-- 添加第一种货币的分离币种字段
ADD COLUMN `from_amount_symbol` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'XME' COMMENT '邀请人货币币种' AFTER `from_amount`,
-- 添加第二种货币相关字段
ADD COLUMN `from_amount_2` bigint DEFAULT NULL COMMENT '邀请人第二种货币数量' AFTER `amount_symbol`,
ADD COLUMN `from_amount_symbol_2` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邀请人第二种货币币种' AFTER `from_amount_2`,
ADD COLUMN `to_amount_2` bigint DEFAULT NULL COMMENT '被邀请人第二种货币数量' AFTER `from_amount_symbol_2`,
ADD COLUMN `amount_symbol_2` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '被邀请人第二种货币币种' AFTER `to_amount_2`,
-- 添加奖励类型字段
ADD COLUMN `reward_type` tinyint NOT NULL DEFAULT 1 COMMENT '奖励类型: 1-邀请奖励, 2-认证奖励, 3-被邀请奖励' AFTER `amount_symbol_2`,
ADD INDEX `idx_reward_type` (`reward_type`) COMMENT '奖励类型索引';

-- 2. 修改 user_invite_relation 表，添加活动ID字段
ALTER TABLE `user_invite_relation` 
ADD COLUMN `activity_id` bigint DEFAULT NULL COMMENT '活动ID-关联的活动标识，非活动邀请时为NULL' AFTER `invite_type`,
ADD INDEX `idx_activity_id` (`activity_id`) COMMENT '活动ID索引';

-- 3. 创建用户邀请奖励汇总表
CREATE TABLE `user_invite_reward_summary` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` bigint NOT NULL COMMENT '用户ID',
  `currency` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '货币类型',
  `total_amount` bigint NOT NULL DEFAULT '0' COMMENT '总奖励数量',
  `total_count` int NOT NULL DEFAULT '0' COMMENT '总奖励次数',
  `last_updated_time` datetime DEFAULT NULL COMMENT '最后更新时间',
  `created_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_uid_currency` (`uid`, `currency`) USING BTREE,
  KEY `idx_uid` (`uid`) USING BTREE,
  KEY `idx_currency` (`currency`) USING BTREE,
  KEY `idx_total_amount` (`total_amount`) USING BTREE,
  KEY `idx_last_updated_time` (`last_updated_time`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户邀请奖励汇总表';

-- 4. 验证表结构
SHOW CREATE TABLE user_invite_face_record;
SHOW CREATE TABLE user_invite_relation; 
SHOW CREATE TABLE user_invite_reward_summary;

-- 5. 查看表字段信息
DESCRIBE user_invite_face_record;
DESCRIBE user_invite_relation;
DESCRIBE user_invite_reward_summary;

-- 6. 数据迁移说明
-- 注意：执行ALTER TABLE后，需要更新现有数据中的 from_amount_symbol 字段
-- 因为我们设置了默认值'XME'，但如果历史数据中有其他货币，需要手动更新

-- 示例：如果需要更新历史数据中的货币符号
-- UPDATE user_invite_face_record 
-- SET from_amount_symbol = amount_symbol 
-- WHERE from_amount_symbol = 'XME' AND amount_symbol != 'XME';

-- 7. 权限检查（可选）
-- 确保应用用户有对新表的操作权限
-- GRANT SELECT, INSERT, UPDATE, DELETE ON database_name.user_invite_reward_summary TO 'app_user'@'%';

COMMIT; 
# 弹窗接口说明文档

## 接口概述

### 获取弹窗列表
**接口路径**: `GET /user/api/system/popup/list`

**接口描述**: 根据场景获取当前可用的弹窗配置列表，服务端会进行基础过滤（时间、版本、审核状态等），客户端根据返回的显示规则控制具体的显示逻辑。

### 请求参数

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| scene | String | 否 | 弹窗显示场景，不传默认为"home" | home, task, profile, login |

### 响应格式

```json
{
    "code": 200,
    "message": "success", 
    "result": [
        {
            "id": "social_popup",
            "title": "加入全球传播狂欢，3 步轻松拿奖",
            "description": "①分享内容 ②邀请好友 ③好友认证，立即开启奖励之路",
            "buttonText": "开始我的传播之旅",
            "backgroundImage": "https://s3.x.me/images/popup/social.png",
            "jumpType": "h5",
            "jumpUrl": "https://test-outer-h5.x.me/shareLeaderboard",
            "clickable": true,
            "status": 2,
            "order": 10,
            "needLogin": false,
            "needFace": false,
            "scene": "home",
            "shouldShow": true,
            "displayRule": {
                "type": "once_per_day",
                "interval": 24,
                "maxTimes": 1,
                "priority": "high"
            }
        }
    ],
    "success": true
}
```

## 返回字段详细说明

### 基础响应字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | Integer | 响应状态码，200表示成功 |
| message | String | 响应消息，通常为"success" |
| result | Array | 弹窗配置列表 |
| success | Boolean | 请求是否成功 |

### 弹窗配置对象字段 (result数组中的对象)

#### 基础信息字段

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | String | 弹窗唯一标识符 | "social_popup" |
| title | String | 弹窗标题（已根据用户语言进行国际化翻译） | "加入全球传播狂欢，3 步轻松拿奖" |
| description | String | 弹窗描述内容（已根据用户语言进行国际化翻译） | "①分享内容 ②邀请好友 ③好友认证，立即开启奖励之路" |
| buttonText | String | 按钮文字（已根据用户语言进行国际化翻译） | "开始我的传播之旅" |

#### 显示相关字段

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| backgroundImage | String | 弹窗背景图片URL | "https://s3.x.me/images/popup/social.png" |
| jumpType | String | 点击跳转类型：<br/>• native: 原生页面<br/>• h5: H5页面<br/>• external: 外部链接 | "h5" |
| jumpUrl | String | 点击后的跳转链接 | "https://test-outer-h5.x.me/shareLeaderboard" |
| clickable | Boolean | 弹窗是否可点击 | true |

#### 状态和控制字段

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| status | Integer | 弹窗状态：<br/>• 1: 激活<br/>• 2: 暂停<br/>• 3: 结束 | 2 |
| order | Integer | 显示优先级排序，数值越小优先级越高 | 10 |
| needLogin | Boolean | 是否需要用户登录才显示 | false |
| needFace | Boolean | 是否需要人脸验证才能操作 | false |
| scene | String | 弹窗适用场景，支持多个场景用逗号分隔 | "home" 或 "home,task" |
| shouldShow | Boolean | 服务端建议是否显示（目前固定为true，由客户端根据displayRule判断） | true |

#### 显示规则字段 (displayRule对象)

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| type | String | 显示规则类型：<br/>• once_per_day: 每天最多显示一次<br/>• once_per_week: 每周最多显示一次<br/>• once_per_session: 每次会话最多显示一次<br/>• always: 总是显示<br/>• custom: 自定义间隔 | "once_per_day" |
| interval | Integer | 显示间隔时间（小时），客户端用于计算下次可显示时间 | 24 |
| maxTimes | Integer | 最大显示次数限制（按天/周计算） | 1 |
| priority | String | 显示优先级：<br/>• high: 高优先级<br/>• medium: 中等优先级<br/>• low: 低优先级 | "high" |

## 客户端处理建议

### 1. 显示规则判断
客户端应根据 `displayRule` 字段控制弹窗的显示时机：

```javascript
// 示例：判断是否应该显示弹窗
function shouldShowPopup(popup) {
    const rule = popup.displayRule;
    const lastShown = localStorage.getItem(`popup_${popup.id}_lastShown`);
    
    if (!rule || rule.type === 'always') return true;
    
    const now = Date.now();
    const lastShownTime = lastShown ? parseInt(lastShown) : 0;
    
    switch (rule.type) {
        case 'once_per_day':
            return (now - lastShownTime) >= 24 * 60 * 60 * 1000;
        case 'once_per_week':
            return (now - lastShownTime) >= 7 * 24 * 60 * 60 * 1000;
        case 'custom':
            return (now - lastShownTime) >= rule.interval * 60 * 60 * 1000;
        default:
            return true;
    }
}
```

### 2. 多场景支持
当 `scene` 字段包含多个场景时（如 "home,task"），弹窗在这些场景下都可以显示。

### 3. 优先级排序
建议按以下顺序对弹窗进行排序：
1. priority（high > medium > low）
2. order（数值小的在前）

### 4. 统计上报
当弹窗显示时，调用记录接口：
```javascript
fetch(`/user/api/system/popup/record?popupId=${popup.id}`);
```

## 常见场景示例

### 场景1：首页欢迎弹窗
- **scene**: "home"
- **displayRule.type**: "once_per_day" 
- **needLogin**: false
- **priority**: "high"

### 场景2：任务中心推广弹窗
- **scene**: "task"
- **displayRule.type**: "once_per_week"
- **needLogin**: true
- **priority**: "medium"

### 场景3：多场景通用弹窗
- **scene**: "home,task,profile"
- **displayRule.type**: "custom", **interval**: 72
- **needLogin**: false
- **priority**: "low"

## 注意事项

1. **国际化**：title、description、buttonText 字段已根据用户语言设置进行翻译
2. **时间控制**：服务端已过滤掉未开始或已结束的弹窗
3. **版本控制**：服务端已过滤掉不满足最低版本要求的弹窗
4. **审核状态**：在审核状态下，部分弹窗可能不会返回
5. **客户端控制**：最终是否显示弹窗由客户端根据displayRule规则决定 
package com.media.user.context;

import com.media.user.model.internal.VerifyInfo;
import com.media.user.domain.ClientUserModel;
import lombok.extern.slf4j.Slf4j;
import java.util.Date;

/**
 * 验证上下文管理器
 * 负责管理用户注册过程中的验证信息
 */
@Slf4j
public class VerificationContext {
    
    private static final ThreadLocal<VerifyInfo> CONTEXT = new ThreadLocal<VerifyInfo>() {
        @Override
        protected VerifyInfo initialValue() {
            return new VerifyInfo();
        }
    };
    
    /**
     * 初始化验证上下文
     */
    public static void init() {
        CONTEXT.set(new VerifyInfo());
        log.debug("Verification context initialized");
    }
    
    /**
     * 获取当前验证上下文
     */
    public static VerifyInfo get() {
        VerifyInfo context = CONTEXT.get();
        if (context == null) {
            context = new VerifyInfo();
            CONTEXT.set(context);
        }
        return context;
    }
    
    /**
     * 设置手机验证状态
     */
    public static void setPhoneVerified(boolean verified) {
        get().setHasPhoneVerify(verified);
        log.debug("Phone verification status set to: {}", verified);
    }
    
    /**
     * 设置邮箱验证状态
     */
    public static void setEmailVerified(boolean verified) {
        get().setHasEmailVerify(verified);
        log.debug("Email verification status set to: {}", verified);
    }
    
    /**
     * 设置人脸识别状态
     */
    public static void setFaceLivenessVerified(boolean verified) {
        get().setHasFaceLiveness(verified);
        log.debug("Face liveness verification status set to: {}", verified);
    }
    
    /**
     * 检查是否已进行手机验证
     */
    public static boolean isPhoneVerified() {
        return get().isHasPhoneVerify();
    }
    
    /**
     * 检查是否已进行邮箱验证
     */
    public static boolean isEmailVerified() {
        return get().isHasEmailVerify();
    }
    
    /**
     * 检查是否已进行人脸识别验证
     */
    public static boolean isFaceLivenessVerified() {
        return get().isHasFaceLiveness();
    }
    
    /**
     * 将验证信息应用到用户模型
     */
    public static void applyToClientUserModel(Date time, ClientUserModel clientUserModel) {
        get().setClientUserModel(time, clientUserModel);
        log.debug("Verification info applied to client user model");
    }
    
    /**
     * 清理验证上下文
     * 必须在请求结束时调用，避免内存泄露
     */
    public static void clear() {
        CONTEXT.remove();
        log.debug("Verification context cleared");
    }
    
    /**
     * 执行带验证上下文的操作
     * 自动管理上下文的创建和清理
     */
    public static <T> T execute(VerificationOperation<T> operation) {
        try {
            init();
            return operation.execute();
        } catch (RuntimeException e) {
            // 直接抛出运行时异常，不包装
            throw e;
        } catch (Exception e) {
            // 只包装检查异常
            throw new RuntimeException("Verification operation failed", e);
        } finally {
            clear();
        }
    }
    
    /**
     * 验证操作接口
     */
    @FunctionalInterface
    public interface VerificationOperation<T> {
        T execute() throws Exception;
    }
} 
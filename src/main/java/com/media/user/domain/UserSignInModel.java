package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("user_sign_in")
@Data
@Accessors(chain = true)
public class UserSignInModel extends Model<UserSignInModel> {


    @TableId(value = "sign_id", type = IdType.AUTO)
    private Long signId;

    /**
     * 签到用户ID
     */
    @TableField("uid")
    private Long uid;

    /**
     * 签到时间 yyyy-mm-dd
     */
    @TableField("sign_date")
    private String signDate;

    /**
     * 签到天数
     */
    @TableField("day_index")
    private Integer dayIndex;

    /**
     * 签到积分
     */
    @TableField("day_point")
    private Integer dayPoint;


    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;


}
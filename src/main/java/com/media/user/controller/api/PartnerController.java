package com.media.user.controller.api;

import com.media.user.dto.response.PartnerAccessTokenResponse;
import com.media.user.service.PartnerTokenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/user/api/partner")
public class PartnerController {

    @Autowired
    private PartnerTokenService partnerTokenService;

    /**
     * 测试获取access_token
     */
    @GetMapping("/token")
    public PartnerAccessTokenResponse getToken(
            @RequestParam String userId,
            @RequestParam(required = false) String userName,
            @RequestParam(required = false) String avatar) {
        
        log.info("获取access_token请求: userId={}, userName={}", userId, userName);
        return partnerTokenService.getAccessToken(userId, userName, avatar);
    }
} 
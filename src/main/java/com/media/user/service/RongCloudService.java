package com.media.user.service;

import com.media.core.auth.CloudSession;
import com.media.user.domain.ClientUserModel;
import com.media.user.dto.response.RongcloudMessageResponse;
import com.media.user.mapper.ClientUserMapper;
import com.media.user.service.RongcloudMessageService;
import io.rong.CenterEnum;
import io.rong.RongCloud;
import io.rong.methods.user.User;
import io.rong.models.response.TokenResult;
import io.rong.models.user.UserModel;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RongCloudService {

    @Value("${rongcloud.appKey:c9kqb3rdcfdyj}")
    private String appKey;

    @Value("${rongcloud.appSecret:AojZ11NRimLj}")
    private String appSecret;

    private RongCloud rongCloud;

    @Autowired
    private ClientUserMapper clientUserMapper;

    @Autowired
    private RongcloudMessageService rongcloudMessageService;

    @PostConstruct
    public void init() {
        this.rongCloud = RongCloud.getInstance(
                appKey,
                appSecret,
                CenterEnum.SG);
    }

    /**
     * 获取用户 Token
     */
    public TokenResult getToken() {
        ClientUserModel clientUserModel = clientUserMapper.selectByUserId(CloudSession.getUid());
        try {
            User user = rongCloud.user;
            UserModel userModel = new UserModel()
                    .setId(String.valueOf(clientUserModel.getUid()))
                    .setName(clientUserModel.getUsername());
            TokenResult result = user.register(userModel);
            if (result != null && result.getToken() != null) {
                log.info("融云Token获取成功: userId={}", clientUserModel.getUid());
                return result;
            } else {
                log.error("融云Token获取失败");
            }
        } catch (Exception e) {
            log.error("融云Token请求异常: userId={}", clientUserModel.getUid(), e);
        }
        return null;
    }

    /**
     * 使用新的REST API发送私聊消息
     * 
     * @param fromUserId 发送者用户ID
     * @param toUserId   接收者用户ID
     * @param content    消息内容
     * @return 发送结果
     */
    public RongcloudMessageResponse sendPrivateMessage(String fromUserId, String toUserId, String content) {
        return rongcloudMessageService.sendTextMessage(fromUserId, toUserId, content, null);
    }

    /**
     * 使用新的REST API发送私聊消息（带额外信息）
     * 
     * @param fromUserId 发送者用户ID
     * @param toUserId   接收者用户ID
     * @param content    消息内容
     * @param extra      额外信息
     * @return 发送结果
     */
    public RongcloudMessageResponse sendPrivateMessageWithExtra(String fromUserId, String toUserId, String content,
            String extra) {
        return rongcloudMessageService.sendTextMessage(fromUserId, toUserId, content, extra);
    }
}
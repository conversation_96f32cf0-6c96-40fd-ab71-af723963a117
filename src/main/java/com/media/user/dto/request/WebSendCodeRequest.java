package com.media.user.dto.request;

import lombok.Data;

@Data
public class WebSendCodeRequest {
    /**
     * 类型: email 或 phone
     */
    private String type;
    /**
     * 邮箱地址（type=email时必填）
     */
    private String email;
    /**
     * 国家区号（type=phone时必填）
     */
    private String countryCode;
    /**
     * 手机前缀（type=phone时必填）
     */
    private String phonePrefix;
    /**
     * 手机号（type=phone时必填）
     */
    private String phone;
    /**
     * 业务类型，如 SIGNUP、FORGET_PASSWORD
     */
    private Integer businessType;
    /**
     * 前端人机校验token（可选）
     */
    private String responseToken;
}

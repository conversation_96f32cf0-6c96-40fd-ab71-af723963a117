//package com.media.user.log;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.core.MethodParameter;
//import org.springframework.http.MediaType;
//import org.springframework.http.server.ServerHttpRequest;
//import org.springframework.http.server.ServerHttpResponse;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;
//
//@RestControllerAdvice
//@Slf4j
//public class ResponseBodyAdvicePlugin implements ResponseBodyAdvice {
//
//    @Override
//    public boolean supports(MethodParameter returnType, Class converterType) {
//        return true;
//    }
//
//    @Override
//    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
//        if (selectedContentType.isCompatibleWith(MediaType.APPLICATION_JSON)) {
//            RequestLog requestLog = RequestLogTrace.getRequestLog();
//            if (requestLog != null && requestLog.getResponseLog() != null) {
//                ResponseLog responseLog = requestLog.getResponseLog();
//                responseLog.setBody(body);
//            }
//        }
//        return body;
//    }
//}

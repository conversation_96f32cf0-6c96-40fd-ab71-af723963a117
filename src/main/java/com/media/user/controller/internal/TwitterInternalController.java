package com.media.user.controller.internal;

import com.media.user.dto.request.internal.TwitterUserRecommendRequest;
import com.media.user.dto.response.internal.TwitterUserRecommendResponse;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.service.internal.TwitterUserInternalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/user/internal/twitter")
public class TwitterInternalController {

    @Autowired
    TwitterUserInternalService twitterUserInternalService;

    /**
     * 查询关注状态
     * @return
     */
    @PostMapping("/recommend/query")
    public List<TwitterUserRecommendResponse> queryRecommend(@RequestBody TwitterUserRecommendRequest query){
        if(query.getUsernames().size() == 0){
            return new ArrayList<>();
        }
        return twitterUserInternalService.queryRecommend(query);
    }


    /**
     * 批量设置 twitter 用户加入推荐或者移除推荐
     * @return
     */
    @PostMapping("/recommend/set")
    public void batchSet(@Validated @RequestBody TwitterUserRecommendRequest request){
        if(request.getRecommendType() == null || request.getUsernames().size() == 0){
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }
        twitterUserInternalService.batchSet(request);
    }



}

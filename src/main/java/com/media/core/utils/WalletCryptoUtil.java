package com.media.core.utils;

import com.alibaba.druid.util.StringUtils;
import org.web3j.crypto.*;
import org.web3j.crypto.Sign.SignatureData;
import org.web3j.utils.Numeric;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class WalletCryptoUtil {

    public static final String PERSONAL_MESSAGE_PREFIX = "\u0019Ethereum Signed Message:\n";

    /**
     * 验签 不带RSV，需要自己分割
     * @param signature
     * @param message
     * @param address
     * @return
     */
    public static boolean validate(String signature, String message, String address) {
        try {
            byte[] signatureBytes = Numeric.hexStringToByteArray(signature);
            byte v = signatureBytes[64];
            if (v < 27) {
                v += 27;
            }
            SignatureData signatureData = new SignatureData(v, Arrays.copyOfRange(signatureBytes, 0, 32), Arrays.copyOfRange(signatureBytes, 32, 64));
            return validate(signatureData, message, address);
        } catch (Exception e) {
            return false;
        }
    }

    private static boolean validate(SignatureData signatureData, String message, String address){
        List<String> addressList = recover(signatureData, message);
        if(addressList.isEmpty()){
            return false;
        }
        for(String _address: addressList){
            if(_address.equalsIgnoreCase(address)){
                return true;
            }
        }
        return false;
    }

    private static List<String> recover(SignatureData signatureData, String message){
        if(StringUtils.isEmpty(message)) {
            return new ArrayList<>();
        }
        String prefix = PERSONAL_MESSAGE_PREFIX + message.length();
        byte[] msgHash = Hash.sha3((prefix + message).getBytes());
        String address = null;
        List<String> addressList = new ArrayList<>();
        for (int i = 0; i < 4; i++) {
            BigInteger publicKey = Sign.recoverFromSignature((byte) i, new ECDSASignature(new BigInteger(1, signatureData.getR()), new BigInteger(1, signatureData.getS())), msgHash);
            if (publicKey != null) {
                address = "0x" + Keys.getAddress(publicKey);
                addressList.add(address);
            }
        }
        return addressList;
    }

    public static void main(String[] args) {
        String sign = "0x729d23cae11f7909f11e13d7fbe3be3018632f2ae76eb4be3270bd87c3ddf8b151a5432f4207abe6e17bb2cc6129277f22ec86edc3402919c9567d9fcae398b81b";
        String message = "Welcome to CoreSky!\n\n" +
                "Click to sign in and accept the CoreSky Terms of Service.\n\n" +
                "This request will not trigger a blockchain transaction or cost any gas fees.\n\n" +
                "Your authentication status will reset after 24 hours.\n\n" +
                "Wallet address:\n\n" +
                "******************************************";
        String address = "******************************************";
        if (validate(sign, message, address)) {
            System.out.println("test success");
        } else {
            System.out.println("test failed");
        }
    }
}

package com.media.user.feign.client;

import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.concurrent.TimeUnit;

@FeignClient(url = "${abtest.url}", value = "abClient", configuration = AbClient.AbClientConfiguration.class)
public interface AbClient {
    @Configuration
    class AbClientConfiguration {
        @Bean("abClientOptions")
        public Request.Options options() {
            // Set connect timeout to 50ms and read timeout to 200ms
            return new Request.Options(100, TimeUnit.MILLISECONDS, 500, TimeUnit.MILLISECONDS, true);
        }
    }

    @GetMapping(value = "/toggle_variations", headers = {"Authorization=${abtest.authorization.key:client-45c776cca688e07eef25c72a09ee372d81f85e2d}"})
    List<String> toggleVariations(@RequestParam("queryUser") String queryUser);
}

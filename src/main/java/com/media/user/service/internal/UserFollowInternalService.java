package com.media.user.service.internal;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Supplier;
import com.google.common.base.Suppliers;
import com.google.common.collect.Lists;
import com.media.core.constant.PageResponse;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.UserFollowRelationModel;
import com.media.user.dto.query.UserFollowingMemberQuery;
import com.media.user.dto.response.UserFollowingMemberResponse;
import com.media.user.dto.response.internal.UserMeResponse;
import com.media.user.enums.UserFollowStateEnum;
import com.media.user.mapper.UserFollowRelationMapper;
import com.media.user.service.cache.ClientUserCacheService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserFollowInternalService {

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    UserFollowRelationMapper userFollowRelationMapper;


    public PageResponse<UserFollowingMemberResponse> getUserFollowMemberPage(UserFollowingMemberQuery query) {
        log.info("getUserFollowMemberPage:{}", JSON.toJSONString(query));
        String key = MediaUserConstant.USER_FOLLOWING_PREFIX + query.getUid();
        var followTotalCnt = stringRedisTemplate.opsForSet().size(key);
        if (followTotalCnt == null || followTotalCnt <= 0) {
            // 没有关注用户
            return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
        }
        // 查询的数量已经超过所有的关注用户，直接从set获取所有的关注用户
        List<Long> followingUids;
        if (query.getSize() >= followTotalCnt) {
            Set<String> followings = stringRedisTemplate.opsForSet().members(key);
            if (CollectionUtils.isEmpty(followings)) {
                return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
            }
            followingUids = followings.stream().filter(StringUtils::isNotEmpty).map(Long::valueOf).toList();
        } else {
            if (!query.getNeedDetail()) {
                List<String> followings = stringRedisTemplate.opsForSet().randomMembers(key,query.getSize());
                if (CollectionUtils.isEmpty(followings)) {
                    return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
                }
                followTotalCnt = query.getSize().longValue();
                followingUids = followings.stream().filter(StringUtils::isNotEmpty).map(Long::valueOf).toList();
            }else {
                //从数据库分页查询当前关注用户量
                IPage<UserFollowRelationModel> userFollowingMemberPage = this.getUserFollowingMemberPage(query);
                followTotalCnt = userFollowingMemberPage.getTotal();
                followingUids = userFollowingMemberPage.getRecords().stream().map(UserFollowRelationModel::getFollowUid).filter(Objects::nonNull).toList();
            }

        }
        if (CollectionUtils.isEmpty(followingUids)) {
            return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
        }
        List<UserFollowingMemberResponse> list = Lists.newArrayListWithCapacity(followingUids.size());
        Supplier<Map<Long, UserMeResponse>> uidMeResMap = Suppliers.memoize(() -> clientUserCacheService.users(followingUids).stream().collect(Collectors.toMap(UserMeResponse::getUid, Function.identity())));
        for (Long followedUid : followingUids) {
            if (followedUid == null || followedUid == 0) {
                continue;
            }
            UserFollowingMemberResponse memberResponse = new UserFollowingMemberResponse();
            memberResponse.setUid(followedUid);
            if (query.getNeedDetail()) {
                UserMeResponse userInfo = uidMeResMap.get().get(followedUid);
                if (userInfo == null) {
                    continue;
                }
                memberResponse.setNickName(userInfo.getNickName()).setPersonIntroduce(userInfo.getPersonIntroduce()).setFollowState(UserFollowStateEnum.FOLLOW_STATE_1.getState()).setFansCount(userInfo.getFansNumber()).setAvatarUrl(userInfo.getAvatarUrl());
            }
            list.add(memberResponse);
        }
        return new PageResponse<>(query.getPage(), query.getSize(), followTotalCnt, list);
    }

    public IPage<UserFollowRelationModel> getUserFollowingMemberPage(UserFollowingMemberQuery query) {
        QueryWrapper<UserFollowRelationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserFollowRelationModel::getFollowUid, query.getUid());
        queryWrapper.lambda().eq(UserFollowRelationModel::getFollowState, UserFollowStateEnum.FOLLOW_STATE_1.getState());
        queryWrapper.orderByDesc("follow_time");

        IPage<UserFollowRelationModel> page = new Page<>(query.getPage(), query.getSize());
        return userFollowRelationMapper.selectPage(page, queryWrapper);
    }

    @Deprecated // 接口目前没有使用，内部逻辑需要优化后才可用
    public PageResponse<UserFollowingMemberResponse> getFansPage(UserFollowingMemberQuery query) {
        Set<String> fans = stringRedisTemplate.opsForSet().members(MediaUserConstant.USER_FANS_PREFIX + query.getUid());
        if (fans == null || fans.isEmpty()) {
            return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
        }

        List<UserFollowingMemberResponse> list = new ArrayList<>();
        for (String uidStr : fans) {
            Long fanUid = Long.valueOf(uidStr);
            if (fanUid == null || fanUid == 0) {
                continue;
            }
            UserMeResponse userInfo = clientUserCacheService.userInfo(fanUid);
            if (userInfo == null) {
                continue;
            }

            UserFollowingMemberResponse userFansResponse = new UserFollowingMemberResponse();
            userFansResponse.setUid(fanUid).setNickName(userInfo.getNickName()).setPersonIntroduce(userInfo.getPersonIntroduce()).setFollowState(UserFollowStateEnum.FOLLOW_STATE_0.getState()).setFansCount(userInfo.getFansNumber()).setAvatarUrl(userInfo.getAvatarUrl());

            boolean isFollowing = this.isFollowing(query.getUid(), fanUid);
            if (isFollowing) {
                userFansResponse.setFollowState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
            }
            list.add(userFansResponse);
        }
        return new PageResponse<>(query.getPage(), query.getSize(), list.size(), list);
    }


    public Boolean isFollowing(Long followUid, Long followedUid) {
        if (followUid == null) {
            return false;
        }
        return stringRedisTemplate.opsForSet().isMember(MediaUserConstant.USER_FOLLOWING_PREFIX + followUid, String.valueOf(followedUid));
    }


}

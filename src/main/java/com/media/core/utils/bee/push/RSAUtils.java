package com.media.core.utils.bee.push;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.Key;
import java.security.KeyFactory;
import java.security.spec.PKCS8EncodedKeySpec;

/**
 * bee  推送使用的 签名方法
 */
public class RSAUtils {

    public static final String KEY_ALGORITHM = "RSA";

     /**
      * RSA最⼤加密明⽂⼤⼩
      */
     private static final int MAX_ENCRYPT_BLOCK = 245;

     /**
      * RSA最⼤解密密⽂⼤⼩
      */
     private static final int MAX_DECRYPT_BLOCK = 256;

     /**
      * RSA 位数 如果采⽤2048 上⾯最⼤加密和最⼤解密则须填写: 245 256
      */
     private static final int INITIALIZE_LENGTH = 2048;


     public static String encryptedDataOnJavaByPrivateKey(String data, String privateKey) {
         try {
             data = Base64.encodeBase64String(encryptByPrivateKey(data.getBytes(), privateKey));
         } catch (Exception e) {
             e.printStackTrace();
         }
         return data;
     }

     public static byte[] encryptByPrivateKey(byte[] data, String privateKey) throws Exception {
         byte[] keyBytes = Base64.decodeBase64(privateKey);
         PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
         KeyFactory keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
         Key privateK = keyFactory.generatePrivate(pkcs8KeySpec);
         Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
         cipher.init(Cipher.ENCRYPT_MODE, privateK);
         int inputLen = data.length;
         ByteArrayOutputStream out = new ByteArrayOutputStream();
         int offSet = 0;
         byte[] cache;
         int i = 0;
         // 对数据分段加密
         while (inputLen - offSet > 0) {
             if (inputLen - offSet > MAX_ENCRYPT_BLOCK) {
                 cache = cipher.doFinal(data, offSet, MAX_ENCRYPT_BLOCK);
             } else {
                 cache = cipher.doFinal(data, offSet, inputLen - offSet);
             }
             out.write(cache, 0, cache.length);
             i++;
             offSet = i * MAX_ENCRYPT_BLOCK;
         }
         byte[] encryptedData = out.toByteArray();
         out.close();
         return encryptedData;
     }
}

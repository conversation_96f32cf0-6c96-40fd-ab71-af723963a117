package com.media.user.service;

import com.media.user.dto.request.CreateShortUrlRequest;
import com.media.user.dto.response.ShortUrlResponse;
import com.media.user.feign.client.ShortUrlClient;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ShortUrlService {

    @Autowired
    private ShortUrlClient shortUrlClient;

    /**
     * 生成短链接
     *
     * @param longUrl 原始长链接
     * @return 短链接，如果生成失败则返回null
     */
    public String generateShortUrl(String longUrl,String createdBy) {
        try {
            CreateShortUrlRequest request = CreateShortUrlRequest.builder()
                    .originalUrl(longUrl)
                    .createdBy(createdBy)
                    .build();

            ApiResponse<ShortUrlResponse> response = shortUrlClient.createShortUrl(request);

            if (response != null && Boolean.TRUE.equals(response.getSuccess()) && response.getResult() != null) {
                return response.getResult().getShortUrl();
            } else {
                log.warn("get short url fail {}", longUrl);
            }
        } catch (Exception e) {
            log.warn("get short url failed with exception {}", e.getMessage());
        }
        return longUrl;
    }
}

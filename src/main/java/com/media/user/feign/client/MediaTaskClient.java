package com.media.user.feign.client;

import com.media.user.dto.request.AddUserPropRequest;
import com.media.user.dto.request.CompleteTaskRequest;
import com.media.user.dto.request.SetFriendRelationRequest;
import com.media.user.dto.response.CompleteTaskResponse;
import com.xme.xme_base_depends.models.ApiResponse;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 媒体任务服务客户端
 */
@FeignClient(url = "${media_task.host:http://envtest-api.x.me}", value = "mediaTaskClient", configuration = MediaTaskClient.MediaTaskClientConfiguration.class)
public interface MediaTaskClient {

    @Configuration
    class MediaTaskClientConfiguration {
        @Bean("mediaTaskClientOptions")
        public Request.Options options() {
            // 设置连接超时为 200ms，读取超时为 2000ms
            return new Request.Options(200, TimeUnit.MILLISECONDS, 2000, TimeUnit.MILLISECONDS, true);
        }
    }

    /**
     * 完成任务
     *
     * @param request 完成任务请求
     * @return 完成任务响应
     */
    @PostMapping(value = "/task/internal/task/completeTask")
    ApiResponse<CompleteTaskResponse> completeTask(@RequestBody CompleteTaskRequest request);

    /**
     * 给用户批量添加道具
     *
     * @param request 添加道具请求
     * @return 添加道具响应
     */
    @PostMapping(value = "/task/internal/prop/add")
    ApiResponse<List<String>> addUserProp(@RequestBody AddUserPropRequest request);

    /**
     * 设置好友关系
     * 
     * @param request 设置好友关系请求
     * @return 设置好友关系响应
     */
    @PostMapping(value = "/task/internal/task/setFriendRelation")
    ApiResponse<Boolean> setFriendRelation(@RequestBody SetFriendRelationRequest request);
}

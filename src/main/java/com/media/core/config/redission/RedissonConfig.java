package com.media.core.config.redission;

import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Configuration
public class RedissonConfig {
    @Bean
    public RedissonClient redissonClient(RedisProperties redisProperties) {
        String host = redisProperties.getHost();
        Config config = new Config();
        if (redisProperties.getCluster() != null && !CollectionUtils.isEmpty(redisProperties.getCluster().getNodes())) {
            ClusterServersConfig clusterServersConfig = config.useClusterServers();
            List<String> nodes = redisProperties.getCluster()
                    .getNodes()
                    .stream()
                    .map(h -> "redis://" + h)
                    .collect(Collectors.toList());
            String[] nodesArr = nodes.toArray(new String[0]);;
            clusterServersConfig.addNodeAddress(nodesArr);
            clusterServersConfig.setPassword(redisProperties.getPassword());
        } else {
            config.useSingleServer()
                    .setAddress("redis://" + host + ":" + redisProperties.getPort())
                    .setPassword(redisProperties.getPassword());
        }
        return Redisson.create(config);
    }
}
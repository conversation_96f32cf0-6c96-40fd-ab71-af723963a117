package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("activity_reservation")
@Data
@Accessors(chain = true)
public class ActivityReservationModel extends Model<ActivityReservationModel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("email")
    private String email;

    @TableField("country_code")
    private String countryCode;

    @TableField("phone_prefix")
    private String phonePrefix;

    @TableField("phone")
    private String phone;

    /**
     * 活动类型，可以用于区分不同活动
     */
    @TableField("activity_type")
    private String activityType;

    /**
     * 是否已经发放道具
     */
    @TableField("props_given")
    private Boolean propsGiven;

    /**
     * 道具类型
     */
    @TableField("props_type")
    private String propsType;

    /**
     * 道具数量
     */
    @TableField("props_amount")
    private Integer propsAmount;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;

    /**
     * 客户端IP地址
     */
    @TableField("client_ip")
    private String clientIp;

    /**
     * 用户注册后的UID，初始为null，用户注册后更新
     */
    @TableField("uid")
    private Long uid;
}

package com.media.user.enums;

import lombok.Getter;

import java.util.Map;

import static java.util.Arrays.stream;
import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

@Getter
public enum UnionStatusEnums {
    AUTOMATICALLY_DISBANDED((byte) -2, "自动解散"),
    REJECTED((byte) -1, "审核不通过"),
    UNDER_REVIEW((byte) 0, "审核中"),
    INSUFFICIENT_MEMBERS((byte) 1, "人数不足"),
    SUCCESSFULLY_FORMED((byte) 2, "组建成功");

    private final Byte status;
    private final String desc;

    private static final Map<Byte, UnionStatusEnums> map = stream(values()).
            collect(toMap(UnionStatusEnums::getStatus, identity()));

    UnionStatusEnums(Byte status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static UnionStatusEnums form(Byte status) {
        if (status == null) return null;
        return map.get(status);
    }

    public static boolean isEffective(Byte status) {
        UnionStatusEnums statusEnums = UnionStatusEnums.form(status);
        if (statusEnums == null) return false;
        return switch (statusEnums) {
            case INSUFFICIENT_MEMBERS, SUCCESSFULLY_FORMED -> true;
            default -> false;
        };
    }
}

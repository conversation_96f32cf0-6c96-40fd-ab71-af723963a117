package com.media.user.tools;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.core.id.InviteCodeGenerator;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.ClientUserModel;
import com.media.user.mapper.ClientUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UserInviteCodeUtils {

    @Autowired
    private ClientUserMapper clientUserMapper;

    @Autowired
    InviteCodeGenerator inviteCodeGenerator;

    /**
     * 生成邀请码，需要检查 db 中是否存在，如果存在，则重新生成
     * @return
     */
    public String makeUserInviteCode(){
        String inviteCode = inviteCodeGenerator.generateInviteCode(MediaUserConstant.INVITE_CODE_LENGTH);
        for(int i =0; i < 15; i++){
            QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("invite_code", inviteCode);
            long count = clientUserMapper.selectCount(queryWrapper);
            log.info("query invite code count: {} - {}", inviteCode, count);
            if(count == 0){
                break;
            }
            inviteCode = inviteCodeGenerator.generateInviteCode(MediaUserConstant.INVITE_CODE_LENGTH);
        }
        return inviteCode;
    }
}

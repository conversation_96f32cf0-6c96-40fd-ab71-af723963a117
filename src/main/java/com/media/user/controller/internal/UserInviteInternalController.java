package com.media.user.controller.internal;

import com.media.user.service.UserInviteRelationService;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户邀请关系内部接口
 */
@Slf4j
@RestController
@RequestMapping("/user/internal/user/invite")
public class UserInviteInternalController {

    @Autowired
    private UserInviteRelationService userInviteRelationService;

    /**
     * 批量获取用户邀请人数
     * @param request 包含用户UID列表的请求
     * @return 每个UID对应的邀请人数
     */
    @PostMapping("/batch/count")
    public ApiResponse<Map<Long, Long>> batchGetInviteCount(@RequestBody BatchInviteCountRequest request) {
        log.info("Batch get invite count for uids: {}", request.getUids());
        
        if (request.getUids() == null || request.getUids().isEmpty()) {
            return ApiResponse.success(new HashMap<>());
        }
        
        Map<Long, Long> result = userInviteRelationService.batchCountInvites(request.getUids());
        return ApiResponse.success(result);
    }

    /**
     * 批量获取用户邀请人数
     * @param request 包含用户UID列表的请求
     * @return 每个UID对应的邀请人数
     */
    @PostMapping("/batch/clear")
    public ApiResponse<Map<Long, Long>> batchClearInviteCount(@RequestBody BatchInviteCountRequest request) {
        log.info("Batch clear invite count for uids: {}", request.getUids());

        if (request.getUids() == null || request.getUids().isEmpty()) {
            return ApiResponse.success(new HashMap<>());
        }

        userInviteRelationService.batchClearInviteCountCache(request.getUids());
        return ApiResponse.success(null);
    }
    
    /**
     * 请求参数类
     */
    public static class BatchInviteCountRequest {
        private List<Long> uids;
        
        public List<Long> getUids() {
            return uids;
        }
        
        public void setUids(List<Long> uids) {
            this.uids = uids;
        }
    }
}

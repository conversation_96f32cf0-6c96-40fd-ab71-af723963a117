package com.media.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.TwitterUserModel;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface TwitterUserMapper extends BaseMapper<TwitterUserModel> {


    @Select("select * from twitter_user where recommend_type = 1 and username not in ('elonmusk','Xme_Anything')\n" +
            "and\n" +
            "not EXISTS (select 1 from user_follow_relation where follow_uid=#{uid} and follow_state=1 and followed_old_uid=old_uid)\n" +
            "and  \n" +
            "not EXISTS (select 1 from user_follow_relation where follow_uid=#{uid} and follow_state=1 and followed_new_uid=new_uid)\n" +
            "LIMIT 100;\n")
    List<TwitterUserModel> getTwitterUser100(@Param("uid") Long uid);


    @Update("UPDATE twitter_user SET profile_image_url=#{imageUrl} where twitter_id = #{twitterId} ")
    long modifyProfileImageUrl(@Param("imageUrl") String imageUrl, @Param("twitterId") String twitterId);


    @Select("select * from twitter_user where fix_info_type = 1 order by created_time desc LIMIT 100;")
    List<TwitterUserModel> getNeedFixInfo100();

    @Select("SELECT * FROM ( " +
            "SELECT * FROM twitter_user " +
            "WHERE new_uid = #{uid} " +
            "AND bind = #{bindState} " +
            "UNION ALL " +
            "SELECT * FROM twitter_user " +
            "WHERE twitter_id = #{twitterId} " +
            "AND bind = #{bindState} " +
            ") t")
    List<TwitterUserModel> selectByUnion(
            @Param("uid") Long uid,
            @Param("twitterId") String twitterId,
            @Param("bindState") Byte bindState
    );
}
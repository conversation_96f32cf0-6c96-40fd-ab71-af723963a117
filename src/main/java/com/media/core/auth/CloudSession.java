package com.media.core.auth;

import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;

/**
 * 会话
 *
 * <AUTHOR>  2022/10/13 10:51
 * @version 1.0
 */

@Data
@Accessors(chain = true)
@Slf4j
public class CloudSession {

    private static final ThreadLocal<CloudSession> THREAD_LOCAL = new InheritableThreadLocal<>();


    private Long uid;

    private String token;




    public static void set(CloudSession cloudSession) {
        THREAD_LOCAL.set(cloudSession);
    }

    public static void clear() {
        THREAD_LOCAL.remove();
    }

    public static boolean isLogin() {
        return THREAD_LOCAL.get() != null;
    }

    private static CloudSession get() {
        return Optional.ofNullable(THREAD_LOCAL.get()).orElseThrow(() -> new RuntimeException("No Auth Session"));
    }

    public static Long getUid() {
        return get().uid;
    }

    public static String getToken(){
        return get().token;
    }

}

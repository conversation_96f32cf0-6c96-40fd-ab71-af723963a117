package com.media.user.exception;

/**
 * 异常错误码
 */
public interface MediaUserExceptionCodeApi {

    Integer SUCCESS = 200;

    /**
     * 参数 不合法
     */
    Integer INCORRECT_PARAMETERS = 400;

    Integer SERVER_ERROR = 500;

    /**
     * 邮箱验证码错误
     */
    Integer EMAIL_CODE_ERROR = 10000;

    /**
     * 邮箱验证码过期
     */
    Integer EMAIL_CODE_EXPIRE = 10001;

    /**
     * 邀请码无效
     */
    Integer INVITE_CODE_INVALID = 10002;

    /**
     * 用户名密码错误
     */
    Integer USERNAME_PASSWORD_ERROR = 10003;

    /**
     * 原密码错误
     */
    Integer OLD_PASSWORD_ERROR = 10004;

    /**
     * 邮箱未注册
     */
    Integer EMAIL_UNREGISTERED_ERROR = 10005;

    /**
     * 业务类型不存在
     */
    Integer BUSINESS_TYPE_NOT_EXIST = 10006;

    /**
     * PIN五小时只能PIN一次
     */
    Integer USER_PIN_LIMIT_ERROR = 10019;

    /**
     * 未授权
     */
    Integer UNAUTHORIZED = 401;

    /**
     * No permission
     */
    Integer NO_PERMISSION = 403;

    /**
     * 邮箱已注册错误
     */
    Integer EMAIL_REGISTERED_ERROR = 10007;

    /**
     * 昵称已存在
     */
    Integer NICK_NAME_EXIST_ERROR = 10008;

    /**
     * 成员数量过少，无法创建家族
     */
    Integer MEMBER_NUMBER_TOO_LITTLE_ERROR = 10009;

    /**
     * 此用户不存在
     */
    Integer USER_NOT_EXIST = 10010;

    /**
     * 用户成员不存在
     */
    Integer MEMBER_NOT_EXIST = 10011;

    /**
     * 成员级别不支持扩容
     */
    Integer MEMBER_LEVEL_NOT_SUPPORT_EXPAND = 10012;

    /**
     * 扩容卡已存在
     */
    Integer EXPAND_CARD_EXIST_ERROR = 10013;

    /**
     * 子成员数量不支持
     */
    Integer SUB_MEMBER_NUM_NOT_SUPPORT_EXPAND = 10014;

    /**
     * 用户当日pin次数已用完
     */
    Integer USER_PINS_USED = 10015;

    /**
     * 字符串长度过长
     */
    Integer LENGTH_TOO_LONG = 10016;
    /**
     * 当前已经签到
     */
    Integer SIGN_ALREADY_ERROR = 10017;
    /**
     * 签到失败
     */
    Integer SIGN_ERROR = 10018;
    /**
     * 密码不符合规则
     */
    Integer PASSWORD_NOT_MATCH = 10020;
    /**
     * 谷歌人机校验失败
     */
    Integer GOOGLE_RECAPTCHA_ERROR = 10021;
    /**
     * 版本过低，请升级到最新版本！
     */
    Integer VERSION_TOO_LOW = 10022;

    /**
     * 邮箱未设置
     */
    Integer EMAIL_NOT_EXIST = 10023;

    /**
     * 手机号未设置
     */
    Integer PHONE_NOT_EXIST = 10024;

    /**
     * 该邮箱已绑定
     */
    Integer EMAIL_ALREADY_BIND = 10025;
    /**
     * 该用户已设置了邮箱，无法绑定新的邮箱
     */
    Integer USER_EMAIL_ALREADY_SET = 10026;

    /**
     * 该手机号已绑定
     */
    Integer PHONE_ALREADY_BIND = 10027;
    /**
     * 该用户已设置了手机号，无法绑定新的手机号
     */
    Integer USER_PHONE_ALREADY_SET = 10028;

    /**
     * 手机号已注册错误
     */
    Integer PHONE_ALREADY_REGISTERED = 10029;

    /**
     * 手机号验证码发送频率过快，请稍后再试
     */
    Integer PHONE_CODE_LIMIT_RATE_ERROR = 10030;
    /**
     * 今日发送短信验证码次数已达到最大，请明日再试
     */
    Integer PHONE_CODE_LIMIT_COUNT_ERROR = 10031;

    /**
     * 邮箱验证码发送频率过快，请稍后再试
     */
    Integer EMAIL_CODE_LIMIT_RATE_ERROR = 10032;
    /**
     * 今日发送邮箱验证码次数已达到最大，请明日再试
     */
    Integer EMAIL_CODE_LIMIT_COUNT_ERROR = 10033;

    /**
     * 用户名不能为空
     */
    Integer USERNAME_NOT_NULL_ERROR = 10034;

    /**
     * 用户名 长度为 5-15位，只允许包含数字，字母或者下划线
     */
    Integer USERNAME_LENGTH_ERROR = 10035;
    /**
     * 当日登录次数已达到 3 次，请输入验证码
     */
    Integer LOGIN_LIMIT_COUNT_SEND_CODE_ERROR = 10036;

    /**
     * 手机验证码错误
     */
    Integer PHONE_CODE_ERROR = 10037;

    /**
     * 手机验证码过期
     */
    Integer PHONE_CODE_EXPIRE = 10038;

    /**
     * 用户名已存在
     */
    Integer USERNAME_EXIST_ERROR = 10039;
    /**
     * 手机号未注册
     */
    Integer PHONE_UNREGISTERED_ERROR = 10040;
    /**
     * 请选择国家区号
     */
    Integer PHONE_COUNTRY_CODE_ERROR = 10041;
    /**
     * 手机号格式有误，请检查
     */
    Integer PHONE_REGEX_ERROR = 10042;

    /**
     * 发送验证码失败
     */
    Integer SEND_CODE_ERROR = 10043;

    /**
     * 邀请人未人脸识别
     */
    Integer INVITE_USER_NOT_FACE_LIVENESS = 10044;
    /**
     * 被邀请人账号已注册错误
     */
    Integer INVITE_USER_REGISTERED_ERROR = 10045;

    /**
     * 钱包余额已被冻结
     */
    Integer ACCOUNT_BALANCE_FROZEN = 20003;

    /**
     * 钱包余额不足
     */
    Integer WALLET_BALANCE_INSUFFICIENT = 20004;

    /**
     * 钱包登录
     */
    Integer ADDRESS_SIGNATURE_ERROR = 30001;
    Integer ADDRESS_NOT_REGISTER = 30002;
    Integer ADDRESS_IS_REGISTER = 30003;
    Integer ADDRESS_IS_RECEIVED_TOKEN = 30004;
    Integer ADDRESS_CAN_NOT_RECEIVED = 30005;
    Integer RECEIVE_IS_STOP = 30006;

    /**
     * twitter已经绑定
     */
    Integer TWITTER_ALREADY_BIND_ERROR = 40001;
    Integer TWITTER_AUTHENTICATION_FAILED = 40002;
    /**
     * Twitter账号未绑定
     */
    Integer TWITTER_NOT_BOUND_ERROR = 40003;
    /**
     * Twitter官方账号ID未配置
     */
    Integer TWITTER_OFFICIAL_ID_NOT_CONFIGURED = 40004;
    /**
     * Twitter关注官方账号失败
     */
    Integer TWITTER_FOLLOW_OFFICIAL_FAILED = 40005;

    /**
     * 人脸识别
     */
    Integer FACE_DUPLICATE = 50001;

    /**
     * AWS相关错误码
     */
    Integer AWS_CLIENT_ERROR = 50001;
    Integer AWS_SERVICE_ERROR = 50002;
    Integer AWS_OPERATION_ERROR = 50003;
    Integer AWS_AUTH_ERROR = 50004;
    Integer AWS_REGION_EMPTY = 50005;
    Integer AWS_SESSION_CREATE_ERROR = 50006;
    Integer AWS_FACE_DETECTION_ERROR = 50007;
    Integer AWS_FACE_LOW_QUALITY = 50008;
    Integer AWS_FACE_DUPLICATE = 50009;
    Integer AWS_LIVENESS_CHECK_FAILED = 50010;
    Integer AWS_FACE_CHECKED = 50011;
    
    /**
     * 邀请系统限流错误码
     */
    Integer INVITE_IP_RATE_LIMIT = 10050;
    Integer INVITE_EMAIL_RATE_LIMIT = 10051;
    Integer INVITE_PHONE_RATE_LIMIT = 10052;
    
    /**
     * 活动预约相关错误码
     */
    Integer ALREADY_RESERVED = 10053;


    /**
     * Telegram用户ID不能为空
     */
    Integer TELEGRAM_USER_ID_NOT_NULL = 60001;

    /**
     * Telegram用户ID已绑定
     */
    Integer TELEGRAM_USER_ID_ALREADY_BIND = 60002;

    /**
     * Telegram用户ID无效
     */
    Integer INVALID_TELEGRAM_USER_ID = 60003;

    /**
     * 用户不在Telegram群组内
     */
    Integer USER_NOT_IN_TELEGRAM_GROUP = 60004;

    /**
     * 用户ID已绑定
     */
    Integer USER_ID_ALREADY_BIND = 60005;


}

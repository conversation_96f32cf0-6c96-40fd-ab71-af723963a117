package com.media.user.service.filter.impl;

import com.media.core.config.SystemConfig;
import com.media.user.dto.im.RongCloudMessageData;
import com.media.user.enums.ImExceptionEnums;
import com.media.user.service.filter.MessageFilter;
import com.media.user.service.filter.annotation.ChannelType;
import com.media.user.utils.CallbackSignatureVerifier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.media.user.enums.ImExceptionEnums.SPECIAL_SCENARIO_BLOCKED;
import static com.media.user.enums.ImExceptionEnums.SUCCESS;

@Slf4j
@Component
@Order(1)
@ChannelType(value = {"PERSON", "GROUP", "PERSONS"})
public class MessageSignatureFilter implements MessageFilter {

    @Autowired
    private SystemConfig systemConfig;

    @Override
    public ImExceptionEnums doFilter(RongCloudMessageData data) {
        log.info("<MessageSignatureFilter> start data:{}", data);
        if (Objects.isNull(data)) {
            return SUCCESS;
        }
        String signature = data.getSignature();
        String timestamp = data.getTimestamp();
        String nonce = data.getNonce();
        if (!CallbackSignatureVerifier.verifySignature(systemConfig.getAppSecret(), nonce, timestamp, signature)) {
            log.error("data:{} verifySignature is err", data);
            return SPECIAL_SCENARIO_BLOCKED;
        }
        return SUCCESS;
    }
}

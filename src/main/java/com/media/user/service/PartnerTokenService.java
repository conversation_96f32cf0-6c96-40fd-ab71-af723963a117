package com.media.user.service;

import com.media.user.dto.request.PartnerAccessTokenRequest;
import com.media.user.dto.response.PartnerAccessTokenResponse;
import com.media.user.feign.client.PartnerTokenClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;

import java.security.MessageDigest;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class PartnerTokenService {

    @Autowired
    private PartnerTokenClient partnerTokenClient;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Value("${partner.secret.key:85041ed075dc4b229bd32815b94388f6}")
    private String secretKey;

    @Value("${partner.id:100114}")
    private Long partnerId;

    @Value("${partner.api.url:https://api-test.loopspace.xyz}")
    private String partnerApiUrl;

    // token缓存key前缀
    private static final String TOKEN_CACHE_PREFIX = "partner:token:uid:";

    // token缓存时间（24小时）
    private static final int TOKEN_CACHE_HOURS = 24;

    /**
     * 获取access_token（带缓存）
     */
    public PartnerAccessTokenResponse getAccessToken(String userId, String userName, String avatar) {
        log.info("获取access_token: userId={}, userName={}", userId, userName);

        // 先从缓存中获取
        String cacheKey = TOKEN_CACHE_PREFIX + userId;
        try {
            String cachedToken = stringRedisTemplate.opsForValue().get(cacheKey);
            if (cachedToken != null) {
                PartnerAccessTokenResponse cachedResponse = JSON.parseObject(cachedToken, PartnerAccessTokenResponse.class);
                if (cachedResponse != null && cachedResponse.getData() != null) {
                    return cachedResponse;
                }
            }
        } catch (Exception e) {
            log.warn("从缓存获取token失败: userId={}", userId, e);
        }

        // 缓存中没有有效token，调用下游服务获取
        PartnerAccessTokenRequest request = new PartnerAccessTokenRequest();
        request.setPartnerId(partnerId);
        request.setUserId(userId);
        request.setUserName(userName);
        request.setAvatar(avatar);

        // 生成签名
        String sign = generateSign(request);
        request.setSign(sign);

                try {
            // 详细日志：打印请求信息
                    /**
            log.info("=== 调用下游服务获取access_token ===");
            log.info("请求URL: POST {}/api/partner/access_token", partnerApiUrl);
            log.info("完整请求参数: {}", JSON.toJSONString(request));
            log.info("partner_id: {}", request.getPartnerId());
            log.info("user_id: {}", request.getUserId());
            log.info("user_name: {}", request.getUserName());
            log.info("avatar: {}", request.getAvatar());
            log.info("生成的签名: {}", request.getSign());
            log.info("===================================");*/

            // 调用下游服务
            PartnerAccessTokenResponse response = partnerTokenClient.getAccessToken(request);

            // 详细日志：打印响应信息
                    /*
            log.info("=== 下游服务响应结果 ===");
            log.info("完整响应: {}", JSON.toJSONString(response));
            if (response != null) {
                log.info("响应code: {}", response.getCode());
                log.info("响应msg: {}", response.getMsg());
                log.info("响应time: {}", response.getTime());
                if (response.getData() != null) {
                    log.info("access_token: {}", response.getData().getAccessToken());
                    log.info("token_type: {}", response.getData().getTokenType());
                    log.info("expires_in: {}", response.getData().getExpiresIn());
                } else {
                    log.warn("响应data为空！");
                }
            } else {
                log.error("响应为null！");
            }
            log.info("========================");
            */
            // 如果获取成功，缓存token
            if (response != null && response.getCode() == 0 && response.getData() != null) {
                try {
                    // 缓存24小时
                    stringRedisTemplate.opsForValue().set(cacheKey,
                        JSON.toJSONString(response),
                        TOKEN_CACHE_HOURS, TimeUnit.HOURS);
                    log.info("access_token缓存成功: userId={}, cacheKey={}", userId, cacheKey);
                } catch (Exception e) {
                    log.warn("缓存access_token失败: userId={}, cacheKey={}", userId, cacheKey, e);
                }
            } else {
                log.warn("获取access_token失败，不进行缓存。code={}, data={}",
                    response != null ? response.getCode() : "null",
                    response != null && response.getData() != null ? "有数据" : "无数据");
            }

            return response;
        } catch (Exception e) {
            log.error("调用下游服务获取access_token失败", e);
            // 返回错误响应
            PartnerAccessTokenResponse errorResponse = new PartnerAccessTokenResponse();
            errorResponse.setCode(1);
            errorResponse.setMsg("获取access_token失败: " + e.getMessage());
            errorResponse.setTime(System.currentTimeMillis());
            return errorResponse;
        }
    }

    /**
     * 清除用户token缓存
     */
    public void clearTokenCache(String userId) {
        try {
            String cacheKey = TOKEN_CACHE_PREFIX + userId;
            stringRedisTemplate.delete(cacheKey);
            log.info("清除用户token缓存成功: userId={}", userId);
        } catch (Exception e) {
            log.error("清除用户token缓存失败: userId={}", userId, e);
        }
    }

    /**
     * 生成签名
     */
    private String generateSign(PartnerAccessTokenRequest request) {
        try {
            // 构建签名参数 - 使用下划线格式的参数名
            Map<String, String> params = new TreeMap<>(); // TreeMap自动按key排序

            params.put("partner_id", request.getPartnerId().toString());
            params.put("user_id", request.getUserId());

            if (request.getUserName() != null && !request.getUserName().trim().isEmpty()) {
                params.put("user_name", request.getUserName());
            }
            if (request.getAvatar() != null && !request.getAvatar().trim().isEmpty()) {
                params.put("avatar", request.getAvatar());
            }

            // 按ASCII码排序并拼接
            StringBuilder signString = new StringBuilder();
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (signString.length() > 0) {
                    signString.append("&");
                }
                signString.append(entry.getKey()).append("=").append(entry.getValue());
            }

            // 添加密钥
            signString.append("&key=").append(secretKey);

            log.info("用于签名的参数: {}", params);
            log.info("待签名字符串: {}", signString.toString());

            // MD5加密并转小写
            String sign = md5(signString.toString()).toLowerCase();
            log.info("生成签名: {}", sign);

            return sign;

        } catch (Exception e) {
            log.error("生成签名失败", e);
            throw new RuntimeException("生成签名失败", e);
        }
    }

    /**
     * MD5加密
     */
    private String md5(String input) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }
}

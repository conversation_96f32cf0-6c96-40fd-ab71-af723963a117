package com.media.user.service;
import com.media.core.auth.CloudSession;
import com.media.core.config.SystemConfig;
import com.media.core.utils.TimeTool;
import com.media.core.utils.VersionUtil;
import com.media.user.dto.response.ClientUserResponse;
import com.media.user.enums.BannerStatusEnum;
import com.media.user.enums.LanguageEnums;
import com.media.user.service.cache.ClientUserCacheService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import com.media.core.i18n.I18nConvert;
import com.alibaba.fastjson.JSON;
import com.media.core.request.ClientInfoContext;
import com.media.user.dto.activity.ActivityConfig;
import com.media.user.dto.response.ActivityResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.media.user.enums.ActivityStatusEnum;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class ActivityConfigService {

    @Value("${activity.config:{}}")
    private String activityConfigJson;

    @Autowired
    SystemConfig systemConfig;

    public List<ActivityResponse.ActivityItem> getActivities(HttpServletRequest request) {
        ActivityConfig activityConfig;
        activityConfig = JSON.parseObject(activityConfigJson, ActivityConfig.class);
        if (activityConfig == null || activityConfig.getActivities() == null) {
            return new ArrayList<>();
        }
        boolean isLogin = CloudSession.isLogin();
        boolean isChina = false;

        if (LanguageEnums.zh_CN.equals(ClientInfoContext.getLanguage())) {
            isChina = true;
        }
        List<ActivityResponse.ActivityItem> result = new ArrayList<>();
        for (ActivityConfig.ActivityItem item : activityConfig.getActivities()) {
            try {
                ActivityResponse.ActivityItem responseItem = new ActivityResponse.ActivityItem();
                if (item.getStatus() == ActivityStatusEnum.ENDED.getCode()) {
                    continue; // 跳过已经结束状态的活动
                }


                // 检查版本控制
                String currentVersion = ClientInfoContext.get().getVersion();
                String minVersion = item.getMinVersion();
                if (minVersion != null && !minVersion.isEmpty() && VersionUtil.CompareVersions(currentVersion, minVersion) < 0) {
                    // 当前版本小于最小要求版本，不展示
                    continue;
                }

                if ("xme_rebate_activity".equals(item.getId())) {
                    if (isLogin) {
                        item.setJumpUrl(systemConfig.appendRebateUrl(item.getJumpUrl(),CloudSession.getUid()));
                    }
                }

                // 检查时间状态
                if (item.getStartTime() != null && !item.getStartTime().isEmpty()) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime startTime = LocalDateTime.parse(item.getStartTime(), formatter);
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isBefore(startTime)) {
                            // 未开始
                            item.setStatus(BannerStatusEnum.NOT_STARTED.getCode());
                        }
                    } catch (Exception e) {
                        continue;
                    }
                }

                if (item.getEndTime() != null && !item.getEndTime().isEmpty()) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime endTime = LocalDateTime.parse(item.getEndTime(), formatter);
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isAfter(endTime)) {
                            // 已结束
                            continue;
                        }
                    } catch (Exception e) {
                        continue;
                    }
                }

                if (!item.isAuditShowable()) {
                    if (systemConfig.isInAudit()) {
                        continue;
                    }
                }


                // 从 i18n 资源文件获取翻译
                String title = I18nConvert.getI18nMessage(item.getTitleKey(), ClientInfoContext.getLanguage());
                String description = I18nConvert.getI18nMessage(item.getDescriptionKey(), ClientInfoContext.getLanguage());


                responseItem.setId(item.getId());
                responseItem.setBackgroundImage(item.getBackgroundImage());
                if (isChina && StringUtils.isNotBlank(item.getBackgroundImageCn())) {
                    responseItem.setBackgroundImage(item.getBackgroundImageCn());
                }
                responseItem.setTitle(title);
                responseItem.setDescription(description);
                responseItem.setIcon(item.getIcon());
                responseItem.setJumpType(item.getJumpType());
                responseItem.setJumpUrl(item.getJumpUrl());
                responseItem.setStatus(item.getStatus());
                responseItem.setClickable(true);
                if (item.getStatus() == ActivityStatusEnum.NOT_STARTED.getCode()) {
                    responseItem.setClickable(false);
                }
                responseItem.setOrder(item.getOrder());
                responseItem.setNeedLogin(item.isNeedLogin());  // 设置是否需要登录
                responseItem.setNeedFace(item.isNeedFace()); //设置是否需要人脸验证
                responseItem.setNeedVerify(item.isNeedVerify()); //是否需要三合一认证
                result.add(responseItem);
            }catch (Exception e) {
                continue;
            }
        }
        // 按 order 排序
        result.sort(Comparator.comparing(ActivityResponse.ActivityItem::getOrder));

        return result;
    }
}

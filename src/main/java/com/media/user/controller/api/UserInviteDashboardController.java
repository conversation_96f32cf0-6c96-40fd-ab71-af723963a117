package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.core.constant.PageResponse;
import com.media.user.dto.query.UserInviteListQuery;
import com.media.user.dto.response.InviteAwardNoticeResponse;
import com.media.user.dto.response.UserInviteDashboardListResponse;
import com.media.user.dto.response.UserInviteDashboardResponse;
import com.media.user.service.UserInviteService;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/user/api/user/v1/invite")
public class UserInviteDashboardController {

    @Autowired
    private UserInviteService userInviteService;

    @PostMapping("/dashboard")
    @RequireAuth
    public ApiResponse<UserInviteDashboardResponse> info() {
        Long uid = CloudSession.getUid();
        return new ApiResponse<>(userInviteService.getInviteInfo(uid));
    }

    @PostMapping("/list")
    @RequireAuth
    public PageResponse<UserInviteDashboardListResponse> inviteList(@RequestBody UserInviteListQuery query) {
        Long uid = CloudSession.getUid();
        query.setUid(uid);
        return userInviteService.getInviteList(query);
    }

    @PostMapping("/rewardNotices")
    public ApiResponse<List<InviteAwardNoticeResponse>> inviteRewardNotice() {
        return new ApiResponse<>(userInviteService.getRewardNotices());
    }
}

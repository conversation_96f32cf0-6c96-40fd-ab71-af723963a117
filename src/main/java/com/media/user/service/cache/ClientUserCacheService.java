package com.media.user.service.cache;


import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.media.user.constant.MediaUserConstant;
import com.media.user.convert.mapper.ClientUserConvert;
import com.media.user.domain.ClientUserModel;
import com.media.user.dto.response.ClientUserResponse;
import com.media.user.dto.response.internal.UserMeResponse;
import com.media.user.enums.LanguageEnums;
import com.media.user.enums.LevelEnums;
import com.media.user.enums.UserFollowStateEnum;
import com.media.user.mapper.ClientUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.media.user.utils.LivenessStatusUtils.livenessStatusVerify;

@Slf4j
@Service
public class ClientUserCacheService {

    private static final int FOLLOW_THRESHOLD = 1000; // 关注数阈值

    @Autowired
    private ClientUserMapper clientUserMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 获取个人信息 redis list
     * @return
     */
    public List<UserMeResponse> users(List<Long> uids) {
        if(uids == null || uids.size() == 0) {
            return null;
        }
        List<String> cacheKeys = new ArrayList<>();
        for(Long uid : uids){
            String key = MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + uid;
            cacheKeys.add(key);
        }
        List<String> list = stringRedisTemplate.opsForValue().multiGet(cacheKeys);
        List<UserMeResponse> userMeResponses = new ArrayList<>();
        if(list != null && list.size() > 0) {
            for(String userInfoStr : list){
                if(userInfoStr != null && !"null".equals(userInfoStr)) {
                    UserMeResponse userResponse = JSON.parseObject(userInfoStr, UserMeResponse.class);
                    if (userResponse != null) {
                        if (userResponse.getEmail() != null) {
                            if (userResponse.getEmail().startsWith("BEE_") && userResponse.getEmail().endsWith("@bee.cn")) {
                                userResponse.setEmail("BEE用户");
                            }
                            if (userResponse.getVirtualType() == 1) {  //预注册用户
                                String[] emails = userResponse.getEmail().split("-");
                                if (emails.length > 1) {
                                    userResponse.setEmail(emails[1]);
                                }
                            }
                        }

                        Long followingsCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FOLLOWING_PREFIX + userResponse.getUid());
                        Long fansCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FANS_PREFIX + userResponse.getUid());
                        if (followingsCount != null) {
                            userResponse.setFollowersNumber(followingsCount);
                        }
                        if (fansCount != null) {
                            userResponse.setFansNumber(fansCount);
                        }
                        userResponse.setIsBindTwitter(0);
                        userMeResponses.add(userResponse);
                    }
                }
            }
        }

        if(userMeResponses.size() != uids.size()) {
            List<Long> uidList = userMeResponses.stream().map(UserMeResponse::getUid).toList();
            // 计算list1中有而list2中没有的元素（差集）
            List<Long> difference = uids.stream().filter(n -> !uidList.contains(n)).collect(Collectors.toList());
            List<ClientUserModel> needSaveRedisUser = clientUserMapper.selectBatchIds(difference);
            if (needSaveRedisUser != null && needSaveRedisUser.size() > 0) {
                for(ClientUserModel userModel : needSaveRedisUser){
                    UserMeResponse userResponse = new UserMeResponse();
                    BeanUtils.copyProperties(userModel, userResponse);
                    if (userResponse.getEmail() != null) {
                        if (userResponse.getEmail().startsWith("BEE_") && userResponse.getEmail().endsWith("@bee.cn")) {
                            userResponse.setEmail("BEE用户");
                        }
                        if (userResponse.getVirtualType() == 1) {  //预注册用户
                            String[] emails = userResponse.getEmail().split("-");
                            if (emails.length > 1) {
                                userResponse.setEmail(emails[1]);
                            }
                        }
                    }
                    Long followingsCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FOLLOWING_PREFIX + userResponse.getUid());
                    Long fansCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FANS_PREFIX + userResponse.getUid());
                    if (followingsCount != null) {
                        userResponse.setFollowersNumber(followingsCount);
                    }
                    if (fansCount != null) {
                        userResponse.setFansNumber(fansCount);
                    }
                    userResponse.setIsBindTwitter(0);
                    userMeResponses.add(userResponse);
                    //set redis
                    ClientUserResponse clientUserResponse = ClientUserConvert.INSTANCE.clientUser2Response(userModel);
                    clientUserResponse.setLevel(LevelEnums.L0.getLevel());
                    stringRedisTemplate.opsForValue().set(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + userModel.getUid(), JSON.toJSONString(clientUserResponse));
                }
            }
        }
        return userMeResponses;
    }

    /**
     * 获取个人信息
     * @param uid
     * @return
     */
    public UserMeResponse userInfoForWeb(Long uid, LanguageEnums languageEnums) {
        ClientUserResponse clientUserResponse = this.me(uid);
        if (clientUserResponse != null){
            UserMeResponse userMeResponse = new UserMeResponse();
            BeanUtils.copyProperties(clientUserResponse, userMeResponse);
            if(userMeResponse.getEmail() != null && userMeResponse.getEmail().startsWith("BEE_") && userMeResponse.getEmail().endsWith("@bee.cn")){
                userMeResponse.setEmail("BEE用户");
            }
            if(userMeResponse.getVirtualType() == 1){  //预注册用户
                String[] emails = userMeResponse.getEmail().split("-");
                if(emails.length > 1){
                    userMeResponse.setEmail(emails[1]);
                }
            }
            Long followingsCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FOLLOWING_PREFIX + uid);
            Long fansCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FANS_PREFIX + uid);
            userMeResponse.setFollowersNumber(followingsCount == null ? 0 : followingsCount);
            userMeResponse.setFansNumber(fansCount == null ? 0 : fansCount);
            userMeResponse.setIsBindTwitter(0);
            userMeResponse.setPhone("");
            userMeResponse.setEmail("");
            userMeResponse.setGenesisBadge(userMeResponse.getGenesisBadge() == null ? 0 : userMeResponse.getGenesisBadge());
            if (!livenessStatusVerify(userMeResponse.getFaceLivenessStatus(), userMeResponse.getPhoneVerify(), userMeResponse.getEmailVerify())) {
                userMeResponse.setInviteCode(""); //人脸识别未认证，邀请码为空
            }
            return userMeResponse;
        }
        return null;
    }

    /**
     * 获取个人信息
     * @param uid
     * @return
     */
    public UserMeResponse userInfoForMe(Long uid, LanguageEnums languageEnums) {
        ClientUserResponse clientUserResponse = this.me(uid);
        if (clientUserResponse != null){
            UserMeResponse userMeResponse = new UserMeResponse();
            BeanUtils.copyProperties(clientUserResponse, userMeResponse);
            if(userMeResponse.getEmail() != null && userMeResponse.getEmail().startsWith("BEE_") && userMeResponse.getEmail().endsWith("@bee.cn")){
                userMeResponse.setEmail("BEE用户");
            }
            if(userMeResponse.getVirtualType() == 1){  //预注册用户
                String[] emails = userMeResponse.getEmail().split("-");
                if(emails.length > 1){
                    userMeResponse.setEmail(emails[1]);
                }
            }
            Long followingsCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FOLLOWING_PREFIX + uid);
            Long fansCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FANS_PREFIX + uid);
            userMeResponse.setFollowersNumber(followingsCount == null ? 0 : followingsCount);
            userMeResponse.setFansNumber(fansCount == null ? 0 : fansCount);
            userMeResponse.setIsBindTwitter(0);
            userMeResponse.setGenesisBadge(userMeResponse.getGenesisBadge() == null ? 0 : userMeResponse.getGenesisBadge());
            if (!livenessStatusVerify(userMeResponse.getFaceLivenessStatus(), userMeResponse.getPhoneVerify(), userMeResponse.getEmailVerify())) {
                userMeResponse.setInviteCode(""); //人脸识别未认证，邀请码为空
            }
            return userMeResponse;
        }
        return null;
    }

    /**
     * 获取个人信息
     * @param uid
     * @return
     */
    public UserMeResponse userInfo(Long uid) {
        ClientUserResponse clientUserResponse = this.me(uid);
        if (clientUserResponse != null){
            UserMeResponse userMeResponse = new UserMeResponse();
            BeanUtils.copyProperties(clientUserResponse, userMeResponse);
            if(userMeResponse.getEmail() != null && userMeResponse.getEmail().startsWith("BEE_") && userMeResponse.getEmail().endsWith("@bee.cn")){
                userMeResponse.setEmail("BEE用户");
            }
            if(userMeResponse.getVirtualType() == 1){  //预注册用户
                String[] emails = userMeResponse.getEmail().split("-");
                if(emails.length > 1){
                    userMeResponse.setEmail(emails[1]);
                }
            }
            Long followingsCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FOLLOWING_PREFIX + uid);
            Long fansCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FANS_PREFIX + uid);
            userMeResponse.setFollowersNumber(followingsCount == null ? 0 : followingsCount);
            userMeResponse.setFansNumber(fansCount == null ? 0 : fansCount);
            userMeResponse.setIsBindTwitter(0);
            userMeResponse.setGenesisBadge(userMeResponse.getGenesisBadge() == null ? 0 : userMeResponse.getGenesisBadge());
            return userMeResponse;
        }
        return null;
    }

    /**
     * 获取个人信息
     * @param uid
     * @return
     */
    public ClientUserResponse me(Long uid) {
        String userInfoStr = stringRedisTemplate.opsForValue().get(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + uid);
        if (StringUtils.isNotBlank(userInfoStr)){
            ClientUserResponse userResponse = JSON.parseObject(userInfoStr, ClientUserResponse.class);
            if(userResponse.getVirtualType() != null){
                return userResponse;
            }

            ClientUserModel clientUserModel = clientUserMapper.selectById(uid);
            if (clientUserModel != null){
                ClientUserResponse clientUserResponse = ClientUserConvert.INSTANCE.clientUser2Response(clientUserModel);
                clientUserResponse.setLevel(LevelEnums.L0.getLevel());
                stringRedisTemplate.opsForValue().set(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + uid, JSON.toJSONString(clientUserResponse));
                return clientUserResponse;
            }

        }
        ClientUserModel clientUserModel = clientUserMapper.selectById(uid);
        if (clientUserModel != null){
            ClientUserResponse clientUserResponse = ClientUserConvert.INSTANCE.clientUser2Response(clientUserModel);
            clientUserResponse.setLevel(LevelEnums.L0.getLevel());
            stringRedisTemplate.opsForValue().set(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + uid, JSON.toJSONString(clientUserResponse));
            return clientUserResponse;
        }
        return null;
    }

    public List<UserMeResponse> usersWithConfig(List<Long> uids) {
        return usersWithConfig(uids, false);
    }

    public List<UserMeResponse> usersWithConfig(List<Long> uids, boolean needFollowCount) {
        if(uids == null || uids.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> cacheKeys = uids.stream()
                .map(uid -> MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + uid)
                .collect(Collectors.toList());

        List<String> list = stringRedisTemplate.opsForValue().multiGet(cacheKeys);
        List<UserMeResponse> userMeResponses = new ArrayList<>();

        if(list != null && !list.isEmpty()) {
            if(needFollowCount) {
                // 使用pipeline批量获取关注数和粉丝数
                stringRedisTemplate.execute((RedisCallback<List<UserMeResponse>>) connection -> {
                    connection.openPipeline();

                    for(String userInfoStr : list) {
                        if(userInfoStr != null && !"null".equals(userInfoStr)) {
                            UserMeResponse userResponse = JSON.parseObject(userInfoStr, UserMeResponse.class);
                            if (userResponse != null) {
                                connection.setCommands().sCard(
                                    (MediaUserConstant.USER_FOLLOWING_PREFIX + userResponse.getUid()).getBytes());
                                connection.setCommands().sCard(
                                    (MediaUserConstant.USER_FANS_PREFIX + userResponse.getUid()).getBytes());
                            }
                        }
                    }

                    List<Object> pipelineResults = connection.closePipeline();

                    int index = 0;
                    for(String userInfoStr : list) {
                        if(userInfoStr != null && !"null".equals(userInfoStr)) {
                            UserMeResponse userResponse = JSON.parseObject(userInfoStr, UserMeResponse.class);
                            if (userResponse != null) {
                                this.dealUserMe(userResponse);
                                Long followingsCount = (Long) pipelineResults.get(index++);
                                Long fansCount = (Long) pipelineResults.get(index++);
                                userResponse.setFollowersNumber(followingsCount);
                                userResponse.setFansNumber(fansCount);
                                userResponse.setIsBindTwitter(0);
                                userMeResponses.add(userResponse);
                            }
                        }
                    }
                    return null;
                });
            } else {
                // 不需要关注数，直接处理用户信息
                for(String userInfoStr : list) {
                    if(userInfoStr != null && !"null".equals(userInfoStr)) {
                        UserMeResponse userResponse = JSON.parseObject(userInfoStr, UserMeResponse.class);
                        if (userResponse != null) {
                            this.dealUserMe(userResponse);
                            userResponse.setFollowersNumber(Long.valueOf(0));
                            userResponse.setFansNumber(Long.valueOf(0));
                            userResponse.setIsBindTwitter(0);
                            userMeResponses.add(userResponse);
                        }
                    }
                }
            }
        }

        // 处理缓存未命中的情况
        if(userMeResponses.size() != uids.size()) {
            handleCacheMiss(uids, userMeResponses);
        }

        return userMeResponses;
    }

    public Map<Long, UserMeResponse> batchUserMes(Long currentUid, List<Long> uids) {
        return batchUserMes(currentUid, uids, false, false, false);
    }

    public Map<Long, UserMeResponse> batchUserMes(Long currentUid, List<Long> uids, boolean needFollowState) {
        return batchUserMes(currentUid, uids, needFollowState, false, false);
    }

    public Map<Long, UserMeResponse> batchUserMes(Long currentUid, List<Long> uids, boolean needFollowState, boolean needFollowedState) {
        return batchUserMes(currentUid, uids, needFollowState, needFollowedState, false);
    }

    public Map<Long, UserMeResponse> batchUserMes(Long currentUid, List<Long> uids, boolean needFollowState, boolean needFollowedState, boolean needFollowCount) {
        List<UserMeResponse> responseList = this.usersWithConfig(uids, needFollowCount);
        if(responseList == null || responseList.isEmpty()) {
            return new HashMap<>();
        }

        Map<Long, UserMeResponse> map = new HashMap<>();

        // 只有需要关注状态且当前用户不为空时才处理关注关系
        if(needFollowState && currentUid != null) {
            // 获取当前用户的关注数
            Long followCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FOLLOWING_PREFIX + currentUid);

            if(followCount != null && followCount < FOLLOW_THRESHOLD) {
                // 关注数较少，直接获取所有关注的用户
                Set<String> followingSet = stringRedisTemplate.opsForSet().members(MediaUserConstant.USER_FOLLOWING_PREFIX + currentUid);

                for (UserMeResponse response : responseList) {
                    response.setFollowState(UserFollowStateEnum.FOLLOW_STATE_0.getState());
                    if(!currentUid.equals(response.getUid())) {
                        if(followingSet != null && followingSet.contains(response.getUid().toString())) {
                            response.setFollowState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
                        }
                    }
                }
            } else {
                // 关注数较多，使用pipeline批量检查关注状态
                stringRedisTemplate.execute((RedisCallback<Void>) connection -> {
                    connection.openPipeline();

                    for (UserMeResponse response : responseList) {
                        if(!currentUid.equals(response.getUid())) {
                            String key = MediaUserConstant.USER_FOLLOWING_PREFIX + currentUid;
                            connection.setCommands().sIsMember(
                                    key.getBytes(),
                                    response.getUid().toString().getBytes()
                            );
                        }
                    }

                    List<Object> pipelineResults = connection.closePipeline();

                    int index = 0;
                    for (UserMeResponse response : responseList) {
                        response.setFollowState(UserFollowStateEnum.FOLLOW_STATE_0.getState());
                        if(!currentUid.equals(response.getUid())) {
                            Boolean isFollowing = (Boolean) pipelineResults.get(index++);
                            if(Boolean.TRUE.equals(isFollowing)) {
                                response.setFollowState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
                            }
                        }
                    }

                    return null;
                });
            }
        }

        // 处理反向关注关系
        if(needFollowedState && currentUid != null) {
            for (UserMeResponse response : responseList) {
                response.setFollowedState(UserFollowStateEnum.FOLLOW_STATE_0.getState());
                if(!currentUid.equals(response.getUid())) {
                    Boolean isFollowed = stringRedisTemplate.opsForSet().isMember(
                            MediaUserConstant.USER_FOLLOWING_PREFIX + response.getUid(),
                            currentUid.toString()
                    );
                    if(Boolean.TRUE.equals(isFollowed)) {
                        response.setFollowedState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
                    }
                }
            }
        }

        // 将处理后的responseList放入map
        for (UserMeResponse response : responseList) {
            map.put(response.getUid(), response);
        }

        return map;
    }

    private void dealUserMe(UserMeResponse userResponse) {
        if (userResponse.getEmail() != null) {
            if(userResponse.getEmail().startsWith("BEE_") && userResponse.getEmail().endsWith("@bee.cn")) {
                userResponse.setEmail("BEE用户");
            }
            if(userResponse.getVirtualType() == 1) {  //预注册用户
                String[] emails = userResponse.getEmail().split("-");
                if(emails.length > 1) {
                    userResponse.setEmail(emails[1]);
                }
            }
        }
    }

    private void handleCacheMiss(List<Long> uids, List<UserMeResponse> userMeResponses) {
        List<Long> uidList = userMeResponses.stream().map(UserMeResponse::getUid).toList();
        List<Long> difference = uids.stream()
                .filter(n -> !uidList.contains(n))
                .collect(Collectors.toList());

        List<ClientUserModel> needSaveRedisUser = clientUserMapper.selectBatchIds(difference);
        if (needSaveRedisUser != null && !needSaveRedisUser.isEmpty()) {
                for(ClientUserModel userModel : needSaveRedisUser) {
                    UserMeResponse userResponse = new UserMeResponse();
                    BeanUtils.copyProperties(userModel, userResponse);
                    this.dealUserMe(userResponse);
                    userResponse.setIsBindTwitter(0);
                    Long followingsCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FOLLOWING_PREFIX + userResponse.getUid());
                    Long fansCount = stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FANS_PREFIX + userResponse.getUid());
                    if (followingsCount != null) {
                        userResponse.setFollowersNumber(followingsCount);
                    }
                    if (fansCount != null) {
                        userResponse.setFansNumber(fansCount);
                    }
                    userMeResponses.add(userResponse);
                    //set redis
                    ClientUserResponse clientUserResponse = ClientUserConvert.INSTANCE.clientUser2Response(userModel);
                    clientUserResponse.setLevel(LevelEnums.L0.getLevel());
                    stringRedisTemplate.opsForValue().set(
                        MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + userModel.getUid(),
                        JSON.toJSONString(clientUserResponse)
                    );
                }
        }
    }

}

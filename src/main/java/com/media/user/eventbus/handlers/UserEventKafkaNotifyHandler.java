package com.media.user.eventbus.handlers;

import com.google.common.eventbus.Subscribe;
import com.media.user.context.RegisterCodeContext;
import com.media.user.dto.request.internal.RegisterCodeInfoRequest;
import com.media.user.dto.response.internal.RegisterCodeInfoResponse;
import com.media.user.feign.client.GameClient;
import com.media.user.mq.provider.UserSendMessageProvider;
import com.xme.xme_base_depends.enums.UserEventTypeEnum;
import com.xme.xme_base_depends.mq.message.UserBehaviorEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

//在eventbus中实例化 不在spring进行实例管理
@Component
public class UserEventKafkaNotifyHandler {

    @Autowired
    private UserSendMessageProvider userSendMessageProvider;

    @Autowired
    private GameClient gameClient;

    @Subscribe
    @Order(2)
    public void handle(UserBehaviorEvent event) {
        boolean isNewEvent = false;
        Date time = new Date();
        
        if (event.getUserEventType() == UserEventTypeEnum.REGISTER) {
            // 使用RegisterCodeContext获取注册码信息
            RegisterCodeInfoResponse response = RegisterCodeContext.getRegisterCodeInfo();
            if (!Objects.isNull(response)&& response.isOpen()) {
                isNewEvent = true;
            }
        } else {
            RegisterCodeInfoRequest registerCodeInfoRequest = new RegisterCodeInfoRequest();
            registerCodeInfoRequest.setTime(time);
            registerCodeInfoRequest.setUserId(event.getUserId());
            RegisterCodeInfoResponse response = gameClient.getRegisterCodeInfo(registerCodeInfoRequest).getResult();
            if (!Objects.isNull(response)&&response.isOpen()) {
                isNewEvent = true;
            }
        }
        
        userSendMessageProvider.sendUserEventMessage(event, isNewEvent, time);
        
        // 如果是注册事件，处理完成后清理上下文
        if (event.getUserEventType() == UserEventTypeEnum.REGISTER) {
            RegisterCodeContext.clear();
        }
    }
}

package com.media.user.controller.internal;

import com.alibaba.fastjson.JSONObject;
import com.media.user.service.UserLoginLogService;
import com.media.user.service.AppReleaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 内部统计接口控制器
 */
@RestController
@RequestMapping("/user/internal/stat")
@Slf4j
public class InternalStatController {

    @Autowired
    private UserLoginLogService userLoginLogService;

    @Autowired
    private AppReleaseService appReleaseService;

    /**
     * 获取昨天登录用户数量
     */
    @GetMapping("/login/yesterday/count")
    public ResponseEntity<JSONObject> getYesterdayLoginUserCount() {
        try {
            Long count = userLoginLogService.getYesterdayLoginUserCount();
            JSONObject result = new JSONObject();
            result.put("count", count);
            result.put("date", cn.hutool.core.date.DateUtil.today());
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取当天登录用户数量失败", e);
            JSONObject error = new JSONObject();
            error.put("error", "获取失败");
            error.put("count", 0);
            return ResponseEntity.ok(error);
        }
    }

    /**
     * 批量添加用户到灰度发布列表
     */
    @PostMapping("/gray/release/users/batch")
    public ResponseEntity<JSONObject> batchAddUsersToGrayRelease(
            @RequestParam String platformType,
            @RequestBody List<Long> userIds) {
        try {
            boolean success = appReleaseService.batchAddUsersToGrayRelease(platformType, userIds);
            JSONObject result = new JSONObject();
            result.put("success", success);
            result.put("platformType", platformType);
            result.put("userCount", userIds.size());
            result.put("message", success ? "批量添加用户到灰度列表成功" : "批量添加用户到灰度列表失败");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("批量添加用户到灰度列表失败", e);
            JSONObject error = new JSONObject();
            error.put("success", false);
            error.put("error", "批量添加失败: " + e.getMessage());
            return ResponseEntity.ok(error);
        }
    }

    /**
     * 添加单个用户到灰度发布列表
     */
    @PostMapping("/gray/release/user")
    public ResponseEntity<JSONObject> addUserToGrayRelease(
            @RequestParam String platformType,
            @RequestParam Long userId) {
        try {
            boolean success = appReleaseService.addUserToGrayRelease(platformType, userId);
            JSONObject result = new JSONObject();
            result.put("success", success);
            result.put("platformType", platformType);
            result.put("userId", userId);
            result.put("message", success ? "添加用户到灰度列表成功" : "添加用户到灰度列表失败");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("添加用户到灰度列表失败", e);
            JSONObject error = new JSONObject();
            error.put("success", false);
            error.put("error", "添加失败: " + e.getMessage());
            return ResponseEntity.ok(error);
        }
    }

    /**
     * 从灰度发布列表移除用户
     */
    @DeleteMapping("/gray/release/user")
    public ResponseEntity<JSONObject> removeUserFromGrayRelease(
            @RequestParam String platformType,
            @RequestParam Long userId) {
        try {
            boolean success = appReleaseService.removeUserFromGrayRelease(platformType, userId);
            JSONObject result = new JSONObject();
            result.put("success", success);
            result.put("platformType", platformType);
            result.put("userId", userId);
            result.put("message", success ? "从灰度列表移除用户成功" : "从灰度列表移除用户失败");
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("从灰度列表移除用户失败", e);
            JSONObject error = new JSONObject();
            error.put("success", false);
            error.put("error", "移除失败: " + e.getMessage());
            return ResponseEntity.ok(error);
        }
    }
}

package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 邀请详情信息
 */
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("user_invite_relation")
@Data
@Accessors(chain = true)
public class UserInviteRelationModel extends Model<UserInviteRelationModel> {


    @TableId(value = "relation_id", type = IdType.AUTO)
    private Long relationId;

    /**
     * 邀请类型-1:普通邀请;2:创世大使邀请;3:活动邀请;4:游戏活动邀请
     */
    @TableField("invite_type")
    private Integer inviteType;

    /**
     * 活动ID-关联的活动标识，非活动邀请时为NULL
     */
    @TableField("activity_id")
    private Long activityId;

    @TableField("invite_uid")
    private Long inviteUid;

    @TableField("invited_uid")
    private Long invitedUid;

    /**
     * 邀请代币发送状态-0:初始化;1:已发送待确认;2:已确认
     */
    @TableField("invite_amount_status")
    private Integer inviteAmountStatus;


    @TableField("invite_amount")
    private Integer inviteAmount;


    @TableField("invite_amount_symbol")
    private String inviteAmountSymbol;

    /**
     * 幂等值 唯一
     */
    @TableField("idempotent")
    private String idempotent;

    /**
     * 是否推送给用户端-0:未推送;1:已推送
     */
    @TableField("is_push_front")
    private Integer isPushFront;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;


}
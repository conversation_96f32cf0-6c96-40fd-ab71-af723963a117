package com.media.user.tools;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;

@Component
public class UserImgUtils {

    @Value(value = "${user.avatarUrl}")
    private String avatarUrl;

    @Value(value = "${user.background}")
    private String background;

    /**
     * 生成头像
     */
    public String getUserAvatarUrl() {
        SecureRandom random = new SecureRandom();
        int randomNumber = random.nextInt(10);
        return this.avatarUrl.formatted(randomNumber);
    }

    /**
     * 生成背景图
     */
    public String getUserBackground() {
        SecureRandom random = new SecureRandom();
        int randomNumber = random.nextInt(10);
        return this.background.formatted(randomNumber);
    }
}

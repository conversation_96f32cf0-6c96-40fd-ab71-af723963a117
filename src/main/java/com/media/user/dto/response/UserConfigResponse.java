package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class UserConfigResponse  {

    private Long id;

    private String userAgreement;

    private String userAgreementTitle;

    private String privacyAgreement;

    private String privacyAgreementTitle;

    private String aboutUs;

    private String communityContentConvention;

    private String walletAnnouncementUrl;

    // 提现规则标题
    private String walletAnnouncementTitle;

    // 提现规则副标题
    private String walletAnnouncementSubTitle;

    // 提现规则查看按钮
    private String walletAnnouncementCheck;

    // 挖矿排行榜规则页
    private String miningRankList;

    // 挖矿规则页
    private String miningRule;
}

# 融云 (RongCloud) API 集成

## 项目结构

```
src/main/java/com/media/user/
├── feign/
│   └── client/
│       ├── RongcloudClient.java           # 融云API客户端接口
│       └── README_Rongyun.md              # 本文档
├── dto/
│   ├── request/
│   │   └── RongcloudMessageRequest.java   # 融云消息请求DTO
│   └── response/
│       └── RongcloudMessageResponse.java  # 融云消息响应DTO
├── service/
│   └── RongcloudMessageService.java       # 融云消息服务
├── controller/
│   └── api/
│       └── RongcloudMessageController.java # 融云消息控制器
└── utils/
    └── CallbackSignatureVerifier.java     # 融云签名工具类
```

## 核心组件说明

### 1. RongcloudClient
- **位置**: `com.media.user.feign.client.RongcloudClient`
- **功能**: 融云REST API的Feign客户端接口
- **特点**: 
  - 使用 `@RequestParam` 直接发送表单数据，无需额外的编码器
  - 自动处理融云API的表单编码需求
  - 支持完整的签名验证机制

### 2. RongcloudMessageService
- **位置**: `com.media.user.service.RongcloudMessageService`
- **功能**: 融云消息发送的业务服务层
- **主要方法**:
  - `sendTextMessage()`: 发送文本私聊消息
  - `sendCustomMessage()`: 发送自定义消息
- **特点**: 
  - 自动生成融云API所需的签名信息
  - 处理消息格式转换和错误处理

### 3. 配置支持
- **融云配置项**:
  ```yaml
  rongcloud:
    host: https://api.sg-light-api.com  # 融云API地址
    appKey: your-app-key                # 应用Key
    appSecret: your-app-secret          # 应用密钥
  ```

## 使用示例

### 发送文本消息
```java
@Autowired
private RongcloudMessageService rongcloudMessageService;

// 发送简单文本消息
RongcloudMessageResponse response = rongcloudMessageService.sendTextMessage(
    "sender123",     // 发送者ID
    "receiver456",   // 接收者ID
    "Hello World!",  // 消息内容
    null            // 额外信息（可选）
);

if (response.isSuccess()) {
    System.out.println("消息发送成功，消息ID: " + response.getFirstMessageUID());
} else {
    System.out.println("消息发送失败: " + response.getErrorMessage());
}
```

### 发送自定义消息
```java
// 构建自定义消息请求
RongcloudMessageRequest request = new RongcloudMessageRequest()
    .setFromUserId("sender123")
    .setToUserId("receiver456")
    .setObjectName("RC:TxtMsg")
    .setContent("{\"content\":\"自定义消息\",\"extra\":\"额外数据\"}")
    .setPushContent("推送内容")
    .setIsPersisted(1);

// 发送消息
RongcloudMessageResponse response = rongcloudMessageService.sendCustomMessage(request);
```

## 技术特点

1. **无依赖表单编码**: 直接使用Spring的 `@RequestParam` 注解，无需额外的表单编码器依赖
2. **自动签名**: 服务会自动生成融云API所需的签名信息
3. **错误处理**: 完善的异常处理和错误响应机制
4. **配置灵活**: 支持通过配置文件进行环境差异化配置
5. **日志完善**: 详细的操作日志，便于问题排查

## 注意事项

1. 确保融云的 `appKey` 和 `appSecret` 配置正确
2. 融云API对签名有严格要求，时间戳误差不能超过5分钟
3. 用户ID需要在融云平台预先注册或使用自动注册功能
4. 消息内容需要符合融云的JSON格式要求
# 多语言图片配置示例

## 概述

支持19种语言的图片配置，通过在Apollo配置中为每个banner项目添加多语言图片映射来实现。

## 配置格式

### Banner配置示例

```json
{
  "banners": [
    {
      "id": "xme_mining_ongoing",
      "titleKey": "banner.mining.title",
      "descriptionKey": "banner.mining.description",
      "icon": "https://s3.x.me/images/default-icon.png",
      "iconCn": "https://s3.x.me/images/icon-cn.png",
      "backgroundImage": "https://s3.x.me/images/default-bg.jpg",
      "backgroundImageCn": "https://s3.x.me/images/bg-cn.jpg",
      
      // 新增：多语言图标配置
      "iconImages": {
        "en-US": "https://s3.x.me/images/icon-en.png",
        "zh-CN": "https://s3.x.me/images/icon-zh-cn.png",
        "zh-TW": "https://s3.x.me/images/icon-zh-tw.png",
        "ja-JP": "https://s3.x.me/images/icon-ja.png",
        "ko-KR": "https://s3.x.me/images/icon-ko.png",
        "de-DE": "https://s3.x.me/images/icon-de.png",
        "fr-FR": "https://s3.x.me/images/icon-fr.png",
        "es-ES": "https://s3.x.me/images/icon-es.png",
        "it-IT": "https://s3.x.me/images/icon-it.png",
        "ru-RU": "https://s3.x.me/images/icon-ru.png",
        "pt-PT": "https://s3.x.me/images/icon-pt.png",
        "ar-SA": "https://s3.x.me/images/icon-ar.png",
        "hi-IN": "https://s3.x.me/images/icon-hi.png",
        "th-TH": "https://s3.x.me/images/icon-th.png",
        "vi-VN": "https://s3.x.me/images/icon-vi.png",
        "tr-TR": "https://s3.x.me/images/icon-tr.png",
        "id-ID": "https://s3.x.me/images/icon-id.png",
        "fa-IR": "https://s3.x.me/images/icon-fa.png",
        "bn-BD": "https://s3.x.me/images/icon-bn.png"
      },
      
      // 新增：多语言背景图配置
      "backgroundImages": {
        "en-US": "https://s3.x.me/images/bg-en.jpg",
        "zh-CN": "https://s3.x.me/images/bg-zh-cn.jpg",
        "zh-TW": "https://s3.x.me/images/bg-zh-tw.jpg",
        "ja-JP": "https://s3.x.me/images/bg-ja.jpg",
        "ko-KR": "https://s3.x.me/images/bg-ko.jpg",
        "de-DE": "https://s3.x.me/images/bg-de.jpg",
        "fr-FR": "https://s3.x.me/images/bg-fr.jpg",
        "es-ES": "https://s3.x.me/images/bg-es.jpg",
        "it-IT": "https://s3.x.me/images/bg-it.jpg",
        "ru-RU": "https://s3.x.me/images/bg-ru.jpg",
        "pt-PT": "https://s3.x.me/images/bg-pt.jpg",
        "ar-SA": "https://s3.x.me/images/bg-ar.jpg",
        "hi-IN": "https://s3.x.me/images/bg-hi.jpg",
        "th-TH": "https://s3.x.me/images/bg-th.jpg",
        "vi-VN": "https://s3.x.me/images/bg-vi.jpg",
        "tr-TR": "https://s3.x.me/images/bg-tr.jpg",
        "id-ID": "https://s3.x.me/images/bg-id.jpg",
        "fa-IR": "https://s3.x.me/images/bg-fa.jpg",
        "bn-BD": "https://s3.x.me/images/bg-bn.jpg"
      },
      
      "jumpType": "native",
      "jumpUrl": "xme://taskCenter",
      "clickable": true,
      "status": 2,
      "order": 1,
      "auditShowable": true,
      "needLogin": true,
      "needFace": false,
      "needVerify": false,
      "minVersion": "1.8.0",
      "textColor": "#665000"
    }
  ]
}
```

## 支持的语言代码

| 语言 | 代码 | 说明 |
|------|------|------|
| 英语（美国） | en-US | 默认回退语言 |
| 中文（简体） | zh-CN | 中文系列默认 |
| 中文（繁体） | zh-TW | |
| 日语 | ja-JP | |
| 韩语 | ko-KR | |
| 德语 | de-DE | 德语系列默认 |
| 法语 | fr-FR | 法语系列默认 |
| 西班牙语 | es-ES | 西班牙语系列默认 |
| 意大利语 | it-IT | |
| 俄语 | ru-RU | |
| 葡萄牙语 | pt-PT | |
| 阿拉伯语 | ar-SA | |
| 印地语 | hi-IN | |
| 泰语 | th-TH | |
| 越南语 | vi-VN | |
| 土耳其语 | tr-TR | |
| 印尼语 | id-ID | |
| 波斯语 | fa-IR | |
| 孟加拉语 | bn-BD | |

## 图片选择逻辑

1. **精确匹配**：优先使用与用户语言完全匹配的图片
2. **语言前缀匹配**：如果没有精确匹配，尝试匹配语言前缀（如 zh-HK -> zh-CN）
3. **简化回退逻辑**：
   - 中文系列语言（zh-*）回退到 `zh-CN`
   - 其他所有语言回退到 `en-US`
4. **全局回退**：使用 en-US > zh-CN > 第一个可用图片 > 原有默认图片

## 向后兼容性

- 保留原有的 `icon`、`iconCn`、`backgroundImage`、`backgroundImageCn` 字段
- 如果没有配置多语言图片，自动使用原有字段
- 中文用户仍然优先使用 `backgroundImageCn`（如果配置了）

## 配置建议

### 最小配置
只配置主要语言的图片：
```json
{
  "iconImages": {
    "en-US": "english-icon.png",
    "zh-CN": "chinese-icon.png"
  }
}
```

### 完整配置
为所有19种语言配置专门的图片，提供最佳用户体验。

### 渐进式迁移
1. 先为主要语言（英语、中文）配置多语言图片
2. 逐步添加其他语言的图片
3. 最终移除旧的 `iconCn`、`backgroundImageCn` 字段

## 注意事项

1. **图片尺寸**：确保所有语言的图片尺寸一致
2. **文件大小**：优化图片大小以提高加载速度
3. **CDN缓存**：使用CDN加速图片加载
4. **回退机制**：确保每个配置都有合适的回退图片
5. **测试覆盖**：测试所有语言的图片显示效果

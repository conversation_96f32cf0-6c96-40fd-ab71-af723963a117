package com.media.user.eventbus.handlers;

import com.google.common.eventbus.Subscribe;
import com.media.core.i18n.I18nConvert;
import com.media.core.utils.MD5Util;

import com.media.user.context.RegisterCodeContext;
import com.media.user.dto.request.RongcloudMessageRequest;
import com.media.user.dto.request.internal.RegisterCodeInfoRequest;
import com.media.user.dto.response.internal.RegisterCodeInfoResponse;
import com.media.user.dto.response.RongcloudMessageResponse;
import com.media.user.enums.LanguageEnums;
import com.media.user.feign.client.GameClient;
import com.media.user.service.RongcloudMessageService;
import com.xme.xme_base_depends.enums.UserBehaviorEventEnum;
import com.xme.xme_base_depends.enums.UserEventTypeEnum;
import com.xme.xme_base_depends.mq.message.UserBehaviorEvent;
import com.xme.xme_base_depends.mq.message.UserEventMessage;
import com.xme.xme_base_depends.mq.message.UserRegisterMqDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

@Component
@Slf4j
public class RegisterEventSyncHandler {

    // 移除ThreadLocal，使用RegisterCodeContext
    // protected static ThreadLocal<RegisterCodeInfoResponse>
    // registerCodeInfoThreadLocal = ...;

    @Autowired
    private GameClient gameClient;

    @Autowired
    private RongcloudMessageService rongcloudMessageService;

    @Subscribe
    @Order(1)
    public void handle(UserBehaviorEvent event) {
        // todo 根据配置决定哪些消息同步处理 待确定同步处理时异步处理要不要

        Set<UserBehaviorEventEnum> type = event.getUserBehaviorEventType();
        if (type.contains(UserBehaviorEventEnum.REGISTERED)) {
            UserRegisterMqDTO dto = (UserRegisterMqDTO) event;
            long userid = dto.getUserId();
            Long inviteUid = dto.getInviteUid();

            // 如果邀请人不为空，则发起融云IM消息请求
            if (inviteUid != null && inviteUid != 0) {
                String content = I18nConvert.getI18nMessage("invite.follow.imtext",
                        LanguageEnums.get(dto.getLanguage()));
                sendInviteWelcomeMessage(inviteUid, userid, content);
            }

            RegisterCodeInfoRequest request = new RegisterCodeInfoRequest().setCode(dto.getInviteCode())
                    .setUserId(userid);
            RegisterCodeInfoResponse resp = gameClient.getRegisterCodeInfo(request).getResult();

            if (Objects.isNull(resp)) {
                log.info("register code info is null,code={}", dto.getInviteCode());
                return;
            }

            // 使用新的上下文管理器
            RegisterCodeContext.setRegisterCodeInfo(resp);

            if (!resp.isOpen()) {
                log.info("register code info is closed,code={}", dto.getInviteCode());
                return;
            }

            if (gameClient.checkIsSync(dto.getInviteCode()).getResult() == 1) {
                UserEventMessage message = new UserEventMessage();
                message.setEventType(UserEventTypeEnum.REGISTER);
                message.setEventIds(dto.getUserBehaviorEventType());
                message.setUserId(dto.getUserId());
                message.setData(dto);
                String uuid = MD5Util.getMD5(message.toString());
                message.setUuid(uuid);
                log.info("registerSync message: {}", message);
                gameClient.registerSync(message);
            }
        }
    }

    /**
     * 发送邀请欢迎消息给被邀请人
     * 
     * @param inviteUid      邀请人ID
     * @param newUserId      新注册用户ID
     * @param welcomeMessage 欢迎消息内容
     */
    private void sendInviteWelcomeMessage(long inviteUid, long newUserId, String welcomeMessage) {
        try {
            // 构建自定义消息请求
            RongcloudMessageRequest request = buildInviteWelcomeMessage(inviteUid, newUserId, welcomeMessage);

            log.info("发送邀请欢迎消息给被邀请人 - 邀请人: {}, 新用户: {}, 消息: {}", inviteUid, newUserId, welcomeMessage);

            // 使用自定义方法发送消息
            RongcloudMessageResponse response = rongcloudMessageService.sendCustomMessage(request);

            if (response.isSuccess()) {
                log.info("邀请欢迎消息发送成功 - 消息ID: {}, 接收者: {}",
                        response.getFirstMessageUID(), response.getFirstUserId());
            } else {
                log.warn("邀请欢迎消息发送失败 - 错误码: {}, 错误信息: {}",
                        response.getCode(), response.getErrorMessage());
            }

        } catch (Exception e) {
            // 不影响主流程，仅记录错误日志
            log.error("发送邀请欢迎消息异常 - 邀请人: {}, 新用户: {}", inviteUid, newUserId, e);
        }
    }

    /**
     * 构建邀请欢迎消息请求
     * 
     * @param inviteUid      邀请人ID
     * @param newUserId      新注册用户ID
     * @param welcomeMessage 欢迎消息内容
     * @return 自定义消息请求对象
     */
    private RongcloudMessageRequest buildInviteWelcomeMessage(long inviteUid, long newUserId, String welcomeMessage) {
        // 构建消息内容JSON
        String messageContent = String.format(
                "{\"content\":\"%s\"}",
                welcomeMessage);

        // 创建自定义请求对象
        RongcloudMessageRequest request = new RongcloudMessageRequest();
        request.setFromUserId(String.valueOf(inviteUid)); // 系统发送者
        request.setToUserId(String.valueOf(newUserId)); // 接收者：新注册用户
        request.setObjectName("app:invite"); // 消息类型：自定义类型
        request.setContent(messageContent); // 消息内容

        return request;
    }
}

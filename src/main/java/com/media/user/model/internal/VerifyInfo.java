package com.media.user.model.internal;

import com.media.user.domain.ClientUserModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain=true)
public class VerifyInfo {
    private boolean hasPhoneVerify =false;
    private boolean hasEmailVerify =false;
    private boolean hasFaceLiveness =false;

    public void setClientUserModel(Date time, ClientUserModel clientUserModel){
        if(hasPhoneVerify){
            clientUserModel.setPhoneVerifyTime(time);
            clientUserModel.setPhoneVerify(1);
        }
        if(hasEmailVerify){
            clientUserModel.setEmailVerifyTime(time);
            clientUserModel.setEmailVerify(1);
        }
        if(hasFaceLiveness){
            clientUserModel.setFaceLivenessAt(time);
            clientUserModel.setFaceLivenessStatus(1);
        }
    }
}

package com.media.user.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 彩票分享响应数据
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LotteryShareResponse {
    
    // 总奖池金额
    private String prizeTotalAmount;
    
    // 奖励币种
    private String currency;
    
    // 用户邀请码
    private String invitationCode;

    // 分享链接
    private String shareUrl;
    
    // 额外奖励金额
    private String additionalRewardAmount;
    
    // 分享提示标题
    private String sharePromptTitle;
    
    // 分享提示描述
    private String sharePromptDescription;

}
package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.user.dto.request.*;
import com.media.user.dto.response.*;
import com.media.user.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/user/api/user")
public class BeeLoginApiController {

    @Autowired
    BeeAuthService beeAuthService;

    /**
     * bee auth
     */
    @PostMapping("/bee/auth")
    public AuthTokenBeeResponse beeAuth(@Validated @RequestBody BeeAuthRequest beeAuthRequest) {
        return beeAuthService.auth(beeAuthRequest);
    }

    /**
     * bee无邮箱用户 需要设置邮箱
     * 
     * @param request
     */
    @PostMapping("/bee/set/email")
    @RequireAuth
    public void updateEmail(@Validated @RequestBody UpdateBeeEmailRequest request) {
        request.setUid(CloudSession.getUid());
        beeAuthService.updateBeeEmail(request);
    }

}

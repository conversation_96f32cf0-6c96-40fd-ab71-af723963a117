package com.media.core.exception;

public class BaseException extends RuntimeException {

    /**
     * 输入日志.
     */
    private boolean printLog;

    /**
     * 报警.
     */
    private AlertMessage alert;

    private String message;

    private Integer status;

    private Integer code;


    public BaseException() {
    }

    public BaseException(Throwable cause) {
        super(cause);
    }

    public BaseException(String message) {
        super(message);
    }

    public BaseException(Integer status, String message, Integer code) {
        super(message);
        this.status = status;
        this.message = message;
        this.code = code;
    }

    public BaseException(String message, AlertMessage alert) {
        super(message);
        this.alert = alert;
    }


    public boolean isPrintLog() {
        return printLog;
    }

    public void setPrintLog(boolean printLog) {
        this.printLog = printLog;
    }

    public AlertMessage getAlert() {
        return alert;
    }

    public void setAlert(AlertMessage alert) {
        this.alert = alert;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}

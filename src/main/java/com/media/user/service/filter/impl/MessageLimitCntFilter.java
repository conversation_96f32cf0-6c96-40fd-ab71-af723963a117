package com.media.user.service.filter.impl;

import com.media.user.dto.im.RongCloudMessageData;
import com.media.user.dto.rongCloud.RongCloudCallbackDTO;
import com.media.user.enums.ImExceptionEnums;
import com.media.user.service.filter.MessageFilter;
import com.media.user.service.filter.annotation.ChannelType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static com.media.user.constant.MediaUserConstant.*;
import static com.media.user.enums.ImExceptionEnums.SUCCESS;

@Slf4j
@Component
@Order(3)
@ChannelType(value = {"PERSON"})
public class MessageLimitCntFilter implements MessageFilter {

    private static final int MAX_MSG_PER_USER = 3;
    private static final int MAX_NONFOLLOW_USERS = 10;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Override
    public ImExceptionEnums doFilter(RongCloudMessageData data) {
        log.info("<MessageLimitCntFilter> start data:{}", data);
        if (Objects.isNull(data) || Objects.isNull(data.getMessageDTO())) {
            return SUCCESS;
        }
        RongCloudCallbackDTO messageDTO = data.getMessageDTO();
        String sender = messageDTO.getFromUserId();
        String receiver = messageDTO.getTargetId();
        if (isMutualFollow(sender, receiver)) {
            return SUCCESS;
        }
        return limitCheck(messageDTO.getFromUserId(), messageDTO.getTargetId());
    }

    private ImExceptionEnums limitCheck(String sender, String receiver) {
        // 检查是否超过每日非互关用户限制
        String dailyNonFollowKey = IM_NONFOLLOW_DAILY_PREFIX + sender;
        long secondsUntilMidnight = getSecondsUntilUTCMidnight();
        // 检查是否已超过10个不同的非互关用户
        Long nonFollowCount = stringRedisTemplate.opsForSet().size(dailyNonFollowKey);
        if (nonFollowCount != null && nonFollowCount >= MAX_NONFOLLOW_USERS) {
            // 检查当前接收者是否已经在集合中
            Boolean isMember = stringRedisTemplate.opsForSet().isMember(dailyNonFollowKey, receiver);
            if (Boolean.FALSE.equals(isMember)) {
                // 已达到今日非互关用户消息限制(最多10人)
                return ImExceptionEnums.DAILY_RECEIVER_LIMIT;
            }
        }
        // 检查是否超过对单个非互关用户的消息限制
        String userMsgKey = IM_MSG_DAILY_PREFIX + sender + ":" + receiver;
        Long msgCount = stringRedisTemplate.opsForValue().increment(userMsgKey);
        if (msgCount != null) {
            // 如果是第一次发送，设置24小时过期时间
            if (msgCount == 1) {
                stringRedisTemplate.expire(userMsgKey, 24, TimeUnit.HOURS);
                // 将接收者添加到发送者的每日非互关用户集合
                stringRedisTemplate.opsForSet().add(dailyNonFollowKey, receiver);
                // 设置集合的过期时间为到UTC 0点
                stringRedisTemplate.expire(dailyNonFollowKey, secondsUntilMidnight, TimeUnit.SECONDS);
            }
            // 检查是否超过3条消息限制
            if (msgCount > MAX_MSG_PER_USER) {
                // 今日对此用户发送消息已达上限(最多3条)
                return ImExceptionEnums.DAILY_MESSAGE_LIMIT_PER_USER;
            }
        }
        return SUCCESS;
    }

    private long getSecondsUntilUTCMidnight() {
        ZonedDateTime now = ZonedDateTime.now(ZoneOffset.UTC);
        ZonedDateTime midnight = now.truncatedTo(ChronoUnit.DAYS).plusDays(1);
        return ChronoUnit.SECONDS.between(now, midnight);
    }

    public boolean isMutualFollow(String userId1, String userId2) {
        Boolean user1FollowsUser2 = stringRedisTemplate.opsForSet().isMember(USER_FOLLOWING_PREFIX + userId1, userId2);
        Boolean user2FollowsUser1 = stringRedisTemplate.opsForSet().isMember(USER_FOLLOWING_PREFIX + userId2, userId1);
        return Boolean.TRUE.equals(user1FollowsUser2) && Boolean.TRUE.equals(user2FollowsUser1);
    }
}

//package com.media.user.advice.config;
//
//
//import com.media.user.advice.aop.ApiResponseAdvice;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//
//@EnableConfigurationProperties(ApiResponseProperties.class)
//public class ApiResponseConfig {
//
//    @Bean
//    @ConditionalOnMissingBean(ApiResponseAdvice.class)
//    public ApiResponseAdvice apiResponseAdvice(ApiResponseProperties properties) {
//        return new ApiResponseAdvice();
//    }
//}

package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("user_feedback")
@Data
@Accessors(chain = true)
public class UserFeedbackModel extends Model<UserFeedbackModel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "uid")
    private Long uid;

    @TableField("name")
    private String name;

    @TableField("contract")
    private String contract;

    @TableField("image_list")
    private String imageList;

    @TableField("content")
    private String content;
    
    @TableField("type")
    private Byte type;

    @TableField("status")
    private Byte status;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;
}
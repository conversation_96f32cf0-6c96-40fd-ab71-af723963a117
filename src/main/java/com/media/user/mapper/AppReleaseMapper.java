package com.media.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.AppReleaseModel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface AppReleaseMapper extends BaseMapper<AppReleaseModel> {

    @Select("SELECT *\n" +
            "FROM app_release AS t1\n" +
            "WHERE t1.id IN (\n" +
            "    SELECT MAX(t2.id)\n" +
            "    FROM app_release AS t2\n" +
            "    WHERE t1.platform_type = t2.platform_type\n" +
            "    GROUP BY t2.platform_type\n" +
            ");")
    List<AppReleaseModel> selectListRelease();


}
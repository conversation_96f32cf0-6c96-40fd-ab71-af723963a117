package com.media.user.dto.im;

import com.media.user.dto.response.ClientUserResponse;
import com.media.user.dto.rongCloud.RongCloudCallbackDTO;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RongCloudMessageData {
    /**
     * 消息数据
     */
    RongCloudCallbackDTO messageDTO;
    /**
     * 时间戳
     */
    String timestamp;
    /**
     * 种子
     */
    String nonce;
    /**
     * 签名
     */
    String signature;
    /**
     * 用户信息
     */
    ClientUserResponse userInfo;
}

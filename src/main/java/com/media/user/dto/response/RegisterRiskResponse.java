package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 注册风险响应结果
 */
@Data
@Accessors(chain = true)
public class RegisterRiskResponse {

    /**
     * 错误代码
     */
    private Integer errorCode;

    /**
     * 操作动作
     */
    private String action;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 持续时间
     */
    private Integer duration;
}

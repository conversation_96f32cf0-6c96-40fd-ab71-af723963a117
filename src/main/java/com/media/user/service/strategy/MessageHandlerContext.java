package com.media.user.service.strategy;

import com.media.user.dto.im.RongCloudMessageData;
import com.media.user.dto.rongCloud.RongCloudCallbackDTO;
import com.media.user.dto.rongCloud.RongCloudCallbackResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MessageHandlerContext {

    @Autowired
    private MessageHandlerStrategyFactory strategyFactory;

    public RongCloudCallbackResponse handleMessage(RongCloudMessageData messageData) {
        RongCloudCallbackDTO messageDTO = messageData.getMessageDTO();
        String channelType = messageDTO.getChannelType();
        MessageHandlerStrategy strategy = strategyFactory.getStrategy(channelType);
        return strategy.handleMessage(messageData);
    }
} 
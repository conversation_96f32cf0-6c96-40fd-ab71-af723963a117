package com.media.user.service;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.media.core.constant.PageResponse;
import com.media.core.exception.ApiException;
import com.media.core.id.RedisIdGenerator;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.TwitterUserModel;
import com.media.user.domain.UserFollowRelationModel;
import com.media.user.dto.query.UserFollowingMemberQuery;
import com.media.user.dto.request.NewBieTaskRequest;
import com.media.user.dto.request.UserFollowBatchRequest;
import com.media.user.dto.response.ClientUserResponse;
import com.media.user.dto.response.UserFollowingMemberResponse;
import com.media.user.dto.response.internal.UserMeResponse;
import com.media.user.enums.PointEventEnums;
import com.media.user.enums.UserFollowStateEnum;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.mapper.TwitterUserMapper;
import com.media.user.mapper.UserFollowRelationMapper;
import com.media.user.mq.provider.UserSendMessageProvider;
import com.media.user.service.cache.ClientUserCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
@Service
public class UserFollowService {

    @Autowired
    private ClientUserCacheService clientUserCacheService;

    @Autowired
    private RedisIdGenerator redisIdGenerator;

    @Autowired
    TwitterUserMapper twitterUserMapper;

    @Autowired
    UserFollowRelationMapper userFollowRelationMapper;

    @Autowired
    UserSendMessageProvider userSendMessageProvider;

    @Autowired
    private NewbieTaskRecordService newbieTaskRecordService;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    /**
     * 批量关注
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchFollow(Long followUid, UserFollowBatchRequest request){
        //查询关注者信息
        ClientUserResponse clientUserResponse = clientUserCacheService.me(followUid);
        if(clientUserResponse == null){
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        //处理被关注者
        List<UserMeResponse> followedUsers = clientUserCacheService.users(request.getUids());
        for(UserMeResponse clientUserModel : followedUsers){
            Long followedUid = clientUserModel.getUid();
            //如果关注的用户是自己，则跳过
            if(Objects.equals(followUid, followedUid)){
                continue;
            }

            Long oldUid = 0L;
            Long newUid = 0L;
            if(clientUserModel.getVirtualType().intValue() == 1){
                //处理预注册用户关注状态
                oldUid = followedUid;
                //检查有没有对应的 newUid
                QueryWrapper<TwitterUserModel> followWrapper = new QueryWrapper<>();
                followWrapper.lambda().eq(TwitterUserModel::getOldUid, oldUid);
                TwitterUserModel twitterUserModel = twitterUserMapper.selectOne(followWrapper);
                if(twitterUserModel != null && twitterUserModel.getNewUid() != null){
                    newUid = twitterUserModel.getNewUid();
                }
            }else{
                newUid = followedUid;
                //检查有没有对应的 oldUid
                QueryWrapper<TwitterUserModel> followWrapper = new QueryWrapper<>();
                followWrapper.lambda().eq(TwitterUserModel::getNewUid, newUid);
                TwitterUserModel twitterUserModel = twitterUserMapper.selectOne(followWrapper);
                if(twitterUserModel != null && twitterUserModel.getOldUid() != null){
                    oldUid = twitterUserModel.getOldUid();
                }
            }

            QueryWrapper<UserFollowRelationModel> followNewWrapper = new QueryWrapper<>();
            followNewWrapper.lambda().eq(UserFollowRelationModel::getFollowUid, followUid);
            followNewWrapper.lambda().eq(UserFollowRelationModel::getFollowedNewUid, followedUid);
            UserFollowRelationModel followRelationModel = userFollowRelationMapper.selectOne(followNewWrapper);
            if(followRelationModel == null){
                QueryWrapper<UserFollowRelationModel> followOldWrapper = new QueryWrapper<>();
                followOldWrapper.lambda().eq(UserFollowRelationModel::getFollowUid, followUid);
                followOldWrapper.lambda().eq(UserFollowRelationModel::getFollowedOldUid, followedUid);
                followRelationModel = userFollowRelationMapper.selectOne(followOldWrapper);
            }

            boolean fansStatus = false;
            if(followRelationModel == null){
                followRelationModel = new UserFollowRelationModel();
                followRelationModel.setRelationId(redisIdGenerator.generate());
                followRelationModel.setFollowUid(followUid);
                followRelationModel.setFollowedNewUid(newUid);
                followRelationModel.setFollowedOldUid(oldUid);
                followRelationModel.setFollowState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
                followRelationModel.setFollowTime(new Date());
                followRelationModel.setCreatedTime(new Date());
                userFollowRelationMapper.insert(followRelationModel);
                fansStatus = true;
            }else{
                if(Objects.equals(followRelationModel.getFollowState(), UserFollowStateEnum.FOLLOW_STATE_0.getState())){
                    followRelationModel.setFollowUid(followUid);
                    followRelationModel.setFollowedNewUid(newUid);
                    followRelationModel.setFollowedOldUid(oldUid);
                    followRelationModel.setFollowState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
                    followRelationModel.setFollowTime(new Date());
                    followRelationModel.setUpdatedTime(new Date());
                    userFollowRelationMapper.updateById(followRelationModel);
                    fansStatus = true;
                }
            }

         //   if(fansStatus) {
                // 添加到我关注列表
                stringRedisTemplate.opsForSet().add(MediaUserConstant.USER_FOLLOWING_PREFIX + followUid, String.valueOf(followedUid));
                // 添加到对方的粉丝列表
                stringRedisTemplate.opsForSet().add(MediaUserConstant.USER_FANS_PREFIX + followedUid, String.valueOf(followUid));
         //   }

            // 发送关注消息
            Long toUid = newUid == 0 ? oldUid : newUid;
            userSendMessageProvider.sendFollowMessage(followUid, clientUserResponse.getNickName(), clientUserResponse.getAvatarUrl(), toUid);
        }

    }

    /**
     * 批量取消关注
     */
    public void batchCancel(Long followUid, UserFollowBatchRequest request){
        for(Long followedUid: request.getUids()){
            boolean fansStatus = false;
            QueryWrapper<UserFollowRelationModel> followNewWrapper = new QueryWrapper<>();
            followNewWrapper.lambda().eq(UserFollowRelationModel::getFollowUid, followUid);
            followNewWrapper.lambda().eq(UserFollowRelationModel::getFollowedNewUid, followedUid);
            UserFollowRelationModel followRelationModel = userFollowRelationMapper.selectOne(followNewWrapper);
            if(followRelationModel == null){
                QueryWrapper<UserFollowRelationModel> followOldWrapper = new QueryWrapper<>();
                followOldWrapper.lambda().eq(UserFollowRelationModel::getFollowUid, followUid);
                followOldWrapper.lambda().eq(UserFollowRelationModel::getFollowedOldUid, followedUid);
                followRelationModel = userFollowRelationMapper.selectOne(followOldWrapper);
            }

            if(followRelationModel != null){
                if(Objects.equals(followRelationModel.getFollowState(), UserFollowStateEnum.FOLLOW_STATE_1.getState())) {
                    followRelationModel.setFollowUid(followUid);
                    followRelationModel.setFollowState(UserFollowStateEnum.FOLLOW_STATE_0.getState());
                    followRelationModel.setUpdatedTime(new Date());
                    userFollowRelationMapper.updateById(followRelationModel);
                    fansStatus = true;
                }
            }

            if(fansStatus){
                //减少我的关注列表
                stringRedisTemplate.opsForSet().remove(MediaUserConstant.USER_FOLLOWING_PREFIX + followUid, String.valueOf(followedUid));
                //删除对方的粉丝列表
                stringRedisTemplate.opsForSet().remove(MediaUserConstant.USER_FANS_PREFIX + followedUid, String.valueOf(followUid));
            }

        }
    }

    public PageResponse<UserFollowingMemberResponse> getUserFollowMemberPage(UserFollowingMemberQuery query) {
        var userFollowingMemberPage = getUserFollowingMemberPage(query);
        var records = userFollowingMemberPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
        }
        var followedUids = records.stream().map(UserFollowService::getFollowedUid).toList();
        var userMeResponseMap = clientUserCacheService.batchUserMes(query.getUid(), followedUids, false, true, true);
        List<UserFollowingMemberResponse> list = Lists.newArrayList();
        for (UserFollowRelationModel model : records) {
            Long followedUid = getFollowedUid(model);
            UserMeResponse userInfo = userMeResponseMap.get(followedUid);
            if(userInfo == null){
                continue;
            }
            UserFollowingMemberResponse userFollowingMemberResponse = new UserFollowingMemberResponse();
            userFollowingMemberResponse.setUid(followedUid)
                    .setNickName(userInfo.getNickName())
                    .setPersonIntroduce(userInfo.getPersonIntroduce())
                    .setFollowState(model.getFollowState())
                    .setFollowedState(userInfo.getFollowedState())
                    .setFansCount(userInfo.getFansNumber())
                    .setGenesisBadge(userInfo.getGenesisBadge() == null ? 0 : userInfo.getGenesisBadge())
                    .setAvatarUrl(userInfo.getAvatarUrl());
            list.add(userFollowingMemberResponse);
        }
        return new PageResponse<>(query.getPage(), query.getSize(), userFollowingMemberPage.getTotal(), list);
    }

    private static Long getFollowedUid(UserFollowRelationModel model) {
        Long followedUid = model.getFollowedNewUid();
        if (followedUid == null || followedUid == 0) {
            followedUid = model.getFollowedOldUid();
        }
        return followedUid;
    }

    public IPage<UserFollowRelationModel> getUserFollowingMemberPage(UserFollowingMemberQuery query) {
        QueryWrapper<UserFollowRelationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserFollowRelationModel::getFollowUid, query.getUid());
        queryWrapper.lambda().eq(UserFollowRelationModel::getFollowState, UserFollowStateEnum.FOLLOW_STATE_1.getState());
        queryWrapper.orderByDesc("follow_time");

        IPage<UserFollowRelationModel> page = new Page<>(query.getPage(), query.getSize());
        return userFollowRelationMapper.selectPage(page, queryWrapper);
    }



    public PageResponse<UserFollowingMemberResponse> getFansPage(UserFollowingMemberQuery query) {
        IPage<UserFollowRelationModel> userFollowingMemberPage = getUserFansPage(query);
        var records = userFollowingMemberPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return new PageResponse<>(query.getPage(), query.getSize(), 0L, null);
        }
        var followUids = records.stream().map(UserFollowRelationModel::getFollowUid).toList();
        var userMeResponseMap = clientUserCacheService.batchUserMes(query.getUid(), followUids, true, false, true);
        List<UserFollowingMemberResponse> list = Lists.newArrayListWithCapacity(records.size());
        for (UserFollowRelationModel model : records) {
            var userInfo = userMeResponseMap.get(model.getFollowUid());
            if(userInfo == null){
                continue;
            }
            var userFansResponse = new UserFollowingMemberResponse();
            userFansResponse.setUid(model.getFollowUid())
                    .setNickName(userInfo.getNickName())
                    .setPersonIntroduce(userInfo.getPersonIntroduce())
                    .setFansCount(userInfo.getFansNumber())
                    .setGenesisBadge(userInfo.getGenesisBadge() == null ? 0 : userInfo.getGenesisBadge())
                    .setAvatarUrl(userInfo.getAvatarUrl())
                    .setFollowState(userInfo.getFollowState())
                    .setFollowedState(UserFollowStateEnum.FOLLOW_STATE_1.getState());
            list.add(userFansResponse);
        }
        return new PageResponse<>(query.getPage(), query.getSize(), userFollowingMemberPage.getTotal(), list);
    }

    public IPage<UserFollowRelationModel> getUserFansPage(UserFollowingMemberQuery query) {
        //查询当前用户是虚拟用户还是真实用户
        ClientUserResponse userResponse = clientUserCacheService.me(query.getUid());

        QueryWrapper<UserFollowRelationModel> followQueryWrapper = new QueryWrapper<>();
        if(userResponse.getVirtualType().intValue() == 1) { //预注册用户
            followQueryWrapper.lambda().eq(UserFollowRelationModel::getFollowedOldUid, query.getUid());
        } else if(userResponse.getVirtualType().intValue() == 0) { //真是用户
            followQueryWrapper.lambda().eq(UserFollowRelationModel::getFollowedNewUid, query.getUid());
        }else{
            followQueryWrapper.lambda().eq(UserFollowRelationModel::getRelationId, 0);  //查无数据
        }
        followQueryWrapper.lambda().eq(UserFollowRelationModel::getFollowState, UserFollowStateEnum.FOLLOW_STATE_1.getState());
        followQueryWrapper.orderByDesc("follow_time");
        IPage<UserFollowRelationModel> page = new Page<>(query.getPage(), query.getSize());
        return userFollowRelationMapper.selectPage(page, followQueryWrapper);
    }

    /**
     * 统计关注的数量
     * @param uid
     * @return
     */
    public Long countFollowers(Long uid) {
        return stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FOLLOWING_PREFIX + uid);
    }

    /**
     * 统计粉丝数量
     * @param uid
     * @return
     */
    public Long countFans(Long uid) {
        return stringRedisTemplate.opsForSet().size(MediaUserConstant.USER_FANS_PREFIX + uid);
    }

    /**
     * 用户是否关注我
     * @param followUid
     * @param followedUid
     * @return
     */
    public Boolean isFollowing(Long followUid, Long followedUid){
        if(followUid == null || followedUid == null){
            return false;
        }
        return stringRedisTemplate.opsForSet().isMember(MediaUserConstant.USER_FOLLOWING_PREFIX + followUid, String.valueOf(followedUid));
    }

    /**
     * 修复用户的关注关系，一般用不到
     * @param uid
     */
    public void refresh(Long uid){
        log.info("refresh user following uid: {}", uid);
        QueryWrapper<UserFollowRelationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserFollowRelationModel::getFollowUid, uid);
        queryWrapper.lambda().eq(UserFollowRelationModel::getFollowState, UserFollowStateEnum.FOLLOW_STATE_1.getState());
        List<UserFollowRelationModel> followingList = userFollowRelationMapper.selectList(queryWrapper);
        if(followingList != null && followingList.size() > 0){
            Set<String> followings = new HashSet<>();
            for(UserFollowRelationModel relationModel : followingList){
                if(relationModel.getFollowedNewUid() != null && relationModel.getFollowedNewUid() > 0){
                    followings.add(relationModel.getFollowedNewUid().toString());
                }else {
                    followings.add(relationModel.getFollowedOldUid().toString());
                }
            }
            //刷新我的关注列表缓存
            stringRedisTemplate.delete(MediaUserConstant.USER_FOLLOWING_PREFIX + uid);
            stringRedisTemplate.opsForSet().add(MediaUserConstant.USER_FOLLOWING_PREFIX + uid, followings.toArray(new String[0]));
        }

        log.info("refresh user fans uid: {}", uid);
        ClientUserResponse userResponse = clientUserCacheService.me(uid);
        if(userResponse != null){
            List<UserFollowRelationModel> fanList = new ArrayList<>();
            if(userResponse.getVirtualType() == 1) { //预注册用户
                QueryWrapper<UserFollowRelationModel> followOldWrapper = new QueryWrapper<>();
                followOldWrapper.lambda().eq(UserFollowRelationModel::getFollowedOldUid, uid);
                followOldWrapper.lambda().eq(UserFollowRelationModel::getFollowState, UserFollowStateEnum.FOLLOW_STATE_1.getState());
                fanList = userFollowRelationMapper.selectList(followOldWrapper);
            } else if(userResponse.getVirtualType() == 0) { //真是用户
                QueryWrapper<UserFollowRelationModel> followNewWrapper = new QueryWrapper<>();
                followNewWrapper.lambda().eq(UserFollowRelationModel::getFollowedNewUid, uid);
                followNewWrapper.lambda().eq(UserFollowRelationModel::getFollowState, UserFollowStateEnum.FOLLOW_STATE_1.getState());
                fanList = userFollowRelationMapper.selectList(followNewWrapper);
            }

            if(fanList != null && fanList.size() > 0){
                Set<String> fans = new HashSet<>();
                for(UserFollowRelationModel relationModel : fanList){
                    fans.add(relationModel.getFollowUid().toString());
                }
                //刷新我的关注列表缓存
                stringRedisTemplate.delete(MediaUserConstant.USER_FANS_PREFIX + uid);
                stringRedisTemplate.opsForSet().add(MediaUserConstant.USER_FANS_PREFIX + uid, fans.toArray(new String[0]));
            }
        }
    }
}

package com.media.user.service;

import com.media.user.dto.request.GetSubscriptionCountRequest;
import com.media.user.dto.request.GetUserBalanceRequest;
import com.media.user.dto.response.SubscriptionCountResponse;
import com.media.user.dto.response.UserProfileResponse;
import com.media.user.feign.client.ContentApiClient;
import com.media.user.feign.client.UserAccountBalanceClient;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

@Slf4j
@Service
public class UserProfileService {

    @Autowired
    UserAccountBalanceClient userAccountBalanceClient;

    @Autowired
    ContentApiClient contentApiClient;

    private ExecutorService executorService;

    @PostConstruct
    public void init() {
        // 创建线程池，用于异步执行
        executorService = Executors.newFixedThreadPool(20);
    }

    @PreDestroy
    public void destroy() {
        if (executorService != null) {
            executorService.shutdown();
        }
    }

    /**
     * 获取用户个人中心信息
     */
    public UserProfileResponse getUserProfile(Long userId) {
        try {
            UserProfileResponse response = new UserProfileResponse();

            // 创建两个异步任务
            CompletableFuture<Integer> subscriptionFuture = getSubscriptionCountAsync(userId);
            CompletableFuture<String> balanceFuture = getUserBalanceAsync(userId);

            // 等待两个任务完成并设置结果
            response.setSubscriptionCount(subscriptionFuture.get());
            response.setTotalBalance(balanceFuture.get());

            return response;
        } catch (Exception e) {
            log.error("获取用户个人中心信息失败，userId: {}", userId, e);
            // 返回默认值
            UserProfileResponse response = new UserProfileResponse();
            response.setSubscriptionCount(0);
            response.setTotalBalance("$0.00");
            return response;
        }
    }

    /**
     * 异步获取订阅数量
     */
    @Async
    public CompletableFuture<Integer> getSubscriptionCountAsync(Long userId) {
        try {
            GetSubscriptionCountRequest request = new GetSubscriptionCountRequest();
            request.setUserID(userId);
            ApiResponse<SubscriptionCountResponse> subscriptionResponse = contentApiClient.getSubscriptionCount(request);
            if (subscriptionResponse.getSuccess()) {
                return CompletableFuture.completedFuture(subscriptionResponse.getResult().getCount());
            }
            return CompletableFuture.completedFuture(0);
        } catch (Exception e) {
            log.error("获取用户订阅数量失败，userId: {}", userId, e);
            return CompletableFuture.completedFuture(0);
        }
    }

    /**
     * 异步获取用户余额
     */
    @Async
    public CompletableFuture<String> getUserBalanceAsync(Long userId) {
        try {
            GetUserBalanceRequest getUserBalanceRequest = new GetUserBalanceRequest();
            getUserBalanceRequest.setUid(userId);
            ApiResponse<BigDecimal> getTotalUSDTResponse = userAccountBalanceClient.getTotalUSDT(getUserBalanceRequest);
            if (getTotalUSDTResponse.getSuccess()) {
                return CompletableFuture.completedFuture(formatBalance(getTotalUSDTResponse.getResult()));
            }
            return CompletableFuture.completedFuture("$0");
        } catch (Exception e) {
            log.error("获取用户余额失败，userId: {}", userId, e);
            return CompletableFuture.completedFuture("$0");
        }
    }

    private String formatBalance(BigDecimal balance) {
        if (balance == null) {
            return "$0";
        }

        DecimalFormat formatter = new DecimalFormat("#,##0.######");
        return "$" + formatter.format(balance.setScale(6, RoundingMode.DOWN));
    }
}

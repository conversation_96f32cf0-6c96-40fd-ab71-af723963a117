# 开发环境配置
server:
  # 服务器的HTTP端口，默认为80
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    # 默认值20s 设置http超时时间(即keep-alive超时时间),没有任何活动则tomcat关闭连接
    connection-timeout: 20000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
#logging:
#  charset:
#    console: UTF-8
#    file: UTF-8
#  level:
#    com.media: info
#    org.springframework: warn
#  config: classpath:logback.xml
#logstash:
#  host: ${LOGSTASH_HOST:*************:5044}

logFile:
  path: /data/media-user
#  path: /Users/<USER>/tmp/logs

logging:
  level:
    software:
      amazon:
        awssdk: WARN
    org:
      apache:
        http:
          wire: WARN
    # Feign客户端详细日志
    com:
      media:
        user:
          feign:
            client:
              PartnerTokenClient: DEBUG

app:
  id: ${spring.application.name}
apollo:
  meta: ${APOLLO_META:http://apollo-config-test.xme.world}
  cluster: ${APOLLO_CLUSTER:dev}
  bootstrap:
    enabled: true
    namespaces: ${APOLLO_NAMESPACE:application}

spring:
  freemarker:
    template-loader-path:
      - classpath:/templates/email/
      - classpath:/templates/message/
    charset: UTF-8
  messages:
    basename: i18n/messages

  #卡夫卡配置
  kafka:
    bootstrap-servers: ${kafka.aws.servers:b-2.xmeenvdevtestkafk.cqf4t4.c3.kafka.ap-southeast-1.amazonaws.com:9098,b-1.xmeenvdevtestkafk.cqf4t4.c3.kafka.ap-southeast-1.amazonaws.com:9098}
    # 是否启用 AWS MSK
    aws-msk:
      enabled: ${kafka.aws.enabled:false}
    security:
      protocol: ${kafka.security.protocol:SASL_SSL}
    consumer:
      group-id: media-user
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: false
      auto-commit-interval: 0
      properties:
        max.poll.interval.ms: 300000  # 5分钟
        max.poll.records: 500
    listener:
      ack-mode: MANUAL_IMMEDIATE  # 手动立即提交
      missing-topics-fatal: false # 缺少topic时不要抛出异常
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: all                  # 需要所有副本确认
      retries: 3                 # 重试次数
      properties:
        max.in.flight.requests.per.connection: 1  # 限制客户端在单个连接上能够发送的未确认请求的个数

  kafka-log:
    bootstrap-servers: ${kafka-log.aws.servers:b-2.xmeenvdevtestkafk.cqf4t4.c3.kafka.ap-southeast-1.amazonaws.com:9098,b-1.xmeenvdevtestkafk.cqf4t4.c3.kafka.ap-southeast-1.amazonaws.com:9098}
    # 是否启用 AWS MSK
    aws-msk:
      enabled: ${kafka-log.aws.enabled:false}
    security:
      protocol: ${kafka-log.security.protocol:SASL_SSL}
    consumer:
      group-id: media-user
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      enable-auto-commit: false
      auto-commit-interval: 0
      properties:
        max.poll.interval.ms: 300000  # 5分钟
        max.poll.records: 500
    listener:
      ack-mode: MANUAL_IMMEDIATE  # 手动立即提交
      missing-topics-fatal: false # 缺少topic时不要抛出异常
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: all                  # 需要所有副本确认
      retries: 3                 # 重试次数
      properties:
        max.in.flight.requests.per.connection: 1  # 限制客户端在单个连接上能够发送的未确认请求的个数

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 主库数据源
      master:
        # DEV
        url: ${MYSQL_URL:******************************************************************************************************************************************************************************************************}
        username: ${MYSQL_USERNAME:envdev-user}
        password: ${MYSQL_PASSWORD:k6xetf4YkB}

        # TEST
#        url: ${MYSQL_URL:**************************************************************************************************************************************************************************************************************************}
#        username: ${MYSQL_USERNAME:envtest-user}
#        password: ${MYSQL_PASSWORD:k7xetf5YKB}


      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 20
      # 最大连接池数量
      maxActive: 100
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置连接超时时间
      connectTimeout: 30000
      # 配置网络超时时间
      socketTimeout: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: ruoyi
        login-password: BHyjJGjBDkApTdYHKioBNJKBK
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  application:
    name: media-user
  data:
    redis:
      lettuce:
        pool:
          max-active: 200
          max-idle: 100
          min-idle: 50
          max-wait: 1000ms
      timeout: 2000ms
      connect-timeout: 2000ms
      ssl:
        enabled: false
      cluster:
        nodes:
          # DEV
          - ${REDIS_HOST:xme-envdev-redis.otyftu.clustercfg.apse1.cache.amazonaws.com:6379}
          # TEST
#          - ${REDIS_HOST:xme-envtest-redis.otyftu.clustercfg.apse1.cache.amazonaws.com:6379}

# Java结果集对象中自动转换成驼峰命名参数
mybatis:
  configuration:
    map-underscore-to-camel-case: true

xxl:
  job:
    admin:
      #调度中心部署根地址 [选填]：如调度中心集群部署存在多个地址则用逗号分隔。执行器将会使用该地址进行"执行器心跳注册"和"任务结果回调"；为空则关闭自动注册；
      addresses: ${XXL_JOB_URL:https://envtest-int.x.me/xxl-job-admin}

    #执行器通讯TOKEN [选填]：非空时启用；
    accessToken: default_token
    executor:
      #执行器AppName [选填]：执行器心跳注册分组依据；为空则关闭自动注册
      appname: media-user
      #${spring.application.name}
      #执行器注册 [选填]：优先使用该配置作为注册地址，为空时使用内嵌服务 ”IP:PORT“ 作为注册地址。从而更灵活的支持容器类型执行器动态IP和动态映射端口问题。
      address: ""
      #执行器IP [选填]：默认为空表示自动获取IP，多网卡时可手动设置指定IP，该IP不会绑定Host仅作为通讯实用；地址信息用于 "执行器注册" 和 "调度中心请求并触发任务"；
      ip: ""
      #执行器端口号 [选填]：小于等于0则自动获取；默认端口为9999，单机部署多个执行器时，注意要配置不同执行器端口；
      port: 0
      ###${server-port}
      #执行器运行日志文件存储磁盘路径 [选填] ：需要对该路径拥有读写权限；为空则使用默认路径；
      logpath: ./logs/xxl-job/jobhandler
      #执行器日志文件保存天数 [选填] ： 过期日志自动清理, 限制值大于等于3时生效; 否则, 如-1, 关闭自动清理功能；
      logretentiondays: 30
api:
  response:
    enable: true
    apiPrefix: /

user:
  inviteUrl: https://test-outer.x.me/invite?code=%s&user=%s
  inviteGenesisUrl: https://test-outer.x.me/invite?code=%s&user=%s
  avatarUrl: https://s3.x.me/avatars/Avatars00-%d.png
  background: https://s3.x.me/profile/background-%d.png
  shareUrl: https://test-outer.x.me/invite?code=%s&user=%s
  registerTokenNum: 69
  recommendCount: 6

feign:
  httpclient:
    enabled: true
    disable-ssl-validation: true


point:
  url: http://slpool.xyz/point

email:
  url: http://mail.default.svc.cluster.local

ipCountry:
  url: https://envtest-api.x.me/geo/api

device:
  url: https://envtest-api.x.me/device/v1

abtest:
  url: http://***********:8080/api/client-sdk

push:
  jiguang:
    appKey: 8236906e3f622cbed11d1d73
    masterSecret: e5be10a99dc58ba2df006d18
    pushHost: https://push-hk.api.engagelab.cc

google:
  auth:
    uri: https://oauth2.googleapis.com
    client_id: 518988885846-svaifa15lqqohumflfkm2f3blrokcu75.apps.googleusercontent.com
    client_secret: 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
    redirect_uri: https://app.xme.world/ref.html

twitter:
  client_id:  TjlITE1jRHZydzZNM0RSdnlqWjE6MTpjaQ
  client_secret: FBANtP2BFxk90R73mfroudWCc_AxSo7GwbacofkPZEpzP2Ljzc
  redirect_uri: http://xts.nat300.top/user/twitter/callback
  access_token: AAAAAAAAAAAAAAAAAAAAAK8YxQEAAAAApp8KcMdVhzZzyoTEE7KGN0x75Z4%3D5cwB5gb5v4Va3fMDY51HU7sLoRlpIQMXLK43yBWHi7STNOLhmw
  api_key_id: *************************
  api_key_secret: DUyEHwVJNENJATHzZt577XftGgBRLoXtx8pKC7UzAIIpk6E61M

recommend:
  uri: http://***************:8087
  apiKey: aa

content:
  twitterUri: http://***************:7777
  bindChangeUri: http://***************:8087/content-server

bee:
  push:
    uri: https://cospush.test.bee996.com
    appid: bbpay
    privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC9etzFVGK4IqGNfbAhynCw+EqamSH/oPtjsoxPjcrTo54oRF+s+hG4CRgx61jPC4y/IT3dkUIvxV6NpZwXZDyRr+V1aOtvrSv837xej7aaie61Kw4618Jt0qSiCtVQLFCigQWm3Gv3MFGeZFka3uSJfmx4oKwFF+6r7jgRxaNBHLUI06VXmVinDvH6zuoSmsvJCgE6qb5kJJOUUGaagObfQvNCmsD+xqpzyGpKAcXU4n80WBjIHHYslyDqATIVoq6NFHKmExKRk6NYqmE1r8EBhhHFcVfc+zIhgjIOimRtUtIwuRL7sUltWSktEC/rT7KyBu3g6k3i6+Nmo87gtol/AgMBAAECggEABsncFwXK86Xp+9PA97T1DKIYqilKWoo993hyJneCCMsX44FvnBbpg5pvz3oLGH/lbwOV39ZQdL2xgYmpaf0hXmba5bX4mWuu48RwT0L+29KNRthgDiOkx6KAW33f3pt32BqfRTcodxa4FeCqgJfzwClTYY8AGr6cwnQqdhiKnM6GotH7U74PuQGW5jGaYmBSjn+AaGw64/elA2AfGgUBNQUfcCS/KFLIqs72UkKXAZbcczep1l5+PJbyRu+hN73BN+o8XeT7wDIO6/RrQmJ/8GSrDGp3o3rHEqxE2s/t04ycblxcLqz46CJmqqnEeOAhcawUEyHujmcWHc59JuuQoQKBgQDyqiGuX2VwqAIkVfT/qtTS2rx3YetQmKisKJEYA07HLYD3Anm3/RO1ht1OE3e5iaIPRiy6kH2ertsTPJk2n17dfiWgYVaS+NjpiUXFejBRJMhDs32Yh0k+YEJBJJJZsIzbS0l5ShiBr7xoWsaFhVqGoupGiF86duhD44mQwJv/LQKBgQDH5IPwPWTPPUWCODQg7A6zt5BiKxmm544dK4O5pESGZZ6cKr09qPSzxUWy+dHee3T8Ig4IhFdAsfCCyYu6HPki3bYYeA0AH0q9hARP+or1bzXMNRy8gXq5KD6vKR7JYHyy+Wk3rDzOKWiMzsBiJqe6CUbENy3/wp9Chac4vQb22wKBgQC73j9pMm3khA30d5/P+EAAZlwWyPZXVXjwoA+E2bq5tV7s3TvC1+nUVe8rrSK5v/Z5gMjMP2Uh3xm4kOfFRCk7rZgPzIwsCQBV/XLI6kpR5/orf+Cyn7py0i85I8kyKt9CP81IW7cRYC6rU+vyH0Xwilx58sZXCwvS02wDZzremQKBgQCOIu6ygGVTe4UWgHKIynH11txDAdS+ur5x/YQwGB9l1ZKAB2ADHUXLIsZZ6owSAIWB15FU+w20AhM8XKYPlBSPd0nJgmv0H0wdEGekm0n+FbMSXt3tKMCkMnrIQJwUo5hoBzOLE90013rxTP69jDEN9xSr53S8/Z4TRkhO7xKpqwKBgFXGeTPZCwu7KhVPag6aivHesDCUgTGubaxOgYeYxFtulGu9ENwJNtCmWB55tEey193VvPRoYiHMVsjvyl0nMPqWAbffLuaWRMgPK0V3035adeHdZFtgMG01qh9m9vYkmfrqUPNYVzWUoaUNc4wICj3FgGWHiUKeRFuLFDlOl7pP
  login:
    privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC2jkN4UR6HmAe6OwfhEOUxEb1+Pqn4uf4Me3xdjBAgv5uOfaL0sUkWXUjI6ig6xWW1DTl6arZcEZCcQn5sykniqb1ZWN9ZGs10XNEHuS6USQhwpwekotxF90Us7od3R0vHaZ7wqpxhcIpqXBiTC2IYmAxVRe1WeG1FgaaVfbW318fRUouwM8uOD3T8CIl0SJKiDgQIGwoDJmQm6mjjbepEjEVQAzaP1wfFIkMQVq9FzNwJVjT1uMXhJ8en8y4kr06QPGPNijc4tYE49p8+bKXJEyVz+zcpniyPEHaXCGv/QxcYm0aSbVBxpAsLtACHxfHRzGmqXwoKfGSBL4sMZ5ElAgMBAAECggEAfwaqx/OaDLuq1NjyeJcrA2PWuRudZeb1cXYTaXxmVIM/GH0vgJhDxP4QAU19xLdKOBsFnbrXPWUzfKeAFO8FTmTRznPapcPSpl9XS8p9MM1IxCpVVSfFoiGWztcDB+piPlWuNMRioCKRDcUbjGXlUjuqiUNpDDwrvKukYGzinxKlKq9cIbca4sXbwXYoC13F+WPzBo6ZMiyLsiEmmr6VVE7ZGc4qiADmsLvoEjPRceQVW4FbuADYb48GwXlz7tRwk6/D3vFHNZQY2y1J1qkBLuCWuDqAHyJz8F/6PdkScO2m/H33uTCwzDWUXlbPeq4hgiExZhHodpV9HGNtYzVyAQKBgQD17Qz1jlLo/RENWXzjDauvIsf65Lmm1JqFv3aI3u/uSlNL2W/sC4ru1L3MhTOMKHWVXG4VAqy2R3iJ+p8xeN2OSzdeKpxKwPAestV8dIlAlIHoIPqm5QFYuvj7VIILjkM+cfa1jFnU2Zk41vcGYTbDNv1Suigx1ItfSTSvJ6Vb5QKBgQC+CKsuIvApkf0k9UsSXi6U12kV+s5aOqaxMrZCdidI1h/5VgT9qb6R5VGVwWxXy8lUfNZju98jDjI1d4Y71f/PVhhHoM/pWl6x6xCGTIcSZDIti34V/ZKPNkzAvLIvY1t8e6LHtO6cirOofvrDdsV0gZOeqVPDibjRHLdh6eKMQQKBgQDKzHzPUlqUv3v8YjtWkcNuXWRERbHDHbd/a/A31oBNJQWjmt9s6+faI52QtpdXkVXFob81nEsI1c9VGVOF3IH4AZbbFJBmJMzew+LBCujUVKL75ZZNYrPFlAoWWA4VsW9KY3hrJh4+JGLyOHLk0+3w9bvZKKJU1U1Jenjzp+H48QKBgCOPq7jugNLGpwCLhojhZwYjaV7bmFH6N8dhHgpB4ggXSdR27Ggro6XZq/LCR2wGjp5o3hW7yn3c4jhvLYd0/HHoQpLk3QNmDeJtgqIfIAepCFrNh8LFuO9j0ekfZpjHlw8sc8G7cguJSSV23aZGS50Jwn4mYa5tz1ASd56zDdoBAoGBAIvyHIVJlWSfr8/fnctZQnwipqm5wF0HzGLA1ge+G7XmJWb8EZKq62coseKa77KWU24t/GjU2sAvHlAwcI8HzUTZ/OqOJnUIeXCz4Pc3uBczZpMGohmGkrBN/ByF7FEwtQHSRXASzehzHbjU6I9UFDCW0a9PmJVs5l6tEjoZTVh2

early:
  bird:
    endDate: 20  #显示的值
    switch:
      startDate: 2025-01-03 00:00:00
      endDate: 2025-01-23 23:59:59

management:
  endpoints:
    web:
      exposure:
        include: "prometheus,info,liveness,readiness,health"
  endpoint:
    health:
      probes:
        enabled: true
  metrics:
    tags:
      application: media-user
    distribution:
      slo[http.server.requests]: 5ms,10ms,25ms,50ms,75ms,100ms,125ms,150ms,175ms,200ms,225ms,250ms,300ms,350ms,400ms,450ms,500ms,750ms,1s,1500ms,2s,2500ms,3s,5s
  server:
    port: 9090
    base-path: /

system:
  config:
    beeLoginShow: 0
    checkApp:
      versionAndroid: 1.0.0
      versionAndroidGP: 1.0.50
      versionIos: 1.0.0
    earlyBird: 0
    taskShow: 1
    giftIconShow: 1
    # 邀请奖励(普通)
    inviteXmeAmount: 20
    # 被邀请奖励(普通)
    invitedXmeAmount: 6
    # 活体认证奖励(普通)
    faceXmeAmount: 6
    # 邀请奖励(创世大使)
    inviteGenesisXmeAmount: 100
    # 被邀请奖励(创世大使)
    invitedGenesisXmeAmount: 20
    # 活体认证奖励(创世大使)
    faceGenesisXmeAmount: 20
    # 注册奖励(普通)
    registerXmeAmount: 20
    miningBeginDate: 25
    genesisBadgeAvatar: https://s3.x.me/profile/genesisBadge.png
    # 创世大使活动的开始时间和结束时间
    genesisActivity:
      startDate: 2025-03-15 00:00:00
      endDate: 2025-04-15 23:59:59
    # KOL的hot图标是否展示：0:隐藏 1: 显示
    kolHotIconShow: 1
    # 是否展示抽奖转盘：0:隐藏 1: 显示
    luckyWheelShow: 1

es:
  prefix: dev_user_
  cluster: false
  scheme: http
  host: ***********
  port: 9200
  username: none
  password: none

importuser:
  award:
    start: 2025-02-20 00:00:00
    end: 2025-03-15 23:59:59

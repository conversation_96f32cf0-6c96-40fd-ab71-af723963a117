package com.media.user;

import com.alibaba.fastjson.JSON;
import com.media.user.dto.request.SendEmailRequest;
import com.media.user.enums.BusinessTypeEnum;
import com.media.user.enums.LanguageEnums;
import com.media.user.feign.client.EmailClient;
import com.media.core.request.ClientInfoContext;
import com.media.user.service.SmsService;
import freemarker.template.Configuration;
import freemarker.template.Template;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;
import org.springframework.web.servlet.view.freemarker.FreeMarkerConfigurer;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@SpringBootTest
class MediaUserApplicationTests {

	@Autowired
	Configuration configuration;

	@Autowired
	SmsService smsService;

	@Autowired
	EmailClient emailClient;

	@Test
	void contextLoads() throws Exception {
//		System.out.println(JSON.toJSONString(configuration.getTemplate("zh_CN/verification_code").toString()));

		String email = "<EMAIL>";
//		email = "<EMAIL>";
//		email = "<EMAIL>"; //pingping

//		SendEmailRequest baseRequest = new SendEmailRequest();
//		baseRequest.setTo(email);
//		baseRequest.setSubject("test");
//		baseRequest.setContent("test content");
//		emailClient.sendEmail(baseRequest);

//		smsService.sendEmailCode(email, BusinessTypeEnum.SIGNUP, LanguageEnums.zh_CN);


		System.out.println("==================================");
//		Thread.sleep(3 * 1000);

//		SendEmailRequest request = new SendEmailRequest();
//		request.setTo(email);
//		request.setSubject("你好，[xinfo768]，欢迎来到X.me ！");
//		Map<String, Object> data = new HashMap<>();
//		Template template = configuration.getTemplate("zh_CN/register_email_content.ftlh");
//		String contentHtml = FreeMarkerTemplateUtils.processTemplateIntoString(template, data);
//		request.setContent(contentHtml);
//		emailClient.sendEmailHtml(request);

		smsService.sendRegisterMessage(email, "xinfo:" + System.currentTimeMillis(),  LanguageEnums.zh_CN);



	}

}

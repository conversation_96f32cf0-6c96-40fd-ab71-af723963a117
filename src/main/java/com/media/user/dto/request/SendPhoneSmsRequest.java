package com.media.user.dto.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
public class SendPhoneSmsRequest {

    /**
     * 手机号前缀（如 +86）
     */
    private String phonePrefix;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 短信内容（直接发送内容时使用）
     */
    private String content;

    /**
     * 是否使用英文模板
     */
    private Boolean useEnglishTemplate;

    /**
     * 模板变量，如 {"code": "1234", "time": "10分钟"}
     */
    private Map<String, String> variables;

    private Integer businessType;
}

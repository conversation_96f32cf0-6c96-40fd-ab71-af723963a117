package com.media.user.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.function.Function.identity;

@Getter
public enum RewardRuleEnum {

    INVITE("invite", "邀请奖励"),

    INVITED("invited", "被邀请奖励"),

    VERIFICATION("verification", "单独三合一认证奖励");

    private final String code;
    private final String desc;

    private static final Map<String, RewardRuleEnum> MAP = Arrays.stream(values()).collect(Collectors.toMap(RewardRuleEnum::getCode, identity()));

    RewardRuleEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RewardRuleEnum form(String code) {
        return MAP.get(code);
    }
}

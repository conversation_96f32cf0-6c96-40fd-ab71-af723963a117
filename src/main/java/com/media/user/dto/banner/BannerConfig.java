package com.media.user.dto.banner;

import com.media.user.dto.response.BannerResponse;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class BannerConfig {
    private List<BannerConfigItem> banners;

    @Data
    public static class BannerConfigItem {
        private String id;
        private String titleKey;       // i18n 资源文件中的标题键
        private String descriptionKey;       // i18n 资源文件中的描述键
        private String icon;
        private String iconCn;  // 保留向后兼容
        private String jumpType;
        private String jumpUrl;
        private boolean clickable;
        private String backgroundImage; // 默认背景图片地址
        private String backgroundImageCn; // 保留向后兼容

        // 多语言图片支持 - 新增字段
        private Map<String, String> iconImages;       // 多语言图标: {"en-US": "url1", "zh-CN": "url2"}
        private Map<String, String> backgroundImages; // 多语言背景图: {"en-US": "url1", "zh-CN": "url2"}
        private String startTime;  // 开始时间，格式：yyyy-MM-dd HH:mm:ss
        private String endTime;
        private Integer status;
        private Integer order;
        private boolean auditShowable; //审核状态是否展示
        private boolean needLogin = false;  // 是否需要登录，默认为false
        private boolean needFace = false;  // 是否需要人脸验证，默认为false
        private boolean needVerify = false;  // 是否需要三合一认证，默认为false
        private String minVersion;  // 最低版本要求
        private String textColor;  // 文本颜色
    }
}


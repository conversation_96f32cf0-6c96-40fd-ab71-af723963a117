package com.media.user.enums;

import lombok.Getter;

/**
 *
 */
@Getter
public enum BusinessTypeEnum {
    /**
     * 忘记密码发送验证码
     */
    FORGET_PASSWORD_CODE(1),

    /**
     * 注册验证码
     */
    SIGNUP(2),
    /**
     * 更新密码
     */
    UPDATE_PASSWORD_CODE(3),

    /**
     * 更新邮箱给老邮箱发送验证码
     */
    UPDATE_EMAIL_OLD_CODE(4),

    /**
     * 更新邮箱给新邮箱邮箱发送验证码
     */
    UPDATE_EMAIL_NEW_CODE(5),

    /**
     * 注册成功发送邮件
     */
    REGISTER_EMAIL(6),

    /**
     * 绑定邮箱
     */
    EMAIL_BIND_CODE(7),

    /**
     * 验证邮箱
     */
    EMAIL_VERIFY_CODE(8),
    /**
     * 登录验证码
     */
    EMAIL_LOGIN_CODE(9),

    ;

    private Integer value;


    BusinessTypeEnum(int value) {
        this.value = value;
    }


    public static BusinessTypeEnum getEnumFromCode(Integer code) {
        for (BusinessTypeEnum userAuthSendCodeTypeEnum : BusinessTypeEnum.values()) {
            if (userAuthSendCodeTypeEnum.getValue().equals(code)) {
                return userAuthSendCodeTypeEnum;
            }
        }
        return null;
    }
}

package com.media.user.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 注册风险请求参数
 */
@Data
@Accessors(chain = true)
public class RegisterRiskRequest {

    /**
     * 用户ID
     */
    @JsonProperty("user_id")
    private Long userId;

    /**
     * IP地址
     */
    @JsonProperty("ip")
    private String ip;

    /**
     * 设备盒子ID
     */
    @JsonProperty("box_id")
    private String boxId;

    /**
     * 用户代理
     */
    @JsonProperty("user_agent")
    private String userAgent;

    /**
     * 应用版本
     */
    @JsonProperty("app_version")
    private String appVersion;

    /**
     * 操作系统
     */
    @JsonProperty("os")
    private String os;

    /**
     * 注册类型
     */
    @JsonProperty("register_type")
    private String registerType;
}

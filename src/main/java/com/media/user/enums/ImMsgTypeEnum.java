package com.media.user.enums;

import lombok.Getter;
import org.apache.commons.collections4.MapUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

import static java.util.function.Function.identity;

@Getter
public enum ImMsgTypeEnum {
    APP_FOLLOW("app:follow");
    private final String type;

    ImMsgTypeEnum(String type) {
        this.type = type;
    }

    private static final Map<String, ImMsgTypeEnum> MAP = Arrays.stream(values()).collect(Collectors.toMap(ImMsgTypeEnum::getType, identity()));

    public static ImMsgTypeEnum form(String type) {
        return MapUtils.getObject(MAP, type);
    }
}

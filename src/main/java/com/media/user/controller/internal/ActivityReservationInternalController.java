package com.media.user.controller.internal;

import com.media.user.service.ActivityReservationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 活动预约内部接口控制器
 */
@Slf4j
@RestController
@RequestMapping("/user/internal/activity/reservation")
public class ActivityReservationInternalController {

    @Autowired
    private ActivityReservationService activityReservationService;

    /**
     * 处理没有UID的预约记录，根据邮箱或手机号查询用户并发放道具
     *
     * @param activityType 活动类型，可选参数
     * @param limit 每次处理的最大记录数，默认100
     * @return 处理结果
     */
    @PostMapping("/process-without-uid")
    public Integer processReservationsWithoutUid(
            @RequestParam(value = "activityType", required = false) String activityType,
            @RequestParam(value = "limit", required = false, defaultValue = "100") int limit) {

        log.info("Processing reservations without UID for activity type: {}, limit: {}",
                activityType != null ? activityType : "ALL", limit);

        try {
            int processedCount = activityReservationService.processReservationsWithoutUid(activityType, limit);
            log.info("Successfully processed {} reservations", processedCount);

            return processedCount;
        } catch (Exception e) {
            log.error("Error processing reservations without UID", e);

            throw e;
        }
    }

    /**
     * 为指定预约ID发放道具
     *
     * @param reservationId 预约ID
     * @return 处理结果
     */
    @PostMapping("/distribute-props/{reservationId}")
    public Boolean distributePropsForReservation(
            @PathVariable("reservationId") Long reservationId) {

        log.info("Distributing props for reservation ID: {}", reservationId);

        try {
            boolean success = activityReservationService.distributePropsForReservation(reservationId);

            return success;
        } catch (Exception e) {
            log.error("Error distributing props for reservation ID: {}", reservationId, e);
            throw e;
        }
    }
}

package com.media.user.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * 参数对应语言 zn,cn
 *
 * <AUTHOR>
 * @create 2022/5/26
 * @desc
 **/
public enum LanguageEnums {
    zh_CN("zh-CN"), // 中国-汉语
    zh_HK("zh-HK"), // 中国香港--
    zh_MO("zh-MO"), // 中国澳门--
    zh_TW("zh-TW"), // 中国台湾--
    zh_SG("zh-SG"), // 新加坡-汉语
    zh_CHS("zh-CHS"), // 中国简-体中文
    zh_CHT("zh-CHT"), // 中国-繁体中文
    en_GB("en-GB"), // 英国-英语
    en_US("en-US"), // 美国-英语
    tt_RU("tt-RU"), // 俄罗斯-俄罗斯Tatar语
    ru_RU("ru-RU"), // 俄罗斯-俄罗斯语
    de_DE("de-DE"), // 德国-德语
    de_CH("de-CH"), // 瑞士-德语
    de_LU("de-LU"), // 卢森堡-德语
    de_AT("de-AT"), // 奥地利-德语
    de_LI("de-LI"), // 列支敦士登-德语
    fr_CA("fr-CA"), // 加拿大-法语
    fr_FR("fr-FR"), // 法国-法语
    fr_LU("fr-LU"), // 卢森堡-法语
    fr_BE("fr-BE"), // 比利时-法语
    fr_MC("fr-MC"), // 摩纳哥-法语
    fr_CH("fr-CH"), // 瑞士-法语
    it_IT("it-IT"), // 意大利-意大利
    ja_JP("ja-JP"), // 日本-日语
    ko_KR("ko-KR"), // 韩国-韩国
    kk_KZ("kk-KZ"), // 哈萨克-哈萨克(Kazakh)
    fi_FI("fi-FI"), // 芬兰-芬兰语
    sv_FI("sv-FI"), // 瑞典-芬兰语
    sv_SE("sv-SE"), // 瑞典-瑞典语
    hu_HU("hu-HU"), // 匈牙利-匈牙利
    is_IS("is-IS"), // 冰岛-冰岛
    id_ID("id-ID"), // 印尼-印尼
    it_CH("it-CH"), // 瑞士-意大利
    tr_TR("tr-TR"), // 土耳其-土耳其语
    uk_UA("uk-UA"), // 乌克兰-乌克兰语
    ur_PK("ur-PK"), // 巴基斯坦-巴基斯坦Urdu语
    en_CA("en-CA"), // 加拿大-英语
    en_IE("en-IE"), // 爱尔兰-英语
    cs_CZ("cs-CZ"), // 捷克-捷克
    en_CB("en-CB"), // 加勒比海-英语
    en_AU("en-AU"), // 澳洲-英语
    en_NZ("en-NZ"), // 新西兰-英语
    en_PH("en-PH"), // 菲律宾-英语
    en_JM("en-JM"), // 牙买加-英语
    en_BZ("en-BZ"), // 伯利兹-英语
    en_TT("en-TT"), // 千里达托贝哥共和国-英语
    en_ZW("en-ZW"), // 津巴布韦-英语
    et_EE("et-EE"), // 爱沙尼亚-爱沙尼亚
    fo_FO("fo-FO"), // 法罗群岛-法罗语(Faroese)
    es_ES("es-ES"), // 西班牙-西班牙语
    es_AR("es-AR"), // 阿根廷-西班牙语
    es_MX("es-MX"), // 墨西哥-西班牙语
    es_BO("es-BO"), // 玻利维亚-西班牙语
    es_CL("es-CL"), // 智利-西班牙语
    es_PA("es-PA"), // 巴拿马-西班牙语
    es_VE("es-VE"), // 委内瑞拉-西班牙语
    es_CO("es-CO"), // 哥伦比亚-西班牙语
    es_PE("es-PE"), // 秘鲁-西班牙语
    es_CR("es-CR"), // 哥斯达黎加-西班牙语
    es_DO("es-DO"), // 多米尼加共和国-西班牙语
    es_EC("es-EC"), // 厄瓜多尔-西班牙语
    es_SV("es-SV"), // 萨尔瓦多-西班牙语
    es_GT("es-GT"), // 危地马拉-西班牙语
    es_HN("es-HN"), // 洪都拉斯-西班牙语
    es_NI("es-NI"), // 尼加拉瓜-西班牙语
    es_PY("es-PY"), // 巴拉圭-西班牙语
    es_PR("es-PR"), // 波多黎各-西班牙语
    es_UY("es-UY"), // 乌拉圭-西班牙语
    ta_IN("ta-IN"), // 印度-坦米尔语
    gu_IN("gu-IN"), // 印度-印度语(Gujarati)
    hi_IN("hi-IN"), // 印度-北印度语
    kn_IN("kn-IN"), // 印度-卡纳达语
    kok_IN("kok-IN"), // 印度-Konkani
    mr_IN("mr-IN"), // 印度-马拉地语
    pa_IN("pa-IN"), // 印度-Punjab语
    sa_IN("sa-IN"), // 印度-梵文
    te_IN("te-IN"), // 印度-印度Telugu语
    nb_NO("nb-NO"), // 挪威-挪威
    nn_NO("nn-NO"), // 挪威-挪威(Nynorsk)
    pl_PL("pl-PL"), // 波兰-波兰
    pt_BR("pt-BR"), // 巴西-葡萄牙语
    pt_PT("pt-PT"), // 葡萄牙-葡萄牙语
    vi_VN("vi-VN"), // 越南-越南语
    th_TH("th-TH"), // 泰国-泰语
    bn_BD("bn-BD"), // 孟加拉国-孟加拉语
    en_ZA("en-ZA"), // 南非-英语
    af_ZA("af-ZA"), // 南非-公用荷兰语
    be_BY("be-BY"), // 白俄罗斯-白俄罗斯语(Belarusian)
    fa_IR("fa-IR"), // 伊朗王国-波斯语
    he_IL("he-IL"), // 以色列-希伯来文
    da_DK("da-DK"), // 丹麦-丹麦
    div_MV("div-MV"), // 马尔代夫-马尔代夫(Dhivehi)
    nl_BE("nl-BE"), // 比利时-荷兰语
    nl_NL("nl-NL"), // 荷兰-荷兰语
    el_GR("el-GR"), // 希腊-希腊
    ar_SA("ar-SA"), // 沙特阿拉伯-阿拉伯语
    ar_EG("ar-EG"), // 埃及-阿拉伯语
    ar_IQ("ar-IQ"), // 伊拉克-阿拉伯语
    ar_JO("ar-JO"), // 约旦-阿拉伯语
    ar_KW("ar-KW"), // 科威特-阿拉伯语
    ar_LB("ar-LB"), // 黎巴嫩-阿拉伯语
    ar_LY("ar-LY"), // 利比亚-阿拉伯语
    ar_QA("ar-QA"), // 卡塔尔-阿拉伯语
    ar_SY("ar-SY"), // 叙利亚共和国-阿拉伯语
    syr_SY("syr-SY"), // 叙利亚共和国-叙利亚语(Syriac)
    bg_BG("bg-BG"), // 保加利亚-保加利亚
    sq_AL("sq-AL"), // 阿尔巴尼亚-阿尔巴尼亚
    ar_DZ("ar-DZ"), // 阿尔及利亚-阿拉伯语
    ar_BH("ar-BH"), // 巴林-阿拉伯语
    ar_MA("ar-MA"), // 摩洛哥-阿拉伯语
    ar_OM("ar-OM"), // 阿曼-阿拉伯语
    ar_TN("ar-TN"), // 北非的共和国-阿拉伯语
    ar_AE("ar-AE"), // 阿拉伯联合酋长国-阿拉伯语
    ar_YE("ar-YE"), // 也门-阿拉伯语
    hy_AM("hy-AM"), // 亚美尼亚-亚美尼亚语
    eu_ES("eu-ES"), // 巴斯克-巴斯克
    ca_ES("ca-ES"), // 嘉泰罗尼亚-嘉泰罗尼亚
    sw_KE("sw-KE"), // 肯尼亚-肯尼亚(Swahili)语
    hr_HR("hr-HR"), // 克罗埃西亚-克罗埃西亚
    gl_ES("gl-ES"), // 加利西亚-加利西亚
    ka_GE("ka-GE"), // 格鲁吉亚州-格鲁吉亚州
    ky_KZ("ky-KZ"), // 哈萨克-Kyrgyz
    lv_LV("lv-LV"), // 拉脱维亚-拉脱维亚
    lt_LT("lt-LT"), // 立陶宛-立陶宛
    mk_MK("mk-MK"), // 马其顿-马其顿
    ms_BN("ms-BN"), // 汶莱-马来语
    ms_MY("ms-MY"), // 马来西亚-马来西亚语
    mn_MN("mn-MN"), // 蒙古-蒙古
    ro_RO("ro-RO"), // 罗马尼亚-罗马尼亚语
    sr_SP_Cyrl("sr-SP-Cyrl"), // 塞尔维亚(西里尔字母的)sr-SP-Cyrl-#{2}
    sr_SP_Latn("sr-SP-Latn"), // 塞尔(拉丁文)-sr-SP-Latn
    sk_SK("sk-SK"), // 斯洛伐克-斯洛伐克
    sl_SI("sl-SI"), // 斯洛文尼亚-斯洛文尼亚
    ;

    private final String value;

    LanguageEnums(String value) {
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    public String getCountryCode() {
        return this.value.split("-")[0];
    }

    public static LanguageEnums get(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        String[] languages = value.split(",");
        for (String language : languages) {
            String[] splits = language.split(";");
            String langCode = splits[0].trim();

            // 1. 精确匹配完整的locale格式 (如 "de-DE")
            for (LanguageEnums item : LanguageEnums.values()) {
                if (Objects.equals(item.getValue(), langCode)) {
                    return item;
                }
            }

            // 2. 语言前缀匹配 (如 "de" -> "de-DE")
            if (langCode.length() == 2) {
                String prefix = langCode.toLowerCase();
                switch (prefix) {
                    case "de": return de_DE;      // 德语 -> 德国德语
                    case "fr": return fr_FR;      // 法语 -> 法国法语
                    case "es": return es_ES;      // 西班牙语 -> 西班牙西班牙语
                    case "it": return it_IT;      // 意大利语 -> 意大利意大利语
                    case "zh": return zh_CN;      // 中文 -> 简体中文
                    case "en": return en_US;      // 英语 -> 美国英语
                    case "ja": return ja_JP;      // 日语 -> 日本日语
                    case "ko": return ko_KR;      // 韩语 -> 韩国韩语
                    case "ru": return ru_RU;      // 俄语 -> 俄罗斯俄语
                    case "tr": return tr_TR;      // 土耳其语 -> 土耳其土耳其语
                    case "id": return id_ID;      // 印尼语 -> 印尼印尼语
                    case "ur": return ur_PK;      // 乌尔都语 -> 巴基斯坦乌尔都语
                    case "th": return th_TH;      // 泰语 -> 泰国泰语
                    case "vi": return vi_VN;      // 越南语 -> 越南越南语
                    case "pt": return pt_PT;      // 葡萄牙语 -> 葡萄牙葡萄牙语
                    case "hi": return hi_IN;      // 印地语 -> 印度印地语
                    case "fa": return fa_IR;      // 波斯语 -> 伊朗波斯语
                    case "bn": return bn_BD;      // 孟加拉语 -> 孟加拉国孟加拉语
                    case "ar": return ar_SA;      // 阿拉伯语 -> 沙特阿拉伯阿拉伯语
                    default:
                        return en_US;
                }
            }
        }
        return null;
    }

    public boolean isZhTW(){
        return this ==  zh_TW;
    }
}

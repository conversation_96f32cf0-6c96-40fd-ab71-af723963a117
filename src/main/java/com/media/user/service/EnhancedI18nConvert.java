package com.media.user.service;

import com.media.core.i18n.I18nConvert;
import com.media.user.enums.LanguageEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 增强的i18n转换器，优先使用远程i18n服务，降级到本地资源文件
 */
@Slf4j
@Component
public class EnhancedI18nConvert {
    
    @Autowired
    private I18nCacheService i18nCacheService;
    
    /**
     * 获取i18n消息，优先使用远程服务缓存，降级到本地资源文件
     * 
     * @param key 翻译键
     * @param language 语言
     * @return 翻译内容
     */
    public String getI18nMessage(String key, LanguageEnums language) {
        if (StringUtils.isBlank(key)) {
            return "";
        }
        
        if (language == null) {
            language = LanguageEnums.en_US;
        }
        
        try {
            // 优先尝试从远程i18n服务缓存获取
            String translation = i18nCacheService.getTranslation(key, language);
            
            // 如果远程服务返回的是原键值（未找到翻译），则降级到本地资源文件
            if (!key.equals(translation)) {
                return translation;
            }
            
            log.debug("Translation not found in remote service for key: {}, falling back to local resources", key);
            
        } catch (Exception e) {
            log.warn("Failed to get translation from remote service for key: {}, falling back to local resources", key, e);
        }
        
        // 降级到本地资源文件
        return I18nConvert.getI18nMessage(key, language);
    }
    
    /**
     * 获取i18n消息，带默认值
     * 
     * @param key 翻译键
     * @param language 语言
     * @param defaultVal 默认值
     * @return 翻译内容
     */
    public String getI18nMessageWithDefaultVal(String key, LanguageEnums language, String defaultVal) {
        if (StringUtils.isBlank(key)) {
            return "";
        }
        
        if (language == null) {
            language = LanguageEnums.en_US;
        }
        
        try {
            // 优先尝试从远程i18n服务缓存获取
            String translation = i18nCacheService.getTranslation(key, language);
            
            // 如果远程服务返回的是原键值（未找到翻译），则降级到本地资源文件
            if (!key.equals(translation)) {
                return translation;
            }
            
            log.debug("Translation not found in remote service for key: {}, falling back to local resources", key);
            
        } catch (Exception e) {
            log.warn("Failed to get translation from remote service for key: {}, falling back to local resources", key, e);
        }
        
        // 降级到本地资源文件
        return I18nConvert.getI18nMessageWithDefaultVal(key, language, defaultVal);
    }
}

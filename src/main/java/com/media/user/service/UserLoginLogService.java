package com.media.user.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.media.core.constant.KafkaTopicConstant;
import com.media.core.exception.ApiException;
import com.media.core.request.ClientInfoContext;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.UserLoginLogModel;
import com.media.user.domain.UserLoginLogVersionModel;
import com.media.user.dto.request.XmeRedemptionRequest;
import com.media.user.feign.client.UserPointClient;
import com.media.user.mapper.UserLoginLogMapper;
import com.media.core.utils.IpUtil;
import com.media.user.mapper.UserLoginLogVersionMapper;
import com.media.user.mq.KafkaMessageProducer;
import com.xme.media.message.model.enums.LanguageEnums;
import com.xme.media.message.model.models.BaseMessage;
import com.xme.media.message.model.models.data.UpdateClientReward811;
import com.xme.xme_base_depends.models.ApiResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.xme.media.message.model.BuildMessage.buildBaseMessage;

@Slf4j
@Service
public class UserLoginLogService {

    @Autowired
    UserLoginLogMapper userLoginLogMapper;

    @Autowired
    IpUtil ipUtil;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @ApolloJsonValue("${target.app.version:1.8.0}")
    private String TARGET_APP_VERSION;

    @ApolloJsonValue("${min.registration.time:2025-08-06}")
    private String MIN_REGISTRATION_TIME;

    @Autowired
    private UserPointClient userPointClient;

    //更新奖励eventId
    private static String XME = "XME";
    private static Integer EVENTID = 45;

    @Autowired
    KafkaMessageProducer kafkaMessageProducer;

    @Autowired
    UserLoginLogVersionMapper userLoginLogVersionMapper;

    /**
     * 记录每日的登录记录
     *
     * @param uid
     */
    public void loginLog(Long uid, String loginType, Byte sourceType) {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = (HttpServletRequest) requestAttributes.resolveReference(RequestAttributes.REFERENCE_REQUEST);
        this.saveLoginLog(request, uid, loginType, sourceType);
    }

    @Async("threadPoolTaskExecutor")
    public void saveLoginLog(HttpServletRequest request, Long uid, String loginType, Byte sourceType) {
        //当日的时间戳
        String loginDate = DateUtil.today();
        String loginCacheKey = MediaUserConstant.USER_LOGIN_LOG_PREFIX + uid + "_" + loginDate;
        try {
            String loginCacheValue = stringRedisTemplate.opsForValue().get(loginCacheKey);
            if (StringUtils.isNotBlank(loginCacheValue) && "1".equals(loginCacheValue)) {
                return;
            }
            Long count = userLoginLogMapper.findByLoginDate(uid, loginDate);
            if (count == null || count == 0L) {
                String loginIp = ipUtil.getIpAddrByRequest(request);
                log.info("save today login log, uid = {}, ip = {}", uid, loginIp);
                UserLoginLogModel loginLog = new UserLoginLogModel();
                loginLog.setLoginTime(loginDate);
                loginLog.setLoginType(loginType);
                loginLog.setSourceType(sourceType);
                loginLog.setLoginIp(loginIp);
                loginLog.setUid(uid);
                loginLog.setCreatedTime(new Date());
                loginLog.setUpdatedTime(new Date());
                userLoginLogMapper.insert(loginLog);
            }
            //加入缓存，有效期一天
            stringRedisTemplate.opsForValue().set(loginCacheKey, "1", 1L, TimeUnit.DAYS);
        } catch (DuplicateKeyException e1) {
            //主键冲突异常，发成主键冲突任务已经记录登陆日志了
            stringRedisTemplate.opsForValue().set(loginCacheKey, "1", 1L, TimeUnit.DAYS);
        } catch (Exception e) {
            if (!e.getMessage().contains("Duplicate entry")) {
                log.error("save loginLog error:{}", e.getMessage());
            }
        }
    }

    /**
     * 获取当天登录用户数量（内部接口）
     */
    public Long getYesterdayLoginUserCount() {
        try {
            String yesterday = DateUtil.formatDate(DateUtil.yesterday());
            return userLoginLogMapper.countUniqueUsersByDate(yesterday);
        } catch (Exception e) {
            log.error("获取当天登录用户数量失败", e);
            return 0L;
        }
    }

    public void checkNewLoginLogs(Date entTime) {
        log.info("开始执行强更奖励登录日志版本检查任务...");

        // 1. 获取上次处理的最大ID
        Long lastProcessedId = getLastProcessedId();
        log.info("上次处理的最大ID：{}", lastProcessedId);
        String targetAppVersion = TARGET_APP_VERSION;
        String minRegistrationTime = MIN_REGISTRATION_TIME;
        log.info("version:{},time:{}", targetAppVersion, minRegistrationTime);
        // 2. 查询新增的登录日志
        List<UserLoginLogVersionModel> newLogs = userLoginLogVersionMapper.findNewLogsAfterId(lastProcessedId, targetAppVersion, minRegistrationTime);
        if (newLogs.isEmpty()) {
            log.info("没有新增的登录日志需要处理");
            return;
        }

        // 3. 处理每条日志
        for (UserLoginLogVersionModel logEntry : newLogs) {
            checkAndRecordVersion(logEntry, entTime);
        }

        // 4. 更新最后处理的ID
        updateLastProcessedId(newLogs.get(newLogs.size() - 1).getId());

        log.info("登录日志版本检查完成，共处理{}条记录", newLogs.size());
    }

    /**
     * 检查版本并记录结果
     */
    private boolean checkAndRecordVersion(UserLoginLogVersionModel logEntry, Date entTime) {

        // 如果是新记录，处理并返回结果
        // 记录匹配结果
        log.info("版本匹配记录 - 用户ID: {}, 版本: {}",
                logEntry.getUid(), logEntry.getVersion());
        String xmeIdempotentId = DigestUtils.md5Hex("activity:reservation:xme:" + logEntry.getUid() + logEntry.getVersion());
        XmeRedemptionRequest xmeRequest = XmeRedemptionRequest.builder()
                .uid(logEntry.getUid())
                .amount(3)
                .ccy(XME)
                .network(XME)
                .eventId(EVENTID)
                .idempotent(xmeIdempotentId)
                .build();
        ApiResponse xmeResponse = userPointClient.xmeRedemptionFromOP(xmeRequest);
        if (xmeResponse.getCode() != 200) {
            log.error("send doge account error. code: {}, msg: {}", xmeResponse.getCode(), xmeResponse.getMessage());
        }
        BaseMessage<UpdateClientReward811> message = buildBaseMessage(LanguageEnums.en_US, logEntry.getUid(), new UpdateClientReward811());
        kafkaMessageProducer.sendMessage(KafkaTopicConstant.PUSH_MESSAGE_NORMAL, message.toJsonString());
        return true;
    }

    /**
     * 获取上次处理的最大ID
     */
    private Long getLastProcessedId() {
        Object lastId = stringRedisTemplate.opsForValue().get(MediaUserConstant.LAST_PROCESSED_ID_KEY);
        return lastId != null ? Long.parseLong(lastId.toString()) : 0L;
    }

    /**
     * 更新最后处理的ID
     */
    private void updateLastProcessedId(Long id) {
        stringRedisTemplate.opsForValue().set(MediaUserConstant.LAST_PROCESSED_ID_KEY, id.toString());
    }


}

package com.media.user.controller.api;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.media.core.auth.CloudSession;
import com.media.core.config.SystemConfig;
import com.media.core.constant.ClientInfo;
import com.media.core.constant.KafkaTopicConstant;
import com.media.core.i18n.I18nConvert;
import com.media.user.service.EnhancedI18nConvert;
import com.media.user.service.I18nCacheService;
import com.media.core.request.ClientInfoContext;
import com.media.core.utils.IpUtil;
import com.media.core.utils.VersionUtil;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.UserLoginLogModel;
import com.media.user.domain.UserLoginLogVersionModel;
import com.media.user.dto.banner.BannerConfig;
import com.media.user.dto.popup.PopupConfig;
import com.media.user.dto.request.ColdStartDataRequest;
import com.media.user.dto.response.BannerResponse;
import com.media.user.dto.response.ClientUserResponse;
import com.media.user.dto.response.PartnerAccessTokenResponse;
import com.media.user.dto.response.PopupResponse;
import com.media.user.dto.response.UserConfigResponse;
import com.media.user.enums.BannerStatusEnum;
import com.media.user.enums.LanguageEnums;
import com.media.user.mapper.UserLoginLogVersionMapper;
import com.media.user.mq.KafkaLogProducer;
import com.media.user.service.*;
import com.media.user.service.cache.ClientUserCacheService;
import com.media.user.util.MultiLanguageImageUtil;
import com.media.user.utils.RequestHeaderUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.media.user.enums.ShareTypeEnum.CHINA;
import static com.media.user.enums.ShareTypeEnum.DEFAULT;

@Slf4j
@RestController
@RequestMapping("/user/api/system")
public class ConfigController {

    @Autowired
    SystemConfig systemConfig;

    @Autowired
    ConfigSwitchService configSwitchService;

    @Autowired
    IpUtil ipUtil;

    @Autowired
    private KafkaLogProducer kafkaLogProducer;

    @Autowired
    private AbTestService abTestService;

    @Autowired
    private IpLocationService ipLocationService;

    @Autowired
    private PartnerTokenService partnerTokenService;

    @Autowired
    private ClientUserCacheService clientUserCacheService;

    @Autowired
    private DeviceService deviceService;

    @Autowired
    private ActivityCommonService activityCommonService;

    @Autowired
    private UserLoginLogVersionMapper userLoginLogVersionMapper;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Autowired
    UserConfigService userConfigService;

    @Autowired
    EnhancedI18nConvert enhancedI18nConvert;

    @Autowired
    I18nCacheService i18nCacheService;

    @GetMapping("/abconfig")
    public Map<String, Object> getAbConfig(HttpServletRequest request) {
        ClientInfo clientInfo = ClientInfoContext.get();
        Map<String, Object> rtn = new HashMap<>();
        boolean isLogin = CloudSession.isLogin();
        Long uid = isLogin ? CloudSession.getUid() : null;
        rtn.put("abConfig", abTestService.GetAbConfigInfo(clientInfo.getDeviceId(), uid));
        return rtn;
    }


    /**
     * 系统的配置
     */
    @GetMapping("/config")
    public Map<String, Object> systemConfig(HttpServletRequest request) {
        ColdStartDataRequest coldStartData = buildColdStartData(request);
        sendColdStartDataMessage(coldStartData);
        Map<String, Object> rtn = new HashMap<>();
        rtn.put("beeLoginShow", systemConfig.getBeeLoginShow());
        rtn.put("checkApp", configSwitchService.switchConfig());
        rtn.put("taskShow", systemConfig.getTaskShow());
        rtn.put("socialActShow", systemConfig.getSocialActivityShow());
        rtn.put("unionActShow", systemConfig.getUnionActivityShow());
        rtn.put("showKolBanner", systemConfig.isShowKolBanner() ? 1 : 0);
        rtn.put("earlyBird", configSwitchService.earlySwitchConfig() ? 1 : 0);
        rtn.put("giftIconShow", systemConfig.getGiftIconShow());
        rtn.put("inviteXmeAmount", systemConfig.getInviteXmeAmount());
        rtn.put("genesisBadgeAvatar", systemConfig.getGenesisBadgeAvatar());
        rtn.put("isGenesisActivity", 0);
        rtn.put("isMiningStart", configSwitchService.IsMiningStarted(new Date()) ? 1 : 0);
        rtn.put("shareType", ipLocationService.isChineseIp(coldStartData.getUserIP()) ? CHINA.getCode() : DEFAULT.getCode());
        rtn.put("kolHotIconShow", systemConfig.getKolHotIconShow());
        rtn.put("luckyWheelShow", 0);
        rtn.put("floating_button", new JSONObject());
        rtn.put("adConfig", JSON.parseObject(systemConfig.getAdShowConfigJson()));
        rtn.put("newUserPopupDelay", systemConfig.getNewUserPopupDelay());
        rtn.put("newShareLink", systemConfig.getNewShareLink());
        deviceService.AsyncUserBindDevice(coldStartData.getDeviceId(), coldStartData.getUid());
        String platformType = coldStartData.getPlatformType();
        if (StringUtils.containsIgnoreCase(platformType, "android")) {
            Map<String, String> headers = RequestHeaderUtils.extractAllHeaders(request);
            deviceService.AsyncUpdateUserDevice(headers);
        }

        // 1. 生成唯一标识（用户ID+version）
        String uniqueKey = String.format("%d_%s",
                coldStartData.getUid(),
                ClientInfoContext.get().getVersion());
        // 2. Redis去重检查
        String redisKey = MediaUserConstant.LOGIN_VERSION_CHECK + uniqueKey;
        if (stringRedisTemplate.opsForValue().setIfAbsent(redisKey, "1", 24, TimeUnit.HOURS)) {
            UserLoginLogVersionModel loginLog = new UserLoginLogVersionModel();
            loginLog.setUid(coldStartData.getUid());
            loginLog.setCreatedTime(new Date());
            loginLog.setUpdatedTime(new Date());
            loginLog.setVersion(ClientInfoContext.get().getVersion());
            userLoginLogVersionMapper.insertIgnore(loginLog);
        }
        return rtn;
    }

    private ColdStartDataRequest buildColdStartData(HttpServletRequest request) {
        ClientInfo clientInfo = ClientInfoContext.get();
        ColdStartDataRequest.ColdStartDataRequestBuilder builder = ColdStartDataRequest.builder();
        if (clientInfo != null) {
            builder.platformType(clientInfo.getPlatformType())
                    .version(clientInfo.getVersion())
                    .deviceId(clientInfo.getDeviceId());
        }
        boolean isLogin = CloudSession.isLogin();
        Long uid = isLogin ? CloudSession.getUid() : null;
        if (isLogin && uid != null) {
            builder.uid(uid);
        }
        String userIP = ipUtil.getIpAddrByRequest(request);
        builder.userIP(userIP).timestamp(String.valueOf(System.currentTimeMillis()));
        return builder.build();
    }

    @Async("threadPoolTaskExecutor")
    public void sendColdStartDataMessage(ColdStartDataRequest messageData) {
        try {
            // 发送冷启动消息到Kafka
            kafkaLogProducer.sendMessage(KafkaTopicConstant.CLIENT_COLD_START, JSON.toJSONString(messageData));
            if (log.isDebugEnabled()) {
                log.debug("Sent client cold start message to Kafka");
            }
        } catch (Exception e) {
            log.error("Failed to send client cold start message to Kafka", e);
        }
    }

    /**
     * 早鸟活动截止日期文案
     *
     * @return
     */
    @GetMapping("/earlyBird/config")
    public Map<String, Object> earlyBirdConfig() {
        Map<String, Object> rtn = new HashMap<>();
        /**
         //早鸟计划结束时间
         rtn.put("endDate", configSwitchService.getEarlyBirdConfig(ClientInfoContext.getLanguage()));
         //挖矿开始时间
         rtn.put("miningBeginDate", configSwitchService.getMiningBeginDate(ClientInfoContext.getLanguage()));
         */
        return rtn;
    }

    /**
     * 获取挖矿排行榜
     *
     * @return Banner配置列表
     */
    @GetMapping("/banner/task")
    public List<BannerResponse.BannerItem> getBannerMiningList() {
        BannerConfig bannerConfig;
        bannerConfig = com.alibaba.fastjson.JSON.parseObject(systemConfig.getBannerTaskConfigJson(), BannerConfig.class);
        if (bannerConfig == null || bannerConfig.getBanners() == null) {
            return new ArrayList<>();
        }
        List<BannerResponse.BannerItem> result = new ArrayList<>();
        if (!configSwitchService.IsMiningStarted(new Date())) {
            return result;
        }
        for (BannerConfig.BannerConfigItem item : bannerConfig.getBanners()) {
            try {
                BannerResponse.BannerItem responseItem = new BannerResponse.BannerItem();
                if (item.getStatus() == BannerStatusEnum.ENDED.getCode()) {
                    continue; // 跳过已经结束状态的活动
                }

                // 从 i18n 资源文件获取翻译
                String title = I18nConvert.getI18nMessage(item.getTitleKey(), ClientInfoContext.getLanguage());

                Integer status = item.getStatus();
                responseItem.setId(item.getId());
                responseItem.setTitle(title);
                responseItem.setIcon(item.getIcon());
                responseItem.setJumpType(item.getJumpType());
                responseItem.setJumpUrl(item.getJumpUrl());
                responseItem.setClickable(true);
                responseItem.setStatus(status);
                responseItem.setOrder(item.getOrder());
                responseItem.setNeedLogin(item.isNeedLogin());  // 设置是否需要登录
                result.add(responseItem);
            } catch (Exception e) {
                continue;
            }
        }
        // 按 order 排序
        result.sort(Comparator.comparing(BannerResponse.BannerItem::getOrder));

        return result;
    }

    /**
     * 获取所有激活的Banner配置
     *
     * @return Banner配置列表
     */
    @GetMapping("/banner/list")
    public List<BannerResponse.BannerItem> getBannerList(HttpServletRequest request) {
        BannerConfig bannerConfig;
        bannerConfig = com.alibaba.fastjson.JSON.parseObject(systemConfig.getBannerConfigJson(), BannerConfig.class);
        if (bannerConfig == null || bannerConfig.getBanners() == null) {
            return new ArrayList<>();
        }
        List<BannerResponse.BannerItem> result = new ArrayList<>();
        for (BannerConfig.BannerConfigItem item : bannerConfig.getBanners()) {
            try {
                BannerResponse.BannerItem responseItem = new BannerResponse.BannerItem();
                if (item.getStatus() == BannerStatusEnum.ENDED.getCode()) {
                    continue; // 跳过已经结束状态的活动
                }

                // 检查时间状态
                if (item.getStartTime() != null && !item.getStartTime().isEmpty()) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime startTime = LocalDateTime.parse(item.getStartTime(), formatter);
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isBefore(startTime)) {
                            // 未开始
                            continue;
                            // item.setStatus(BannerStatusEnum.NOT_STARTED.getCode());
                        }
                    } catch (Exception e) {
                        continue;
                    }
                }

                if (item.getEndTime() != null && !item.getEndTime().isEmpty()) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime endTime = LocalDateTime.parse(item.getEndTime(), formatter);
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isAfter(endTime)) {
                            // 已结束
                            continue;
                        }
                    } catch (Exception e) {
                        continue;
                    }
                }

                if (!item.isAuditShowable()) {
                    if (systemConfig.isInAudit()) {
                        continue;
                    }
                }

                // 从 i18n 资源文件获取翻译
                String title = I18nConvert.getI18nMessage(item.getTitleKey(), ClientInfoContext.getLanguage());

                responseItem.setId(item.getId());
                responseItem.setTitle(title);
                responseItem.setIcon(item.getIcon());
                responseItem.setJumpType(item.getJumpType());
                responseItem.setJumpUrl(item.getJumpUrl());
                responseItem.setClickable(true);
                responseItem.setStatus(item.getStatus());
                responseItem.setOrder(item.getOrder());
                responseItem.setNeedLogin(item.isNeedLogin());  // 设置是否需要登录
                result.add(responseItem);
            } catch (Exception e) {
                continue;
            }
        }
        // 按 order 排序
        result.sort(Comparator.comparing(BannerResponse.BannerItem::getOrder));

        return result;
    }

    /**
     * 获取所有激活的Banner配置 版本 >= 1.8.0
     *
     * @return Banner配置列表
     */
    @GetMapping("/v1/banners")
    public List<BannerResponse.BannerItem> getBannersV1(HttpServletRequest request) {
        BannerConfig bannerConfig;
        bannerConfig = com.alibaba.fastjson.JSON.parseObject(systemConfig.getBannerConfigHomeV1Json(), BannerConfig.class);
        if (bannerConfig == null || bannerConfig.getBanners() == null) {
            return new ArrayList<>();
        }
        boolean isLogin = CloudSession.isLogin();
        List<BannerResponse.BannerItem> result = new ArrayList<>();
        boolean isChina = false;

        if (LanguageEnums.zh_CN.equals(ClientInfoContext.getLanguage())) {
            isChina = true;
        }

        for (BannerConfig.BannerConfigItem item : bannerConfig.getBanners()) {
            try {
                BannerResponse.BannerItem responseItem = new BannerResponse.BannerItem();
                if (item.getStatus() == BannerStatusEnum.ENDED.getCode()) {
                    continue; // 跳过已经结束状态的活动
                }

                // 检查版本控制
                String currentVersion = ClientInfoContext.get().getVersion();
                String minVersion = item.getMinVersion();
                if (minVersion != null && !minVersion.isEmpty() && VersionUtil.CompareVersions(currentVersion, minVersion) < 0) {
                    // 当前版本小于最小要求版本，不展示
                    continue;
                }


                if ("xme_rebate_activity".equals(item.getId())) {
                    if (isLogin) {
                        item.setJumpUrl(systemConfig.appendRebateUrl(item.getJumpUrl(), CloudSession.getUid()));
                    }
                }

                // 检查时间状态
                if (item.getStartTime() != null && !item.getStartTime().isEmpty()) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime startTime = LocalDateTime.parse(item.getStartTime(), formatter);
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isBefore(startTime)) {
                            // 未开始
                            continue;
                        }
                    } catch (Exception e) {
                        continue;
                    }
                }

                if (item.getEndTime() != null && !item.getEndTime().isEmpty()) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime endTime = LocalDateTime.parse(item.getEndTime(), formatter);
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isAfter(endTime)) {
                            // 已结束
                            continue;
                        }
                    } catch (Exception e) {
                        continue;
                    }
                }
                if (!item.isAuditShowable()) {
                    if (systemConfig.isInAudit()) {
                        continue;
                    }
                }

                // 从 i18n 服务获取翻译（优先远程服务，降级到本地资源文件）
                if (item.getTitleKey() != null && !item.getTitleKey().isEmpty()) {
                    String title = enhancedI18nConvert.getI18nMessage(item.getTitleKey(), ClientInfoContext.getLanguage());
                    responseItem.setTitle(title);
                }

                if (item.getDescriptionKey() != null && !item.getDescriptionKey().isEmpty()) {
                    String desc = enhancedI18nConvert.getI18nMessage(item.getDescriptionKey(), ClientInfoContext.getLanguage());
                    responseItem.setDescription(desc);
                }

                responseItem.setId(item.getId());
                responseItem.setIcon(item.getIcon());
                responseItem.setBackgroundImage(item.getBackgroundImage());
                if (isChina && StringUtils.isNotBlank(item.getBackgroundImageCn())) {
                    responseItem.setBackgroundImage(item.getBackgroundImageCn());
                }
                if (isChina && StringUtils.isNotBlank(item.getIconCn())) {
                    responseItem.setIcon(item.getIconCn());
                }
                responseItem.setJumpType(item.getJumpType());
                responseItem.setJumpUrl(item.getJumpUrl());
                responseItem.setClickable(true);
                responseItem.setOrder(item.getOrder());
                responseItem.setStatus(item.getStatus());
                responseItem.setNeedLogin(item.isNeedLogin());  // 设置是否需要登录
                responseItem.setNeedFace(item.isNeedFace()); //是否需要人脸
                responseItem.setNeedVerify(item.isNeedVerify()); //是否需要三合一
                responseItem.setTextColor(item.getTextColor()); // 颜色
                result.add(responseItem);
            } catch (Exception e) {
                continue;
            }
        }
        // 按 order 排序
        result.sort(Comparator.comparing(BannerResponse.BannerItem::getOrder));

        // 根据结果长度条件性地交换icon和backgroundImage
        if (result.size() == 3) {
            for (BannerResponse.BannerItem item : result) {
                item.setBackgroundImage(item.getIcon());
            }
        }

        return result;
    }


    /**
     * 获取所有激活的Banner配置
     *
     * @return Banner配置列表
     */
    @GetMapping("/banners")
    public List<BannerResponse.BannerItem> getBanners(HttpServletRequest request) {
        BannerConfig bannerConfig;
        bannerConfig = com.alibaba.fastjson.JSON.parseObject(systemConfig.getBannerConfigHomeJson(), BannerConfig.class);
        if (bannerConfig == null || bannerConfig.getBanners() == null) {
            return new ArrayList<>();
        }
        boolean isLogin = CloudSession.isLogin();
        List<BannerResponse.BannerItem> result = new ArrayList<>();
        boolean isChina = false;

        if (LanguageEnums.zh_CN.equals(ClientInfoContext.getLanguage())) {
            isChina = true;
        }

        for (BannerConfig.BannerConfigItem item : bannerConfig.getBanners()) {
            try {
                BannerResponse.BannerItem responseItem = new BannerResponse.BannerItem();
                if (item.getStatus() == BannerStatusEnum.ENDED.getCode()) {
                    continue; // 跳过已经结束状态的活动
                }

                // 检查版本控制
                String currentVersion = ClientInfoContext.get().getVersion();
                String minVersion = item.getMinVersion();
                if (minVersion != null && !minVersion.isEmpty() && VersionUtil.CompareVersions(currentVersion, minVersion) < 0) {
                    // 当前版本小于最小要求版本，不展示
                    continue;
                }


                if ("xme_rebate_activity".equals(item.getId())) {
                    if (isLogin) {
                        item.setJumpUrl(systemConfig.appendRebateUrl(item.getJumpUrl(), CloudSession.getUid()));
                    }
                }

                // 检查时间状态
                if (item.getStartTime() != null && !item.getStartTime().isEmpty()) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime startTime = LocalDateTime.parse(item.getStartTime(), formatter);
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isBefore(startTime)) {
                            // 未开始
                            continue;
                        }
                    } catch (Exception e) {
                        continue;
                    }
                }

                if (item.getEndTime() != null && !item.getEndTime().isEmpty()) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime endTime = LocalDateTime.parse(item.getEndTime(), formatter);
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isAfter(endTime)) {
                            // 已结束
                            continue;
                        }
                    } catch (Exception e) {
                        continue;
                    }
                }
                if (!item.isAuditShowable()) {
                    if (systemConfig.isInAudit()) {
                        continue;
                    }
                }

                // 从 i18n 资源文件获取翻译
                if (item.getTitleKey() != null && !item.getTitleKey().isEmpty()) {
                    String title = I18nConvert.getI18nMessage(item.getTitleKey(), ClientInfoContext.getLanguage());
                    responseItem.setTitle(title);
                }

                responseItem.setId(item.getId());

                // 使用多语言图片工具类获取对应语言的图片
                LanguageEnums currentLanguage = ClientInfoContext.getLanguage();

                // 设置图标 - 优先使用多语言配置，回退到原有逻辑
                String iconUrl = MultiLanguageImageUtil.getImageByLanguage(
                    item.getIconImages(),
                    currentLanguage,
                    item.getIcon()
                );
                responseItem.setIcon(iconUrl);

                // 设置背景图片 - 优先使用多语言配置，回退到原有逻辑
                String backgroundImageUrl = MultiLanguageImageUtil.getImageByLanguage(
                    item.getBackgroundImages(),
                    currentLanguage,
                    item.getBackgroundImage()
                );

                // 向后兼容：如果是中文且有专门的中文背景图，使用中文背景图
                if (isChina && StringUtils.isNotBlank(item.getBackgroundImageCn())) {
                    backgroundImageUrl = item.getBackgroundImageCn();
                }

                responseItem.setBackgroundImage(backgroundImageUrl);

                // 如果有背景图片，将其也设置为图标（保持原有逻辑）
                if (StringUtils.isNotBlank(backgroundImageUrl)) {
                    responseItem.setIcon(backgroundImageUrl);
                }
                responseItem.setJumpType(item.getJumpType());
                responseItem.setJumpUrl(item.getJumpUrl());
                responseItem.setClickable(true);
                responseItem.setOrder(item.getOrder());
                responseItem.setStatus(item.getStatus());
                responseItem.setNeedLogin(item.isNeedLogin());  // 设置是否需要登录
                responseItem.setNeedFace(item.isNeedFace()); //是否需要人脸
                responseItem.setNeedVerify(item.isNeedVerify()); //是否需要三合一
                result.add(responseItem);
            } catch (Exception e) {
                continue;
            }
        }
        // 按 order 排序
        result.sort(Comparator.comparing(BannerResponse.BannerItem::getOrder));
        return result;
    }

    /**
     * 获取所有激活的Banner配置
     *
     * @return Banner配置列表
     */
    @GetMapping("/web3/banners")
    public List<BannerResponse.BannerItem> getWeb3Banners() {
        BannerConfig bannerConfig;
        bannerConfig = com.alibaba.fastjson.JSON.parseObject(systemConfig.getBannerWeb3ConfigJson(), BannerConfig.class);
        if (bannerConfig == null || bannerConfig.getBanners() == null) {
            return new ArrayList<>();
        }
        boolean isLogin = CloudSession.isLogin();
        List<BannerResponse.BannerItem> result = new ArrayList<>();
        for (BannerConfig.BannerConfigItem item : bannerConfig.getBanners()) {
            try {
                BannerResponse.BannerItem responseItem = new BannerResponse.BannerItem();
                if (item.getStatus() == BannerStatusEnum.ENDED.getCode()) {
                    continue; // 跳过已经结束状态的活动
                }

                // 检查版本控制
                String currentVersion = ClientInfoContext.get().getVersion();
                String minVersion = item.getMinVersion();
                if (minVersion != null && !minVersion.isEmpty() && VersionUtil.CompareVersions(currentVersion, minVersion) < 0) {
                    // 当前版本小于最小要求版本，不展示
                    continue;
                }

                if ("loop_live".equals(item.getId())) {
                    if (isLogin) {
                        try {
                            String authorizedURL = this.appendAccessTokenToUrl(item.getJumpUrl());
                            if (authorizedURL == null && authorizedURL.isEmpty()) {
                                continue;
                            }
                            item.setJumpUrl(authorizedURL);
                        } catch (Exception e) {
                            log.error("failed to get loop live authorized url", e);
                            continue;
                        }
                    }
                }

                // 检查时间状态
                if (item.getStartTime() != null && !item.getStartTime().isEmpty()) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime startTime = LocalDateTime.parse(item.getStartTime(), formatter);
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isBefore(startTime)) {
                            // 未开始
                            continue;
                        }
                    } catch (Exception e) {
                        continue;
                    }
                }

                if (item.getEndTime() != null && !item.getEndTime().isEmpty()) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime endTime = LocalDateTime.parse(item.getEndTime(), formatter);
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isAfter(endTime)) {
                            // 已结束
                            continue;
                        }
                    } catch (Exception e) {
                        continue;
                    }
                }
                if (!item.isAuditShowable()) {
                    if (systemConfig.isInAudit()) {
                        continue;
                    }
                }

                // 从 i18n 资源文件获取翻译
                if (item.getTitleKey() != null && !item.getTitleKey().isEmpty()) {
                    String title = I18nConvert.getI18nMessage(item.getTitleKey(), ClientInfoContext.getLanguage());
                    responseItem.setTitle(title);
                }

                responseItem.setId(item.getId());
                responseItem.setIcon(item.getIcon());
                responseItem.setBackgroundImage(item.getBackgroundImage());
                responseItem.setJumpType(item.getJumpType());
                responseItem.setJumpUrl(item.getJumpUrl());
                responseItem.setClickable(true);
                responseItem.setOrder(item.getOrder());
                responseItem.setStatus(item.getStatus());
                responseItem.setNeedLogin(item.isNeedLogin());  // 设置是否需要登录
                responseItem.setNeedFace(item.isNeedFace()); //是否需要人脸
                result.add(responseItem);
            } catch (Exception e) {
                continue;
            }
        }
        // 按 order 排序
        result.sort(Comparator.comparing(BannerResponse.BannerItem::getOrder));
        return result;
    }

    /**
     * 获取弹窗配置列表
     *
     * @param scene 弹窗场景，可选参数：home/task/profile/login等
     * @return 弹窗配置列表
     */
    @GetMapping("/popup/list")
    public List<PopupResponse.PopupItem> getPopupList(String scene) {

        if (StringUtils.isBlank(scene)) {
            scene = "home";
        }
        PopupConfig popupConfig;
        try {
            popupConfig = com.alibaba.fastjson.JSON.parseObject(systemConfig.getPopupConfigJson(), PopupConfig.class);
        } catch (Exception e) {
            log.error("Failed to parse popup config JSON", e);
            return new ArrayList<>();
        }

        if (popupConfig == null || popupConfig.getPopups() == null) {
            return new ArrayList<>();
        }

        boolean isLogin = CloudSession.isLogin();

        List<PopupResponse.PopupItem> candidatePopups = new ArrayList<>();
        ClientInfo clientInfo = ClientInfoContext.get();

        // === 第一步：基础过滤 ===
        for (PopupConfig.PopupConfigItem item : popupConfig.getPopups()) {
            try {
                // 跳过已结束状态的弹窗
                if (item.getStatus() == BannerStatusEnum.ENDED.getCode()) { // 3-结束状态
                    continue;
                }

                // 场景过滤
                if (StringUtils.isNotBlank(scene) && !scene.equals(item.getScene())) {
                    continue;
                }

                // 检查权限
                if (item.isNeedLogin() && !isLogin) {
                    continue;
                }

                // 检查版本控制
                String currentVersion = clientInfo != null ? clientInfo.getVersion() : "";
                String platform = clientInfo != null ? clientInfo.getPlatformType() : "";

                // 根据平台类型选择对应的版本要求
                String minVersion;
                if (("android".equalsIgnoreCase(platform) || "android-gp".equalsIgnoreCase(platform)) && item.getMinVersionAndroid() != null && !item.getMinVersionAndroid().isEmpty()) {
                    // 使用Android特定版本要求
                    minVersion = item.getMinVersionAndroid();
                } else if ("ios".equalsIgnoreCase(platform) && item.getMinVersionIos() != null && !item.getMinVersionIos().isEmpty()) {
                    // 使用iOS特定版本要求
                    minVersion = item.getMinVersionIos();
                } else {
                    // 使用通用版本要求（向后兼容）
                    minVersion = item.getMinVersion();
                }

                // 执行版本比较
                if (minVersion != null && !minVersion.isEmpty() && VersionUtil.CompareVersions(currentVersion, minVersion) < 0) {
                    continue;
                }

                // 检查时间状态
                if (item.getStartTime() != null && !item.getStartTime().isEmpty()) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime startTime = LocalDateTime.parse(item.getStartTime(), formatter);
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isBefore(startTime)) {
                            continue;
                        }
                    } catch (Exception e) {
                        continue;
                    }
                }

                if (item.getEndTime() != null && !item.getEndTime().isEmpty()) {
                    try {
                        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        LocalDateTime endTime = LocalDateTime.parse(item.getEndTime(), formatter);
                        LocalDateTime now = LocalDateTime.now();
                        if (now.isAfter(endTime)) {
                            continue;
                        }
                    } catch (Exception e) {
                        continue;
                    }
                }

                // 检查审核状态
                if (!item.isAuditShowable()) {
                    if (systemConfig.isInAudit()) {
                        continue;
                    }
                }

                if ("withdraw_popup".equals(item.getId())) {
                    UserConfigResponse configResponse =  userConfigService.getUserConfig(ClientInfoContext.getLanguage());
                    item.setJumpUrl(configResponse.getWalletAnnouncementUrl());
                }

                PopupResponse.PopupItem responseItem = new PopupResponse.PopupItem();
                responseItem.setId(item.getId());
                responseItem.setStyle(item.getStyle());
                responseItem.setWidth(item.getWidth());
                responseItem.setHeight(item.getHeight());
                responseItem.setBackgroundImage(item.getBackgroundImage());
                responseItem.setJumpType(item.getJumpType());
                responseItem.setJumpUrl(item.getJumpUrl());
                responseItem.setClickable(item.isClickable());
                responseItem.setStatus(item.getStatus());
                responseItem.setOrder(item.getOrder());
                responseItem.setNeedLogin(item.isNeedLogin());
                responseItem.setNeedFace(item.isNeedFace());
                responseItem.setNeedVerify(item.isNeedVerify());
                responseItem.setScene(item.getScene());
                responseItem.setHighlightColor(item.getHighlightColor());

                // 设置版本要求信息，包括平台特定版本
                responseItem.setMinVersion(item.getMinVersion());
                responseItem.setMinVersionAndroid(item.getMinVersionAndroid());
                responseItem.setMinVersionIos(item.getMinVersionIos());

                // 从 i18n 资源文件获取翻译
                if (item.getTitleKey() != null && !item.getTitleKey().isEmpty()) {
                    String title = enhancedI18nConvert.getI18nMessage(item.getTitleKey(), ClientInfoContext.getLanguage());
                    responseItem.setTitle(title);
                }
                if (item.getDescriptionKey() != null && !item.getDescriptionKey().isEmpty()) {
                    String description = enhancedI18nConvert.getI18nMessage(item.getDescriptionKey(), ClientInfoContext.getLanguage());
                    responseItem.setDescription(description);
                }
                if (item.getButtonTextKey() != null && !item.getButtonTextKey().isEmpty()) {
                    String buttonText = enhancedI18nConvert.getI18nMessage(item.getButtonTextKey(), ClientInfoContext.getLanguage());
                    responseItem.setButtonText(buttonText);
                }
                if (item.getContentKey() != null && !item.getContentKey().isEmpty()) {
                    String content = enhancedI18nConvert.getI18nMessage(item.getContentKey(), ClientInfoContext.getLanguage());
                    responseItem.setContent(content);
                }

                if (CloudSession.isLogin() && Objects.equals(item.getId(), "invite_popup")) {
                    activityCommonService.processDate(responseItem);
                }

                // 设置显示规则信息 - 让客户端自己处理显示逻辑
                responseItem.setShouldShow(true); // 服务端不再控制是否显示，由客户端根据规则判断

                // 设置显示规则信息，传递给客户端处理
                if (item.getDisplayRule() != null) {
                    PopupResponse.PopupItem.DisplayRuleInfo ruleInfo = new PopupResponse.PopupItem.DisplayRuleInfo();
                    ruleInfo.setType(item.getDisplayRule().getType());
                    ruleInfo.setInterval(item.getDisplayRule().getInterval());
                    ruleInfo.setMaxTimes(item.getDisplayRule().getMaxTimes());
                    ruleInfo.setPriority(item.getDisplayRule().getPriority());
                    responseItem.setDisplayRule(ruleInfo);
                }

                candidatePopups.add(responseItem);
            } catch (Exception e) {
                log.error("Error processing popup item: {}", item.getId(), e);
                continue;
            }
        }

        // === 第二步：应用类型互斥规则 ===
        List<PopupResponse.PopupItem> finalResult = applyPopupTypeMutex(candidatePopups, popupConfig);

        // 按优先级和order排序
        finalResult.sort(Comparator.comparingInt(PopupResponse.PopupItem::getOrder));

        return finalResult;
    }

    /**
     * 手动重新加载i18n翻译数据
     * 适用于多台机器的分布式环境，通过Redis共享缓存
     */
    @GetMapping("/i18n/reload")
    public Map<String, Object> reloadI18nTranslations() {
        Map<String, Object> result = new HashMap<>();
        try {
            long beforeSize = i18nCacheService.getCacheSize();
            i18nCacheService.reloadTranslations();
            long afterSize = i18nCacheService.getCacheSize();

            result.put("success", true);
            result.put("message", "I18n translations reloaded successfully");
            result.put("cacheSizeBefore", beforeSize);
            result.put("cacheSizeAfter", afterSize);
            result.put("timestamp", System.currentTimeMillis());

            log.info("I18n translations reloaded manually, cache size: {} -> {}", beforeSize, afterSize);

        } catch (Exception e) {
            log.error("Failed to reload i18n translations", e);
            result.put("success", false);
            result.put("message", "Failed to reload i18n translations: " + e.getMessage());
        }
        return result;
    }

    /**
     * 清空i18n翻译缓存
     */
    @GetMapping("/i18n/clear")
    public Map<String, Object> clearI18nCache() {
        Map<String, Object> result = new HashMap<>();
        try {
            long beforeSize = i18nCacheService.getCacheSize();
            i18nCacheService.clearCache();

            result.put("success", true);
            result.put("message", "I18n cache cleared successfully");
            result.put("clearedCount", beforeSize);
            result.put("timestamp", System.currentTimeMillis());

            log.info("I18n cache cleared manually, {} items removed", beforeSize);

        } catch (Exception e) {
            log.error("Failed to clear i18n cache", e);
            result.put("success", false);
            result.put("message", "Failed to clear i18n cache: " + e.getMessage());
        }
        return result;
    }

    /**
     * 获取i18n缓存状态
     */
    @GetMapping("/i18n/status")
    public Map<String, Object> getI18nCacheStatus() {
        Map<String, Object> result = new HashMap<>();
        try {
            long cacheSize = i18nCacheService.getCacheSize();

            result.put("success", true);
            result.put("cacheSize", cacheSize);
            result.put("cacheType", "Redis");
            result.put("timestamp", System.currentTimeMillis());

        } catch (Exception e) {
            log.error("Failed to get i18n cache status", e);
            result.put("success", false);
            result.put("message", "Failed to get cache status: " + e.getMessage());
        }
        return result;
    }

    /**
     * 应用弹窗类型互斥规则 - 同一类型只显示一个弹窗
     */
    private List<PopupResponse.PopupItem> applyPopupTypeMutex(List<PopupResponse.PopupItem> candidatePopups, PopupConfig popupConfig) {
        if (candidatePopups.isEmpty()) {
            return candidatePopups;
        }

        List<PopupResponse.PopupItem> result = new ArrayList<>();
        java.util.Set<String> usedTypes = new java.util.HashSet<>();

        // 按优先级排序（高优先级优先处理）
        candidatePopups.sort(Comparator.comparingInt(PopupResponse.PopupItem::getOrder));

        for (PopupResponse.PopupItem popup : candidatePopups) {
            try {
                // 从配置中获取弹窗类型
                String popupType = getPopupTypeFromConfig(popup.getId(), popupConfig);

                if (StringUtils.isBlank(popupType)) {
                    // 没有类型限制的弹窗直接添加
                    result.add(popup);
                } else {
                    // 检查该类型是否已使用
                    if (!usedTypes.contains(popupType)) {
                        usedTypes.add(popupType);
                        result.add(popup);
                    }
                }
            } catch (Exception e) {
                result.add(popup);
            }
        }
        return result;
    }

    /**
     * 从配置中获取弹窗类型
     */
    private String getPopupTypeFromConfig(String popupId, PopupConfig popupConfig) {
        if (popupConfig.getPopups() == null) {
            return null;
        }

        return popupConfig.getPopups().stream()
                .filter(item -> popupId.equals(item.getId()))
                .findFirst()
                .map(item -> {
                    // 临时使用 scene 字段或其他字段来代替
                    return item.getPopupType(); // 临时使用场景作为类型
                })
                .orElse(null);
    }

    /**
     * 记录弹窗显示（简化版，仅用于统计）
     *
     * @param popupId 弹窗ID
     * @return 成功标识
     */
    @GetMapping("/popup/record")
    public Map<String, Object> recordPopupShown(String popupId) {
        Map<String, Object> result = new HashMap<>();
        try {
            // 可以在这里添加简单的统计逻辑，比如发送到Kafka进行数据分析
            // 但不再维护显示状态，由客户端自己控制
            log.info("Popup shown: {}", popupId);
            result.put("success", true);
            result.put("message", "记录成功");
        } catch (Exception e) {
            log.error("Failed to record popup shown: {}", popupId, e);
            result.put("success", false);
            result.put("message", "记录失败");
        }
        return result;
    }

    /**
     * 拼接access_token到URL
     */
    private String appendAccessTokenToUrl(String originalUrl) {
        if (originalUrl == null || originalUrl.isEmpty()) {
            return originalUrl;
        }

        try {

            String userId = CloudSession.getUid().toString();
            ClientUserResponse clientUserResponse = clientUserCacheService.me(CloudSession.getUid());
            PartnerAccessTokenResponse tokenResponse = partnerTokenService.getAccessToken(userId, clientUserResponse.getNickName(), clientUserResponse.getAvatarUrl());
            String accessToken = null; // 默认值
            if (tokenResponse != null && tokenResponse.getCode() == 0 && tokenResponse.getData() != null) {
                accessToken = tokenResponse.getData().getAccessToken();
            }
            if (accessToken == null || accessToken.isEmpty()) {
                return null;
            }

            // 检查URL是否已经包含参数
            String separator = originalUrl.contains("?") ? "&" : "?";

            // 拼接access_token参数
            return originalUrl + separator + "access_token=" + accessToken;

        } catch (Exception e) {
            return null;
        }
    }
}

package com.media.user.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserDeviceRequest {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 设备ID.
     */
    private String deviceId;
}

package com.media.user.feign.client;

import com.media.user.dto.request.UserDeviceRequest;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.Map;
import java.util.concurrent.TimeUnit;

@FeignClient(url = "${device.url}", value = "deviceClient", configuration = DeviceClient.DeviceClientConfiguration.class)
public interface DeviceClient {
    @Configuration
    class DeviceClientConfiguration {
        @Bean("deviceClientOptions")
        public Request.Options options() {
            // Set connect timeout to 50ms and read timeout to 200ms
            return new Request.Options(100, TimeUnit.MILLISECONDS, 1000, TimeUnit.MILLISECONDS, true);
        }
    }

    @PostMapping(value = "/user/bind")
    void UserBindDevice(@RequestBody UserDeviceRequest request);

    @PostMapping(value = "/fingerprint/update")
    void UpdateUserDevice(@RequestHeader Map<String, String> headers);
}

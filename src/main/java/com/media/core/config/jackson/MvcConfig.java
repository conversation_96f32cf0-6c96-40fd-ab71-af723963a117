package com.media.core.config.jackson;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
  public class MvcConfig implements WebMvcConfigurer {
      @Bean
      public MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter(){
  
          MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
          ObjectMapper objectMapper = new ObjectMapper();
          //添加此配置
          objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
          converter.setObjectMapper(objectMapper);
          return converter;
      }
  }
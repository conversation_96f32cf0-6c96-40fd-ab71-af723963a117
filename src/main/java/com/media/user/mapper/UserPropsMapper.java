package com.media.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.ClientUserModel;
import com.media.user.domain.UserPropsModel;
import com.media.user.dto.query.RobotPointQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface UserPropsMapper extends BaseMapper<UserPropsModel> {

}
package com.media.user.service;

import com.media.core.config.SystemConfig;
import com.media.user.dto.request.SensitiveWordDetectionRequest;
import com.media.user.dto.response.SensitiveWordDetectionResponse;
import com.media.user.feign.client.SensitiveWordClient;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SensitiveWordService {
    @Autowired
    private SensitiveWordClient sensitiveWordClient;

    @Autowired
    private SystemConfig systemConfig;

    public SensitiveWordDetectionResponse sensitiveWordDetection(String word) {
        if (!systemConfig.isSensitiveWordEnable()) return null;
        log.info("<sensitiveWordDetection> start word: {}", word);
        try {
            if (StringUtils.isBlank(word)) {
                return null;
            }
            SensitiveWordDetectionRequest request = new SensitiveWordDetectionRequest();
            request.setText(word);
            request.setCheckSensitive(true);
            request.setDetectLanguage(true);
            request.setLanguageCandidates(1);
            ApiResponse<SensitiveWordDetectionResponse> resp = sensitiveWordClient.sensitiveWordDetection(request);
            if (resp != null && resp.getSuccess() && resp.getResult() != null) {
                log.info("<sensitiveWordClient> res:{}", resp.getResult());
                return resp.getResult();
            }
        } catch (Exception e) {
            log.error("<sensitiveWordDetection> error:", e);
        }
        return null;
    }
}

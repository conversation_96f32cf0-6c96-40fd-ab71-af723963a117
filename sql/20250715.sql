-- coreSky活动参与记录表
DROP TABLE IF EXISTS `core_sky_activity_record`;
CREATE TABLE `core_sky_activity_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uid` bigint(20) NOT NULL COMMENT '用户ID',
  `client_ip` varchar(45) NOT NULL COMMENT '客户端IP地址',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '参加活动时间',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='coreSky活动参与记录表';

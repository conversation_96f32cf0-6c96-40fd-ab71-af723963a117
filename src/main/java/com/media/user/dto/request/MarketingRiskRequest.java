package com.media.user.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class MarketingRiskRequest {
    @JsonProperty("user_id")
    private Long userId;

    @JsonProperty("ip")
    private String ip;

    @JsonProperty("box_id")
    private String boxId;

    @JsonProperty("user_agent")
    private String userAgent;

    @JsonProperty("app_version")
    private String appVersion;

    @JsonProperty("os")
    private String os;

    @JsonProperty("marketing_type")
    private String marketingType;

    @JsonProperty("task_id")
    private String taskId;
}

package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 用户邀请仪表板响应
 */
@Data
@Accessors(chain = true)
public class UserInviteDashboardResponse {

    /**
     * 第一种货币
     */
    private String firstCcy;

    /**
     * 第二种货币
     */
    private String secondCcy;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 邀请奖励
     */
    private List<InviteRewardItemResponse> inviteReward;

    /**
     * 被邀请奖励
     */
    private List<InviteRewardItemResponse> invitedReward;

    /**
     * 累计获得奖励
     */
    private List<InviteRewardItemResponse> totalRewards;
} 
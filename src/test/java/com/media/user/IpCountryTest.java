package com.media.user;

import com.media.user.feign.client.IpCountryClient;
import com.media.user.mq.provider.UserSendMessageProvider;
import com.media.user.service.ClientUserService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


@SpringBootTest
public class IpCountryTest {

    @Autowired
    IpCountryClient ipCountryClient;

    @Test
    public void testIp() {
        System.out.println(ipCountryClient.getIpCountryInfo("**********"));
    }



}

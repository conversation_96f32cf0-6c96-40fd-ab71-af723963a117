package com.media.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.UserFollowRelationModel;
import com.media.user.dto.query.UserAttentionCount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface UserFollowRelationMapper extends BaseMapper<UserFollowRelationModel> {

    /**
     * 查询当天的关注数据(真实用户)
     * @return
     */
    @Select("SELECT followed_new_uid 'uid',count(1) 'count' FROM user_follow_relation where followed_new_uid>0 and (created_time >= CONCAT(CURDATE(), ' 00:00:00') or updated_time >= CONCAT(CURDATE(), ' 00:00:00')) GROUP BY followed_new_uid;")
    List<UserAttentionCount> listTodayNormalUser();

    /**
     * 查询当天的关注数据(预注册用户)
     * @return
     */
    @Select("SELECT followed_old_uid 'uid',count(1) 'count' FROM user_follow_relation where followed_old_uid>0 and (created_time >= CONCAT(CURDATE(), ' 00:00:00') or updated_time >= CONCAT(CURDATE(), ' 00:00:00')) GROUP BY followed_old_uid;")
    List<UserAttentionCount> listTodayVirtualUser();
}
package com.media.user.feign.client;

import com.media.user.dto.response.RongcloudMessageResponse;
import feign.Request;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.concurrent.TimeUnit;

/**
 * 融云API客户端
 * 用于调用融云REST API发送消息等操作
 */
@FeignClient(url = "${rongcloud.host:https://api.sg-light-api.com}", value = "rongcloudClient", configuration = RongcloudClient.RongcloudClientConfiguration.class)
public interface RongcloudClient {

    @Configuration
    class RongcloudClientConfiguration {

        @Value("${rongcloud.appKey:}")
        private String appKey;

        @Value("${rongcloud.appSecret:}")
        private String appSecret;

        /**
         * 请求超时配置
         */
        @Bean("rongcloudClientOptions")
        public Request.Options options() {
            // 设置连接超时为500ms，读取超时为3000ms
            return new Request.Options(500, TimeUnit.MILLISECONDS, 3000, TimeUnit.MILLISECONDS, true);
        }
    }

    /**
     * 发送私聊消息
     *
     * @param appKey            融云应用Key
     * @param timestamp         时间戳
     * @param nonce             随机数
     * @param signature         签名
     * @param fromUserId        发送人用户ID
     * @param toUserId          接收人用户ID
     * @param objectName        消息类型
     * @param content           消息内容
     * @param pushContent       推送内容
     * @param pushData          推送数据
     * @param count             数量
     * @param verifyBlacklist   是否验证黑名单
     * @param isPersisted       是否持久化
     * @param isIncludeSender   是否包含发送者
     * @param disablePush       是否禁用推送
     * @param expansion         是否扩展
     * @return 发送结果
     */
    @PostMapping(value = "/message/private/publish.json", 
                 consumes = "application/x-www-form-urlencoded",
                 headers = {"Content-Type=application/x-www-form-urlencoded"})
    RongcloudMessageResponse sendPrivateMessage(
            @RequestHeader("App-Key") String appKey,
            @RequestHeader("Timestamp") String timestamp,
            @RequestHeader("Nonce") String nonce,
            @RequestHeader("Signature") String signature,
            @RequestParam("fromUserId") String fromUserId,
            @RequestParam("toUserId") String toUserId,
            @RequestParam("objectName") String objectName,
            @RequestParam("content") String content,
            @RequestParam(value = "pushContent", required = false) String pushContent,
            @RequestParam(value = "pushData", required = false) String pushData,
            @RequestParam(value = "count", defaultValue = "1") Integer count,
            @RequestParam(value = "verifyBlacklist", defaultValue = "0") Integer verifyBlacklist,
            @RequestParam(value = "isPersisted", defaultValue = "1") Integer isPersisted,
            @RequestParam(value = "isIncludeSender", defaultValue = "0") Integer isIncludeSender,
            @RequestParam(value = "disablePush", defaultValue = "false") Boolean disablePush,
            @RequestParam(value = "expansion", defaultValue = "false") Boolean expansion);
}

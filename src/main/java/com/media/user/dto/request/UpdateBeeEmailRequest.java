package com.media.user.dto.request;

import com.media.core.constant.ValidatorRegexpConstant;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class UpdateBeeEmailRequest {

    private Long uid;

    private String sign;

    @Email
    @NotBlank
    private String email;

    @Pattern(regexp = ValidatorRegexpConstant.PASSWORD_REGEX, message = "{result.code.10020}")
    @NotBlank
    private String password;

//    @NotBlank
    @Size(max = 6)
    private String emailCode;
}

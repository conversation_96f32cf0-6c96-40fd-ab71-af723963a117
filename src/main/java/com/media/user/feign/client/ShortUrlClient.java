package com.media.user.feign.client;

import com.media.user.dto.request.CreateShortUrlRequest;
import com.media.user.dto.response.ShortUrlResponse;
import com.xme.xme_base_depends.models.ApiResponse;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.concurrent.TimeUnit;

/**
 * 短链接服务客户端
 */

@FeignClient(url = "${shorturl.service.url:http://envtest-api.x.me}", value = "shortUrlClient", configuration = ShortUrlClient.ShortUrlClientConfiguration.class)
public interface ShortUrlClient {

    @Configuration
    class ShortUrlClientConfiguration {
        @Bean("shortUrlClientOptions")
        public Request.Options options() {
            // Set connect timeout to 200ms and read timeout to 1000ms
            return new Request.Options(50, TimeUnit.MILLISECONDS, 500, TimeUnit.MILLISECONDS, true);
        }
    }

    /**
     * 创建短链接
     *
     * @param request 创建短链接请求
     * @return 短链接响应
     */
    @PostMapping(value = "/short/shorten", headers = {"X-API-KEY=${shorturl.api.key:123456}"})
    ApiResponse<ShortUrlResponse> createShortUrl(@RequestBody CreateShortUrlRequest request);

}

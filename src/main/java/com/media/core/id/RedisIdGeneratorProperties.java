package com.media.core.id;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@ConfigurationProperties(prefix = "id.generator.redis")
@Component
public class RedisIdGeneratorProperties {

    private String keyPrefix = "media:id:generator";

    /**
     * ID起始位.
     */
    private long startId = 221_112_312_824_388L;

    /**
     * 用户子业务前缀
     */
    private String userSubPrefix = ":user";

    /**
     * 用户ID起始位.
     */
    private long userStartId = 20212312L;

}

package com.media.user.feign.vo;

import lombok.Data;

@Data
public class GoogleUserInfo {

    private String iss;//": "https://accounts.google.com",
    private String azp;//": "************-svaifa15lqqohumflfkm2f3blrokcu75.apps.googleusercontent.com",
    private String aud;//": "************-svaifa15lqqohumflfkm2f3blrokcu75.apps.googleusercontent.com",
    private String sub;//": "115288708055792744236",
    private String email;//": "<EMAIL>",
    private String email_verified;//": "true",
    private String nbf;//": "**********",
    private String name;//": "lei yan",
    private String picture;//": "https://lh3.googleusercontent.com/a/ACg8ocKSXgL7UAAMs8YoD1OuPzbDaeA5bUKqNQtp-yTh6x3mH4BDdg=s96-c",
    private String given_name;//": "lei",
    private String family_name;//": "yan",
    private String iat;//": "**********",
    private String exp;//": "**********",
    private String jti;//": "6014df3c135fc75bfe603d86e04cb284adb20c2b",
    private String alg;//": "RS256",
    private String kid;//": "a50f6e70ef4b548a5fd9142eecd1fb8f54dce9ee",
    private String typ;//": "JWT"


}

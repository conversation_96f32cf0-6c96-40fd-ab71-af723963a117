CREATE TABLE `channel_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `channel_code` varchar(50) NOT NULL COMMENT '渠道代码',
  `channel_id` bigint(20) NOT NULL COMMENT '渠道ID',
  `invite_code` varchar(50) DEFAULT NULL COMMENT '邀请码',
  `download_url` varchar(255) DEFAULT NULL COMMENT '下载地址',
  `qr_code` varchar(255) DEFAULT NULL COMMENT '二维码地址',
  `version` varchar(20) DEFAULT NULL COMMENT '版本',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态 0:禁用 1:启用',
  `created_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` varchar(20) DEFAULT NULL COMMENT '创建人',
  `updated_by` varchar(20) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_channel_code` (`channel_code`),
  UNIQUE KEY `uk_invite_code` (`invite_code`),
  UNIQUE KEY `uk_channel_id` (`channel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='渠道配置表';

-- 插入默认数据
INSERT INTO `channel_config`
(channel_code, channel_id, invite_code, download_url, qr_code, status, created_time, updated_time)
VALUES
('yiki', 1001, 'YIK4ZQ', 'https://s3.x.me/app/channel/yiki.apk', 'https://s3.x.me/app/channel/qrcode_yiki.png', 1, NOW(), NOW()),
('wxkd', 1002, 'WXKD9M', 'https://s3.x.me/app/channel/wxkd.apk', 'https://s3.x.me/app/channel/qrcode_wxkd.png', 1, NOW(), NOW()),
('suke', 1003, 'SUK8JL', 'https://s3.x.me/app/channel/suke.apk', 'https://s3.x.me/app/channel/qrcode_suke.png', 1, NOW(), NOW()),
('falati', 1004, 'XZ3T9Q', 'https://s3.x.me/app/channel/xme_falati.apk', 'https://s3.x.me/app/channel/xme_falati.apk.png', 1, NOW(), NOW()),
('timsea', 1005, 'R7X2JP', 'https://s3.x.me/app/channel/timsea.apk', 'https://s3.x.me/app/channel/timsea.png', 1, NOW(), NOW());

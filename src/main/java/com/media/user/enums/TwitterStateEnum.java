package com.media.user.enums;

import lombok.Getter;

/**
 * twitter 回调 state 值
 */
@Getter
public enum TwitterStateEnum {

    REFRESH_TOKEN("refresh_token"),
    XME_BINDING("xme_binding");

    private String state;

    TwitterStateEnum(String state){
        this.state = state;
    }

    public static String getState(String code) {
        for (TwitterStateEnum entityEnum : TwitterStateEnum.values()) {
            if (entityEnum.getState().equalsIgnoreCase(code)) {
                return entityEnum.getState();
            }
        }
        return XME_BINDING.getState();
    }

}

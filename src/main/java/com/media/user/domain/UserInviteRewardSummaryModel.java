package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 用户邀请奖励汇总
 */
@Data
@Accessors(chain = true)
@TableName("user_invite_reward_summary")
public class UserInviteRewardSummaryModel {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("uid")
    private Long uid;

    /**
     * 货币类型
     */
    @TableField("currency")
    private String currency;

    /**
     * 总奖励数量
     */
    @TableField("total_amount")
    private Long totalAmount;

    /**
     * 总奖励次数
     */
    @TableField("total_count")
    private Integer totalCount;

    /**
     * 最后更新时间
     */
    @TableField("last_updated_time")
    private Date lastUpdatedTime;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private Date createdTime;
} 
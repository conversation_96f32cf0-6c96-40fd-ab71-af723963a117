package com.media.user.enums;

import lombok.Getter;

@Getter
public enum AppReleasePlatformEnum {

    ANDROID("AND<PERSON><PERSON>", "AND<PERSON>ID"),
    IOS("IOS", "IOS"),
    GOOGLEPLAY("ANDROID-GP", "GOOGLEPLAY"),
    ;

    private String code;
    private String type;

    private AppReleasePlatformEnum(String code, String type){
        this.code = code;
        this.type = type;
    }

    public static String getByCode(String code) {
        for (AppReleasePlatformEnum appReleasePlatformEnum : AppReleasePlatformEnum.values()) {
            if (appReleasePlatformEnum.getCode().equalsIgnoreCase(code)) {
                return appReleasePlatformEnum.getType();
            }
        }
        return "";
    }
}

package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("third_platform_user")
@Data
@Accessors(chain = true)
public class ThirdPlatformUserModel extends Model<ThirdPlatformUserModel> {


    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 用户ID
     */
    @TableField("uid")
    private Long uid;
    /**
     * 邮箱用户名
     */
    @TableField("email")
    private String email;

    /**
     * 三方平台唯一ID(google的sub，...)
     */
    @TableField("third_id")
    private String thirdId;

    /**
     * 昵称
     */
    @TableField("display_name")
    private String displayName;

    /**
     * 头像
     */
    @TableField("photo_url")
    private String photoUrl;

    /**
     * 邮件认证状态，0：未认证，1：认证
     */
    @TableField("email_verified")
    private Byte emailVerified;

    /**
     * 平台，1：apple,2:google
     */
    @TableField("platform_type")
    private Byte platformType;

    /**
     * 创建人
     */
    @TableField("created_by")
    private Long createdBy;
    /**
     * 更新人
     */
    @TableField("updated_by")
    private Long updatedBy;
    /**
     * 创建时间
     */
    @TableField("created_time")
    private Date createdTime;
    /**
     * 更新时间
     */
    @TableField("updated_time")
    private Date updatedTime;
}
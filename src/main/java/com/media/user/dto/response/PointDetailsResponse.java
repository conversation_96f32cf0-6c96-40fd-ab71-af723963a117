package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class PointDetailsResponse{

    private Long id;

    private Long uid;

    private Long eventId;

    private BigDecimal pointChangeCount;

    private Byte changeType;

    private String remark;

    private Date createdTime;

    private Date updatedTime;
}
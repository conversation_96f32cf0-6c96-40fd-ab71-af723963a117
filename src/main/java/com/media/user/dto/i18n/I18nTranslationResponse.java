package com.media.user.dto.i18n;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * i18n翻译服务响应DTO
 */
@Data
public class I18nTranslationResponse {
    
    private Integer code;
    private String message;
    private Boolean success;
    private I18nResult result;
    
    @JsonProperty("request_id")
    private String requestId;
    
    @Data
    public static class I18nResult {
        private String namespace;
        private String version;
        private Map<String, String> translations;
        private I18nMetadata metadata;
    }
    
    @Data
    public static class I18nMetadata {
        private Integer count;
        
        @JsonProperty("lastUpdated")
        private String lastUpdated;
    }
}

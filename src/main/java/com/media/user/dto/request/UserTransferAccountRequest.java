package com.media.user.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.jetbrains.annotations.NotNull;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class UserTransferAccountRequest {


    private Long fromUid;

    private Byte type;

    @NotNull
    private BigDecimal amount;

    private String ccy;

    private String network;

    @NotNull
    private Long eventId;

    private Long toUid;

    private String toAddress;

    /**
     * 幂等
     */
    private String idempotent;

    private List<Long> uidList;
}

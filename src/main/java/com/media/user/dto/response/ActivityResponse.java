package com.media.user.dto.response;

import lombok.Data;
import java.util.List;

@Data
public class ActivityResponse {
    private List<ActivityItem> activities;

    @Data
    public static class ActivityItem {
        private String id;          // 活动ID
        private String title;       // 活动标题
        private String description; // 活动描述
        private String icon;        // 图标
        private String jumpType;        // 跳转类型: native/h5
        private String jumpUrl;         // 跳转链接
        private String backgroundImage; // 背景图片地址
        private String statusText; //状态描述
        private boolean clickable;  // 是否可点击
        private Integer status;     // 活动状态
        private Integer order;      // 排序
        private boolean needLogin;  // 是否需要登录
        private boolean needFace;  // 是否需要人脸验证
        private boolean needVerify; //是否需要三选一认证
    }
}

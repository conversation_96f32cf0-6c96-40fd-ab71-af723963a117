package com.media.user.dto.i18n;

import lombok.Data;

import java.util.List;

/**
 * i18n服务配置
 */
@Data
public class I18nServiceConfig {
    
    /**
     * 命名空间配置列表
     */
    private List<NamespaceConfig> namespaces;
    
    @Data
    public static class NamespaceConfig {
        /**
         * 命名空间名称
         */
        private String namespace;
        
        /**
         * 版本号
         */
        private String version;
    }
}

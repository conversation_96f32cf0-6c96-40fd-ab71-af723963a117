package com.media.user.enums;

import lombok.Getter;

/**
 * 奖励类型枚举
 */
@Getter
public enum RewardTypeEnum {
    VERIFICATION(0, "认证奖励"),
    OLD_INVITE(1, "邀请奖励"),
    NEW_INVITE(2, "新邀请奖励");

    private final Integer type;
    private final String desc;

    RewardTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    /**
     * 根据类型获取枚举
     */
    public static RewardTypeEnum getByType(Integer type) {
        for (RewardTypeEnum rewardType : values()) {
            if (rewardType.getType().equals(type)) {
                return rewardType;
            }
        }
        return null;
    }
}

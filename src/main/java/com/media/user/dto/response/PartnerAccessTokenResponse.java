package com.media.user.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class PartnerAccessTokenResponse {
    
    @JsonProperty("code")
    private Integer code;
    
    @JsonProperty("data")
    private AccessTokenData data;
    
    @JsonProperty("msg")
    private String msg;
    
    @JsonProperty("time")
    private Long time;

    @Data
    public static class AccessTokenData {
        @JsonProperty("access_token")
        private String accessToken;
        
        @JsonProperty("token_type")
        private String tokenType = "bearer";
        
        @JsonProperty("expires_in")
        private Long expiresIn;
    }
} 
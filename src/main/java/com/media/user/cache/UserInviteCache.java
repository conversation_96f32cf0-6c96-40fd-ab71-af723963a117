package com.media.user.cache;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.media.core.auth.CloudSession;
import com.media.user.dto.popup.PopupUserInviteInfo;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.media.user.constant.MediaUserConstant.USER_INVITE_LIST;
import static com.media.user.constant.MediaUserConstant.USER_INVITE_POPUP_TYPE;

@Component
@Slf4j
public class UserInviteCache {

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 用户进入邀请页清除弹窗key
     */
    @Async("threadPoolTaskExecutor")
    public void clickPopup(Long uid) {
        String redisValue = stringRedisTemplate.opsForValue().get(String.format(USER_INVITE_POPUP_TYPE, uid));
        if (StrUtil.isNotBlank(redisValue)) {
            UserClickPopup userClickPopup = JSON.parseObject(redisValue, UserClickPopup.class);
            userClickPopup.setClick(true);
            userClickPopup.setData(DateUtil.today());
            stringRedisTemplate.opsForValue().set(String.format(USER_INVITE_POPUP_TYPE, uid), JSON.toJSONString(userClickPopup), 30, TimeUnit.DAYS);
        }
    }

    /**
     * 邀请人成功后设置奖励信息
     *
     * @param popupUserInviteInfo
     */
    @Async("threadPoolTaskExecutor")
    public void setInviteUserCache(PopupUserInviteInfo popupUserInviteInfo) {
        log.info("新用户注册设置弹窗奖励信息:{}", JSON.toJSONString(popupUserInviteInfo));
        String inviteListKey = String.format(USER_INVITE_LIST, popupUserInviteInfo.getHostUid());
        String invitePopupTypeKey = String.format(USER_INVITE_POPUP_TYPE, popupUserInviteInfo.getInviteUid());
        String inviteInfoJson = JSON.toJSONString(popupUserInviteInfo);
        UserClickPopup userClickPopup = new UserClickPopup();
        userClickPopup.setType(3); // 弹窗3
        userClickPopup.setData(DateUtil.today());
        userClickPopup.setClick(false);
        stringRedisTemplate.executePipelined((RedisCallback<?>) (redisConnection) -> {
            byte[] listKeyBytes = stringRedisTemplate.getStringSerializer().serialize(inviteListKey);
            byte[] valueBytes = stringRedisTemplate.getStringSerializer().serialize(inviteInfoJson);
            byte[] popupTypeKeyBytes = stringRedisTemplate.getStringSerializer().serialize(invitePopupTypeKey);
            byte[] popupTypeValueBytes = stringRedisTemplate.getStringSerializer().serialize(JSON.toJSONString(userClickPopup));

            redisConnection.lPush(listKeyBytes, valueBytes);
            redisConnection.expire(listKeyBytes, TimeUnit.DAYS.toSeconds(30));
            redisConnection.setEx(popupTypeKeyBytes, TimeUnit.DAYS.toSeconds(30), popupTypeValueBytes);

            return null;
        });
    }

    /**
     * 1-默认弹框 2-二次弹框 3-新用户弹框
     *
     * @return
     */
    public int getPopupType(Long uid) {
        String key = String.format(USER_INVITE_POPUP_TYPE, uid);
        String redisValue = stringRedisTemplate.opsForValue().get(key);
        UserClickPopup userClickPopup;
        int type = 1;
        String today = DateUtil.today();

        if (StrUtil.isNotBlank(redisValue)) {
            userClickPopup = JSON.parseObject(redisValue, UserClickPopup.class);

            // 当天
            if (Objects.equals(userClickPopup.getData(), today)) {
                return userClickPopup.getType();
            }

            // 非当天
            userClickPopup.setData(today);
            if (userClickPopup.isClick()) {
                userClickPopup.setType(1);
                userClickPopup.setClick(false);
            } else {
                if (userClickPopup.getType() != 1) {
                    type = userClickPopup.getType();
                } else {
                    userClickPopup.setType(2);
                    type = 2;
                }
            }
        } else {
            // Redis无数据，初始化
            userClickPopup = new UserClickPopup();
            userClickPopup.setType(1);
            userClickPopup.setData(today);
            userClickPopup.setClick(false);
        }

        // 只在非当天或无数据时写回
        stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(userClickPopup), 30, TimeUnit.DAYS);
        return type;
    }


    public List<PopupUserInviteInfo> getInviteUserCache(Long uid) {
        String key = String.format(USER_INVITE_LIST, uid);
        List<Object> pipelineResults = stringRedisTemplate.executePipelined(new SessionCallback<Object>() {
            @Override
            @SuppressWarnings("unchecked")
            public Object execute(@NotNull RedisOperations operations) throws DataAccessException {
                operations.opsForList().range(key, 0, -1);
                operations.delete(key);
                return null;
            }
        });

        @SuppressWarnings("unchecked")
        List<String> valueStr = (List<String>) pipelineResults.get(0);  // 第一个操作的结果

        if (CollUtil.isEmpty(valueStr)) {
            return CollUtil.newArrayList();
        }

        return valueStr.stream()
                .map(t -> {
                    try {
                        return JSON.parseObject(t, PopupUserInviteInfo.class);
                    } catch (Exception e) {
                        log.warn("解析PopupUserInviteInfo失败: {}", t, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Data
    public static class UserClickPopup {
        private String data;
        private boolean isClick;
        private int type;
    }
}
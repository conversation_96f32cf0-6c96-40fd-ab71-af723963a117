package com.media.user.enums;

import lombok.Getter;

import java.math.BigDecimal;

@Getter
public enum LevelEnums {

    L0(0,new BigDecimal("0")),
    L1(1, new BigDecimal("1")),
    L2(2, new BigDecimal("1.1")),
    L3(3, new BigDecimal("1.15"));

    private final Integer level;

    private final BigDecimal pointRate;

    private LevelEnums(Integer level, BigDecimal pointRate) {
        this.level = level;
        this.pointRate = pointRate;
    }

}

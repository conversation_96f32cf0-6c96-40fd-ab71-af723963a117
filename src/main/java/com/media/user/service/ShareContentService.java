package com.media.user.service;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.core.config.SystemConfig;
import com.media.core.i18n.I18nConvert;
import com.media.user.domain.*;
import com.media.user.dto.request.SignupRequest;
import com.media.user.dto.request.internal.UnionInfoRequest;
import com.media.user.dto.response.*;
import com.media.user.dto.response.internal.UnionInfoResponse;
import com.media.user.enums.*;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.GameClient;
import com.media.user.feign.client.UserAccountBalanceClient;
import com.media.user.mapper.*;
import com.media.user.service.cache.ClientUserCacheService;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.media.core.i18n.LocalMessageResource;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.security.SecureRandom;
import java.time.LocalDate;
import java.util.*;

@Slf4j
@Service
public class ShareContentService {

    @Autowired
    @Lazy
    private ClientUserService clientUserService;

    @Autowired
    private ShareContentMapper shareContentMapper;

    @Getter
    @Value(value = "${user.shareUrl}")
    private String shareUrl;

    @Value(value = "${user.inviteGenesisUrl}")
    private String inviteGenesisUrl;

    @Autowired
    ConfigSwitchService configSwitchService;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @Autowired
    SystemConfig systemConfig;

    @Autowired
    UserAccountBalanceClient userAccountBalanceClient;

    @Autowired
    ShortUrlService shortUrlService;

    @Autowired
    private GameClient unionClient;


    /**
     *  获取活动分享数据
     */
    public ShareResponse getActivityContent(Long uid, LanguageEnums languageEnums,String activityID) {
        // 查询用户
        ClientUserResponse clientUser = clientUserCacheService.me(uid);
        if (clientUser == null) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        // 构建响应数据
        ShareResponse response = new ShareResponse();

        // 设置用户邀请码
        response.setInvitationCode(clientUser.getInviteCode());

        // 设置分享链接（包含邀请码）
        String longUrl = String.format(this.getShareUrl(), clientUser.getInviteCode(), URLEncoder.encode(clientUser.getNickName())) + "&contentType=A&utmSource=0704";
        String shortUrl = shortUrlService.generateShortUrl(longUrl,Long.toString(uid));
        response.setShareUrl(shortUrl != null ? shortUrl : longUrl);

        // 根据当前日期设置海报类型（每天轮换1和2）
        LocalDate today = LocalDate.now();
        int dayOfMonth = today.getDayOfMonth();
        response.setSharePosterType(dayOfMonth % 2 == 0 ? 1 : 2);

        // 根据当前日期设置分享文案（7天一个轮回）
        int textIndex = (dayOfMonth % 7) + 1;
        String descriptionKey = String.format("activity.social.share.text%d", textIndex);

        response.setSharePromptTitle("share prompt title");

        response.setSharePromptDescription(I18nConvert.getI18nMessage(descriptionKey,languageEnums).replace("{url}",shortUrl));

        return response;
    }

    /**
     *  获取活动分享数据
     */
    public ShareResponse getTaskShareContent(Long uid, LanguageEnums languageEnums) {
        // 查询用户
        ClientUserResponse clientUser = clientUserCacheService.me(uid);
        if (clientUser == null) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        // 构建响应数据
        ShareResponse response = new ShareResponse();


        // 设置用户邀请码
        response.setInvitationCode(clientUser.getInviteCode());

        // 设置分享链接（包含邀请码）

        String longUrl = String.format(this.getShareUrl(), clientUser.getInviteCode(), URLEncoder.encode(clientUser.getNickName())) + "&contentType=A&from=miningRanking";
       // String shortUrl = shortUrlService.generateShortUrl(longUrl,Long.toString(uid));
        response.setShareUrl(longUrl);

        // 设置分享提示标题
        response.setSharePromptTitle(I18nConvert.getI18nMessage("task.share.prompt.title",languageEnums));
        response.setSharePromptDescription(I18nConvert.getI18nMessage("task.share.prompt.description",languageEnums));

        return response;
    }
    /**
     * @param uid
     * @param languageEnums
     * @return
     */
    public Map<String, Object> shareContent(Long uid, LanguageEnums languageEnums) {
        //查询用户
        ClientUserResponse clientUserModel = clientUserCacheService.me(uid);
        if (clientUserModel == null) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        // 审核 app 期间返回固定的文案
        Integer check = configSwitchService.switchConfig();
        // 0 为app 提审状态
        if(check == 0){
            log.info("app is checking");
            Map<String, Object> rtnMap = new HashMap<>();
            rtnMap.put("content", "Come and use XME with me");
            rtnMap.put("contentInvite", "Come and use XME with me");
            rtnMap.put("url", String.format(shareUrl, clientUserModel.getInviteCode(),  URLEncoder.encode(clientUserModel.getNickName())));
            return rtnMap;
        }

        //早鸟活动文案
//        if(clientUserService.earlyBirdSwitch()){
//            log.info("app is early bird switch");
//            Map<String, Object> rtnMap = new HashMap<>();
//            rtnMap.put("content", ShareContentEarlyBirdEnum.getContent(languageEnums.getValue()));
//            rtnMap.put("url", String.format(shareUrl, clientUserModel.getInviteCode()));
//            return rtnMap;
//        }

        //文档内容增加 userTokenNum registerTokenNum 做替换
        String userTokenNum = "0";
        try {
            SignupRequest request = new SignupRequest();
            request.setUid(uid);
            request.setCcy("XME");
            ApiResponse<BigDecimal> response =  userAccountBalanceClient.obtainBalance(request);
            if(response.getSuccess()){
                userTokenNum = response.getResult().setScale(2, RoundingMode.HALF_DOWN).toPlainString();
            }
        }catch (Exception e){
            log.error("获取用户挖XME数量失败", e);
        }
        log.info("get user token xme Num:{}", userTokenNum);
        String contentType = null;
        contentType = this.getRandomMessage(true);
        log.info("分享文案类型: contentType={}", contentType);

        String language = languageEnums.getValue();
        if(StringUtils.isBlank(language)){
            language = LanguageEnums.en_US.getValue();
        }

        //查询分享文案内容
        String shareContent = this.getOneShareContent(language, contentType);
        if (StringUtils.isBlank(shareContent)){
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }
        Map<String, Object> rtnMap = new HashMap<>();

        Integer amountXme = systemConfig.getInvitedXmeAmount() + systemConfig.getFaceXmeAmount();
        String inviteShareUrl = String.format(shareUrl, clientUserModel.getInviteCode(),  URLEncoder.encode(clientUserModel.getNickName()));

        shareContent = getI18nShareContent(language, clientUserModel.getNickName());

        // 生成短链接
        String longUrl = inviteShareUrl + "&contentType=A";
        String shortUrl = longUrl;// shortUrlService.generateShortUrl(longUrl,Long.toString(uid));

        rtnMap.put("content", shareContent);
        rtnMap.put("contentInvite", shareContent + " " + InviteCodeEnum.getContent(language) + clientUserModel.getInviteCode());
        rtnMap.put("url", shortUrl != null ? shortUrl : longUrl);
        log.info("share content {}, longUrl: {}, shortUrl: {}", JSONObject.toJSON(shareContent), longUrl, shortUrl);
        return rtnMap;
    }

    /**
     * 获取邀请分享内容
     */
    public InviteShareResponse getInviteShareContent(Long uid, LanguageEnums languageEnums,String activityID) {
        // 查询用户
        ClientUserResponse clientUser = clientUserCacheService.me(uid);
        if (clientUser == null) {
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        UnionInfoResponse unionInfo = getUnionInviteCode(uid);
        if (unionInfo == null || !UnionStatusEnums.isEffective(unionInfo.getStatus())) {
            // todo 团邀请码失效，或未在联盟中
            throw new ApiException(MediaUserExceptionCodeApi.USER_NOT_EXIST);
        }

        // 构建响应数据
        InviteShareResponse response = new InviteShareResponse();

        // 设置邀请码
        String unionInviteCode = unionInfo.getInviteCode() + clientUser.getInviteCode();
        response.setInvitationCode(unionInviteCode);

        // 设置分享链接（包含邀请码）
        String longUrl = String.format(this.getShareUrl(), unionInviteCode, URLEncoder.encode(clientUser.getNickName())) + "&contentType=A&utmSource=0715";
        String shortUrl = shortUrlService.generateShortUrl(longUrl, Long.toString(uid));
        response.setShareUrl(shortUrl != null ? shortUrl : longUrl);
        switch (UnionStatusEnums.form(unionInfo.getStatus())) {
            case INSUFFICIENT_MEMBERS: {  // 加入联盟 未成团情况
                response.setTitle(I18nConvert.getI18nMessage("share.uion.notcompleted.title",languageEnums));
                response.setDescription(I18nConvert.getI18nMessage("share.uion.notcompleted.description",languageEnums));
                response.setHighlightTitleText("3%");
            }
            case SUCCESSFULLY_FORMED: { // 加入联盟已成团情况
                response.setTitle(I18nConvert.getI18nMessage("share.uion.completed.title",languageEnums));
                response.setDescription(I18nConvert.getI18nMessage("share.uion.completed.description",languageEnums));
                response.setHighlightTitleText("50 XME！");
            }
        }
        return response;
    }

    /**
     * 获取一条分享内容
     * @param language
     * @return
     */
    private String getOneShareContent(String language, String contentType) {
        List<ShareContentModel> listContent = this.queryShareContentList(language, contentType);
        if (CollectionUtils.isEmpty(listContent)) {
            return null;
        }
        int size = listContent.size();
        int index = new SecureRandom().nextInt(size);
        return listContent.get(index).getContentDetail();
    }

    /**
     * 查询所有的指定语言的 分享内容数据
     * @param contentLanguage
     * @param contentType
     * @return
     */
    private List<ShareContentModel> queryShareContentList(String contentLanguage, String contentType){
        QueryWrapper<ShareContentModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("content_language", contentLanguage);
        queryWrapper.eq("content_type", contentType);
        queryWrapper.eq("status", ShareContentEnableEnum.ENABLED.getStatus());
        return shareContentMapper.selectList(queryWrapper);
    }

    /**
     * 返回需要的文档类型
     * @param hasA
     * @return
     */
    public String getRandomMessage(boolean hasA) {
        return ShareContentTypeEnum.CONTENT_TYPE_A.getCode(); // 50%
    }


    /**
     * 从i18n资源获取分享文案，并替换用户昵称
     * @param language 语言代码
     * @param nickname 用户昵称
     * @return 个性化的分享内容
     */
    private String getI18nShareContent(String language, String nickname) {
        // 从i18n资源获取分享文案
        String i18nKey = "share.content.type.b";
        String content = LocalMessageResource.getMessage(i18nKey, new Locale(language.split("-")[0], language.split("-")[1]));

        // 如果没有找到对应的i18n资源，使用默认英文文案
        if (StringUtils.isBlank(content)) {
            content = LocalMessageResource.getMessage(i18nKey, Locale.US);
        }

        // 替换昵称占位符
        return content.replace("{nickname}", nickname).replace("{amount}", "12");
    }


    private UnionInfoResponse getUnionInviteCode(Long uid) {
        if (uid == null) return null;
        ApiResponse<UnionInfoResponse> resp = unionClient.QueryUnionInfo(new UnionInfoRequest().setUid(uid));
        log.info("getUnionInviteCode#QueryUnionInfo:{}", JSON.toJSONString(resp));
        if (resp != null && resp.getSuccess() && resp.getResult() != null) {
            return resp.getResult();
        }
        return null;
    }
}

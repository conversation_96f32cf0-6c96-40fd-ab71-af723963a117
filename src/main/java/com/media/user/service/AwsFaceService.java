package com.media.user.service;

import com.alibaba.fastjson.JSON;
import com.google.common.eventbus.EventBus;
import com.media.core.constant.BeanConstant;
import com.media.core.exception.ApiException;
import com.media.core.exception.BaseException;
import com.media.core.utils.aws.AwsCreateSessionResponse;
import com.media.core.utils.aws.AwsUtils;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.ClientUserModel;
import com.media.user.domain.UserFaceRecognitionLog;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.mapper.ClientUserMapper;
import com.media.user.mapper.UserFaceRecognitionLogMapper;
import com.xme.xme_base_depends.enums.UserBehaviorEventEnum;
import com.xme.xme_base_depends.mq.message.UserFinishEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.rekognition.RekognitionClient;
import software.amazon.awssdk.services.rekognition.model.GetFaceLivenessSessionResultsResponse;
import software.amazon.awssdk.services.rekognition.model.LivenessSessionStatus;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import jakarta.annotation.PreDestroy;
import java.time.Duration;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;

/**
 * AWS人脸识别服务
 */
@Slf4j
@Service
public class AwsFaceService {

    @Autowired
    private AwsUtils awsUtils;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ClientUserMapper clientUserMapper;

    @Autowired
    private UserInviteFaceRecordService userInviteFaceRecordService;

    @Value("${aws.face.session.expiry.hours:1}")
    private int sessionExpiryHours;

    @Value("${aws.face.cache.expiry.days:1}")
    private int cacheExpiryDays;

    @Value("${aws.s3.bucket:xme-face-bucket}")
    private String s3Bucket;

    @Value("${aws.s3.region:ap-southeast-1}")
    private String s3Region;

    @Value("${aws.s3.url.prefix:https://xme-face-bucket.s3.ap-southeast-1.amazonaws.com/test/}")
    private String s3UrlPrefix;

    @Value("${aws.s3.access-key-id:}")
    private String awsAccessKeyId;

    @Value("${aws.s3.secret-access-key:}")
    private String awsSecretAccessKey;

    @Value("${aws.face.s3.prefix:test/}")
    private String s3FacePrefix;

    @Value("${aws.face.save:false}")
    private boolean needSaveFace;

    // 用于异步上传的线程池
    private final ScheduledExecutorService executorService;

    // 共享的S3客户端
    private S3Client s3Client;

    @Autowired
    private UserFaceRecognitionLogMapper faceRecognitionLogMapper;

    @Autowired
    @Qualifier(value = BeanConstant.USER_EVENT_BUS_BEAN)
    private EventBus userEventBus;

    /**
     * 构造函数，初始化线程池和S3客户端
     */
    public AwsFaceService() {
        this.executorService = Executors.newScheduledThreadPool(5);
    }

    @jakarta.annotation.PostConstruct
    public void init() {
        this.s3Client = createS3Client();
    }

    /**
     * 应用关闭时释放资源
     */
    @PreDestroy
    public void destroy() {
        if (executorService != null) {
            executorService.shutdown();
        }
        if (s3Client != null) {
            s3Client.close();
        }
    }

    /**
     * AWS人脸识别缓存键前缀
     */
    private static final String AWS_FACE_SESSION_KEY = "aws:face:session:key:";
    private static final String USER_FACE_CACHE_KEY = "user:face:status:";

    /**
     * 创建人脸识别会话
     *
     * @param uid     用户ID
     * @param country 国家/地区代码，用于确定AWS区域
     * @return AWS会话响应
     * @throws ApiException 如果会话创建失败
     */
    public AwsCreateSessionResponse createSession(Long uid, String country) {
        if (uid == null) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }

        Region region = getRegionFromCountry(country);
        log.info("为用户 {} 创建AWS人脸识别会话，区域: {}", uid, region);

        try {
            AwsCreateSessionResponse response = awsUtils.createSession(region);
            if (response == null || response.getSessionId() == null) {
                log.error("无法为用户 {} 创建有效的会话", uid);
                throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
            }

            // 将sessionID存储到Redis中，用于后续查询
            String cacheKey = AWS_FACE_SESSION_KEY + uid;
            stringRedisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(response), sessionExpiryHours,
                    TimeUnit.HOURS);
            log.info("用户 {} 的会话ID已存储在Redis中, 键: {}", uid, cacheKey);

            return response;
        } catch (ApiException e) {
            log.error("创建用户 {} 的AWS会话时发生基础异常: {}", uid, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("创建用户 {} 的AWS会话时发生异常: {}", uid, e.getMessage(), e);
            throw new ApiException(MediaUserExceptionCodeApi.AWS_SESSION_CREATE_ERROR);
        }
    }

    /**
     * 获取用户人脸识别结果
     *
     * @param uid 用户ID
     * @return 人脸识别结果
     */
    public Float getFaceResult(Long uid) {
        log.info("开始处理用户 {} 的人脸识别结果", uid);

        if (uid == null) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }

        // 验证用户人脸状态
        boolean hasValidate = isValidateUserFace(uid);
        if (hasValidate) {
            return 95.0f;
        }

        // 创建日志对象
        UserFaceRecognitionLog recognitionLog = new UserFaceRecognitionLog();
        recognitionLog.setUid(uid);
        recognitionLog.setCreatedTime(new Date());

        Float confidence = 0.0f;
        Map<String, Object> faceCheckResult = null;

        try {
            // 验证用户并获取会话信息
            AwsCreateSessionResponse response = getValidatedSessionResponse(uid);

            RekognitionClient client = null;
            try {
                client = awsUtils.getRekognitionClient(response);
                // 获取人脸活体检测结果
                GetFaceLivenessSessionResultsResponse faceLiveSession = awsUtils.getFaceLiveSession(response, client);
                if (faceLiveSession == null) {
                    throw new BaseException("无法获取人脸活体检测结果");
                }

                if (!LivenessSessionStatus.SUCCEEDED.equals(faceLiveSession.status())) {
                    log.info("活体检测失败: {}", response);
                    throw new ApiException(MediaUserExceptionCodeApi.AWS_LIVENESS_CHECK_FAILED);
                }

                confidence = faceLiveSession.confidence();
                log.info("用户 {} 的人脸活体检测置信度: {}", uid, confidence);

                // 设置置信度
                recognitionLog.setConfidence(confidence);

                // 提取人脸特征并校验
                if (faceLiveSession.referenceImage() == null) {
                    log.error("用户 {} 的人脸活体检测结果 referenceImage 为空，响应内容: {}", uid, faceLiveSession);
                    throw new ApiException(MediaUserExceptionCodeApi.AWS_LIVENESS_CHECK_FAILED,
                            "referenceImage is null");
                }

                SdkBytes referenceImage = faceLiveSession.referenceImage().bytes();

                if (faceLiveSession.confidence() < awsUtils.getLivenessThreshold()) {
                    if (needSaveFace) {
                        // 异步上传人脸图片
                        CompletableFuture.runAsync(() -> {
                            try {
                                saveReferenceImageToS3Async(uid, referenceImage, "low" + UUID.randomUUID().toString(),
                                        true);
                            } catch (Exception e) {
                                log.error("异步上传用户 {} 的人脸图片时出错: {}", uid, e.getMessage(), e);
                            }
                        }, executorService);
                    }
                    log.info("活体检测置信度过低（得分: {}, 阈值:{}", faceLiveSession.confidence(), awsUtils.getLivenessThreshold());
                    throw new ApiException(MediaUserExceptionCodeApi.AWS_LIVENESS_CHECK_FAILED);
                }

                faceCheckResult = awsUtils.isDuplicateFace(referenceImage, response, client);

                // 检查是否存在重复人脸
                if ((boolean) faceCheckResult.get("isDuplicate")) {

                    Float similarity = (Float) faceCheckResult.get("similarity");
                    String matchedFaceId = (String) faceCheckResult.get("faceId");
                    if (needSaveFace) {
                        // 异步上传人脸图片
                        CompletableFuture.runAsync(() -> {
                            try {
                                saveReferenceImageToS3Async(uid, referenceImage, "dup" + matchedFaceId, true);
                            } catch (Exception e) {
                                log.error("异步上传用户 {} 的人脸图片时出错: {}", uid, e.getMessage(), e);
                            }
                        }, executorService);
                    }
                    log.warn("用户 {} 的人脸已存在于系统中，匹配度: {}%, 匹配的人脸ID: {}", uid, similarity, matchedFaceId);
                    throw new ApiException(MediaUserExceptionCodeApi.AWS_FACE_DUPLICATE);
                }

                // 提取并存储人脸特征向量
                String faceId = awsUtils.extractFaceVector(referenceImage, response, uid, client);
                log.info("用户 {} 的人脸ID已成功提取: {}", uid, faceId);

                // 更新用户人脸识别状态(数据库和缓存)
                updateUserFaceStatus(uid, faceId);

                userEventBus.post(new UserFinishEvent( UserBehaviorEventEnum.FACE_FINISH,uid));

                // 记录成功日志
                recognitionLog.setResult(1);
                recognitionLog.setCode(0);
                // 异步保存人脸图片到S3
                if (needSaveFace) {
                    // 异步上传人脸图片
                    CompletableFuture.runAsync(() -> {
                        try {
                            saveReferenceImageToS3Async(uid, referenceImage, faceId, false);
                        } catch (Exception e) {
                            log.error("异步上传用户 {} 的人脸图片时出错: {}", uid, e.getMessage(), e);
                        }
                    }, executorService);
                }
                // 记录成功日志
                faceRecognitionLogMapper.insert(recognitionLog);

                // * 发送完成邀请新人的任务
                userInviteFaceRecordService.handleFirstVerification(uid);
            } finally {
                // 确保客户端使用后被关闭
                if (client != null) {
                    try {
                        client.close();
                        log.debug("已关闭RekognitionClient");
                    } catch (Exception e) {
                        log.warn("关闭RekognitionClient时发生异常: {}", e.getMessage());
                    }
                }
            }
        } catch (ApiException e) {
            // 记录失败日志 - API异常
            Integer failedReason = 3;
            String failDetail = e.getMessage();

            // 重复人脸错误，增加相似度和匹配的face_id信息
            if (e.getCode().equals(MediaUserExceptionCodeApi.AWS_FACE_DUPLICATE)) {
                failedReason = 1;

                // 如果本地变量中有人脸检查结果，则使用其添加详细信息
                if (faceCheckResult != null && (boolean) faceCheckResult.get("isDuplicate")) {
                    Float similarity = (Float) faceCheckResult.get("similarity");
                    String matchedFaceId = (String) faceCheckResult.get("faceId");
                    failDetail = String.format("%s 匹配度: %.2f%%, 匹配的人脸ID: %s",
                            e.getMessage(), similarity, matchedFaceId);
                }
            }

            if (e.getCode().equals(MediaUserExceptionCodeApi.AWS_LIVENESS_CHECK_FAILED)) {
                failedReason = 2;
            }

            recognitionLog.setResult(0);
            recognitionLog.setFailReason(failedReason);
            recognitionLog.setCode(e.getCode());
            recognitionLog.setFailDetail(failDetail);
            faceRecognitionLogMapper.insert(recognitionLog);
            log.error("处理用户 {} 的人脸结果时发生API异常: {}", uid, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            // 记录失败日志 - 其他异常
            recognitionLog.setResult(0);
            recognitionLog.setFailReason(3);
            recognitionLog.setCode(1);
            recognitionLog.setFailDetail(e.getMessage());
            faceRecognitionLogMapper.insert(recognitionLog);
            log.error("处理用户 {} 的人脸结果时发生异常: {}", uid, e.getMessage(), e);
            throw new ApiException(MediaUserExceptionCodeApi.AWS_LIVENESS_CHECK_FAILED);
        }
        return confidence;
    }

    /**
     * 获取并验证用户的会话响应信息
     *
     * @param uid 用户ID
     * @return 验证后的会话响应对象
     * @throws ApiException 如果验证失败
     */
    private AwsCreateSessionResponse getValidatedSessionResponse(Long uid) {
        // 从Redis获取会话信息
        String cacheKey = AWS_FACE_SESSION_KEY + uid;
        String json = stringRedisTemplate.opsForValue().get(cacheKey);
        if (json == null || json.isEmpty()) {
            log.error("用户 {} 的会话信息不存在", uid);
            throw new ApiException(MediaUserExceptionCodeApi.AWS_AUTH_ERROR);
        }

        try {
            AwsCreateSessionResponse response = JSON.parseObject(json, AwsCreateSessionResponse.class);
            if (response.getSessionId() == null || response.getAccessKeyId() == null) {
                log.error("用户 {} 的会话信息不完整", uid);
                throw new ApiException(MediaUserExceptionCodeApi.AWS_AUTH_ERROR);
            }
            return response;
        } catch (Exception e) {
            log.error("解析用户 {} 的会话信息时出错: {}", uid, e.getMessage(), e);
            throw new ApiException(MediaUserExceptionCodeApi.AWS_SERVICE_ERROR);
        }
    }

    /**
     * 验证用户人脸识别状态
     *
     * @param userId 用户ID
     * @throws BaseException 如果用户已完成人脸认证
     */
    private boolean isValidateUserFace(Long userId) {
        // 1. 先查Redis缓存
        String cacheKey = USER_FACE_CACHE_KEY + userId;
        String authStatus = stringRedisTemplate.opsForValue().get(cacheKey);
        // 2. 缓存存在时验证状态
        if (StringUtils.isNotBlank(authStatus)) {
            try {
                if (1 == Integer.parseInt(authStatus)) {
                    log.warn("用户已认证过: {}", userId);
                    return true;
                }
            } catch (NumberFormatException e) {
                log.warn("用户 {} 的缓存状态格式不正确: {}", userId, authStatus);
            }
        }

        // 3. 缓存未命中或无效时查数据库
        ClientUserModel user = clientUserMapper.selectByUserId(userId);
        if (user == null) {
            throw new ApiException(MediaUserExceptionCodeApi.AWS_AUTH_ERROR);
        } else if (1 == user.getFaceLivenessStatus()) {
            // 4. 更新缓存并验证状态
            stringRedisTemplate.opsForValue().set(cacheKey, String.valueOf(user.getFaceLivenessStatus()),
                    cacheExpiryDays, TimeUnit.DAYS);
            log.warn("用户已认证过from db: {}", userId);
            return true;
        }

        return false;
    }

    /**
     * 更新用户人脸识别状态
     *
     * @param uid            用户ID
     * @param referenceImage 人脸参考图片
     * @return S3图片URL
     */
    private String saveReferenceImageToS3Async(Long uid, SdkBytes referenceImage, String faceId, boolean failed) {
        try {
            if (this.s3Client != null) {
                // 生成唯一的文件名
                String key = s3FacePrefix + "faces/" + uid + "/" + faceId + ".jpg";
                if (failed) {
                    key = s3FacePrefix + "failed_faces/" + uid + "/" + faceId + ".jpg";
                }

                // 准备请求
                PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                        .bucket(s3Bucket)
                        .key(key)
                        .contentType("image/jpeg")
                        .build();

                // 上传文件
                byte[] imageBytes = referenceImage.asByteArray();
                RequestBody requestBody = RequestBody.fromBytes(imageBytes);

                PutObjectResponse response = s3Client.putObject(putObjectRequest, requestBody);
                log.info("用户 {} 的人脸图片异步上传成功，ETag: {}", uid, response.eTag());

                // 返回URL
                return s3UrlPrefix + key;
            }

        } catch (Exception e) {
            log.error("异步保存用户 {} 的人脸图片时出错: {}", uid, e.getMessage(), e);

        }
        return null;
    }

    /**
     * 保存识别日志并返回日志ID
     *
     * @param recognitionLog 识别日志
     * @return 日志ID
     */
    private Long saveRecognitionLog(UserFaceRecognitionLog recognitionLog) {
        recognitionLog.setResult(1);
        recognitionLog.setCode(0);
        faceRecognitionLogMapper.insert(recognitionLog);
        return recognitionLog.getId();
    }

    /**
     * 创建S3客户端
     *
     * @return S3客户端
     */
    private S3Client createS3Client() {
        try {
            // 直接使用配置的AWS凭证
            AwsBasicCredentials credentials = AwsBasicCredentials.create(
                    awsAccessKeyId,
                    awsSecretAccessKey);

            // 创建S3客户端并配置连接池
            // 使用共享的HTTP客户端配置来防止线程泄漏
            return S3Client.builder()
                    .region(Region.of(s3Region))
                    .credentialsProvider(StaticCredentialsProvider.create(credentials))
                    .httpClient(ApacheHttpClient.builder()
                            .connectionTimeout(Duration.ofSeconds(10))
                            .socketTimeout(Duration.ofSeconds(30))
                            .connectionMaxIdleTime(Duration.ofSeconds(60))
                            .maxConnections(50)
                            .build())
                    .build();
        } catch (Exception e) {
            log.error("创建S3客户端时出错: {}", e.getMessage(), e);
            // throw new ApiException(MediaUserExceptionCodeApi.AWS_SERVICE_ERROR);
        }
        return null;
    }

    public void updateUserFaceStatus(Long uid, String faceId) {
        try {
            // 更新数据库
            Date now = new Date();
            long rowsAffected = clientUserMapper.modifyFaceStatus(1, uid, now, faceId);
            if (rowsAffected == 0) {
                log.warn("用户 {} 的人脸状态更新失败，可能用户不存在", uid);
            }

            // 更新缓存
            String cacheKey = USER_FACE_CACHE_KEY + uid;
            stringRedisTemplate.opsForValue().set(cacheKey, "1", cacheExpiryDays, TimeUnit.DAYS);
            log.info("用户 {} 的人脸状态已更新，缓存键: {}", uid, cacheKey);

            // 删除个人信息缓存
            stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + uid);

        } catch (Exception e) {
            log.error("更新用户 {} 的人脸状态时出错: {}", uid, e.getMessage(), e);
            // 不抛出异常，因为人脸识别本身已成功
        }
    }

    public void delFace(Long uid, String faceId) {
        AwsCreateSessionResponse response = null;

        String cacheKey = "aws:face:session:key:" + uid;
        String json = stringRedisTemplate.opsForValue().get(cacheKey);
        if (json != null && !json.isEmpty()) {
            response = JSON.parseObject(json, AwsCreateSessionResponse.class);
        }
        if (response == null) {
            response = createSession(uid, "");
        }

        if (StringUtils.isBlank(faceId)) {
            ClientUserModel user = clientUserMapper.selectByUserId(uid);
            faceId = user.getFaceLivenessId();
            if (StringUtils.isBlank(faceId)) {
                return;
            }
        }

        RekognitionClient client = null;
        try {
            client = awsUtils.getRekognitionClient(response);
            awsUtils.delFace(client, response, List.of(faceId));
        } finally {
            // 确保客户端被关闭
            if (client != null) {
                try {
                    client.close();
                    log.debug("已关闭RekognitionClient");
                } catch (Exception e) {
                    log.warn("关闭RekognitionClient时发生异常: {}", e.getMessage());
                }
            }
        }
    }

    public void delCollection(Long uid) {
        AwsCreateSessionResponse response = null;

        String cacheKey = "aws:face:session:key:" + uid;
        String json = stringRedisTemplate.opsForValue().get(cacheKey);
        if (json != null && !json.isEmpty()) {
            response = JSON.parseObject(json, AwsCreateSessionResponse.class);
        }
        if (response == null) {
            response = createSession(uid, "");
        }

        RekognitionClient client = null;
        try {
            client = awsUtils.getRekognitionClient(response);
            awsUtils.delCollection(client);
            awsUtils.createCollection(client);
        } finally {
            // 确保客户端被关闭
            if (client != null) {
                try {
                    client.close();
                    log.debug("已关闭RekognitionClient");
                } catch (Exception e) {
                    log.warn("关闭RekognitionClient时发生异常: {}", e.getMessage());
                }
            }
        }
    }

    public String getCollection() {
        return awsUtils.getCollectionId();
    }

    /**
     * 根据国家/地区代码确定AWS区域
     */
    private Region getRegionFromCountry(String country) {
        if (country == null || country.isEmpty()) {
            return Region.US_EAST_1;
        }

        // 使用映射表代替switch
        Map<String, Region> regionMap = Map.ofEntries(
                // 中国 -> 香港
                Map.entry("CN", Region.AP_EAST_1),
                // 新加坡
                Map.entry("SG", Region.AP_SOUTHEAST_1),
                // 马来西亚
                Map.entry("MY", Region.AP_SOUTHEAST_1),
                // 印度尼西亚
                Map.entry("ID", Region.AP_SOUTHEAST_1),
                // 泰国
                Map.entry("TH", Region.AP_SOUTHEAST_1),
                // 日本 -> 东京
                Map.entry("JP", Region.AP_NORTHEAST_1),
                // 澳大利亚 -> 悉尼
                Map.entry("AU", Region.AP_SOUTHEAST_2),
                // 欧洲 -> 法兰克福
                Map.entry("EU", Region.EU_CENTRAL_1),
                // 德国
                Map.entry("DE", Region.EU_CENTRAL_1),
                // 法国
                Map.entry("FR", Region.EU_CENTRAL_1),
                // 意大利
                Map.entry("IT", Region.EU_CENTRAL_1),
                // 西班牙
                Map.entry("ES", Region.EU_CENTRAL_1),
                // 英国 -> 伦敦
                Map.entry("GB", Region.EU_WEST_2),
                // 英国别名
                Map.entry("UK", Region.EU_WEST_2));

        return regionMap.getOrDefault(country.toUpperCase(), Region.US_EAST_1);
    }
}

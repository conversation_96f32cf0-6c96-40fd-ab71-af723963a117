package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("user_family_config")
@Data
@Accessors(chain = true)
public class UserFamilyConfigModel extends Model<UserFamilyConfigModel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("level1_rate")
    private BigDecimal level1Rate;

    @TableField("level2_rate")
    private BigDecimal level2Rate;

    @TableField("level3_rate")
    private BigDecimal level3Rate;

    @TableField("level2_min_num")
    private Integer level2MinNum;

    @TableField("super_level2_min_num")
    private Integer superLevel2MinNum;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;

    @TableField("created_by")
    private Long createdBy;

    @TableField("updated_by")
    private Long updatedBy;

}
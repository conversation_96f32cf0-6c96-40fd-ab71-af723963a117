package com.media.user.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.core.exception.ApiException;
import com.media.user.domain.*;
import com.media.user.dto.request.*;
import com.media.user.dto.response.InviterInfoResponse;
import com.media.user.feign.client.RiskClient;
import org.springframework.scheduling.annotation.Async;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.mapper.ActivityInviteRelationMapper;
import com.media.user.mapper.ClientUserMapper;
import com.media.user.mapper.ChannelConfigMapper;
import com.media.user.tools.PhoneUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.media.core.utils.RedisUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ActivityInviteRelationService {

    @Autowired
    private ClientUserMapper clientUserMapper;

    @Autowired
    PhoneUtils phoneUtils;

    @Autowired
    ActivityInviteRelationMapper activityInviteRelationMapper;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private ChannelConfigMapper channelConfigMapper;

    @Autowired
    private RiskClient riskClient;

    @Value("${invite.anti.abuse.ip.limit:20}")
    private int ipLimitPerHour;

    @Value("${invite.anti.abuse.email.limit:5}")
    private int emailLimitPerDay;

    @Value("${invite.anti.abuse.phone.limit:5}")
    private int phoneLimitPerDay;

    /**
     * 用户邀请关系绑定，并带有防刷机制
     * 
     * @param request  邀请绑定请求
     * @param clientIp 客户端IP地址，用于防刷
     */
    @Transactional(rollbackFor = Exception.class)
    public void inviteBind(UserInviteSaveRequest request, String clientIp) {
        // 参数校验
        if (request == null || StringUtils.isBlank(request.getInviteCode())) {
            log.error("inviteBind request invalid: {}", request);
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }

        // 检查IP防刷限制
        checkIpRateLimit(clientIp);

        // 检查邮箱/手机号防刷限制
        if (StringUtils.isNotBlank(request.getEmail())) {
            checkEmailRateLimit(request.getEmail());
        } else if (StringUtils.isNotBlank(request.getPhone())) {
            checkPhoneRateLimit(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());
        }

        // 校验邀请码有效性
        if (isIgnoreInviteCode(request.getInviteCode())) {
            // 如果是系统预设的渠道邀请码，直接通过
            log.info("Using system channel invite code: {}", request.getInviteCode());
        } else {
            // 否则检查是否是有效的用户邀请码
            if (request.getInviteCode() != null) {
                String currentInviteCode = request.getInviteCode();
                if (currentInviteCode.length() == 10) {
                    currentInviteCode = currentInviteCode.substring(4);
                }
                QueryWrapper<ClientUserModel> queryInviteWrapper = new QueryWrapper<>();
                queryInviteWrapper.lambda().eq(ClientUserModel::getInviteCode, currentInviteCode);
                queryInviteWrapper.lambda().eq(ClientUserModel::getStatus, 1);
                Long count = clientUserMapper.selectCount(queryInviteWrapper);
                // TODO caoshuo 绑定机制是否还用
                if (count == null || count == 0) {
                    log.warn("Invalid invite code: {}", request.getInviteCode());
                    throw new ApiException(MediaUserExceptionCodeApi.INVITE_CODE_INVALID);
                }
            }

        }

        if (StringUtils.isNotBlank(request.getEmail())) {
            log.info("inviteBind by email: {} with invite code: {}, utmSource: {}", request.getEmail(),
                    request.getInviteCode(), request.getUtmSource());

            // 校验邮箱是否已注册
            // checkEmail(request.getEmail());

            try {
                // 查询该邮箱的现有绑定记录
                QueryWrapper<ActivityInviteRelationModel> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("email", request.getEmail());

                // 删除该邮箱的所有现有绑定记录，只保留最新的
                int deleted = activityInviteRelationMapper.delete(queryWrapper);
                if (deleted > 0) {
                    log.info("Deleted {} existing email binding records for: {}", deleted, request.getEmail());
                }

                // 创建新的绑定记录
                ActivityInviteRelationModel relationModel = new ActivityInviteRelationModel();
                relationModel.setEmail(request.getEmail());
                relationModel.setInviteCode(request.getInviteCode());
                relationModel.setUtmSource(request.getUtmSource());
                relationModel.setCreatedTime(new Date());
                int inserted = activityInviteRelationMapper.insert(relationModel);

                if (inserted <= 0) {
                    log.error("Failed to insert new email binding record for: {}", request.getEmail());
                    throw new RuntimeException("Failed to create new email binding record");
                }

                log.info("Created new email binding record: {} -> {}, result: {}", request.getEmail(),
                        request.getInviteCode(), inserted > 0);
            } catch (Exception e) {
                log.error("Transaction failed during email binding process: {}", e.getMessage(), e);
                throw e; // 确保事务回滚
            }

        } else if (StringUtils.isNotBlank(request.getPhone())) {
            if (StringUtils.isBlank(request.getCountryCode()) || StringUtils.isBlank(request.getPhonePrefix())) {
                log.error("Phone information incomplete");
                throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
            }

            log.info("inviteBind by phone: {}-{}-{} with invite code: {}, utmSource: {}",
                    request.getCountryCode(), request.getPhonePrefix(), request.getPhone(), request.getInviteCode(),
                    request.getUtmSource());

            // 校验手机号格式
            phoneUtils.checkPhoneRegex(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());

            try {
                // 查询该手机号的现有绑定记录
                QueryWrapper<ActivityInviteRelationModel> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("country_code", request.getCountryCode());
                queryWrapper.eq("phone_prefix", request.getPhonePrefix());
                queryWrapper.eq("phone", request.getPhone());

                // 删除该手机号的所有现有绑定记录，只保留最新的
                int deleted = activityInviteRelationMapper.delete(queryWrapper);
                if (deleted > 0) {
                    log.info("Deleted {} existing phone binding records for: {}-{}-{}",
                            deleted, request.getCountryCode(), request.getPhonePrefix(), request.getPhone());
                }

                // 创建新的绑定记录
                ActivityInviteRelationModel relationModel = new ActivityInviteRelationModel();
                relationModel.setCountryCode(request.getCountryCode());
                relationModel.setPhonePrefix(request.getPhonePrefix());
                relationModel.setPhone(request.getPhone());
                relationModel.setInviteCode(request.getInviteCode());
                relationModel.setUtmSource(request.getUtmSource());
                relationModel.setCreatedTime(new Date());
                int inserted = activityInviteRelationMapper.insert(relationModel);

                if (inserted <= 0) {
                    log.error("Failed to insert new phone binding record for: {}-{}-{}",
                            request.getCountryCode(), request.getPhonePrefix(), request.getPhone());
                    throw new RuntimeException("Failed to create new phone binding record");
                }

                log.info("Created new phone binding record: {}-{}-{} -> {}, result: {}",
                        request.getCountryCode(), request.getPhonePrefix(), request.getPhone(),
                        request.getInviteCode(), inserted > 0);
            } catch (Exception e) {
                log.error("Transaction failed during phone binding process: {}", e.getMessage(), e);
                throw e; // 确保事务回滚
            }
        } else {
            log.error("inviteBind missing both email and phone");
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
    }

    private Triple<Boolean, String, String> parseUnionInviteCode(String inviteCode) {
        if (StringUtils.isNotBlank(inviteCode) && inviteCode.length() == 10) {
            String unionInviteCode = inviteCode.substring(0, 4);
            String useCode = inviteCode.substring(4);
            return Triple.of(true, useCode, unionInviteCode);
        }
        return Triple.of(false, inviteCode, null);
    }

    public String getInviteCodeByEmail(String email) {
        QueryWrapper<ActivityInviteRelationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email);
        ActivityInviteRelationModel activityInviteRelation = activityInviteRelationMapper.selectOne(queryWrapper);
        return activityInviteRelation == null ? null : activityInviteRelation.getInviteCode();
    }

    public String getInviteCodeByPhone(String countryCode, String phonePrefix, String phone) {
        QueryWrapper<ActivityInviteRelationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("country_code", countryCode);
        queryWrapper.eq("phone_prefix", phonePrefix);
        queryWrapper.eq("phone", phone);
        ActivityInviteRelationModel activityInviteRelation = activityInviteRelationMapper.selectOne(queryWrapper);
        return activityInviteRelation == null ? null : activityInviteRelation.getInviteCode();
    }

    private void checkEmail(String email) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email);
        ClientUserModel clientUserModel = clientUserMapper.selectOne(queryWrapper);
        if (clientUserModel != null) {
            throw new ApiException(MediaUserExceptionCodeApi.INVITE_USER_REGISTERED_ERROR);
        }
    }

    private void checkPhone(String countryCode, String phonePrefix, String phone) {
        QueryWrapper<ClientUserModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("country_code", countryCode);
        queryWrapper.eq("phone_prefix", phonePrefix);
        queryWrapper.eq("phone", phone);
        ClientUserModel clientUserModel = clientUserMapper.selectOne(queryWrapper);
        if (clientUserModel != null) {
            throw new ApiException(MediaUserExceptionCodeApi.INVITE_USER_REGISTERED_ERROR);
        }
    }

    /**
     * 根据邮箱查询邀请人信息
     * 
     * @param email 被邀请人邮箱
     * @return 邀请人信息，如果没有邀请关系则返回null
     */
    public InviterInfoResponse getInviterInfoByEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return null;
        }

        // 查询邮箱关联的邀请关系
        QueryWrapper<ActivityInviteRelationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("email", email);
        ActivityInviteRelationModel relationModel = activityInviteRelationMapper.selectOne(queryWrapper);

        if (relationModel == null || StringUtils.isBlank(relationModel.getInviteCode())) {
            return null;
        }

        return getInviterInfoByInviteCode(relationModel.getInviteCode(), relationModel.getRelationId());
    }

    /**
     * 根据手机号查询邀请人信息
     * 
     * @param countryCode 国家代码
     * @param phonePrefix 手机区号
     * @param phone       手机号
     * @return 邀请人信息，如果没有邀请关系则返回null
     */
    public InviterInfoResponse getInviterInfoByPhone(String countryCode, String phonePrefix, String phone) {
        if (StringUtils.isBlank(countryCode) || StringUtils.isBlank(phonePrefix) || StringUtils.isBlank(phone)) {
            return null;
        }

        // 查询手机号关联的邀请关系
        QueryWrapper<ActivityInviteRelationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("country_code", countryCode);
        queryWrapper.eq("phone_prefix", phonePrefix);
        queryWrapper.eq("phone", phone);
        ActivityInviteRelationModel relationModel = activityInviteRelationMapper.selectOne(queryWrapper);

        if (relationModel == null || StringUtils.isBlank(relationModel.getInviteCode())) {
            return null;
        }

        return getInviterInfoByInviteCode(relationModel.getInviteCode(), relationModel.getRelationId());
    }

    /**
     * 根据邀请码查询邀请人信息
     * 
     * @param inviteCode 邀请码
     * @param relationId 邀请关系 ID
     * @return 邀请人信息
     */
    private InviterInfoResponse getInviterInfoByInviteCode(String inviteCode, Long relationId) {
        // 查询邀请码对应的用户
        String currentInviteCode = inviteCode;
        if (!StringUtils.isBlank(currentInviteCode) && currentInviteCode.length() == 10) {
            currentInviteCode = currentInviteCode.substring(4);
        }
        QueryWrapper<ClientUserModel> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("invite_code", currentInviteCode);
        userQueryWrapper.eq("status", 1); // 确保用户有效
        ClientUserModel inviter = clientUserMapper.selectOne(userQueryWrapper);
        if (inviter == null) {
            return null;
        }

        // 构建响应对象
        InviterInfoResponse response = new InviterInfoResponse();
        response.setInviterUserId(inviter.getUid());
        response.setInviterUsername(inviter.getNickName());
        response.setInviterInviteCode(inviteCode);
        response.setRelationId(relationId);

        return response;
    }

    /**
     * 检查IP频率限制
     * 
     * @param clientIp 客户端IP地址
     */
    private void checkIpRateLimit(String clientIp) {
        if (StringUtils.isBlank(clientIp)) {
            log.warn("Client IP is empty, skipping IP rate limit check");
            return;
        }

        String key = "invite:ip:limit:" + clientIp;
        Long count = redisUtils.getCount(key);

        if (count >= ipLimitPerHour) {
            log.warn("IP {} has exceeded rate limit: {} requests per hour", clientIp, ipLimitPerHour);
            throw new ApiException(MediaUserExceptionCodeApi.INVITE_IP_RATE_LIMIT);
        }

        // 增加计数并设置1小时过期
        redisUtils.incrementCount(key, 1L, java.util.concurrent.TimeUnit.HOURS);
        log.debug("IP {} request count: {} in the last hour", clientIp, count + 1);
    }

    /**
     * 检查邮箱频率限制
     * 
     * @param email 邮箱地址
     */
    private void checkEmailRateLimit(String email) {
        if (StringUtils.isBlank(email)) {
            return;
        }

        String key = "invite:email:limit:" + email;
        Long count = redisUtils.getCount(key);

        if (count >= emailLimitPerDay) {
            log.warn("Email {} has exceeded rate limit: {} requests per day", email, emailLimitPerDay);
            throw new ApiException(MediaUserExceptionCodeApi.INVITE_EMAIL_RATE_LIMIT);
        }

        // 增加计数并设置24小时过期
        redisUtils.incrementCount(key, 24L, java.util.concurrent.TimeUnit.HOURS);
        log.debug("Email {} request count: {} in the last 24 hours", email, count + 1);
    }

    /**
     * 检查手机号频率限制
     * 
     * @param countryCode 国家代码
     * @param phonePrefix 手机区号
     * @param phone       手机号
     */
    private void checkPhoneRateLimit(String countryCode, String phonePrefix, String phone) {
        if (StringUtils.isBlank(countryCode) || StringUtils.isBlank(phonePrefix) || StringUtils.isBlank(phone)) {
            return;
        }

        String phoneKey = countryCode + ":" + phonePrefix + ":" + phone;
        String key = "invite:phone:limit:" + phoneKey;
        Long count = redisUtils.getCount(key);

        if (count >= phoneLimitPerDay) {
            log.warn("Phone {}-{}-{} has exceeded rate limit: {} requests per day",
                    countryCode, phonePrefix, phone, phoneLimitPerDay);
            throw new ApiException(MediaUserExceptionCodeApi.INVITE_PHONE_RATE_LIMIT);
        }

        // 增加计数并设置24小时过期
        redisUtils.incrementCount(key, 24L, java.util.concurrent.TimeUnit.HOURS);
        log.debug("Phone {}-{}-{} request count: {} in the last 24 hours",
                countryCode, phonePrefix, phone, count + 1);
    }

    private List<String> getIgnoreInviteCodes() {
        List<ChannelConfigModel> configs = channelConfigMapper.selectList(
                new QueryWrapper<ChannelConfigModel>()
                        .select("invite_code")
                        .eq("status", 1));
        return configs.stream()
                .map(ChannelConfigModel::getInviteCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());
    }

    private boolean isIgnoreInviteCode(String inviteCode) {
        if (StringUtils.isBlank(inviteCode)) {
            return false;
        }
        return getIgnoreInviteCodes().contains(inviteCode);
    }

    /**
     * 异步执行营销风险检测
     *
     * @param userId        用户ID
     * @param ip            IP地址
     * @param userAgent     用户代理
     * @param appVersion    应用版本
     * @param os            操作系统
     * @param boxId         设备盒子ID
     * @param marketingType 营销类型
     * @param taskId        任务ID
     */
    @Async("threadPoolTaskExecutor")
    public void sendMarketingRiskAsync(Long userId, String ip, String userAgent, String appVersion, String os,
            String boxId, String marketingType, String taskId) {
        if (boxId == null) {
            log.warn("营销风险检测异步调用失败: userId={}, boxId is null", userId);
            return;
        }
        try {
            MarketingRiskRequest marketingRiskRequest = new MarketingRiskRequest();
            marketingRiskRequest.setUserId(userId);
            marketingRiskRequest.setIp(ip);
            marketingRiskRequest.setUserAgent(userAgent);
            marketingRiskRequest.setAppVersion(appVersion);
            marketingRiskRequest.setOs(os);
            marketingRiskRequest.setBoxId(boxId);
            marketingRiskRequest.setMarketingType(marketingType);
            marketingRiskRequest.setTaskId(taskId);

            riskClient.marketingRisk(marketingRiskRequest);
            log.info("营销风险检测异步调用成功: userId={}, marketingType={}, taskId={}", userId, marketingType, taskId);
        } catch (Exception e) {
            log.warn("营销风险检测异步调用失败: userId={}, marketingType={}, taskId={}, error={}",
                    userId, marketingType, taskId, e.getMessage());
        }
    }
}

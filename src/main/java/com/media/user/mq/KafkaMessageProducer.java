package com.media.user.mq;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
 
@Service
@Slf4j
public class KafkaMessageProducer {

 
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
 
    public void sendMessage(String topic,String message) {
        log.info("topic:{},message:{}",topic, JSON.toJSONString(message));
        kafkaTemplate.send(topic, message);
    }

    public void sendMessage(String topic,String message,String key){
        log.info("topic:{},message:{}",topic, JSON.toJSONString(message));

        kafkaTemplate.send(topic,key,message);
    }
}
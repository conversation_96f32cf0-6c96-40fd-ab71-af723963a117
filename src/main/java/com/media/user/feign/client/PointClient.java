package com.media.user.feign.client;

import com.media.core.exception.ApiException;
import com.media.user.dto.request.SignupRequest;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PointClient {

    @Autowired
    UserAccountBalanceClient userAccountBalanceClient;

    /**
     * 调用 point 服务创建账户
     * @param uid
     */
    public void pointCreatAccountBalance(Long uid) {
        log.info("pointCreatAccountBalance create account uid:{}", uid);
        //生成用户钱包账户
        ApiResponse apiResponse = userAccountBalanceClient.createUserAccountBalance(new SignupRequest().setUid(uid));
        if(!MediaUserExceptionCodeApi.SUCCESS.equals(apiResponse.getCode())){
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }
    }


}

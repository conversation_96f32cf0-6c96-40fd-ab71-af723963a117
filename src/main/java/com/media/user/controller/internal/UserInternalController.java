package com.media.user.controller.internal;

import com.media.core.constant.PageResponse;
import com.alibaba.fastjson.JSONObject;
import com.media.core.exception.ApiException;
import com.media.core.utils.MD5Util;
import com.media.user.dto.query.ClientUserBatchQuery;
import com.media.user.dto.query.ClientUserQuery;
import com.media.user.dto.query.UserSearchQuery;
import com.media.user.dto.request.internal.ModifyUserInfoRequest;
import com.media.user.dto.request.internal.UpdateUserRequest;
import com.media.user.dto.request.internal.UserBatchRegisterRequest;
import com.media.user.dto.request.internal.UserRegisterRequest;
import com.media.user.dto.request.internal.UserStatusUpdateRequest;
import com.media.user.dto.response.*;
import com.media.user.dto.response.internal.PreBatchRegisterUserResponse;
import com.media.user.dto.response.internal.PreRegisterUserResponse;
import com.media.user.dto.response.internal.SearchUserResponse;
import com.media.user.dto.response.internal.UserMeResponse;
import com.media.user.es.EsClientUserService;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.service.ClientUserService;
import com.media.user.service.internal.UserInternalService;
import com.media.user.service.internal.UserSignInternalService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/internal/user")
public class UserInternalController {


    @Autowired
    ClientUserService clientUserService;

    @Autowired
    UserInternalService userInternalService;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    UserSignInternalService userSignInternalService;

    @Autowired
    EsClientUserService esClientUserService;

    /**
     * 获取用户邀请信息
     * @return
     */
    @PostMapping("/map")
    public Map<Long, ClientUserResponse> selectUserByIds(@RequestBody ClientUserQuery query) {
        return clientUserService.selectUserByIds(query.getUids());
    }

    /**
     * 用户模糊搜索
     * @return
     */
    @PostMapping("/search")
    public PageResponse<SearchUserResponse> searchUser(@RequestBody UserSearchQuery query){
        return esClientUserService.searchUser(query);
    }

    /**
     * 获取用户成员信息
     * @return
     */
    @PostMapping("/mes")
    public Map<Long, UserMeResponse> userMe(@RequestBody ClientUserQuery query) {
        log.info("mes: {}", query.getUids());
        if(query == null || query.getUids() == null || query.getUids().isEmpty()){
            return new HashMap<>();
        }
        return userInternalService.userMes(query.getCurrentUid(), query.getUids());
    }

    /**
     * 注册
     * @param request
     */
    @PostMapping("/pre/register")
    public PreRegisterUserResponse preRegister(@Validated @RequestBody UserRegisterRequest request){
        RLock rLock = redissonClient.getLock("user:pre:register:" + request.getUserName());
        try {
            rLock.lock(3000, TimeUnit.MILLISECONDS);
            return userInternalService.preRegister(request);
        }finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    /**
     * 批量注册
     * @param list
     */
    @PostMapping("/pre/batch/register")
    public List<PreBatchRegisterUserResponse> preBatchRegister(@Validated @RequestBody List<UserBatchRegisterRequest> list){
        //如果 list 超过 50个报错
        log.info("preBatchRegister size:{}", list == null ? 0 : list.size());
        if(list == null || list.isEmpty() || list.size() > 50){
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
        RLock rLock = redissonClient.getLock("user:pre:batch:register:" + MD5Util.getMD5(JSONObject.toJSONString(list)));
        try {
            rLock.lock(3000, TimeUnit.MILLISECONDS);
            return userInternalService.preBatchRegister(list);
        }finally {
            if (rLock.isLocked() && rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    /**
     * 查询用户的 twitterId
     * @return
     */
    @PostMapping("/twitterId")
    public Map<String, String> userTwitterId(@RequestBody UserSearchQuery query) {
        log.info("userTwitterId:{}", query.getUid());
        String twitterId = userInternalService.getTwitterIdById(query.getUid());
        Map<String, String> map = new HashMap<>();
        map.put("twitterId", twitterId);
        return map;
    }

    /**
     * 获取用户加成
     * @return
     */
    @PostMapping("/addition")
    public Map<Long, UserOverviewResponse> selectUserAdditionByUids(@RequestBody ClientUserQuery query) {
        return userInternalService.selectUserAdditionByUids(query);
    }

    /**
     * 今日是否签到
     * @return
     */
    @PostMapping("/isSign/today")
    public Map<String, Integer> isSignToday(@RequestBody UserSearchQuery query) {
        return userSignInternalService.isSignToday(query.getUid());
    }

    @PostMapping("/batch/mes")
    public Map<Long, UserMeResponse> batchUserMe(@RequestBody ClientUserBatchQuery query) {
        log.info("batch mes: {}", query);
        if(query == null || query.getUids() == null || query.getUids().isEmpty()){
            return new HashMap<>();
        }
        return userInternalService.batchUserMes(
                query.getCurrentUid(),
                query.getUids(),
                query.getNeedFollowState() != null && query.getNeedFollowState(),
                query.getNeedFollowedState() != null && query.getNeedFollowedState(),
                query.getNeedFollowCount() != null && query.getNeedFollowCount()
        );
    }

    /**
     * 更新个人信息
     * @param request
     */
    @PostMapping("/info/modify")
    public void infoModify(@Validated @RequestBody ModifyUserInfoRequest request){
        userInternalService.modifyUserInfo(request);
    }

    /**
     * 更新个人信息,包括email
     * @param request
     */
    @PostMapping("/info/update")
    public void infoUpdate(@Validated @RequestBody UpdateUserRequest request){
        userInternalService.updateUserInfo(request);
    }
    
    /**
     * 更新用户状态（封禁/解封）
     * @param request 包含用户ID和目标状态的请求
     */
    @PostMapping("/status/update")
    public void updateUserStatus(@Validated @RequestBody UserStatusUpdateRequest request){
        userInternalService.updateUserStatus(request);
    }
}

package com.media.user.dto.rongCloud;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.media.common.utils.JsonUtils;
import com.media.core.i18n.I18nConvert;
import com.media.user.enums.LanguageEnums;
import lombok.Data;
import java.util.Map;

import static com.media.user.constant.MediaUserConstant.IM_CONTENT_TIPS_I18N_KEY;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RongCloudCallbackResponse {
    private Integer pass; // 必须字段
    private Map<String, Object> replaceContent;
    private String replacePushContent;
    private Boolean replaceDisablePush;
    private String replacePushExt;
    private Map<String, Object> replaceExtraContent;
    private String extra;

    // 快速创建成功响应
    public static RongCloudCallbackResponse success() {
        RongCloudCallbackResponse response = new RongCloudCallbackResponse();
        response.setPass(1);
        return response;
    }

    // 快速创建错误响应
    public static RongCloudCallbackResponse error(int code, String language) {
        RongCloudCallbackResponse response = new RongCloudCallbackResponse();
        response.setPass(0);
        String tipsMsg = I18nConvert.getI18nMessage(String.format(IM_CONTENT_TIPS_I18N_KEY, code), LanguageEnums.get(language));
        response.setExtra(JsonUtils.toJsonString(Map.of("code", code, "msg", tipsMsg)));
        return response;
    }
}
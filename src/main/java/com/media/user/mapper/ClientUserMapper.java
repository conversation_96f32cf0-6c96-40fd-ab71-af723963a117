package com.media.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.ClientUserModel;
import com.media.user.dto.query.RobotPointQuery;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.List;

@Mapper
public interface ClientUserMapper extends BaseMapper<ClientUserModel> {

    @Select("select * from client_user where uid = #{uid}")
    ClientUserModel selectByUserId(@Param("uid") Long uid);

    @Select("select * from client_user where country_code = #{countryCode} and phone_prefix = #{phonePrefix} and phone = #{phone}")
    ClientUserModel selectByPhone(@Param("countryCode") String countryCode, @Param("phonePrefix") String phonePrefix, @Param("phone") String phone);

    @Select("select * from client_user where email = #{email}")
    ClientUserModel selectByEmail(@Param("email") String email);

    @Select("select * from client_user where uid in <foreach item='uid' index='index' collection='uids' open='(' separator=',' close=')'>#{uid}</foreach>")
    List<ClientUserModel> selectListByUserIds(@Param("uids") List<Long> uids);

    @Select("<script>select uid from client_user where uid > #{query.id} and type=2 and MOD(uid,#{query.total}) = #{query.index} order by uid asc limit 0,#{query.pageSize} </script>")
    List<Long> selectClientUserList(@Param("query")RobotPointQuery query);


    @Select("select * from client_user where email not in ('Xme_Anything','elonmusk') and uid not in (\n" +
            "select followed_old_uid from user_follow_relation where follow_uid=#{uid} and follow_state=1\n" +
            "UNION\n" +
            "select followed_new_uid from user_follow_relation where follow_uid=#{uid} and follow_state=1\n" +
            ") LIMIT 100")
    List<ClientUserModel> getUser100(@Param("uid") Long uid);


    @Update("UPDATE client_user SET avatar_url=#{imageUrl} where uid = #{uid} ")
    long modifyAvatarImageUrl(@Param("imageUrl") String imageUrl, @Param("uid") Long uid);


    @Select("select uid from client_user where is_recommend=0 and virtual_type=0 limit 1000")
    List<Long> needPushRecommend();

    @Update("<script>update client_user set is_recommend=1 where uid in <foreach item='uid' index='index' collection='uids' open='(' separator=',' close=')'>#{uid}</foreach></script>")
    void modifyPushRecommend(@Param("uids") List<Long> uids);


    @Select("select * from client_user where fix_image_type = 1 order by created_time desc LIMIT 100;")
    List<ClientUserModel> getNeedFixImage100();

    @Insert("<script> " +
            "INSERT IGNORE INTO client_user(uid, `type`, email, nick_name, password, `language`, status, invite_code, avatar_url, person_introduce, virtual_type, fix_image_type, username, username_original, nick_name_original, avatar_url_original, created_time, registration_time) VALUES " +
            "<foreach collection='list' item='item' separator=','> " +
            "(#{item.uid}, #{item.type}, #{item.email}, #{item.nickName}, #{item.password}, #{item.language}, #{item.status}, #{item.inviteCode}, #{item.avatarUrl}, #{item.personIntroduce}, #{item.virtualType}, #{item.fixImageType}, #{item.username}, #{item.usernameOriginal}, #{item.nickNameOriginal}, #{item.avatarUrlOriginal}, #{item.createdTime}, #{item.registrationTime}) " +
            "</foreach> " +
            "</script>")
    long insertBatch(@Param("list") List<ClientUserModel> list);


    @Update({
            "<script>",
            "UPDATE client_user",
            "<set>",
                "face_liveness_status = #{status},",
                "face_liveness_at = #{faceLivenessAt}",
                "<if test='faceLivenessId != null'>,face_liveness_id = #{faceLivenessId}</if>",
            "</set>",
            "WHERE uid = #{uid}",
            "</script>"
    })
    long modifyFaceStatus(@Param("status") int status, @Param("uid") Long uid, @Param("faceLivenessAt") Date faceLivenessAt, @Param("faceLivenessId") String faceLivenessId);
}
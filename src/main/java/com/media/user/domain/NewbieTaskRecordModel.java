package com.media.user.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.web3j.abi.datatypes.Int;

import java.util.Date;


@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("newbie_task_record")
@Accessors(chain = true)
public class NewbieTaskRecordModel extends Model<NewbieTaskRecordModel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("event_id")
    private Long eventId;

    @TableField("uid")
    private Long uid;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;

    @TableField("status")
    private Byte status;

}

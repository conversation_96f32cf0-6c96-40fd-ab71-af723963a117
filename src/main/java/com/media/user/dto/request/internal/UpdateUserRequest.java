package com.media.user.dto.request.internal;


import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors
public class UpdateUserRequest {

    private Long uid;

    @Size(max = 500)
    private String avatarUrl;

    @Size(max = 32)
    private String nickName;

    @Size(max = 600)
    private String personIntroduce;

    @Size(max = 64)
    private String email;
}

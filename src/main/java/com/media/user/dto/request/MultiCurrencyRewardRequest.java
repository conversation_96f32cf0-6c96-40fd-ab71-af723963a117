package com.media.user.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 多货币奖励请求
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class MultiCurrencyRewardRequest {

    /**
     * 事件ID
     */
    private Long eventId;

    /**
     * 认证类型：1-自己认证, 2-被邀请认证
     */
    private Integer verificationType;

    /**
     * 邀请类型：1-普通邀请, 2-创世大使邀请, 3-活动邀请, 4-游戏活动邀请
     */
    private Integer inviteType;

    /**
     * 活动ID（如果是活动邀请）
     */
    private Long activityId;

    /**
     * 邀请人信息
     */
    private UserReward fromUser;

    /**
     * 被邀请人/认证人信息
     */
    private UserReward toUser;

    /**
     * 幂等键
     */
    private String idempotent;

    /**
     * 创建时间戳
     */
    private Long createTime;

    /**
     * 用户奖励信息
     */
    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class UserReward {
        /**
         * 用户ID
         */
        private Long uid;

        /**
         * 奖励列表，每种货币一个条目
         */
        private List<CurrencyReward> rewards;
    }

    /**
     * 单种货币奖励信息
     */
    @Data
    @Accessors(chain = true)
    @NoArgsConstructor
    public static class CurrencyReward {
        /**
         * 货币符号
         */
        private String currency;

        /**
         * 网络类型
         */
        private String network;

        /**
         * 奖励数量
         */
        private Integer amount;

        public CurrencyReward(String currency, String network, Integer amount) {
            this.currency = currency;
            this.network = network;
            this.amount = amount;
        }
    }
} 
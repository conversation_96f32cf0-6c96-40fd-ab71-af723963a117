package com.media.user.controller.api;

import com.alibaba.fastjson.JSONObject;
import com.github.scribejava.core.model.OAuth2AccessToken;
import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.core.exception.ApiException;
import com.media.user.domain.TwitterUserModel;
import com.media.user.dto.request.TwitterBindRequest;
import com.media.user.dto.response.TwitterUserResponse;
import com.media.user.enums.TwitterStateEnum;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.ContentTwitterClient;
import com.media.user.model.request.twitter.TwitterUser;
import com.media.user.service.UserSecretService;
import com.media.user.service.twitter.impl.TwitterService;
import com.xme.xme_base_depends.models.ApiResponse;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.ws.rs.QueryParam;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequestMapping(value = "/user/twitter", produces = "application/json;charset=UTF-8")
public class TwitterController {

    @Autowired
    protected TwitterService twitterService;

    @Autowired
    protected UserSecretService userSecretService;

    @Autowired
    ContentTwitterClient contentTwitterClient;

    /**
     * TODO 暂不使用， 推特相关信息
     *
     * @return
     * @throws Exception
     */
    @GetMapping("/callback")
    public Map<String, String>  twitterCallback(
            @QueryParam("state") String state,
            @QueryParam("code") String code,
            HttpServletResponse response
    ) throws Exception {
        log.info("Exec Twitter ......state:{},code={}", state, code);
        String[] states = state.split(";");
        String stateType = states[0];
        String uid = states[1];
        Map<String, String> map = new HashMap<>();

        //去绑定
        if(stateType.equals(TwitterStateEnum.XME_BINDING.getState())){
            //TODO 调用接口获取对应的 accessToken
            //根据 accessToken 落地 twitter 的用户数据
            String accessToken = contentTwitterClient.getTwitterAccessToken(code);
            if(StringUtils.isBlank(accessToken)){
                // TODO 发送日志
                OAuth2AccessToken oAuth2AccessToken = twitterService.getOAuth2AccessToken(code);
                log.info("access Token: {}", JSONObject.toJSON(oAuth2AccessToken));
                accessToken =  oAuth2AccessToken.getAccessToken();
            }
            //根据 accessToken 落地 twitter 的用户数据
            TwitterUser twitterUser = twitterService.twitterUser(accessToken);

            map.put("code", code);
            map.put("access Token", accessToken);
            map.put("twitterUser", JSONObject.toJSONString(twitterUser));
        }
        return map;
    }

    /**
     * bind uri
     */
    @GetMapping("/auth/uri")
    @RequireAuth
    public Map<String, Object> authUri(String type) {
//        String state = TwitterStateEnum.getState(type);
        Map<String, Object> map = new HashMap<>();
        Long uid = CloudSession.getUid();
        TwitterUserModel model = twitterService.checkUserTwitterBind(uid);
        map.put("bind", false);
        if(model != null){
            map.put("bind", true);
            TwitterUserResponse twitterUserResponse = new TwitterUserResponse();
            twitterUserResponse.setId(model.getTwitterId());
            twitterUserResponse.setName(model.getName());
            twitterUserResponse.setScreenName(model.getUsername());
            twitterUserResponse.setThumbnailImage(model.getProfileImageUrl());
            map.put("twitterUser", twitterUserResponse);
        }
        return map;
    }


    /**
     * 绑定推特
     */
    @PostMapping("/bind")
    @RequireAuth
    public ApiResponse<Void> bind(@Validated @RequestBody Map<String, String> body) {
        Long uid = CloudSession.getUid();
        if (body.isEmpty()) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
        String twitterAuth = body.get("twitterAuth");
        log.info("bind twitterAuth:{}", twitterAuth);
        if (StringUtils.isBlank(twitterAuth)) {
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }
        TwitterBindRequest request = null;
        try {
            request = JSONObject.parseObject(twitterAuth, TwitterBindRequest.class);
        } catch (Exception e) {
            log.error("param error:{}", e.getMessage());
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }
        TwitterUser twitterUser = twitterService.twitterUser(request.getAccessToken());
        if (twitterUser == null) {
            throw new ApiException(MediaUserExceptionCodeApi.TWITTER_AUTHENTICATION_FAILED);
        }
        twitterUser.setAccessToken(request.getAccessToken());
        twitterUser.setRefreshToken(request.getRefreshToken());
        if (request.getScopes() != null) {
            twitterUser.setScopes(String.join(",", request.getScopes()));
        }
        twitterUser.setExpireAt(request.getExpireAt());
        twitterService.followOfficialAccount(uid, twitterUser); //绑定官方账号
        twitterService.saveBindTwitterUserModel(uid, twitterUser);
        Integer ep1 = userSecretService.sendMediaTask(uid, "10004", "/user/twitter/bind");
        ep1 = ep1 == null ? 0 : ep1;
        Integer ep2 = userSecretService.sendMediaTask(uid, "10007", "/user/twitter/bind");
        ep2 = ep2 == null ? 0 : ep2;
        return ApiResponse.success(null, ep1 + ep2,  ep1 + ep2);
    }

    /**
     * bind cancel
     */
    @PostMapping("/bind/cancel")
    @RequireAuth
    public void bindCancel() {
        twitterService.bindCancel(CloudSession.getUid());
    }
}

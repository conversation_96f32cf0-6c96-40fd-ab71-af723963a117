package com.media.user.dto.request;

import com.media.core.constant.ValidatorRegexpConstant;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors
public class ForgetPasswordRequest {

    private String countryCode;

    private String phonePrefix;

    private String phone;

    @Email
    private String email;

    @Pattern(regexp = ValidatorRegexpConstant.PASSWORD_REGEX, message = "{result.code.10020}")
    @NotBlank
    private String newPassword;

    @NotBlank
    @Size(max = 6)
    private String code;

}

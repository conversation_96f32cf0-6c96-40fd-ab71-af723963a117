package com.media.user.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.eventbus.EventBus;
import com.media.core.constant.BeanConstant;
import com.media.core.exception.ApiException;
import com.media.core.utils.MD5Util;
import com.media.core.utils.RedisUtils;
import com.media.user.constant.MediaUserConstant;
import com.media.user.constant.PhoneSnsTemplateConstant;
import com.media.user.dto.request.SendPhoneCodeRequest;
import com.media.user.dto.request.SendPhoneSmsRequest;
import com.media.user.enums.*;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.feign.client.EmailClient;
import com.media.user.tools.PhoneUtils;
import com.xme.xme_base_depends.enums.UserBehaviorEventEnum;
import com.xme.xme_base_depends.mq.message.UserFinishEvent;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 发送验证码
 */
@Slf4j
@Service
public class PhoneService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    RedisUtils redisUtils;

    @Autowired
    private  EmailClient emailClient;

    @Autowired
    PhoneUtils phoneUtils;

    @Value("${super.code.ignorePhone:false}")
    private boolean ignorePhoneCode;

    @Value("${phone.ratelimit.count:3}")
    private int phoneRateLimitCount;


    @Autowired
    @Qualifier(value = BeanConstant.USER_EVENT_BUS_BEAN)
    private EventBus userEventBus;

    /**
     * 发送验证码（直接内容方式）
     *
     * @param uid 用户ID
     * @param request 发送验证码请求
     */
    public void sendCode(Long uid, SendPhoneCodeRequest request) {

        phoneUtils.checkPhoneRegex(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());

        //增加验证码的拦截频率  1分钟发一次， 每天最多发送 10次
        String redisLimitKey = getPhoneCodeLimitRateRedisKey(uid, request.getCountryCode(), request.getPhonePrefix(), request.getPhone(), request.getBusinessType());
        if(stringRedisTemplate.hasKey(redisLimitKey)){
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_CODE_LIMIT_RATE_ERROR);
        }
        String redisCountKey = getPhoneCodeLimitCountRedisKey(uid, request.getCountryCode(), request.getPhonePrefix(), request.getPhone(), request.getBusinessType());
        Long count = redisUtils.getCount(redisCountKey);
        if(count >= 10){
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_CODE_LIMIT_COUNT_ERROR);
        }

        stringRedisTemplate.opsForValue().set(redisLimitKey, "1", 1, TimeUnit.MINUTES);
        redisUtils.incrementCount(redisCountKey, 24L, TimeUnit.HOURS);

        int code = RandomUtils.nextInt(100000, 999999);
        String redisKey = getPhoneCodeRedisKey(uid, request.getCountryCode(), request.getPhonePrefix(), request.getPhone(), request.getBusinessType());
        /**
         * 验证码五分钟过期
         */
        stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(code), 15, TimeUnit.MINUTES);

        SupportPhonePrefixEnum prefixEnum = SupportPhonePrefixEnum.getPhonePrefixEnum(request.getCountryCode(), request.getPhonePrefix());
        if(prefixEnum == null){
            log.error("phone send message config error: {}-{}", request.getCountryCode(), request.getPhonePrefix());
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }

        SendPhoneSmsRequest sendPhoneSmsRequest = new SendPhoneSmsRequest()
                .setPhonePrefix(request.getPhonePrefix())
                .setPhone(request.getPhone())
                .setBusinessType(request.getBusinessType())
                //生成需要发送验证码的消息模板
                .setContent(PhoneSnsTemplateConstant.getSnsByLanguage(prefixEnum.getSnsTemplate(), String.valueOf(code)));
        log.info("PhoneService.sendCode content: {}", JSONObject.toJSON(sendPhoneSmsRequest));
        // 更换发送短信
        try {
            emailClient.sendPhoneCode(sendPhoneSmsRequest);
        }catch (Exception e){
            log.error("phone send code error: {}", e.getMessage());
            throw new ApiException(MediaUserExceptionCodeApi.SEND_CODE_ERROR);
        }
    }

    /**
     * 通过模板发送验证码（模板变量方式）
     *
     * @param uid 用户ID
     * @param request 发送验证码请求
     */
    public void sendCodeByTemplate(Long uid, SendPhoneCodeRequest request) {

        phoneUtils.checkPhoneRegex(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());

        //增加验证码的拦截频率  1分钟发一次， 每天最多发送 10次
        String redisLimitKey = getPhoneCodeLimitRateRedisKey(uid, request.getCountryCode(), request.getPhonePrefix(), request.getPhone(), request.getBusinessType());
        if(stringRedisTemplate.hasKey(redisLimitKey)){
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_CODE_LIMIT_RATE_ERROR);
        }
        String redisCountKey = getPhoneCodeLimitCountRedisKey(uid, request.getCountryCode(), request.getPhonePrefix(), request.getPhone(), request.getBusinessType());
        Long count = redisUtils.getCount(redisCountKey);
        if(count >= phoneRateLimitCount){
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_CODE_LIMIT_COUNT_ERROR);
        }

        stringRedisTemplate.opsForValue().set(redisLimitKey, "1", 1, TimeUnit.MINUTES);
        redisUtils.incrementCount(redisCountKey, 24L, TimeUnit.HOURS);

        int code = RandomUtils.nextInt(100000, 999999);
        String redisKey = getPhoneCodeRedisKey(uid, request.getCountryCode(), request.getPhonePrefix(), request.getPhone(), request.getBusinessType());
        /**
         * 验证码五分钟过期
         */
        stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(code), 15, TimeUnit.MINUTES);

        SupportPhonePrefixEnum prefixEnum = SupportPhonePrefixEnum.getPhonePrefixEnum(request.getCountryCode(), request.getPhonePrefix());
        if(prefixEnum == null){
            log.error("phone send message config error: {}-{}", request.getCountryCode(), request.getPhonePrefix());
            throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
        }

        // 创建模板变量Map
        java.util.HashMap<String, String> variables = new java.util.HashMap<>();
        variables.put("code", String.valueOf(code));
        variables.put("time", "5"); // 验证码有效期

        SendPhoneSmsRequest sendPhoneSmsRequest = new SendPhoneSmsRequest()
                .setPhonePrefix(request.getPhonePrefix())
                .setPhone(request.getPhone())
                .setVariables(variables)
                .setBusinessType(request.getBusinessType())
                .setUseEnglishTemplate(prefixEnum.getSnsTemplate().equals(PhoneSnsTemplateConstant.en_US));

        log.info("PhoneService.sendCodeByTemplate content: {}", JSONObject.toJSON(sendPhoneSmsRequest));

        // 使用模板发送短信
        try {
            emailClient.sendPhoneCodeByTemplate(sendPhoneSmsRequest);
        } catch (Exception e) {
            log.error("phone send code by template error: {}", e.getMessage());
            throw new ApiException(MediaUserExceptionCodeApi.SEND_CODE_ERROR);
        }
    }



    private String getPhoneCodeRedisKey(Long uid, String countryCode, String phonePrefix, String phone, Integer businessType) {
        if(uid == null){
            return MediaUserConstant.SMS_PHONE_CODE_PREFIX + businessType + ":" + MD5Util.getMD5(countryCode + phonePrefix + phone);
        }
        return MediaUserConstant.SMS_PHONE_CODE_PREFIX + businessType + ":" + uid + ":" + MD5Util.getMD5(countryCode + phonePrefix + phone);
    }


    private String getPhoneCodeLimitRateRedisKey(Long uid, String countryCode, String phonePrefix, String phone, Integer businessType) {
        if(uid == null){
            return MediaUserConstant.SMS_PHONE_CODE_LIMIT_RATE_PREFIX + businessType + ":" + MD5Util.getMD5(countryCode + phonePrefix + phone);
        }
        return MediaUserConstant.SMS_PHONE_CODE_LIMIT_RATE_PREFIX + businessType + ":" + uid + ":" + MD5Util.getMD5(countryCode + phonePrefix + phone);
    }

    private String getPhoneCodeLimitCountRedisKey(Long uid, String countryCode, String phonePrefix, String phone, Integer businessType) {
        if(uid == null){
            return MediaUserConstant.SMS_PHONE_CODE_LIMIT_COUNT_PREFIX + businessType + ":" + MD5Util.getMD5(countryCode + phonePrefix + phone);
        }
        return MediaUserConstant.SMS_PHONE_CODE_LIMIT_COUNT_PREFIX + businessType + ":" + uid + ":" + MD5Util.getMD5(countryCode + phonePrefix + phone);
    }

    /**
     * 校验缓存 key
     * @param uid 可为空
     * @param phone
     * @param businessType
     * @param phoneCode
     */
    public void verifyPhoneCode(Long uid, String countryCode, String phonePrefix, String phone, Integer businessType, String phoneCode) {
        if (ignorePhoneCode && MediaUserConstant.SUPER_CODE.equals(phoneCode)) {
            return;
        }
        String key = getPhoneCodeRedisKey(uid, countryCode, phonePrefix, phone, businessType);
        boolean exist = stringRedisTemplate.hasKey(key);
        if (!exist) {
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_CODE_EXPIRE);
        }
        String code = stringRedisTemplate.opsForValue().get(key);
        if (!phoneCode.equals(code)) {
            throw new ApiException(MediaUserExceptionCodeApi.PHONE_CODE_ERROR);
        }
    }

    /**
     * 标记 key 失效
     * @param uid 可为空
     * @param phone
     * @param businessType
     */
    public void signPhoneCodeExpire(Long uid, String countryCode, String phonePrefix, String phone, Integer businessType) {
        String key = getPhoneCodeRedisKey(uid, countryCode, phonePrefix, phone, businessType);
        stringRedisTemplate.delete(key);
    }

    /**
     * TODO test 上线需关掉
     * @param uid
     * @param phone
     * @param businessType
     * @return
     */
    public String getPhoneCode(Long uid, String countryCode, String phonePrefix, String phone, Integer businessType) {
//        businessType 1, 2, 6 不需要 uid
//        3， 4， 5 需要 uid
        if(businessType == 1 || businessType == 2 || businessType == 6){
            uid = null;
        }
        return stringRedisTemplate.opsForValue().get(getPhoneCodeRedisKey(uid, countryCode, phonePrefix, phone, businessType));
    }


}

package com.media.user.dto.response.internal;

import com.google.common.collect.Sets;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Set;


@Data
@Accessors(chain = true)
public class InviteCodeParseResult {
    public long userID;
    public Long mainActivityId; // 当前活动id
    public Set<Long> activityIds; // 当前活动可能触发的活动id
    public  boolean open; // 当前主活动是否开启


//    public InviteCodeParseResult(long userInfo, long extraInfo) {
//        this.userID = userInfo;
//        this.mainActivityId = extraInfo;
//        this.activityIds = Sets.newHashSet(extraInfo);
//    }
}

package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.user.dto.request.*;
import com.media.user.dto.response.*;
import com.media.user.enums.PlatformEnums;
import com.media.core.exception.ApiException;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.core.request.ClientInfoContext;
import com.media.user.service.UserAddressService;
import com.media.core.utils.WalletCryptoUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.Map;


@RestController
@RequestMapping("/user/api/address")
public class UserAddressApiController {


    @Autowired
    private UserAddressService userAddressService;

    /**
     * 钱包登录
     * @param request
     */
    @PostMapping("/login")
    public AuthTokenResponse addressLogin(@Validated @RequestBody AddressLoginRequest request) {
        //签名验证
        if (!WalletCryptoUtil.validate(request.getSignature(), request.getMessage(), request.getAddress())) {
            throw new ApiException(MediaUserExceptionCodeApi.ADDRESS_SIGNATURE_ERROR);
        }
        return userAddressService.addressLogin(request);
    }

    /**
     * 钱包地址注册 TODO 后期优化
     * @param request
     */
    @PostMapping("/register")
    public AuthTokenResponse addressRegister(HttpServletRequest httpRequest, @Validated @RequestBody ClientUserRequest request) {
        //签名验证
        if (!WalletCryptoUtil.validate(request.getSignature(), request.getMessage(), request.getAddress())) {
            throw new ApiException(MediaUserExceptionCodeApi.ADDRESS_SIGNATURE_ERROR);
        }
        request.setSourceType(PlatformEnums.get(ClientInfoContext.get().getPlatformType()).getCode());
        return userAddressService.addressRegister(httpRequest, request);
    }

    /**
     * 获取邀请信息
     */
    @PostMapping("/info")
    public Map<String, Object> addressInfo() throws ParseException {
        return userAddressService.addressInfo();
    }

    /**
     * 获取邀请信息
     */
    @PostMapping("/invite")
    @RequireAuth
    public Map<String, Object> addressInvite(){
        return userAddressService.addressInvite(CloudSession.getUid());
    }

    /**
     * 获取邀请信息
     */
    @RequireAuth
    @PostMapping("/receive")
    public Object receiveToken(){
        return userAddressService.receiveToken(CloudSession.getUid());
    }

}

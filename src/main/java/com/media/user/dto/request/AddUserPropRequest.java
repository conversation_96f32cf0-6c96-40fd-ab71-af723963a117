package com.media.user.dto.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 给用户批量添加道具请求参数
 * {"uid":1111,"props":[{"propCode":"EP_800","quantity":1},{"propCode":"EP_1000","quantity":2}],"idempotentId":"123"}
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddUserPropRequest {
    /**
     * 用户ID
     */
    private Long uid;
    
    /**
     * 道具列表
     */
    private List<AddUserPropProd> props;
    
    /**
     * 幂等ID
     */
    private String idempotentId;
}

package com.media.user.feign.client;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import com.media.user.dto.request.TelegramBindRequest;
import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.MessageDigest;

/**
 * Telegram Bot API客户端
 */
@Slf4j
@Component
@Data
public class TelegramBotClient {

    @Value("${telegram.bot.token}")
    private String botToken;

    @Value("${telegram.bot.api_url:https://api.telegram.org}")
    private String apiUrl;
    
    @Value("${telegram.target.group_id:}")
    private String targetGroupId;
    
    private RestTemplate restTemplate;

    @PostConstruct
    public void init() {
        // 设置连接超时(5秒)和读取超时(10秒)
        this.restTemplate = new RestTemplateBuilder()
                .setConnectTimeout(Duration.ofSeconds(5))
                .setReadTimeout(Duration.ofSeconds(10))
                .build();
        log.info("TelegramBotClient初始化完成");
    }

    /**
     * 验证Telegram用户ID是否存在
     * @param telegramUserId Telegram用户ID
     * @return 是否有效
     */
    public boolean verifyTelegramUser(Long uid,Long telegramUserId) {
        if (telegramUserId == null) {
            return false;
        }
        
        try {
            // 使用GET请求，将参数添加到URL中
            String baseUrl = String.format("%s/bot%s/getChat", apiUrl, botToken);
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .queryParam("chat_id", telegramUserId);
            
            ResponseEntity<TelegramResponse> response = restTemplate.getForEntity(
                    builder.toUriString(), 
                    TelegramResponse.class);
                    
            return response.getBody() != null && response.getBody().isOk();
        } catch (RestClientException e) {
            log.error("验证用户uid={}的telegram用户id={}失败={}", uid, telegramUserId, ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * 检查用户是否在指定群组内
     * @param telegramUserId Telegram用户ID
     * @param groupId 群组ID（如果为空则使用配置的目标群组ID）
     * @return 是否在群组内
     */
    public boolean isUserInGroup(Long telegramUserId, String groupId) {
        if (telegramUserId == null) {
            return false;
        }
        
        if (groupId == null || groupId.isEmpty()) {
            groupId = this.targetGroupId;
        }
        
        if (groupId == null || groupId.isEmpty()) {
            log.warn("未指定目标群组ID");
            return false;
        }
        
        try {
            // 使用GET请求，将参数添加到URL中
            String baseUrl = String.format("%s/bot%s/getChatMember", apiUrl, botToken);
            UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl)
                    .queryParam("chat_id", groupId)
                    .queryParam("user_id", telegramUserId);
            
            ResponseEntity<TelegramResponse> response = restTemplate.getForEntity(
                    builder.toUriString(), 
                    TelegramResponse.class);
                    
            if (response.getBody() != null && response.getBody().isOk()) {
                String status = response.getBody().getResult() != null ? 
                    (String) ((Map<String, Object>) response.getBody().getResult()).get("status") : null;
                
                // 用户状态：member, administrator, creator 表示在群组内
                return "member".equals(status) || "administrator".equals(status) || "creator".equals(status);
            }
            return false;
        } catch (RestClientException e) {
            log.error("检查用户是否在群组内失败: userId={}, groupId={}", telegramUserId, groupId, e);
            return false;
        }
    }

    /**
     * 检查Telegram认证数据
     * @param params
     * @return
     */
    public boolean checkTelegramAuthorization(TelegramBindRequest params) {
        try {
            // 构造数据字符串
            String dataCheckString = getTokenStringBuilder(params);
            // 使用 bot token 计算密钥
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] secretKey = digest.digest(botToken.getBytes(java.nio.charset.StandardCharsets.UTF_8));

            // 使用密钥计算哈希
            Mac sha256Hmac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey, "HmacSHA256");
            sha256Hmac.init(secretKeySpec);
            byte[] computedHash = sha256Hmac.doFinal(dataCheckString.getBytes(java.nio.charset.StandardCharsets.UTF_8));

            // 将计算出的哈希值转换为 Base64 编码
            String computedHashString = bytesToHex(computedHash);
            // 比较计算的哈希和传递的哈希
            return !computedHashString.equals(params.getHash());
        } catch (Exception e) {
            log.error("验证Telegram认证数据时出错, params={}, error={}", params, e.getMessage(), e);
            return false;
        }
    }

    private static String getTokenStringBuilder(TelegramBindRequest params) {
        Map<String, String> dataMap = new java.util.HashMap<>();
        dataMap.put("auth_date", params.getAuth_date());
        dataMap.put("first_name", params.getFirst_name());
        dataMap.put("id", params.getId().toString());
        dataMap.put("username", params.getUsername());
        dataMap.put("photo_url", params.getPhoto_url());
        return dataMap.entrySet().stream()
                 .sorted(Map.Entry.comparingByKey())
                 .map(entry -> entry.getKey() + "=" + entry.getValue())
                 .collect(java.util.stream.Collectors.joining("\n"));
    }
    
    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }


    /**
     * Telegram API响应包装类
     */
    @Data
    public static class TelegramResponse {
        private boolean ok;
        private Object result;
        private Integer errorCode;
        private String description;
    }

}

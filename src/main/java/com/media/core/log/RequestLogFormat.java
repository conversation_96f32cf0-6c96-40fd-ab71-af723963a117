package com.media.core.log;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
public class RequestLogFormat {

    private static final ObjectMapper MAPPER = new ObjectMapper()
            .setSerializationInclusion(JsonInclude.Include.NON_NULL);

    public static String formatRequestRestClient(RequestLog requestLog) {
        StringBuilder sb = new StringBuilder();
        sb.append(requestLog.getMethod().toUpperCase())
                .append(" ")
                .append(requestLog.getUrl());
        StringBuilder url = new StringBuilder();
        if (requestLog.getBody() != null) {
            url.append(url).append("?");
            for (Map.Entry<String, String> entry : requestLog.getParams().entrySet()) {
                String name = entry.getKey();
                url.append(name).append("=").append(entry.getValue()).append("&");
            }
        }
        sb.append(url).append("\r\n");

        for (Map.Entry<String, String> entry : requestLog.getHeaders().entrySet()) {
            String name = entry.getKey();
            String value = entry.getValue();
            sb.append(name)
                    .append(":")
                    .append(value)
                    .append("\r\n");
        }
        sb.append("\r\n");
        if (requestLog.getBody() != null) {
            String body = toJsonString(requestLog.getBody());
            sb.append(body);
        } else if (!requestLog.getParams().isEmpty()) {
            StringBuilder body = new StringBuilder();
            for (Map.Entry<String, String> entry : requestLog.getParams().entrySet()) {
                String name = entry.getKey();
                body.append(name).append("=").append(entry.getValue()).append("&");
            }
            sb.append(body);
        }

        return sb.toString();
    }

    public static String toJsonString(Object value) {
        if (value instanceof String) {
            return (String) value;
        }
        try {
            return MAPPER.writeValueAsString(value);
        } catch (JsonProcessingException e) {
            log.warn(e.getMessage(), e);
        }
        return "";
    }

}

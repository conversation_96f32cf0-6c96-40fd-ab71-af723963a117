package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class UserFamilyInfoResponse {

    private Long id;

    private String familyAvatarUrl;

    private String familyName;

    private Integer l2MaxNum;

    private Integer times;

    private Integer maxTimes;
    /**
     * 子成员数量
     */
    private Integer subMemberTotal;
    private Integer mySubMemberTotal;
    /**
     * 积分总数
     */
    private BigDecimal pointTotal;
    /**
     * 代币总数
     */
    private BigDecimal tokenTotal;
    /**
     * 今日积分加成
     */
    private BigDecimal todayMemberPointTotal;
    /**
     * 今日代币加成
     */
    private BigDecimal todayPointBonusTotal;

    /**
     * 加成速率
     */
    private BigDecimal pointRate;



}
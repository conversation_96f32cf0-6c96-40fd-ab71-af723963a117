package com.media.user.service;

import com.media.user.constant.MediaUserConstant;
import com.media.user.convert.mapper.WebsiteCustomerConvert;
import com.media.user.domain.WebsiteCustomerModel;
import com.media.user.dto.request.AppDownloadUrlRequest;
import com.media.user.dto.request.WebsiteCustomerRequest;
import com.media.user.dto.response.AppReleaseResponse;
import com.media.user.dto.response.CountdownResponse;
import com.media.core.id.RedisIdGenerator;
import com.media.user.mapper.WebsiteCustomerMapper;
import com.media.core.request.ClientInfoContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.Date;


@Service
@Slf4j
public class WebsiteCustomerService {


    @Autowired
    private RedisIdGenerator redisIdGenerator;

    @Autowired
    private WebsiteCustomerMapper websiteCustomerMapper;

    @Autowired
    private AppReleaseService appReleaseService;

    @Autowired
    private ClientUserService clientUserService;

    @Transactional(rollbackFor = Exception.class)
    public void saveWebsiteCustomer(WebsiteCustomerRequest request){
        WebsiteCustomerModel websiteCustomerModel = WebsiteCustomerConvert.INSTANCE.req2Model(request);
        websiteCustomerModel.setId(redisIdGenerator.generate())
                .setLanguage(ClientInfoContext.getLanguage().getValue())
                .setCreatedTime(new Date());
        websiteCustomerMapper.insert(websiteCustomerModel);
    }

    public CountdownResponse countdown() throws ParseException {
        return new CountdownResponse().setStartTime(new Date())
                .setEndTime(DateUtils.parseDate("2025-02-15 02:00:00", MediaUserConstant.dateTimeFormat));
    }

    public String getAppDownloadUrl(AppDownloadUrlRequest request){
//        TODO 邀请码是否有效 caoshuo
        clientUserService.inviteCodeExist(request.getInviteCode());

        AppReleaseResponse response = appReleaseService.getAppReleaseLatestByPlatformType(ClientInfoContext.getLanguage(), request.getPlatformType());
        if (response == null) {
            return null;
        }
        return response.getUrl();
    }
}

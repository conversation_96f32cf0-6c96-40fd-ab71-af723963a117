package com.media.user.service;

import com.media.core.config.SystemConfig;
import com.media.core.utils.VersionUtil;
import com.media.user.enums.EarlyBirdEndDateEnum;
import com.media.user.enums.LanguageEnums;
import com.media.user.enums.MiningStartDateEnum;
import com.media.core.request.ClientInfoContext;
import com.media.user.enums.MiningStartDateEnum;
import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

@Slf4j
@Service
public class ConfigSwitchService {

    @Autowired
    SystemConfig shareContent;

    @Value("${early.bird.endDate}")
    private Integer earlyBirdEndDate;

    @Value("${importuser.award.start}")
    private String awardStart;

    @Value("${importuser.award.end}")
    private String awardEnd;


    @Value("${directRewardVersionStart:1.2.2}")
    private String directRewardVersionStart;

    @Autowired
    SystemConfig systemConfig;

    /**
     * 审核状态：0:审核app 1: 不审核
     * @return
     */
    public Integer switchConfig() {
        try {
            String clientVersion = ClientInfoContext.get().getVersion();
            String platformType = ClientInfoContext.get().getPlatformType();
            if(platformType == null) {
                return 1;
            }
            String checkAppVersion = "";
            if(platformType.equalsIgnoreCase("android")) {
                checkAppVersion = shareContent.getCheckVersionAndroid();
            }else if(platformType.equalsIgnoreCase("android-gp")) {
                checkAppVersion = shareContent.getCheckVersionAndroidGp();
            }else if(platformType.equalsIgnoreCase("ios")) {
                checkAppVersion = shareContent.getCheckVersionIos();
            }
            if (checkAppVersion.equals(clientVersion)) {
                return 0;
            }
            return 1;
        }catch (Exception e){
            log.error("analysis head data error", e.getMessage());
        }
        return 1;
    }

    /**
     * 早鸟活动状态：0:不开启 1: 开启
     * @return
     */
    public Boolean earlySwitchConfig() {
        return false;
    }

    /**
     * 早鸟活动截止日期文案
     * @return
     */
    public String getEarlyBirdConfig(LanguageEnums languageEnums){
        if(languageEnums == null) {
            languageEnums = LanguageEnums.en_US;
        }
        String contentLanguage = EarlyBirdEndDateEnum.getContent(languageEnums.getValue());
        return buildSubject(contentLanguage, Map.of("day", earlyBirdEndDate) );
    }

    /**
     * 是否在挖矿阶段
     * @param date
     * @return
     */
    public Boolean IsMiningStarted(Date date) {
        return true;
    }


    private long date2Timestamp(String dateStr) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date date = sdf.parse(dateStr);
            return date.getTime(); // 获取时间戳（毫秒）
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0;
    }

    public Boolean isImportUserAwardTime(Date date) {
        String dateStr = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
        return StringUtils.compare(dateStr, awardStart)>=0 && StringUtils.compare(dateStr, awardEnd)<=0;
    }

    private String buildSubject(String template, Map<String, Object> data) {
        try {
            Configuration configuration = new Configuration(Configuration.VERSION_2_3_23);
            // 渲染标题
            StringTemplateLoader templateLoader = new StringTemplateLoader();

            templateLoader.putTemplate("subject", template); // template = 虚拟名称, 用来当作获取静态文件的key
            configuration.setTemplateLoader(templateLoader);
            Template subjectTemplate = configuration.getTemplate("subject", "utf-8");
            return FreeMarkerTemplateUtils.processTemplateIntoString(subjectTemplate, data);
        } catch (Exception e) {
            log.error("ConfigSwitchService.buildSubject err::{}", e.getMessage());
        }
        return null;
    }
}

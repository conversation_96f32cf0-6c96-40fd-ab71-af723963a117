package com.media.user.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 邀请人信息响应
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class InviterInfoResponse {
    
    /**
     * 邀请人用户ID
     */
    private Long inviterUserId;
    
    /**
     * 邀请人用户名
     */
    private String inviterUsername;
    
    /**
     * 邀请人邀请码
     */
    private String inviterInviteCode;
    
    /**
     * 邀请关系ID
     */
    private Long relationId;
}

package com.media.user.dto.response;

import lombok.Data;

import java.util.List;

/**
 * 融云私聊消息发送响应
 */
@Data
public class RongcloudMessageResponse {

    /**
     * 响应码
     * 200-成功，其他-失败
     */
    private Integer code;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 消息UID列表
     */
    private List<RongcloudMessageUID> messageUIDs;

    /**
     * 是否成功
     */
    public boolean isSuccess() {
        return code != null && code == 200;
    }

    /**
     * 获取第一个消息的UID（兼容旧版本）
     */
    public String getFirstMessageUID() {
        if (messageUIDs != null && !messageUIDs.isEmpty()) {
            return messageUIDs.get(0).getMessageUID();
        }
        return null;
    }

    /**
     * 获取第一个消息的用户ID（兼容旧版本）
     */
    public String getFirstUserId() {
        if (messageUIDs != null && !messageUIDs.isEmpty()) {
            return messageUIDs.get(0).getUserId();
        }
        return null;
    }
}
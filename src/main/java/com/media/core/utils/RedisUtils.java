package com.media.core.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class RedisUtils {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 统计某个特定操作的次数
     * @param key
     * @param timeout
     * @param timeUnit
     */
    public void incrementCount(String key, Long timeout, TimeUnit timeUnit) {
        // 增加计数
        stringRedisTemplate.opsForValue().increment(key, 1);
        // 设置有效期
        stringRedisTemplate.expire(key, timeout, timeUnit);
    }

    /**
     * 获取当前计数
     * @param key
     * @return
     */
    public Long getCount(String key) {
        String value = stringRedisTemplate.opsForValue().get(key);
        return value != null ? Long.parseLong(value) : 0;
    }
}

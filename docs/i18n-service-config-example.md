# i18n Service Configuration Example

## Apollo Configuration

Add the following configuration to your Apollo configuration:

### i18n Service URL
```properties
i18n.service.url=http://your-i18n-service-host:port
```

### i18n Service Configuration (JSON format)
```properties
i18n.service.config={
  "namespaces": [
    {
      "namespace": "user",
      "version": "v1.0.0"
    },
    {
      "namespace": "banner",
      "version": "v1.2.0"
    },
    {
      "namespace": "activity",
      "version": "v2.0.0"
    }
  ]
}
```

### Cache Refresh Interval (Optional)
```properties
# Cache refresh interval in minutes (default: 30)
i18n.cache.refresh.interval=30
```

## Expected i18n Service Response Format

The i18n service should return data in the following format:

```json
{
  "code": 200,
  "message": "success",
  "success": true,
  "result": {
    "namespace": "user",
    "version": "v1.0.0",
    "translations": {
      "xme.gift.activity.descriptionKey.ar_SA": "افتح & احصل",
      "xme.gift.activity.descriptionKey.ru_RU": "Открой & Получи",
      "xme.gift.activity.descriptionKey.en_US": "Open & Get",
      "xme.gift.activity.descriptionKey.zh_CN": "打开并获取",
      "xme.mining.ongoing.titleKey.en_US": "Mining Ongoing",
      "xme.mining.ongoing.titleKey.zh_CN": "挖矿进行中",
      "xme.rebate.titleKey.en_US": "Rebate Activity",
      "xme.rebate.titleKey.zh_CN": "返佣活动"
    },
    "metadata": {
      "count": 152,
      "lastUpdated": "2025-08-11T12:50:42.314Z"
    }
  },
  "request_id": "7846559f-ce85-43f5-9591-83d2f54a0d5a"
}
```

## How It Works

1. **Initialization**: On application startup, the `I18nCacheService` loads all configured namespaces and versions from the remote i18n service.

2. **Caching**: All translations are cached in local memory using a `ConcurrentHashMap` for fast access.

3. **Fallback**: If a translation is not found in the remote service cache, the system falls back to the existing local resource files.

4. **Refresh**: The cache is automatically refreshed at the configured interval (default: 30 minutes).

5. **Usage**: The `EnhancedI18nConvert` service provides the same interface as the original `I18nConvert` but with remote service integration.

## Benefits

- **Performance**: Translations are cached in memory for fast access
- **Reliability**: Automatic fallback to local resource files if remote service is unavailable
- **Flexibility**: Support for multiple namespaces and versions
- **Real-time Updates**: Translations can be updated remotely without application restart
- **Backward Compatibility**: Existing code continues to work with minimal changes

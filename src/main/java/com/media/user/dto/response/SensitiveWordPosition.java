package com.media.user.dto.response;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 敏感词位置信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SensitiveWordPosition {
    
    /**
     * 敏感词
     */
    private String word;
    
    /**
     * 开始位置
     */
    private Integer start;
    
    /**
     * 结束位置
     */
    private Integer end;
    
    /**
     * 原始文本
     */
    private String originalText;
    
    /**
     * 敏感级别
     */
    private Integer level;
    
    /**
     * 分类
     */
    private String category;
    
    /**
     * 语言
     */
    private String language;
} 
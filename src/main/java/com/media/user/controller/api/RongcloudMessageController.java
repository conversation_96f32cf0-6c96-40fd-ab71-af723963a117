package com.media.user.controller.api;

import com.media.core.i18n.I18nConvert;
import com.media.core.request.ClientInfoContext;
import com.media.user.dto.request.RongcloudMessageRequest;
import com.media.user.dto.response.RongcloudMessageResponse;
import com.media.user.service.RongcloudMessageService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 融云消息控制器
 * 提供融云消息发送的REST接口
 */
@Slf4j
@RestController
@RequestMapping("/user/api/rongcloud")
public class RongcloudMessageController {

    @Autowired
    private RongcloudMessageService rongcloudMessageService;

    /**
     * 发送简单文本消息
     */
    @PostMapping("/message/text")
    public RongcloudMessageResponse sendTextMessage(@RequestBody SendTextMessageRequest request) {
        log.info("language: {}", ClientInfoContext.getLanguage());
        String content = I18nConvert.getI18nMessage("invite.follow.imtext", ClientInfoContext.getLanguage());
        return rongcloudMessageService.sendTextMessage(
                request.getFromUserId(),
                request.getToUserId(),
                request.getContent(),
                request.getExtra());
    }

    /**
     * 发送自定义消息
     */
    @PostMapping("/message/custom")
    public RongcloudMessageResponse sendCustomMessage(@RequestBody RongcloudMessageRequest request) {
        return rongcloudMessageService.sendCustomMessage(request);
    }

    /**
     * 简单文本消息请求对象
     */
    @Data
    public static class SendTextMessageRequest {
        private String fromUserId;
        private String toUserId;
        private String content;
        private String extra;
    }
}
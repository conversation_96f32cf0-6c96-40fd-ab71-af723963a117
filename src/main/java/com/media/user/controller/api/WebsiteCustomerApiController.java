package com.media.user.controller.api;



import com.media.user.dto.request.*;
import com.media.user.dto.response.*;

import com.media.user.service.WebsiteCustomerService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;


@RestController
@RequestMapping("/user/api/website")
public class WebsiteCustomerApiController {

    @Autowired
    private WebsiteCustomerService websiteCustomerService;

    @PostMapping("/subscript")
    public void saveWebsiteCustomer (@Validated @RequestBody WebsiteCustomerRequest request){
        websiteCustomerService.saveWebsiteCustomer(request);
    }

    @PostMapping("/countdown")
    public CountdownResponse countdown() throws ParseException {
       return websiteCustomerService.countdown();
    }

    @PostMapping("/download")
    public String getAppDownloadUrl(@Validated @RequestBody AppDownloadUrlRequest request){
        return websiteCustomerService.getAppDownloadUrl(request);
    }

}

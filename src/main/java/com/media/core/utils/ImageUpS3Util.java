package com.media.core.utils;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 图片上传 s3
 */
@Slf4j
public class ImageUpS3Util {


    public static String upload(String originalFileUrl){
        try {
            URL url = new URL(originalFileUrl);
            URLConnection conn = url.openConnection();
            InputStream ins = conn.getInputStream();
            byte[] data = ins.readAllBytes();
            String base64Image = Base64.getEncoder().encodeToString(data);

            String[] fileArr = url.getFile().split("/");
            Map<String, String> request = new HashMap<>();
            request.put("filename", fileArr[fileArr.length - 1]);
            request.put("base64", base64Image);
            HttpRequest httpRequest = HttpRequest.post("https://api.x.me/public-upload/image/upload/base64");
            httpRequest.body(JSON.toJSONString(request));
            try (HttpResponse httpResponse = httpRequest.execute()) {
                String body = httpResponse.body();
			    return JSON.parseObject(body).getString("result");
            }
        }catch (Exception e){
            log.error("Image s3 upload error: {}", e.getMessage());
        }
        return null;
    }

}
package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("channel_config")
@Data
@Accessors(chain = true)
public class ChannelConfigModel extends Model<ChannelConfigModel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 渠道代码
     */
    @TableField("channel_code")
    private String channelCode;

    /**
     * 渠道ID
     */
    @TableField("channel_id")
    private Long channelId;

    /**
     * 邀请码
     */
    @TableField("invite_code")
    private String inviteCode;

    /**
     * 下载地址
     */
    @TableField("download_url")
    private String downloadUrl;

    /**
     * 二维码地址
     */
    @TableField("qr_code")
    private String qrCode;

    /**
     * 状态 0:禁用 1:启用
     */
    @TableField("status")
    private Integer status;
}

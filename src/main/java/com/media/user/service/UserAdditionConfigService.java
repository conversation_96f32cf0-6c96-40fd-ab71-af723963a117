package com.media.user.service;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.UserAdditionConfigModel;
import com.media.user.enums.UserStatusEnum;
import com.media.user.mapper.UserAdditionConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class UserAdditionConfigService {

    @Autowired
    private UserAdditionConfigMapper userAdditionConfigMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public List<UserAdditionConfigModel> getUserAdditionConfig(){
        if (!Boolean.TRUE.equals(stringRedisTemplate.hasKey(MediaUserConstant.USER_ADDITION_CONFIG))){
            QueryWrapper<UserAdditionConfigModel> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("status", UserStatusEnum.ENABLED.getStatus());
            List<UserAdditionConfigModel> userAdditionConfigModelList = userAdditionConfigMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(userAdditionConfigModelList)){
                return userAdditionConfigModelList;
            }
            Map<String, String> map = userAdditionConfigModelList.stream().collect(Collectors.toMap((userAdditionConfigModel) -> userAdditionConfigModel.getId().toString(), JSON::toJSONString));
            stringRedisTemplate.opsForHash().putAll(MediaUserConstant.USER_ADDITION_CONFIG, map);
            return userAdditionConfigModelList;
        }
        Map<Object, Object> userAddittionModelMap = stringRedisTemplate.opsForHash().entries(MediaUserConstant.USER_ADDITION_CONFIG);
        if (CollectionUtils.isEmpty(userAddittionModelMap)){
            return new ArrayList<>();
        }
        return userAddittionModelMap.values().stream().map(str -> JSON.parseObject(str.toString(), UserAdditionConfigModel.class)).collect(Collectors.toList());
    }

}
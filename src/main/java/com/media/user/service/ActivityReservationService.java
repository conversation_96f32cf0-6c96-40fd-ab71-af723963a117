package com.media.user.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.media.core.constant.KafkaTopicConstant;
import com.media.core.exception.ApiException;
import com.media.core.utils.RedisUtils;
import com.media.user.domain.ActivityReservationModel;
import com.media.user.domain.ClientUserModel;
import com.media.user.dto.request.ActivityReservationRequest;
import com.media.user.dto.request.AddUserPropProd;
import com.media.user.dto.request.LeadOrderRemindRequest;
import com.media.user.dto.request.XmeRedemptionRequest;
import com.media.user.dto.response.AddUserPropResponse;
import com.media.user.feign.client.UserPointClient;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.mapper.ActivityReservationMapper;
import com.media.user.mapper.ClientUserMapper;
import com.media.user.mq.KafkaMessageProducer;
import com.media.user.tools.PhoneUtils;
import com.xme.xme_base_depends.models.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ActivityReservationService {

    @Autowired
    private ActivityReservationMapper activityReservationMapper;

    @Autowired
    private PhoneUtils phoneUtils;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private ClientUserMapper clientUserMapper;

    @Autowired
    private MediaTaskService mediaTaskService;

    @Autowired
    private KafkaMessageProducer kafkaMessageProducer;

    @Autowired
    private UserPointClient userPointClient;

    @Value("${activity.reservation.anti.abuse.ip.limit:20}")
    private int ipLimitPerHour;

    @Value("${activity.reservation.anti.abuse.email.limit:5}")
    private int emailLimitPerDay;

    @Value("${activity.reservation.anti.abuse.phone.limit:5}")
    private int phoneLimitPerDay;

    /**
     * 活动预约处理，收集用户信息
     * 用户注册后将获得道具奖励
     * @param request 活动预约请求
     * @param clientIp 客户端IP地址，用于防刷
     */
    @Transactional(rollbackFor = Exception.class)
    public void reserveActivity(ActivityReservationRequest request, String clientIp) {
        // 参数校验
        if (request == null) {
            log.error("activityReserve request invalid: {}", request);
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }

        // 检查IP防刷限制
        checkIpRateLimit(clientIp);

        // 检查邮箱/手机号防刷限制
        if (StringUtils.isNotBlank(request.getEmail())) {
            checkEmailRateLimit(request.getEmail());
        } else if (StringUtils.isNotBlank(request.getPhone())) {
            checkPhoneRateLimit(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());
        } else {
            log.error("activityReserve missing both email and phone");
            throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
        }

        if(StringUtils.isNotBlank(request.getEmail())) {
            log.info("activityReserve by email: {} for activity type: {}", request.getEmail(), request.getActivityType());

            try {
                // 查询该邮箱的现有预约记录
                QueryWrapper<ActivityReservationModel> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("email", request.getEmail());
                queryWrapper.eq("activity_type", request.getActivityType());

                // 检查是否已经预约过
                ActivityReservationModel existingReservation = activityReservationMapper.selectOne(queryWrapper);
                if (existingReservation != null) {
                    log.info("Email {} has already made a reservation for activity type: {}", request.getEmail(), request.getActivityType());
                    throw new ApiException(MediaUserExceptionCodeApi.ALREADY_RESERVED);
                }

                // 创建新的预约记录
                ActivityReservationModel reservationModel = new ActivityReservationModel();
                reservationModel.setEmail(request.getEmail());
                reservationModel.setActivityType(request.getActivityType());
                reservationModel.setCreatedTime(new Date());
                reservationModel.setUpdatedTime(new Date());
                reservationModel.setPropsGiven(false);
                reservationModel.setClientIp(clientIp);

                // 默认道具类型和数量，可以根据活动类型设置不同的值
                reservationModel.setPropsType("DEFAULT_PROP");
                reservationModel.setPropsAmount(1);

                int inserted = activityReservationMapper.insert(reservationModel);

                if (inserted <= 0) {
                    log.error("Failed to insert new email reservation record for: {}", request.getEmail());
                    throw new RuntimeException("Failed to create new email reservation record");
                }

                log.info("Created new email reservation record: {} for activity: {}, result: {}",
                        request.getEmail(), request.getActivityType(), inserted > 0);

                // 记录到Redis中，用于后续用户注册时检查是否需要发放道具
                String redisKey = "activity:reserve:email:" + request.getEmail();
                stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(reservationModel.getId()), 30, TimeUnit.DAYS); // 30天有效期
            } catch (ApiException e) {
                throw e; // 直接抛出业务异常
            } catch (Exception e) {
                log.error("Transaction failed during email reservation process: {}", e.getMessage(), e);
                throw e; // 确保事务回滚
            }
        } else if(StringUtils.isNotBlank(request.getPhone())) {
            if (StringUtils.isBlank(request.getCountryCode()) || StringUtils.isBlank(request.getPhonePrefix())) {
                log.error("Phone information incomplete");
                throw new ApiException(MediaUserExceptionCodeApi.INCORRECT_PARAMETERS);
            }

            log.info("activityReserve by phone: {}-{}-{} for activity type: {}",
                    request.getCountryCode(), request.getPhonePrefix(), request.getPhone(), request.getActivityType());

            // 校验手机号格式
            phoneUtils.checkPhoneRegex(request.getCountryCode(), request.getPhonePrefix(), request.getPhone());

            try {
                // 查询该手机号的现有预约记录
                QueryWrapper<ActivityReservationModel> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("country_code", request.getCountryCode());
                queryWrapper.eq("phone_prefix", request.getPhonePrefix());
                queryWrapper.eq("phone", request.getPhone());
                queryWrapper.eq("activity_type", request.getActivityType());

                // 检查是否已经预约过
                ActivityReservationModel existingReservation = activityReservationMapper.selectOne(queryWrapper);
                if (existingReservation != null) {
                    log.info("Phone {}-{}-{} has already made a reservation for activity type: {}",
                            request.getCountryCode(), request.getPhonePrefix(), request.getPhone(), request.getActivityType());
                    throw new ApiException(MediaUserExceptionCodeApi.ALREADY_RESERVED);
                }

                // 创建新的预约记录
                ActivityReservationModel reservationModel = new ActivityReservationModel();
                reservationModel.setCountryCode(request.getCountryCode());
                reservationModel.setPhonePrefix(request.getPhonePrefix());
                reservationModel.setPhone(request.getPhone());
                reservationModel.setActivityType(request.getActivityType());
                reservationModel.setCreatedTime(new Date());
                reservationModel.setUpdatedTime(new Date());
                reservationModel.setPropsGiven(false);
                reservationModel.setClientIp(clientIp);

                // 默认道具类型和数量，可以根据活动类型设置不同的值
                reservationModel.setPropsType("DEFAULT_PROP");
                reservationModel.setPropsAmount(1);

                int inserted = activityReservationMapper.insert(reservationModel);

                if (inserted <= 0) {
                    log.error("Failed to insert new phone reservation record for: {}-{}-{}",
                            request.getCountryCode(), request.getPhonePrefix(), request.getPhone());
                    throw new RuntimeException("Failed to create new phone reservation record");
                }

                log.info("Created new phone reservation record: {}-{}-{} for activity: {}, result: {}",
                        request.getCountryCode(), request.getPhonePrefix(), request.getPhone(),
                        request.getActivityType(), inserted > 0);

                // 记录到Redis中，用于后续用户注册时检查是否需要发放道具
                String redisKey = "activity:reserve:phone:" + request.getCountryCode() + ":" + request.getPhonePrefix() + ":" + request.getPhone();
                stringRedisTemplate.opsForValue().set(redisKey, String.valueOf(reservationModel.getId()), 30, TimeUnit.DAYS); // 30天有效期
            } catch (ApiException e) {
                throw e; // 直接抛出业务异常
            } catch (Exception e) {
                log.error("Transaction failed during phone reservation process: {}", e.getMessage(), e);
                throw e; // 确保事务回滚
            }
        }
    }

    /**
     * 检查IP频率限制
     * @param clientIp 客户端IP地址
     */
    private void checkIpRateLimit(String clientIp) {
        if (StringUtils.isBlank(clientIp)) {
            log.warn("Client IP is empty, skipping IP rate limit check");
            return;
        }

        String key = "activity:reserve:ip:limit:" + clientIp;
        Long count = redisUtils.getCount(key);

        if (count >= ipLimitPerHour) {
            log.warn("IP {} has exceeded rate limit: {} requests per hour", clientIp, ipLimitPerHour);
            throw new ApiException(MediaUserExceptionCodeApi.INVITE_IP_RATE_LIMIT);
        }

        // 增加计数并设置1小时过期
        redisUtils.incrementCount(key, 1L, TimeUnit.HOURS);
        log.debug("IP {} request count: {} in the last hour", clientIp, count + 1);
    }

    /**
     * 检查邮箱频率限制
     * @param email 邮箱地址
     */
    private void checkEmailRateLimit(String email) {
        if (StringUtils.isBlank(email)) {
            return;
        }

        String key = "activity:reserve:email:limit:" + email;
        Long count = redisUtils.getCount(key);

        if (count >= emailLimitPerDay) {
            log.warn("Email {} has exceeded rate limit: {} requests per day", email, emailLimitPerDay);
            throw new ApiException(MediaUserExceptionCodeApi.INVITE_EMAIL_RATE_LIMIT);
        }

        // 增加计数并设置24小时过期
        redisUtils.incrementCount(key, 24L, TimeUnit.HOURS);
        log.debug("Email {} request count: {} in the last 24 hours", email, count + 1);
    }

    /**
     * 检查手机号频率限制
     * @param countryCode 国家代码
     * @param phonePrefix 手机区号
     * @param phone 手机号
     */
    private void checkPhoneRateLimit(String countryCode, String phonePrefix, String phone) {
        if (StringUtils.isBlank(countryCode) || StringUtils.isBlank(phonePrefix) || StringUtils.isBlank(phone)) {
            return;
        }

        String phoneKey = countryCode + ":" + phonePrefix + ":" + phone;
        String key = "activity:reserve:phone:limit:" + phoneKey;
        Long count = redisUtils.getCount(key);

        if (count >= phoneLimitPerDay) {
            log.warn("Phone {}-{}-{} has exceeded rate limit: {} requests per day",
                    countryCode, phonePrefix, phone, phoneLimitPerDay);
            throw new ApiException(MediaUserExceptionCodeApi.INVITE_PHONE_RATE_LIMIT);
        }

        // 增加计数并设置24小时过期
        redisUtils.incrementCount(key, 24L, TimeUnit.HOURS);
        log.debug("Phone {}-{}-{} request count: {} in the last 24 hours",
                countryCode, phonePrefix, phone, count + 1);
    }

    /**
     * 根据预约信息查询用户并发放道具
     * 如果用户存在，则调用发放道具接口
     *
     * @param reservationId 预约ID
     * @return 是否成功发放道具
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean distributePropsForReservation(Long reservationId) {
        // 查询预约信息
        ActivityReservationModel reservation = activityReservationMapper.selectById(reservationId);
        if (reservation == null) {
            log.warn("Reservation not found with id: {}", reservationId);
            return false;
        }

        // 检查是否已经发放过道具
        if (Boolean.TRUE.equals(reservation.getPropsGiven())) {
            log.info("Props already given for reservation: {}", reservationId);
            return true;
        }


        // 查找用户
        ClientUserModel user = null;

        // 如果已经有UID，直接使用
        if (reservation.getUid() != null) {
            user = clientUserMapper.selectByUserId(reservation.getUid());
        }
        // 否则尝试通过邮箱查找
        else if (StringUtils.isNotBlank(reservation.getEmail())) {
            user = clientUserMapper.selectByEmail(reservation.getEmail());
        }
        // 最后尝试通过手机号查找
        else if (StringUtils.isNotBlank(reservation.getPhone()) &&
                 StringUtils.isNotBlank(reservation.getCountryCode()) &&
                 StringUtils.isNotBlank(reservation.getPhonePrefix())) {
            user = clientUserMapper.selectByPhone(
                reservation.getCountryCode(),
                reservation.getPhonePrefix(),
                reservation.getPhone()
            );
        }

        // 如果找不到用户，返回失败
        if (user == null) {
            log.info("No user found for reservation: {}", reservationId);
            return false;
        }

        // 准备道具列表
        List<AddUserPropProd> propsList = new ArrayList<>();
        propsList.add(new AddUserPropProd("GOLD_PICKAXE",1));

        // 调用发放道具接口
        AddUserPropResponse response = mediaTaskService.addUserProp(user.getUid(), propsList, String.format("activity:reserve:%d",reservationId));

        // 检查发放结果
        boolean success = response != null && response.getSuccess() != null && response.getSuccess();

        //发送50个XME
        try {
            String xmeIdempotentId = DigestUtils.md5Hex("activity:reservation:xme:" + reservationId);
            XmeRedemptionRequest xmeRequest = XmeRedemptionRequest.builder()
                    .uid(user.getUid())
                    .amount(50)
                    .ccy("XME")
                    .network("XME")
                    .eventId(35)
                    .idempotent(xmeIdempotentId)
                    .build();

            ApiResponse xmeResponse = userPointClient.xmeRedemptionFromOP(xmeRequest);
            if (xmeResponse != null && Boolean.TRUE.equals(xmeResponse.getSuccess())) {
                log.info("Successfully sent 50 XME to user {} for reservation {}", user.getUid(), reservationId);
            } else {
                log.error("Failed to send XME to user {} for reservation {}: {}",
                        user.getUid(), reservationId,
                        xmeResponse != null ? xmeResponse.getMessage() : "null response");
            }
        } catch (Exception e) {
            log.error("Error sending XME to user {} for reservation {}", user.getUid(), reservationId, e);
        }

        //发送消息
        try {
            LeadOrderRemindRequest req = new LeadOrderRemindRequest();
            req.setUid(user.getUid());
            kafkaMessageProducer.sendMessage(KafkaTopicConstant.LEAD_ORDER_REMIND, JSONObject.toJSONString(req));
            log.debug("Sent lead order remind message for user {}", user.getUid());
        } catch (Exception e) {
            log.error("Error sending lead order remind message for user {}", user.getUid(), e);
        }
        if (success) {
            // 更新预约记录，标记为已发放道具
            UpdateWrapper<ActivityReservationModel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda()
                    .eq(ActivityReservationModel::getId, reservationId)
                    .set(ActivityReservationModel::getPropsGiven, true)
                    .set(ActivityReservationModel::getUid, user.getUid())
                    .set(ActivityReservationModel::getUpdatedTime, new Date());
            activityReservationMapper.update(updateWrapper);

            log.info("Successfully distributed props to user {} for reservation {}", user.getUid(), reservationId);
        } else {
            log.error("Failed to distribute props to user {} for reservation {}", user.getUid(), reservationId);
        }

        return success;
    }

    /**
     * 查找所有uid为null的预约记录，根据邮箱或手机号查询用户
     * 如果找到用户，则设置uid并发放道具奖励
     *
     * @param activityType 活动类型，可为null表示处理所有类型
     * @param limit 每次处理的最大记录数
     * @return 成功处理的记录数
     */
    @Transactional(rollbackFor = Exception.class)
    public int processReservationsWithoutUid(String activityType, int limit) {
        // 构建查询条件
        QueryWrapper<ActivityReservationModel> queryWrapper = new QueryWrapper<>();
        queryWrapper.isNull("uid");
        queryWrapper.eq("props_given", false);
        if (StringUtils.isNotBlank(activityType)) {
            queryWrapper.eq("activity_type", activityType);
        }
        queryWrapper.last("LIMIT " + limit);

        // 查询符合条件的预约记录
        List<ActivityReservationModel> reservations = activityReservationMapper.selectList(queryWrapper);
        if (reservations.isEmpty()) {
            log.info("No reservations without UID found");
            return 0;
        }

        log.info("Found {} reservations without UID to process", reservations.size());
        int successCount = 0;

        // 逐个处理预约记录
        for (ActivityReservationModel reservation : reservations) {
            try {
                // 查找用户
                ClientUserModel user = findUserByReservation(reservation);

                if (user != null) {
                    // 更新预约记录的UID
                    reservation.setUid(user.getUid());
                    UpdateWrapper<ActivityReservationModel> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.lambda()
                            .eq(ActivityReservationModel::getId, reservation.getId())
                            .set(ActivityReservationModel::getUid, user.getUid())
                            .set(ActivityReservationModel::getUpdatedTime, new Date());
                    activityReservationMapper.update(updateWrapper);

                    // 分发道具
                    if (distributePropsForReservation(reservation.getId())) {
                        successCount++;
                    }
                } else {
                    log.debug("No user found for reservation ID: {}", reservation.getId());
                }
            } catch (Exception e) {
                log.error("Error processing reservation ID: {}", reservation.getId(), e);
            }
        }

        log.info("Successfully processed {} out of {} reservations", successCount, reservations.size());
        return successCount;
    }

    /**
     * 根据预约信息查找用户
     *
     * @param reservation 预约信息
     * @return 用户模型，如果找不到则返回null
     */
    private ClientUserModel findUserByReservation(ActivityReservationModel reservation) {
        ClientUserModel user = null;

        // 先尝试通过邮箱查找
        if (StringUtils.isNotBlank(reservation.getEmail())) {
            user = clientUserMapper.selectByEmail(reservation.getEmail());
            if (user != null) {
                log.debug("Found user by email: {} for reservation ID: {}", reservation.getEmail(), reservation.getId());
                return user;
            }
        }

        // 再尝试通过手机号查找
        if (StringUtils.isNotBlank(reservation.getPhone()) &&
            StringUtils.isNotBlank(reservation.getCountryCode()) &&
            StringUtils.isNotBlank(reservation.getPhonePrefix())) {

            user = clientUserMapper.selectByPhone(
                reservation.getCountryCode(),
                reservation.getPhonePrefix(),
                reservation.getPhone()
            );

            if (user != null) {
                log.debug("Found user by phone: {}-{}-{} for reservation ID: {}",
                        reservation.getCountryCode(), reservation.getPhonePrefix(),
                        reservation.getPhone(), reservation.getId());
            }
        }

        return user;
    }
}

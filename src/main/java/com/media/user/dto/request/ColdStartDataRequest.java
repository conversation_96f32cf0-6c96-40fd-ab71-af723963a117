package com.media.user.dto.request;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 冷启动数据请求实体
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ColdStartDataRequest {
    
    /**
     * 平台类型
     */
    private String platformType;
    
    /**
     * 版本号
     */
    private String version;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 用户ID
     */
    private Long uid;
    
    /**
     * 用户IP
     */
    private String userIP;
    
    /**
     * 时间戳
     */
    private String timestamp;
} 
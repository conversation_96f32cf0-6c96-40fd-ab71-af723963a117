package com.media.user;

import com.media.user.mapper.UserFollowRelationMapper;
import com.media.user.service.twitter.impl.TwitterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class TwitterTest {

    @Autowired
    protected TwitterService twitterService;

    @Autowired
    UserFollowRelationMapper userFollowRelationMapper;

//    @Test
//    public void user() {
//        TwitterUser twitterUser = twitterService.twitterUser("c2FpOW5xdTJNQXhaNEZTdXB0NzQ4THlQdHRlNVZqSWxnWkt0V2tjNHNDcFNOOjE3MzE5OTcxNzcxNTQ6MTowOmF0OjE");
//        System.out.println(twitterUser);

//        PlatTwitterConfigModel twitterConfigModel = platTwitterConfigMapper.selectById(155L);
//        twitterService.refreshToken(twitterConfigModel);

//        QueryWrapper<UserFollowRelationModel> followWrapper = new QueryWrapper<>();
//        followWrapper.lambda().eq(UserFollowRelationModel::getFollowUid, 1);
//        followWrapper.and(
//                qr -> qr.lambda().eq(UserFollowRelationModel::getFollowedOldUid, 2)
//                        .or(qrw -> qrw.eq(UserFollowRelationModel::getFollowedNewUid, 3)));
//        UserFollowRelationModel followRelationModel = userFollowRelationMapper.selectOne(followWrapper);
//        System.out.println(followRelationModel);

//        String accessToken = "QTNDdGFBNnQ5a05oNm92RjZXc2NNejNYMWRxOVU0c1RJRVFNTC15a001RmZnOjE3MzIwODU0NDU0MDg6MToxOmF0OjE";
//        String twitterId = "1821456735963627520";
//        twitterId = "44196397";
//        String url = "https://api.twitter.com/2/users/" + twitterId + "?user.fields=affiliation,connection_status,created_at,description,entities,id,location,most_recent_tweet_id,name,pinned_tweet_id,profile_banner_url,profile_image_url,protected,public_metrics,receives_your_dm,subscription_type,url,username,verified,verified_type,withheld";
//        HttpRequest httpRequest = new HttpRequest(UrlBuilder.of(url));
//        httpRequest.header("Authorization","Bearer " + accessToken);
//        httpRequest.method(Method.GET);
//        try (HttpResponse httpResponse = httpRequest.execute()) {
//            if (httpResponse.isOk()) {
//                String responseBoy = httpResponse.body();
//                System.out.println(responseBoy);
//            }
//        }
//    }



}

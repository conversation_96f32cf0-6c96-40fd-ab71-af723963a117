package com.media.user.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Getter
public enum CcyEnums {
    XME("XME", "XME"), USDT("USDT", "USDT"), BTC("BTC", "BTC(sats)"),
    ;

    private final String ccy;
    private final String unit;

    private static final Map<String, CcyEnums> maps = Arrays.stream(values()).collect(Collectors.toMap(CcyEnums::getCcy, Function.identity()));

    CcyEnums(String ccy, String unit) {
        this.ccy = ccy;
        this.unit = unit;
    }

    public static CcyEnums form(String ccy) {
        return maps.get(ccy);
    }
}


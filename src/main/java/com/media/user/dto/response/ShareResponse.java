package com.media.user.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * 彩票分享响应数据
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ShareResponse {
    // 用户邀请码
    private String invitationCode;

    // 分享链接
    private String shareUrl;

    // 分享提示标题
    private String sharePromptTitle;

    // 分享提示描述
    private String sharePromptDescription;

    // 分享海报类型 1 XME  2 美元
    private Integer sharePosterType;

}

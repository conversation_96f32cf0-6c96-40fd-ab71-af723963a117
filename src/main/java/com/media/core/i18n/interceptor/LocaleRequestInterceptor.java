package com.media.core.i18n.interceptor;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


import java.util.Enumeration;

public class LocaleRequestInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate t) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (servletRequestAttributes != null) {
            HttpServletRequest request = servletRequestAttributes.getRequest();
            String name = "accept-language";
            t.header(name, request.getHeader(name));
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                if (headerName.toLowerCase().startsWith("gs-")) {
                    String value = request.getHeader(headerName);
                    t.header(headerName, value);
                }
            }
        }
    }

}

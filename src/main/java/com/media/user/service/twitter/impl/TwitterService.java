package com.media.user.service.twitter.impl;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.scribejava.core.model.OAuth2AccessToken;
import com.github.scribejava.core.pkce.PKCE;
import com.github.scribejava.core.pkce.PKCECodeChallengeMethod;
import com.media.core.config.twitter.TwitterConfiguration;
import com.media.core.exception.ApiException;
import com.media.core.utils.ImageUpS3Util;
import com.media.user.domain.TwitterUserModel;
import com.media.user.exception.MediaUserExceptionCodeApi;
import com.media.user.mapper.TwitterUserMapper;
import com.media.user.model.request.twitter.PublicMetrics;
import com.media.user.model.request.twitter.TwitterUser;
import com.media.user.model.request.twitter.TwitterUserData;
import com.media.user.service.twitter.TwitterUserService;
import com.twitter.clientlib.auth.TwitterOAuth20Service;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import twitter4j.Twitter;
import twitter4j.TwitterFactory;
import twitter4j.conf.ConfigurationBuilder;

import java.util.Date;

import static com.media.user.enums.TwitterBindStateEnum.BIND;
import static com.media.user.enums.TwitterBindStateEnum.UNBIND;

@Service
public class TwitterService extends ServiceImpl<TwitterUserMapper, TwitterUserModel> implements TwitterUserService {

    protected static Logger logger = LoggerFactory.getLogger(TwitterService.class);

    @Autowired
    protected TwitterUserMapper twitterUserMapper;

    @Autowired
    private TwitterOAuth20Service twitterOAuth20Service;

    @Autowired
    TwitterConfiguration twitterConfiguration;

    /**
     * state=xme_binding;1091090340324
     * @param secretState
     * @return
     */
    public String getAuthorizationUrl(String secretState) {
        logger.info("Fetching the Authorization URL...");
        PKCE pkce = new PKCE();
        pkce.setCodeChallenge("challenge");
        pkce.setCodeChallengeMethod(PKCECodeChallengeMethod.PLAIN);
        pkce.setCodeVerifier("challenge");
        return twitterOAuth20Service.getAuthorizationUrl(pkce, secretState);
    }


    public Twitter twitterAuth(String authToken, String authTokenSecret){
        try {
            ConfigurationBuilder cb = new ConfigurationBuilder();
            cb.setDebugEnabled(true)
                    .setOAuthConsumerKey(twitterConfiguration.getApiKeyId())
                    .setOAuthConsumerSecret(twitterConfiguration.getApiKeySecret())
                    .setOAuthAccessToken(authToken)
                    .setOAuthAccessTokenSecret(authTokenSecret)
            ;
            TwitterFactory tf = new TwitterFactory(cb.build());
            Twitter twitter = tf.getInstance();
            twitter.getAccountSettings();
            return twitter;
        } catch (Exception e) {
            logger.error("twitter auth failed: {}", e.getMessage());
        }
        return null;
    }

    /**
     * TODO 废弃掉
     * @param code
     * @return
     */
    public OAuth2AccessToken getOAuth2AccessToken(String code) {
        OAuth2AccessToken accessToken = null;
        try {
            PKCE pkce = new PKCE();
            pkce.setCodeChallenge("challenge");
            pkce.setCodeChallengeMethod(PKCECodeChallengeMethod.PLAIN);
            pkce.setCodeVerifier("challenge");
            accessToken = twitterOAuth20Service.getAccessToken(pkce, code);
        } catch (Exception e) {
            logger.error("Error while getting the access token:\n " + e);
        }
        return accessToken;
    }

    /**
     * 查询当前用户是否绑定了 twitter
     */
    public TwitterUserModel checkUserTwitterBind(Long uid) {
        QueryWrapper<TwitterUserModel> twitterWrapper = new QueryWrapper<>();
        twitterWrapper.lambda().eq(TwitterUserModel::getNewUid, uid);
        twitterWrapper.lambda().eq(TwitterUserModel::getBind, BIND.getState());
        return twitterUserMapper.selectOne(twitterWrapper);
    }

    /**
     * 查询是否有 twitter 用户
     */
    public TwitterUserModel getByTwitterId(String twitterId) {
        QueryWrapper<TwitterUserModel> twitterWrapper = new QueryWrapper<>();
        twitterWrapper.lambda().eq(TwitterUserModel::getTwitterId, twitterId);
        return twitterUserMapper.selectOne(twitterWrapper);
    }


    /**
     * 获取twitter 用户详情
     * @param accessToken
     * @return
     */
    public TwitterUser twitterUser(String accessToken) {
        String url = "https://api.twitter.com/2/users/me?user.fields=affiliation,connection_status,created_at,description,entities,id,location,most_recent_tweet_id,name,pinned_tweet_id,profile_banner_url,profile_image_url,protected,public_metrics,receives_your_dm,subscription_type,url,username,verified,verified_type,withheld";
        HttpRequest httpRequest = new HttpRequest(UrlBuilder.of(url));
        httpRequest.header("Authorization","Bearer " + accessToken);
        httpRequest.method(Method.GET);
        try (HttpResponse httpResponse = httpRequest.execute()) {
            if (httpResponse.isOk()) {
                String responseBoy = httpResponse.body();
                logger.info("twitter user:{}", responseBoy);
                TwitterUserData twitterUserData = JSONObject.parseObject(responseBoy, TwitterUserData.class);
                return twitterUserData.getData().get(0);
            }
        }
        return null;
    }

    /**
     * 获取twitter 用户详情
     * @param accessToken
     * @return
     */
    public TwitterUser twitterUserByTwitterId(String twitterId, String accessToken) {
        String url = "https://api.twitter.com/2/users/" + twitterId + "?user.fields=affiliation,connection_status,created_at,description,entities,id,location,most_recent_tweet_id,name,pinned_tweet_id,profile_banner_url,profile_image_url,protected,public_metrics,receives_your_dm,subscription_type,url,username,verified,verified_type,withheld";
        HttpRequest httpRequest = new HttpRequest(UrlBuilder.of(url));
        httpRequest.header("Authorization","Bearer " + accessToken);
        httpRequest.method(Method.GET);
        try (HttpResponse httpResponse = httpRequest.execute()) {
            System.out.println(httpResponse);
            if (httpResponse.isOk()) {
                String responseBoy = httpResponse.body();
                TwitterUserData twitterUserData = JSONObject.parseObject(responseBoy, TwitterUserData.class);
                return twitterUserData.getData().get(0);
            }else{
                throw new ApiException(MediaUserExceptionCodeApi.SERVER_ERROR);
            }
        }
    }

    /**
     * 保存绑定关系
     * @param twitterUser
     * @param uid
     */
    @Transactional
    public void saveBindTwitterUserModel(Long uid, TwitterUser twitterUser) {
        var twitterUsers = twitterUserMapper.selectByUnion(uid, twitterUser.getId(), BIND.getState());
        if (!CollectionUtils.isEmpty(twitterUsers)) {
            throw new ApiException(MediaUserExceptionCodeApi.TWITTER_ALREADY_BIND_ERROR);
        }
        TwitterUserModel saveModel = new TwitterUserModel();
        BeanUtils.copyProperties(twitterUser, saveModel);
        saveModel.setTwitterId(twitterUser.getId());
        saveModel.setNewUid(uid);
        saveModel.setBind(BIND.getState());
        saveModel.setBindTime(new Date());
        saveModel.setCreatedTime(new Date());
        saveModel.setVerified(twitterUser.isVerified() ? 1 : 0);
        saveModel.set_protected(twitterUser.is_protected() ? 1 : 0);
        saveModel.setReceivesYourDm(twitterUser.isReceivesYourDm() ? 1 : 0);
        saveModel.setFixInfoType(0);
        saveModel.setRecommendType(1);
        if (StringUtils.isNotBlank(twitterUser.getProfileImageUrl())) {
            saveModel.setOriginalImageUrl(twitterUser.getProfileImageUrl());
        }
        PublicMetrics publicMetrics = twitterUser.getPublicMetrics();
        if (publicMetrics != null) {
            saveModel.setFollowersCount(publicMetrics.getFollowersCount());
            saveModel.setFollowingCount(publicMetrics.getFollowingCount());
            saveModel.setTweetCount(publicMetrics.getTweetCount());
            saveModel.setListedCount(publicMetrics.getListedCount());
            saveModel.setLikeCount(publicMetrics.getLikeCount());
        }
        saveModel.setAuthToken(twitterUser.getAuthToken());
        saveModel.setAuthTokenSecret(twitterUser.getAuthTokenSecret());
        saveModel.setAccessToken(twitterUser.getAccessToken());
        saveModel.setRefreshToken(twitterUser.getRefreshToken());
        saveModel.setScopes(twitterUser.getScopes());
        saveModel.setExpireAt(twitterUser.getExpireAt());
        saveModel.setFollowedOfficial(1);// 目前直接自动给关注
        saveOrUpdate(saveModel);
        this.modifyTwitterImageUrl(saveModel.getProfileImageUrl(), saveModel.getTwitterId());
    }


    public void bindCancel(Long uid){
        UpdateWrapper<TwitterUserModel> updateTwitterUserWrapper = new UpdateWrapper<>();
        updateTwitterUserWrapper.lambda()
                .eq(TwitterUserModel::getNewUid, uid)
                .set(TwitterUserModel::getBind, UNBIND.getState());
        twitterUserMapper.update(null, updateTwitterUserWrapper);
    }


    //上传 twitter 的头像至 s3
    @Async("threadPoolTaskExecutor")
    public void modifyTwitterImageUrl(String profileImageUrl, String twitterId){
        String imageUrl = ImageUpS3Util.upload(profileImageUrl);
        if(StringUtils.isNotBlank(imageUrl)){
            twitterUserMapper.modifyProfileImageUrl(imageUrl, twitterId);
        }
    }

    public void followOfficialAccount(Long uid, TwitterUser twitterUser) {
        // 官方Twitter账号ID，从配置中读取
        String officialTwitterId = twitterConfiguration.getOfficialAccountId();
        if (StringUtils.isBlank(officialTwitterId)) {
            logger.error("Official Twitter account ID is not configured");
            throw new ApiException(MediaUserExceptionCodeApi.TWITTER_OFFICIAL_ID_NOT_CONFIGURED);
        }
        try {
            // 构建关注API请求
            String url = "https://api.twitter.com/2/users/" + twitterUser.getId() + "/following";
            HttpRequest httpRequest = new HttpRequest(UrlBuilder.of(url));
            httpRequest.header("Authorization", "Bearer " + twitterUser.getAccessToken());
            httpRequest.header("Content-Type", "application/json");
            httpRequest.method(Method.POST);

            // 请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("target_user_id", officialTwitterId);
            httpRequest.body(requestBody.toJSONString());

            // 发送请求
            try (HttpResponse httpResponse = httpRequest.execute()) {
                if (httpResponse.isOk()) {
                    logger.info("User {} successfully followed official account", uid);
                } else {
                    logger.error("Failed to follow official account for user {}, response: {}", uid, httpResponse.body());
                    throw new ApiException(MediaUserExceptionCodeApi.TWITTER_FOLLOW_OFFICIAL_FAILED);
                }
            }
        } catch (ApiException e) {
            // 直接抛出已定义的API异常
            throw e;
        } catch (Exception e) {
            logger.error("Error when following official account for user {}: {}", uid, e.getMessage(), e);
            throw new ApiException(MediaUserExceptionCodeApi.TWITTER_FOLLOW_OFFICIAL_FAILED);
        }
    }
}

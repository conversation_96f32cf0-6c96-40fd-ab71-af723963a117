package com.media.user.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 登录风险请求参数
 */
@Data
@Accessors(chain = true)
public class LoginRiskRequest {

    /**
     * 用户ID
     */
    @JsonProperty("user_id")
    private Long userId;

    /**
     * IP地址
     */
    @JsonProperty("ip")
    private String ip;

    /**
     * 设备盒子ID
     */
    @JsonProperty("box_id")
    private String boxId;

    /**
     * 用户代理
     */
    @JsonProperty("user_agent")
    private String userAgent;

    /**
     * 应用版本
     */
    @JsonProperty("app_version")
    private String appVersion;

    /**
     * 操作系统
     */
    @JsonProperty("os")
    private String os;

    /**
     * 登录类型
     */
    @JsonProperty("login_type")
    private String loginType;

    /**
     * 登录是否有效
     */
    @JsonProperty("login_valid")
    private Integer loginValid;
}

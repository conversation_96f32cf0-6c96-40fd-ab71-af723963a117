package com.media.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.media.user.domain.UserFamilyDayAssetsModel;
import com.media.user.dto.query.UserFamilyDayAssetsSumQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface UserFamilyDayAssetsMapper extends BaseMapper<UserFamilyDayAssetsModel> {

    @Select("<script> select sum(ufda.member_point_total) as member_point_total, sum(ufda.points_bonus) as points_bonus  from user_family_member ufa inner join  user_family_day_assets ufda on ufa.family_id = ufda.family_id and ufa.uid = ufda.uid where ufa.family_id = #{query.familyId}" +
            "<if test='query.userLevel == 2'> and ufa.pid = #{query.pid}</if>" +
            "<if test = 'query.userLevel == 1'> and ufa.user_level in(2,3)</if>" +
            "and ufda.created_date = #{query.createdDate} </script>")
    UserFamilyDayAssetsModel selectUserFamilyDayAssetsSum(@Param("query")UserFamilyDayAssetsSumQuery query);
}
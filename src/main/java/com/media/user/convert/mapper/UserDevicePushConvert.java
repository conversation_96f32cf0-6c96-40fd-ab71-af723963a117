package com.media.user.convert.mapper;

import com.media.user.domain.UserDevicePushModel;
import com.media.user.dto.request.DeviceInfoRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring", unmappedSourcePolicy = ReportingPolicy.IGNORE, unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface UserDevicePushConvert {

    UserDevicePushConvert INSTANCE = Mappers.getMapper(UserDevicePushConvert.class);

    UserDevicePushModel req2Model(DeviceInfoRequest request);

}

package com.media.user.scheduler;


import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.media.user.constant.MediaUserConstant;
import com.media.user.domain.ClientUserModel;
import com.media.user.mapper.ClientUserMapper;
import com.media.core.utils.ImageUpS3Util;
import com.media.user.tools.UserImgUtils;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class UserImageFixScheduler {

    @Autowired
    ClientUserMapper clientUserMapper;

    @Autowired
    UserImgUtils UserImgUtils;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 定时修复 user avatar 的信息，每5分钟跑一次，每一次更新 100 条信息
     */
    @XxlJob("robotUserImageFix")
    public void userImageFix() {
        List<ClientUserModel> list =  clientUserMapper.getNeedFixImage100();
        log.info("user avatar need fix size: {}", list.size());

        for(ClientUserModel userModel : list) {
            log.info("uid: {}, start fix avatar ... ", userModel.getUid());
            if(userModel.getVirtualType() != 1) {
                continue;
            }
            if(StringUtils.isEmpty(userModel.getAvatarUrlOriginal())) {
                log.error("uid:{} ,AvatarUrlOriginal is null fix type 9", userModel.getUid());
                userModel.setFixImageType(9);  //标记成 9 不处理了
                clientUserMapper.updateById(userModel);
                continue;
            }
            try{
                //上传 twitter 的头像至 s3
                if(userModel.getAvatarUrlOriginal().startsWith("https://s3.x.me") || userModel.getAvatarUrlOriginal().startsWith("https://s3-video.x.me")) {
                    userModel.setAvatarUrl(userModel.getAvatarUrlOriginal());
                }else{
                    String imageUrl = ImageUpS3Util.upload(userModel.getAvatarUrlOriginal());
                    if(StringUtils.isNotBlank(imageUrl)) {
                        userModel.setAvatarUrl(imageUrl);
                    }else if(StringUtils.isBlank(userModel.getAvatarUrl())){
                        userModel.setAvatarUrl(UserImgUtils.getUserAvatarUrl());
                    }
                }
                userModel.setFixImageType(0);
                clientUserMapper.updateById(userModel);
                //删除个人信息缓存
                stringRedisTemplate.delete(MediaUserConstant.USER_PERSONINFO_REDIS_KEY_PREFIX + userModel.getUid());
                log.info("uid: {} fix avatar finished.", userModel.getUid());
            }catch (Exception e){
                log.error("uid:{}, exception:{}, fix type 9", userModel.getUid(), e.getMessage());
                userModel.setFixImageType(9);  //标记成 9 不处理了
                clientUserMapper.updateById(userModel);
            }
        }

    }

}

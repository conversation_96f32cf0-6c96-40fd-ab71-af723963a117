package com.media.user.controller.api;

import com.media.user.dto.im.RongCloudMessageData;
import com.media.user.dto.response.ClientUserResponse;
import com.media.user.dto.rongCloud.RongCloudCallbackDTO;
import com.media.user.dto.rongCloud.RongCloudCallbackResponse;
import com.media.user.service.cache.ClientUserCacheService;
import com.media.user.service.strategy.MessageHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


@Slf4j
@RestController
@RequestMapping("/user/rc/callback")
public class RongCloudCallbackController {

    @Autowired
    private MessageHandlerContext messageHandlerContext;

    @Autowired
    ClientUserCacheService clientUserCacheService;

    @PostMapping("/message")
    public RongCloudCallbackResponse receiveMessage(
            @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce,
            @RequestParam("signature") String signature,
            @ModelAttribute RongCloudCallbackDTO messageDTO) {
        log.info("rcCallback回调成功，验签参数，时间戳：{}，随机数：{}，签名：{}，回调数据：{}", timestamp, nonce, signature, messageDTO);
        String fromUserId = messageDTO.getFromUserId();
        if (StringUtils.isBlank(fromUserId)) {
            return RongCloudCallbackResponse.success();
        }
        ClientUserResponse userInfo = clientUserCacheService.me(Long.valueOf(fromUserId));
        RongCloudMessageData messageData = new RongCloudMessageData();
        messageData.setTimestamp(timestamp);
        messageData.setNonce(nonce);
        messageData.setSignature(signature);
        messageData.setUserInfo(userInfo);
        messageData.setMessageDTO(messageDTO);

        RongCloudCallbackResponse response = messageHandlerContext.handleMessage(messageData);
        log.info("rcCallback最终回调数据：{}", response);
        return response;
    }


}
package com.media.user.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
@TableName("user_config")
@Data
@Accessors(chain = true)
public class UserConfigModel extends Model<UserConfigModel> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("language")
    private String language;

    @TableField("user_agreement")
    private String userAgreement;

    @TableField("user_agreement_title")
    private String userAgreementTitle;

    @TableField("privacy_agreement")
    private String privacyAgreement;

    @TableField("privacy_agreement_title")
    private String privacyAgreementTitle;

    @TableField("about_us")
    private String aboutUs;

    @TableField("community_content_convention")
    private String communityContentConvention;

    @TableField("wallet_announcement_url")
    private String walletAnnouncementUrl;

    @TableField("mining_rank_list")
    private String miningRankList;

    @TableField("mining_rule")
    private String miningRule;

    @TableField("created_time")
    private Date createdTime;

    @TableField("updated_time")
    private Date updatedTime;
}

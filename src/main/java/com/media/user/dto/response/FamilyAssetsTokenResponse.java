package com.media.user.dto.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


@Data
@Accessors(chain = true)
public class FamilyAssetsTokenResponse {

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 创建日期
     */
    private String createdDate;

    /**
     * 代币数量
     */
    private BigDecimal amount;

    /**
     * 当日获得总积分
     */
    private BigDecimal pointTotal;
}
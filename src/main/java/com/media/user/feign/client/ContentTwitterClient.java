package com.media.user.feign.client;

import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;


@Component
@Slf4j
public class ContentTwitterClient {

    @Value(value = "${content.twitterUri}")
    private String contentUri;

    /**
     * 调用内容系统获取 twitter access Token
     * @param code
     */
    public String getTwitterAccessToken(String code) {
        log.info("调用内容系统获取 twitter access Token。。。");
        HttpRequest httpRequest = new HttpRequest(UrlBuilder.of(contentUri + "/syncCode?authCode=" + code));
        httpRequest.method(Method.GET);
        try (HttpResponse httpResponse = httpRequest.execute()) {
            if (httpResponse.isOk()) {
                String responseBoy = httpResponse.body();
//                System.out.println(responseBoy);
                log.info("调用推荐系统推送用户返回值: {}", responseBoy);
                return null;
            }
        }
        return null;
    }

    /**
     * 推送 twitter access Token
     * @param accessToken
     * @param refreshToken
     */
    @Async("threadPoolTaskExecutor")
    public void sendTwitterAccessToken(String accessToken, String refreshToken) {
        log.info("推送 twitter access Token。。。");
        HttpRequest httpRequest = new HttpRequest(UrlBuilder.of(contentUri + "/syncToken?accessToken=" + accessToken +"&refreshToken=" + refreshToken));
        httpRequest.method(Method.POST);
        try (HttpResponse httpResponse = httpRequest.execute()) {
            if (httpResponse.isOk()) {
                String responseBoy = httpResponse.body();
//                System.out.println(responseBoy);
                log.info("推送完成: {}", responseBoy);
            }else{
                log.info("推送失败: {}", httpResponse.getStatus());
            }
        }
    }

}

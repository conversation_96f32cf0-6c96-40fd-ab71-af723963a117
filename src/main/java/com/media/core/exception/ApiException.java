package com.media.core.exception;

import org.springframework.http.HttpStatus;

/**
 * API统一异常.
 *
 * <AUTHOR>
 */
public class ApiException extends BaseException {

    private static final long serialVersionUID = 1L;

    public ApiException() {
    }

    public ApiException(Integer code) {
        this(code, null);
    }


    public ApiException(Integer code, String message) {
        setCode(code);
        setMessage(message);
        setStatus(HttpStatus.BAD_REQUEST.value());
    }

    @Override
    public String getMessage() {
        return "code=" + getCode();
    }
}

package com.media.user.controller.api;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.availability.ApplicationAvailability;
import org.springframework.boot.availability.LivenessState;
import org.springframework.boot.availability.ReadinessState;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
public class HealthController {

    @Autowired
    private ApplicationAvailability applicationAvailability;

    @GetMapping("/")
    public ResponseEntity<Map<String, Object>> index() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("message", "Application is running");
        response.put("timestamp", System.currentTimeMillis());

        LivenessState livenessState = applicationAvailability.getLivenessState();
        response.put("livenessState", livenessState.toString());

        return ResponseEntity.ok(response);
    }

    /**
     * 健康检查接口
     * 用于检查应用程序是否存活
     * @return 健康状态信息
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("message", "Application is running");
        response.put("timestamp", System.currentTimeMillis());

        LivenessState livenessState = applicationAvailability.getLivenessState();
        response.put("livenessState", livenessState.toString());

        return ResponseEntity.ok(response);
    }

    /**
     * 就绪状态检查接口
     * 用于检查应用程序是否准备好处理请求
     * @return 就绪状态信息
     */
    @GetMapping("/readiness")
    public ResponseEntity<Map<String, Object>> readiness() {
        Map<String, Object> response = new HashMap<>();
        ReadinessState readinessState = applicationAvailability.getReadinessState();

        response.put("status", readinessState == ReadinessState.ACCEPTING_TRAFFIC ? "UP" : "DOWN");
        response.put("message", readinessState == ReadinessState.ACCEPTING_TRAFFIC ?
                "Application is ready to accept traffic" : "Application is not ready to accept traffic");
        response.put("readinessState", readinessState.toString());
        response.put("timestamp", System.currentTimeMillis());

        HttpStatus httpStatus = readinessState == ReadinessState.ACCEPTING_TRAFFIC ?
                HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;

        return new ResponseEntity<>(response, httpStatus);
    }

    /**
     * Liveness 探针接口
     * 用于 Kubernetes 存活探针
     * @return 存活状态信息
     */
    @GetMapping("/liveness")
    public ResponseEntity<Map<String, Object>> liveness() {
        Map<String, Object> response = new HashMap<>();
        LivenessState livenessState = applicationAvailability.getLivenessState();

        response.put("status", livenessState == LivenessState.CORRECT ? "UP" : "DOWN");
        response.put("message", livenessState == LivenessState.CORRECT ?
                "Application is live" : "Application is not live");
        response.put("livenessState", livenessState.toString());
        response.put("timestamp", System.currentTimeMillis());

        HttpStatus httpStatus = livenessState == LivenessState.CORRECT ?
                HttpStatus.OK : HttpStatus.SERVICE_UNAVAILABLE;

        return new ResponseEntity<>(response, httpStatus);
    }
}
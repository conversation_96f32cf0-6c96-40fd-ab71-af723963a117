package com.media.user.feign.client;

import com.media.user.dto.request.internal.ActivityStatusRequest;
import com.media.user.dto.request.internal.RegisterCodeInfoRequest;
import com.media.user.dto.request.internal.UnionInfoRequest;
import com.media.user.dto.request.internal.UnionInviteInfoRequest;
import com.media.user.dto.response.internal.ActivityStatusResponse;
import com.media.user.dto.response.internal.InviteCodeParseResult;
import com.media.user.dto.response.internal.RegisterCodeInfoResponse;
import com.media.user.dto.response.internal.UnionInfoResponse;
import com.xme.xme_base_depends.models.ApiResponse;
import com.xme.xme_base_depends.mq.message.UserEventMessage;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.concurrent.TimeUnit;

@FeignClient(url = "${union.url:http://envtest-api.x.me}", value = "gameClient", configuration = GameClient.GameClientConfiguration.class)
public interface GameClient {

    @Configuration
    class GameClientConfiguration {
        @Bean("unionClientOptions")
        public Request.Options options() {
            return new Request.Options(200, TimeUnit.MILLISECONDS, 2000, TimeUnit.MILLISECONDS, true);
        }
    }

    @GetMapping(value = "/game/internal/union/info")
    ApiResponse<UnionInfoResponse> QueryUnionInfo(@RequestBody UnionInfoRequest request);

    @PostMapping(value = "/game/internal/union/invite/save")
    ApiResponse<Boolean> inviteSave(@RequestBody UnionInviteInfoRequest request);

    @PostMapping(value = "/game/internal/register/relation/sync/register")
    String registerSync(@RequestBody UserEventMessage request);


    @PostMapping(value = "/game/internal/register/relation/activity/isSync")
    ApiResponse<Integer> checkIsSync(@RequestBody String code);

    @PostMapping(value = "/game/internal/register/relation/activity/codeinfo")
    ApiResponse<RegisterCodeInfoResponse> getRegisterCodeInfo(@RequestBody RegisterCodeInfoRequest req);

    @PostMapping(value = "/game/internal/register/relation/parseInviteCode")
    ApiResponse<InviteCodeParseResult> parseInviteCode(@RequestParam String code);

    @GetMapping(value = "/game/internal/short-term-growth/getBitcoinPopupType")
    ApiResponse<Integer> getBitcoinPopupType(@RequestParam Long uid);

    @PostMapping(value = "/game/internal/activity/status")
    ApiResponse<ActivityStatusResponse> getActivityStatus(@RequestBody ActivityStatusRequest req);
}

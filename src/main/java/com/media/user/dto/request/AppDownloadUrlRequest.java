package com.media.user.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class AppDownloadUrlRequest {

    @NotBlank
    @Size(min = 1, max = 64)
    private String platformType;

    @NotBlank
    @Size(min = 1, max = 12)
    private String inviteCode;
}
package com.media.user.controller.api;

import com.media.core.auth.CloudSession;
import com.media.core.auth.RequireAuth;
import com.media.core.config.SystemConfig;
import com.media.core.i18n.I18nConvert;
import com.media.core.request.ClientInfoContext;
import com.media.user.dto.response.UserConfigResponse;

import com.media.user.enums.LanguageEnums;
import com.media.user.enums.WelcomeMessageEnum;
import com.media.user.service.ConfigSwitchService;
import com.media.user.service.UserConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/user/api/userConfig")
public class UserConfigApiController {

    @Autowired
    private UserConfigService userConfigService;

    @Autowired
    ConfigSwitchService configSwitchService;

    @Autowired
    SystemConfig systemConfig;



    /**
     * 审核 app 期间返回固定的文案
     */
    @PostMapping("/getUserConfig")
    public UserConfigResponse getUserConfig(){
        Integer check = configSwitchService.switchConfig();
        // 0 为app 提审状态
        if(check == 0){
            log.info("app is checking");
            UserConfigResponse userConfigResponse = new UserConfigResponse();
            userConfigResponse.setId(0L);
            userConfigResponse.setUserAgreement("https://api.x.me/policy/UserAgreement_en.html");
            userConfigResponse.setUserAgreementTitle("");
            userConfigResponse.setPrivacyAgreement("https://api.x.me/policy/Privacy_en.html");
            userConfigResponse.setPrivacyAgreementTitle("");
            userConfigResponse.setAboutUs("https://api.x.me/policy/AboutUS_en.html");
            userConfigResponse.setCommunityContentConvention("https://api.x.me/policy/Community_en.html");
            userConfigResponse.setMiningRankList("https://api.x.me/policy/policy/MiningRankingList_en.html");
            return userConfigResponse;
        }
        UserConfigResponse configResponse =  userConfigService.getUserConfig(ClientInfoContext.getLanguage());
        if (!systemConfig.isWalletAnnouncementEnabled()) {
            configResponse.setWalletAnnouncementUrl("");
        } else {
            // 填充钱包公告相关的多语言文本
            configResponse.setWalletAnnouncementTitle(I18nConvert.getI18nMessage("wallet.withdraw.rule.title",ClientInfoContext.getLanguage()));
            configResponse.setWalletAnnouncementSubTitle(I18nConvert.getI18nMessage("wallet.withdraw.rule.sub_title",ClientInfoContext.getLanguage()));
            configResponse.setWalletAnnouncementCheck(I18nConvert.getI18nMessage("wallet.withdraw.rule.check",ClientInfoContext.getLanguage()));
        }
        return configResponse;
    }

    /**
     * 人脸识别的 xme 的数量
     */
    @PostMapping("/face/xme")
    @RequireAuth
    public Map<String, Integer> faceXme(){
        return userConfigService.calcFaceXme(CloudSession.getUid());
    }


    /**
     * 实时调用的配置
     */
    @GetMapping("/variable")
    public Map<String, Object> VariableConfig(){
        Map<String, Object> rtn = new HashMap<>();
        LanguageEnums language = ClientInfoContext.getLanguage();
        if(language == null) {
            language = LanguageEnums.en_US;
        }
        rtn.put("welcome", WelcomeMessageEnum.getContent(language.getValue()));
        return rtn;
    }
}
